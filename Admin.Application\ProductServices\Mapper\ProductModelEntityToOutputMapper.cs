// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ProductServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ProductServices.Mapper;

/// <summary>
/// 产品模型实体到输出DTO的映射配置
/// </summary>
public class ProductModelEntityToOutputMapper : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // 配置 ProductModelEntity 到 ProductModelOutput 的映射，包含设备组名称转换
        config.ForType<ProductModelEntity, ProductModelOutput>()
            .Map(dest => dest.DeviceGroupName, src => GetDeviceGroupName(src.DeviceGroup));

        // 配置 ProductModelEntity 到 ProductModelSimpleOutput 的映射
        config.ForType<ProductModelEntity, ProductModelSimpleOutput>()
            .Map(dest => dest.DeviceGroupName, src => GetDeviceGroupName(src.DeviceGroup));
    }

    /// <summary>
    /// 获取设备组名称
    /// </summary>
    private static string GetDeviceGroupName(int deviceGroup) => deviceGroup switch
    {
        1 => "温湿度",
        2 => "漏水检测",
        3 => "空调",
        4 => "UPS",
        5 => "配电",
        6 => "开关",
        7 => "发电机",
        8 => "红外",
        9 => "门禁",
        10 => "传感器",
        11 => "冷通道",
        _ => "未知设备组"
    };
} 