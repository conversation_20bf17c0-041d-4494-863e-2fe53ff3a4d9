// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Modbus.Models;
using Admin.Communication.Mqtt.Services;
using Admin.Multiplex.Contracts.Consts;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Text;
using Volo.Abp.Application.Services;

namespace Admin.Application.ModbusServices;

/// <summary>
/// Modbus测试服务
/// 提供Modbus指令测试和调试功能
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class ModbusTestService(
    ISqlSugarClient db,
    MqttBrokerService mqttBroker,
    ILogger<ModbusTestService> logger) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly MqttBrokerService _mqttBroker = mqttBroker;
    private readonly ILogger<ModbusTestService> _logger = logger;

    /// <summary>
    /// 手动发送Modbus指令
    /// </summary>
    /// <param name="input">测试输入</param>
    /// <returns>发送结果</returns>
    public async Task<ModbusTestResult> SendTestCommandAsync(ModbusTestInput input)
    {
        try
        {
            _logger.LogInformation("发送Modbus测试指令: DeviceId={DeviceId}, Command={Command}",
                input.DeviceId, input.Command);

            // 验证设备是否存在
            var device = await _db.Queryable<DeviceEntity>()
                .Where(d => d.Id == input.DeviceId)
                .FirstAsync();

            if (device == null)
            {
                return new ModbusTestResult
                {
                    Success = false,
                    Message = "设备不存在"
                };
            }

            // 构建MQTT主题
            var topic = $"/devices/{input.DeviceId}/modbus/command/down";
            var payload = Encoding.UTF8.GetBytes(input.Command);

            // 发送MQTT消息
            await _mqttBroker.PublishAsync(topic, payload);

            _logger.LogInformation("Modbus测试指令发送成功: Topic={Topic}, Command={Command}",
                topic, input.Command);

            return new ModbusTestResult
            {
                Success = true,
                Message = "指令发送成功",
                Topic = topic,
                Command = input.Command,
                SendTime = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送Modbus测试指令失败: DeviceId={DeviceId}", input.DeviceId);

            return new ModbusTestResult
            {
                Success = false,
                Message = $"发送失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 模拟设备响应
    /// </summary>
    /// <param name="input">响应输入</param>
    /// <returns>模拟结果</returns>
    public async Task<ModbusTestResult> SimulateDeviceResponseAsync(ModbusResponseInput input)
    {
        try
        {
            _logger.LogInformation("模拟设备响应: DeviceId={DeviceId}, Response={Response}",
                input.DeviceId, input.ResponseData);

            // 构建响应主题
            var topic = $"/devices/{input.DeviceId}/modbus/command/up";
            var payload = Convert.FromHexString(input.ResponseData.Replace(" ", ""));

            // 发送模拟响应
            await _mqttBroker.PublishAsync(topic, payload);

            _logger.LogInformation("设备响应模拟成功: Topic={Topic}, Response={Response}",
                topic, input.ResponseData);

            return new ModbusTestResult
            {
                Success = true,
                Message = "响应模拟成功",
                Topic = topic,
                Command = input.ResponseData,
                SendTime = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模拟设备响应失败: DeviceId={DeviceId}", input.DeviceId);

            return new ModbusTestResult
            {
                Success = false,
                Message = $"模拟失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 获取设备指令列表（用于测试）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>指令列表</returns>
    public async Task<List<DeviceInstructionTestOutput>> GetDeviceInstructionsForTestAsync(long deviceId)
    {
        var instructions = await _db.Queryable<DeviceInstructionEntity>()
            .Where(di => di.DeviceId == deviceId)
            .OrderBy(di => di.Id)
            .ToListAsync();

        return instructions.Select(i => new DeviceInstructionTestOutput
        {
            Id = i.Id,
            InstructionName = i.InstructionName,
            Command = i.send_str,
            ReadInterval = i.ReadInterval,
            ResponseTime = i.ResponseTime,
            RetryCount = i.RetryCount,
            IsEnabled = i.IsEnabled
        }).ToList();
    }

    /// <summary>
    /// 生成标准Modbus RTU指令
    /// </summary>
    /// <param name="input">指令参数</param>
    /// <returns>生成的指令</returns>
    public ModbusCommandResult GenerateModbusCommand(ModbusCommandInput input)
    {
        try
        {
            // 简化的Modbus RTU指令生成
            var command = new List<byte>
            {
                (byte)input.SlaveAddress,  // 从站地址
                (byte)input.FunctionCode   // 功能码
            };

            // 添加起始地址（大端序）
            command.Add((byte)(input.StartAddress >> 8));
            command.Add((byte)(input.StartAddress & 0xFF));

            // 添加数量（大端序）
            command.Add((byte)(input.Quantity >> 8));
            command.Add((byte)(input.Quantity & 0xFF));

            // 计算CRC16（简化实现）
            var crc = CalculateCRC16(command.ToArray());
            command.Add((byte)(crc & 0xFF));
            command.Add((byte)(crc >> 8));

            var hexCommand = Convert.ToHexString(command.ToArray());

            return new ModbusCommandResult
            {
                Success = true,
                Command = hexCommand,
                Description = $"读取从站{input.SlaveAddress}，功能码{input.FunctionCode:X2}，地址{input.StartAddress}，数量{input.Quantity}"
            };
        }
        catch (Exception ex)
        {
            return new ModbusCommandResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// 简化的CRC16计算
    /// </summary>
    private ushort CalculateCRC16(byte[] data)
    {
        ushort crc = 0xFFFF;
        for (int i = 0; i < data.Length; i++)
        {
            crc ^= data[i];
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= 0xA001;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }
        return crc;
    }
}

/// <summary>
/// Modbus测试输入
/// </summary>
public class ModbusTestInput
{
    public long DeviceId { get; set; }
    public string Command { get; set; } = string.Empty;
}

/// <summary>
/// Modbus响应输入
/// </summary>
public class ModbusResponseInput
{
    public long DeviceId { get; set; }
    public string ResponseData { get; set; } = string.Empty;
}

/// <summary>
/// Modbus测试结果
/// </summary>
public class ModbusTestResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Topic { get; set; }
    public string? Command { get; set; }
    public DateTime? SendTime { get; set; }
}

/// <summary>
/// 设备指令测试输出
/// </summary>
public class DeviceInstructionTestOutput
{
    public long Id { get; set; }
    public string InstructionName { get; set; } = string.Empty;
    public string Command { get; set; } = string.Empty;
    public int ReadInterval { get; set; }
    public int ResponseTime { get; set; }
    public int RetryCount { get; set; }
    public bool IsEnabled { get; set; }
}

/// <summary>
/// Modbus指令输入
/// </summary>
public class ModbusCommandInput
{
    public int SlaveAddress { get; set; } = 1;
    public int FunctionCode { get; set; } = 3;
    public int StartAddress { get; set; } = 0;
    public int Quantity { get; set; } = 2;
}

/// <summary>
/// Modbus指令生成结果
/// </summary>
public class ModbusCommandResult
{
    public bool Success { get; set; }
    public string Command { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ErrorMessage { get; set; }
}
