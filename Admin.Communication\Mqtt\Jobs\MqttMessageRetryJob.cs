// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Admin.Multiplex.Contracts.Enums.Mqtt;
using Admin.Multiplex.Contracts.BackgroundArgs;

namespace Admin.Communication.Mqtt.Jobs;

/// <summary>
/// MQTT消息重试后台作业
/// 处理QoS 1和QoS 2消息的超时重发
/// </summary>
public class MqttMessageRetryJob(
    ISqlSugarClient db,
    IMqttConnectionManager connectionManager,
    IBackgroundJobManager backgroundJobManager,
    ILogger<MqttMessageRetryJob> logger) : AsyncBackgroundJob<MqttMessageRetryArgs>, ITransientDependency
{
    private readonly ISqlSugarClient _db = db;
    private readonly IMqttConnectionManager _connectionManager = connectionManager;
    private readonly IBackgroundJobManager _backgroundJobManager = backgroundJobManager;
    private readonly ILogger<MqttMessageRetryJob> _logger = logger;

    public override async Task ExecuteAsync(MqttMessageRetryArgs args)
    {
        try
        {
            _logger.LogDebug("开始处理MQTT消息重试: ClientId={ClientId}, MessageId={MessageId}, RetryCount={RetryCount}", 
                args.ClientId, args.MessageId, args.RetryCount);

            // 1. 检查客户端是否仍然连接
            if (!_connectionManager.IsConnected(args.ClientId))
            {
                _logger.LogDebug("客户端已断开连接，跳过消息重试: ClientId={ClientId}", args.ClientId);
                return;
            }

            // 2. 从数据库获取待重试的消息
            var pendingMessage = await _db.Queryable<MqttPendingMessageEntity>()
                .FirstAsync(x => x.ClientId == args.ClientId && 
                                x.PacketId == args.MessageId && 
                                x.Status != (int)MqttMessageStatusEnum.Acknowledged);

            if (pendingMessage == null)
            {
                _logger.LogDebug("未找到待重试的消息: ClientId={ClientId}, MessageId={MessageId}", 
                    args.ClientId, args.MessageId);
                return;
            }

            // 3. 检查是否已超过最大重试次数
            if (args.RetryCount >= args.MaxRetryCount)
            {
                _logger.LogWarning("消息重试已达到最大次数，标记为失败: ClientId={ClientId}, MessageId={MessageId}, RetryCount={RetryCount}", 
                    args.ClientId, args.MessageId, args.RetryCount);

                // 更新消息状态为发送失败
                await _connectionManager.UpdatePendingMessageStatusAsync(
                    pendingMessage.MessageId, 
                    (int)MqttMessageStatusEnum.SendFailed, 
                    $"重试次数已达到最大值({args.MaxRetryCount})");

                return;
            }

            // 4. 检查消息是否已过期
            if (pendingMessage.ExpiryTime.HasValue && pendingMessage.ExpiryTime.Value <= DateTime.Now)
            {
                _logger.LogDebug("消息已过期，跳过重试: ClientId={ClientId}, MessageId={MessageId}, ExpiryTime={ExpiryTime}", 
                    args.ClientId, args.MessageId, pendingMessage.ExpiryTime);

                // 更新消息状态为过期
                await _connectionManager.UpdatePendingMessageStatusAsync(
                    pendingMessage.MessageId, 
                    (int)MqttMessageStatusEnum.Expired, 
                    "消息已过期");

                return;
            }

            // 5. 重新发送消息
            var connection = _connectionManager.GetConnection(args.ClientId);
            if (connection == null)
            {
                _logger.LogWarning("无法获取客户端连接，跳过重试: ClientId={ClientId}", args.ClientId);
                return;
            }

            // 构造重发消息
            var retryMessage = new MqttPublishMessage
            {
                Topic = pendingMessage.Topic,
                Payload = pendingMessage.Payload,
                QualityOfService = (byte)pendingMessage.Qos,
                Retain = pendingMessage.Retain,
                IsDuplicate = true, // 重发消息设置DUP标志
                MessageId = (ushort)pendingMessage.PacketId.GetValueOrDefault()
            };

            // 发送消息（这里需要通过IMqttBroker接口发送，但为了避免循环依赖，我们直接更新重试计数）
            _logger.LogDebug("准备重发消息: ClientId={ClientId}, MessageId={MessageId}, Topic={Topic}", 
                args.ClientId, args.MessageId, pendingMessage.Topic);

            // 6. 更新重试计数
            await _connectionManager.IncrementMessageRetryCountAsync(pendingMessage.MessageId, 
                $"重试发送消息，当前重试次数: {args.RetryCount + 1}");

            // 7. 安排下一次重试（如果还没达到最大重试次数）
            if (args.RetryCount + 1 < args.MaxRetryCount)
            {
                var nextRetryArgs = new MqttMessageRetryArgs
                {
                    ClientId = args.ClientId,
                    MessageId = args.MessageId,
                    RetryCount = args.RetryCount + 1,
                    MaxRetryCount = args.MaxRetryCount
                };

                // 延迟执行下一次重试（30秒后）
                await _backgroundJobManager.EnqueueAsync(nextRetryArgs, delay: TimeSpan.FromSeconds(30));
                
                _logger.LogDebug("已安排下一次重试: ClientId={ClientId}, MessageId={MessageId}, NextRetryCount={NextRetryCount}", 
                    args.ClientId, args.MessageId, args.RetryCount + 1);
            }

            _logger.LogDebug("MQTT消息重试处理完成: ClientId={ClientId}, MessageId={MessageId}", 
                args.ClientId, args.MessageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理MQTT消息重试时发生错误: ClientId={ClientId}, MessageId={MessageId}", 
                args.ClientId, args.MessageId);
            throw;
        }
    }
}
