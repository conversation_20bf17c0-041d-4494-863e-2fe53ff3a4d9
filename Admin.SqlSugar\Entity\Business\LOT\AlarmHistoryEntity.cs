// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 告警历史实体
/// </summary>
[SugarTable("LOT_ALARM_HISTORY")]
public partial class AlarmHistoryEntity
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 事件id
    /// </summary>
    [SugarColumn(ColumnName = "EVENT_ID")]
    public string EventId { get; set; }

    /// <summary>
    /// 告警值
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_VALUE")]
    public decimal AlarmValue { get; set; }

    /// <summary>
    /// 告警值描述
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_DESCRIPTION")]
    public string AlarmDescription { get; set; }

    /// <summary>
    /// 告警时间
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_TIME")]
    public DateTime AlarmTime { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 告警状态 (1:待确认, 2:已确认, 3:待处理, 4:已处理)
    /// </summary>
    /// 3、4预留方便后期拓展
    [SugarColumn(ColumnName = "ALARM_STATUS")]
    public int AlarmStatus { get; set; }

    /// <summary>
    /// 告警确认人
    /// </summary>
    [SugarColumn(ColumnName = "CONFIRMED_PEOPLE")]
    public string? ConfirmedPeople { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    [SugarColumn(ColumnName = "CONFIRMED_TIME")]
    public DateTime? ConfirmedTime { get; set; }

    /// <summary>
    /// 是否已解除
    /// 0： 未接触 1：已解除
    /// </summary>
    [SugarColumn(ColumnName = "IS_RELEASED")]
    public int IsReleased { get; set; }

    /// <summary>
    /// 解除时间
    /// </summary>
    [SugarColumn(ColumnName = "RELEASE_TIME")]
    public DateTime? ReleaseTime { get; set; }

    /// <summary>
    /// 解除数值
    /// </summary>
    [SugarColumn(ColumnName = "RELEASE_VALUE")]
    public decimal? ReleaseValue { get; set; }

    /// <summary>
    /// 解除描述
    /// </summary>
    [SugarColumn(ColumnName = "RELEASE_DESCRIPTION")]
    public string ReleaseDescription { get; set; }

    /// <summary>
    /// 处理人
    /// </summary>
    // [SugarColumn(ColumnName = "PROCESSED_BY")]
    // public long? ProcessedBy { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    // [SugarColumn(ColumnName = "PROCESSED_TIME")]
    // public DateTime? ProcessedTime { get; set; }

    /// <summary>
    /// 处理备注
    /// </summary>
    // [SugarColumn(ColumnName = "PROCESS_REMARK")]
    // public string ProcessRemark { get; set; }
}
