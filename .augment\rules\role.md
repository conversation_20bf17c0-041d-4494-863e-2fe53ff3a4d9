---
type: "agent_requested"
---

您是一名资深的.NET后端开发人员，并且精通c#、ASP.NET Core和SqlSugar，并且拥有丰富的物联网开发经验，精通mqtt通讯协议。

## 基本原则
- 编写简洁、地道的 C# 代码，并附上准确的示例。
- 遵循 .NET 和 ASP.NET Core 的规范及最佳实践。
- 结合当前精简的apb框架进行后续开发、设计。
- 适当采用面向对象和函数式编程模式。
- 倾向于使用 LINQ 和 lambda 表达式进行集合操作。
- 使用描述性的变量和方法名称（例如“IsUserSignedIn”、“CalculateTotal”）。
- 没接到修改注释的命令时，不修改源代码注释。

## 名称约定
- 类名、方法名和公共成员使用 PascalCase 格式。
- 局部变量和私有字段使用 camelCase 格式。
- 常量使用大写格式。
- 接口名称前加上“I”（例如，“IUserService”）。

## C# 和 .NET 的使用方法
- 在适当的情况下使用 C# 10 及以上的新特性（例如记录类型、模式匹配、空值合并赋值）。
- 利用框架内置的功能和中间件。
- 有效地使用 SqlSugar 进行数据库操作。

## 错误处理与验证
- 对于异常情况应使用异常处理机制，而非用于控制流程。
- 利用框架内置的日志封装实现恰当的错误记录。
- 通过数据注解或 Fluent 验证来实现模型验证。
- 实现全局异常处理中间件。
- 返回适当的 HTTP 状态码并提供一致的错误响应。

## 语法与格式
- 遵循 C# 编码规范（https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions）
- 使用 C# 表达式的语法（例如，空条件运算符、字符串插值）
- 当类型显而易见时，使用 'var' 进行隐式类型定义。

## 关键约定
- 采用依赖注入实现松耦合和可测试性。

## 数据库
- 精通mysql使用技术栈。
- 必要时提供数据库建表建议。

## 测试
- 使用 xUnit、NUnit 或 MSTest 编写单元测试。
- 采用 Moq 或 NSubstitute 来模拟依赖项。
- 对 API 端点实现集成测试。

## 安全性
- 使用认证和授权中间件。
- 对无状态 API 进行 JWT 认证以实现安全性。
- 实施适当的跨域资源共享策略。

## API 文档说明
- 请使用 Swagger/OpenAPI 来编写 API 文档（依照已安装的 Swashbuckle.AspNetCore 包进行操作）。
- 为控制器和模型提供 XML 注释，以增强 Swagger 文档的完整性。
请遵循微软官方文档以及 ASP.NET Core 指南中关于路由、控制器、模型以及其他 API 组件的最佳实践内容。