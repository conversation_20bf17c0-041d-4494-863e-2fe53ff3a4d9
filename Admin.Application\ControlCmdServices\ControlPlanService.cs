// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ControlCmdServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ControlCmdServices;

/// <summary>
/// 控制计划服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class ControlPlanService(ISqlSugarClient db, Repository<ControlPlanEntity> repository) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<ControlPlanEntity> _repository = repository;

    /// <summary>
    /// 分页查询控制计划
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页列表</returns>
    public async Task<PagedList<ControlPlanOutput>> GetPagedListAsync(ControlPlanPagedInput input)
    {
        var query = _db.Queryable<ControlPlanEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.Name), cp => cp.Name.Contains(input.Name!))
            .WhereIF(input.TriggerType.HasValue, cp => cp.TriggerType == input.TriggerType!.Value)
            .WhereIF(input.IsAppControl.HasValue, cp => cp.IsAppControl == input.IsAppControl!.Value)
            .Select(cp => new ControlPlanOutput
            {
                Id = cp.Id,
                Name = cp.Name,
                TriggerType = cp.TriggerType,
                TriggerTypeName = cp.TriggerType == 1 ? "重复执行" : cp.TriggerType == 2 ? "条件执行" : "未知",
                ExecuteType = cp.ExecuteType,
                ExecuteTypeName = cp.ExecuteType == 1 ? "时间点" : cp.ExecuteType == 2 ? "间隔" : null,
                TimePoint = cp.TimePoint,
                Interval = cp.Interval,
                IntervalUnit = cp.IntervalUnit,
                IntervalUnitName = cp.IntervalUnit == 1 ? "分钟" : cp.IntervalUnit == 2 ? "小时" : null,
                Method = cp.Method,
                MethodName = cp.Method == 1 ? "大于参考值" : cp.Method == 2 ? "小于参考值" : cp.Method == 3 ? "等于参考值" : null,
                ParameterId = cp.ParameterId,
                ReferenceValue = cp.ReferenceValue,
                CmdIds = cp.CmdIds,
                CmdInterval = cp.CmdInterval,
                IsAppControl = cp.IsAppControl,
                IsAppControlName = cp.IsAppControl == 1 ? "启用" : "不启用"
            })
            .OrderByDescending(cp => cp.Id);

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList.Adapt<PagedList<ControlPlanOutput>>();
    }

    /// <summary>
    /// 根据ID获取控制计划
    /// </summary>
    /// <param name="id">计划ID</param>
    /// <returns>控制计划信息</returns>
    public async Task<ControlPlanOutput> GetByIdAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("控制计划不存在");

        return new ControlPlanOutput
        {
            Id = entity.Id,
            Name = entity.Name,
            TriggerType = entity.TriggerType,
            TriggerTypeName = entity.TriggerType == 1 ? "重复执行" : entity.TriggerType == 2 ? "条件执行" : "未知",
            ExecuteType = entity.ExecuteType,
            ExecuteTypeName = entity.ExecuteType == 1 ? "时间点" : entity.ExecuteType == 2 ? "间隔" : null,
            TimePoint = entity.TimePoint,
            Interval = entity.Interval,
            IntervalUnit = entity.IntervalUnit,
            IntervalUnitName = entity.IntervalUnit == 1 ? "分钟" : entity.IntervalUnit == 2 ? "小时" : null,
            Method = entity.Method,
            MethodName = entity.Method == 1 ? "大于参考值" : entity.Method == 2 ? "小于参考值" : entity.Method == 3 ? "等于参考值" : null,
            ParameterId = entity.ParameterId,
            ReferenceValue = entity.ReferenceValue,
            CmdIds = entity.CmdIds,
            CmdInterval = entity.CmdInterval,
            IsAppControl = entity.IsAppControl,
            IsAppControlName = entity.IsAppControl == 1 ? "启用" : "不启用"
        };
    }

    /// <summary>
    /// 添加控制计划
    /// </summary>
    /// <param name="input">计划信息</param>
    /// <returns>计划信息</returns>
    public async Task<ControlPlanOutput> AddAsync(AddControlPlanInput input)
    {
        // 1. 验证计划名称是否重复
        await ValidatePlanNameUniqueAsync(input.Name);

        // 2. 验证输入参数
        await ValidateInputAsync(input);

        // 3. 验证指令ID是否存在
        await ValidateCommandIdsExistAsync(input.CmdIds);

        // 4. 创建控制计划实体
        var entity = input.Adapt<ControlPlanEntity>();

        // 5. 保存到数据库
        var id = await _repository.InsertReturnSnowflakeIdAsync(entity);
        entity.Id = id;

        // 6. 返回结果
        return await GetByIdAsync(id);
    }

    /// <summary>
    /// 更新控制计划
    /// </summary>
    /// <param name="input">计划信息</param>
    /// <returns>计划信息</returns>
    public async Task<ControlPlanOutput> UpdateAsync(UpdateControlPlanInput input)
    {
        // 1. 验证计划是否存在
        var existingEntity = await _repository.GetByIdAsync(input.Id) ??
            throw PersistdValidateException.Message("控制计划不存在");

        // 2. 验证计划名称是否重复（排除当前计划）
        await ValidatePlanNameUniqueAsync(input.Name, input.Id);

        // 3. 验证输入参数
        await ValidateInputAsync(input);

        // 4. 验证指令ID是否存在
        await ValidateCommandIdsExistAsync(input.CmdIds);

        // 5. 更新实体
        var entity = input.Adapt<ControlPlanEntity>();

        await _repository.UpdateAsync(entity);

        // 6. 返回结果
        return await GetByIdAsync(input.Id);
    }

    /// <summary>
    /// 删除控制计划
    /// </summary>
    /// <param name="id">计划ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("控制计划不存在");

        return await _repository.DeleteAsync(entity);
    }

    /// <summary>
    /// 验证输入参数
    /// </summary>
    /// <param name="input">输入参数</param>
    private static async Task ValidateInputAsync(dynamic input)
    {
        await Task.CompletedTask;

        // 重复执行验证
        if (input.TriggerType == 1)
        {
            if (!input.ExecuteType.HasValue)
                throw PersistdValidateException.Message("重复执行时执行方式不能为空");

            if (input.ExecuteType == 1) // 时间点
            {
                if (string.IsNullOrEmpty(input.TimePoint))
                    throw PersistdValidateException.Message("执行方式为时间点时，时间点不能为空");

                // 验证时间点格式
                var timePoints = input.TimePoint.Split(',');
                foreach (var timePoint in timePoints)
                {
                    if (!TimeSpan.TryParseExact(timePoint.Trim(), @"hh\:mm", null, out TimeSpan _))
                        throw PersistdValidateException.Message($"时间点格式错误：{timePoint}，正确格式为HH:mm");
                }
            }
            else if (input.ExecuteType == 2) // 间隔
            {
                if (!input.Interval.HasValue || input.Interval <= 0)
                    throw PersistdValidateException.Message("执行方式为间隔时，间隔必须大于0");

                if (!input.IntervalUnit.HasValue || (input.IntervalUnit != 1 && input.IntervalUnit != 2))
                    throw PersistdValidateException.Message("执行方式为间隔时，间隔单位必须为1(分钟)或2(小时)");
            }
        }
        // 条件执行验证
        else if (input.TriggerType == 2)
        {
            if (!input.Method.HasValue || (input.Method < 1 || input.Method > 3))
                throw PersistdValidateException.Message("条件执行时，方法必须为1(大于)、2(小于)或3(等于)");

            if (!input.ParameterId.HasValue || input.ParameterId <= 0)
                throw PersistdValidateException.Message("条件执行时，关联参数ID不能为空");

            if (!input.ReferenceValue.HasValue)
                throw PersistdValidateException.Message("条件执行时，参考值不能为空");
        }
    }

    /// <summary>
    /// 验证指令ID是否存在
    /// </summary>
    /// <param name="cmdIds">指令ID集合字符串</param>
    private async Task ValidateCommandIdsExistAsync(string cmdIds)
    {
        if (string.IsNullOrEmpty(cmdIds))
            throw PersistdValidateException.Message("指令ID集不能为空");

        var cmdIdArray = cmdIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
        if (cmdIdArray.Length == 0)
            throw PersistdValidateException.Message("指令ID集不能为空");

        var cmdIdList = new List<long>();
        foreach (var cmdIdStr in cmdIdArray)
        {
            if (!long.TryParse(cmdIdStr.Trim(), out var cmdId))
                throw PersistdValidateException.Message($"指令ID格式错误：{cmdIdStr}");
            cmdIdList.Add(cmdId);
        }

        // 验证指令是否存在
        var existingCmdIds = await _db.Queryable<ControlCmdEntity>()
            .Where(cc => cmdIdList.Contains(cc.Id))
            .Select(cc => cc.Id)
            .ToListAsync();

        var notExistCmdIds = cmdIdList.Except(existingCmdIds).ToList();
        if (notExistCmdIds.Count > 0)
            throw PersistdValidateException.Message($"以下指令ID不存在：{string.Join(",", notExistCmdIds)}");
    }

    /// <summary>
    /// 验证计划名称是否唯一
    /// </summary>
    /// <param name="name">计划名称</param>
    /// <param name="excludeId">排除的计划ID（用于更新时排除自身）</param>
    private async Task ValidatePlanNameUniqueAsync(string name, long? excludeId = null)
    {
        var query = _db.Queryable<ControlPlanEntity>()
            .Where(cp => cp.Name == name);

        if (excludeId.HasValue)
        {
            query = query.Where(cp => cp.Id != excludeId.Value);
        }

        var exists = await query.AnyAsync();

        if (exists)
        {
            throw PersistdValidateException.Message($"已存在相同名称的控制计划: {name}");
        }
    }
}
