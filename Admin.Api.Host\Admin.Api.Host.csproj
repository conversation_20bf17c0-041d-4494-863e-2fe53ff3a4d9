﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    <NoWarn>1701;1702;1591;8618;8601;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.Application\Admin.Application.csproj" />
    <ProjectReference Include="..\Admin.Communication\Admin.Communication.csproj" />
    <ProjectReference Include="..\Admin.Core\Admin.Core.csproj" />
    <ProjectReference Include="..\Admin.Multiplex.Contracts\Admin.Multiplex.Contracts.csproj" />
    <ProjectReference Include="..\Admin.Multiplex\Admin.Multiplex.csproj" />
    <ProjectReference Include="..\Admin.Workflow\Admin.Workflow.csproj" />
  </ItemGroup>

</Project>
