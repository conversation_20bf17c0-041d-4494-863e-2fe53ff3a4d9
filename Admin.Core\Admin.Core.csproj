﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <NoWarn>1701;1702;1591;8618;8602;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Flurl.Http" Version="4.0.0" />
    <PackageReference Include="IP2Region.Net" Version="2.0.2" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="8.0.7" />
    <PackageReference Include="Namotion.Reflection" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.2" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.SignalR" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.Autofac" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.BackgroundJobs" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.BackgroundWorkers" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.BlobStoring.FileSystem" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.Validation" Version="8.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.SqlSugar\Admin.SqlSugar.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Ip2region\ip2region.xdb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
