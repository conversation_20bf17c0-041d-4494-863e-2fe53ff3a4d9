// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Communication.Modbus;

/// <summary>
/// Modbus响应处理器接口
/// 负责处理设备返回的Modbus RTU响应数据
/// </summary>
public interface IModbusResponseHandler
{
    /// <summary>
    /// 处理Modbus响应数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="responseData">响应数据</param>
    /// <returns>处理任务</returns>
    Task HandleModbusResponseAsync(string deviceId, byte[] responseData);

    /// <summary>
    /// 检查是否有等待响应的指令
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>是否有等待响应的指令</returns>
    bool HasPendingInstruction(string deviceId);
}
