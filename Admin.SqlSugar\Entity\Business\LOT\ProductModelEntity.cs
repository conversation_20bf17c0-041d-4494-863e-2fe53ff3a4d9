// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 产品模型实体
/// </summary>
[SugarTable("LOT_PRODUCT_MODEL")]
public partial class ProductModelEntity : BaseEntity
{
    /// <summary>
    /// 产品ID
    /// </summary>
    [SugarColumn(ColumnName = "PRODUCT_ID")]
    public long ProductId { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    [SugarColumn(ColumnName = "MODEL_NAME")]
    public string ModelName { get; set; }

    /// <summary>
    /// 设备组 (1:温湿度 2:漏水检测 3:空调 4:UPS 5:配电 6:开关 7:发电机 8:红外 9:门禁 10:传感器 11.冷通道)
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_GROUP")]
    public int DeviceGroup { get; set; }

    /// <summary>
    /// 模型描述
    /// </summary>
    [SugarColumn(ColumnName = "DESCRIPTION")]
    public string Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnName = "IS_ENABLED")]
    public bool IsEnabled { get; set; } = true;
}