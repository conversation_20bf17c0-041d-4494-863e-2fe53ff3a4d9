using Admin.Communication.Mqtt.Configuration;
using Admin.Multiplex.Contracts;
using System.ComponentModel.DataAnnotations;

namespace Admin.Application.MqttBrokerServices.Dto
{
    /// <summary>
    /// ACL规则查询输入
    /// </summary>
    public class GetAclRulesInput : PaginationParams
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; }

        /// <summary>
        /// 访问类型
        /// </summary>
        public string AccessType { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// 权限（allow/deny）
        /// </summary>
        public string Permission { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// ACL规则输出
    /// </summary>
    public class AclRuleOutput
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 访问类型
        /// </summary>
        public MqttAccessType AccessType { get; set; }

        /// <summary>
        /// 是否允许访问
        /// </summary>
        public bool Allow { get; set; }

        /// <summary>
        /// 用户名（可选）
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 客户端ID（可选）
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// IP地址（可选）
        /// </summary>
        public string IpAddress { get; set; }

        /// <summary>
        /// MQTT主题
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 生效开始时间
        /// </summary>
        public DateTime? EffectiveStartTime { get; set; }

        /// <summary>
        /// 生效结束时间
        /// </summary>
        public DateTime? EffectiveEndTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public long CreateBy { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public long? UpdateBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }

    /// <summary>
    /// 添加ACL规则输入
    /// </summary>
    public class AddAclRuleInput
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        [Required(ErrorMessage = "规则名称不能为空")]
        [StringLength(100, ErrorMessage = "规则名称长度不能超过100个字符")]
        public string RuleName { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [Range(0, 999, ErrorMessage = "优先级必须在0-999之间")]
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 访问类型
        /// </summary>
        [Required(ErrorMessage = "访问类型不能为空")]
        public MqttAccessType AccessType { get; set; } = MqttAccessType.All;

        /// <summary>
        /// 是否允许访问
        /// </summary>
        public bool Allow { get; set; } = true;

        /// <summary>
        /// 用户名（可选）
        /// </summary>
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; }

        /// <summary>
        /// 客户端ID（可选）
        /// </summary>
        [StringLength(100, ErrorMessage = "客户端ID长度不能超过100个字符")]
        public string ClientId { get; set; }

        /// <summary>
        /// IP地址（可选）
        /// </summary>
        [StringLength(50, ErrorMessage = "IP地址长度不能超过50个字符")]
        public string IpAddress { get; set; }

        /// <summary>
        /// MQTT主题
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        [StringLength(500, ErrorMessage = "主题长度不能超过500个字符")]
        public string Topic { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 生效开始时间
        /// </summary>
        public DateTime? EffectiveStartTime { get; set; }

        /// <summary>
        /// 生效结束时间
        /// </summary>
        public DateTime? EffectiveEndTime { get; set; }
    }

    /// <summary>
    /// 更新ACL规则输入
    /// </summary>
    public class PutAclRuleInput
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        [Required(ErrorMessage = "规则名称不能为空")]
        [StringLength(100, ErrorMessage = "规则名称长度不能超过100个字符")]
        public string RuleName { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [Range(0, 999, ErrorMessage = "优先级必须在0-999之间")]
        public int Priority { get; set; }

        /// <summary>
        /// 访问类型
        /// </summary>
        [Required(ErrorMessage = "访问类型不能为空")]
        public MqttAccessType AccessType { get; set; }

        /// <summary>
        /// 是否允许访问
        /// </summary>
        public bool Allow { get; set; }

        /// <summary>
        /// 用户名（可选）
        /// </summary>
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; }

        /// <summary>
        /// 客户端ID（可选）
        /// </summary>
        [StringLength(100, ErrorMessage = "客户端ID长度不能超过100个字符")]
        public string ClientId { get; set; }

        /// <summary>
        /// IP地址（可选）
        /// </summary>
        [StringLength(50, ErrorMessage = "IP地址长度不能超过50个字符")]
        public string IpAddress { get; set; }

        /// <summary>
        /// MQTT主题
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        [StringLength(500, ErrorMessage = "主题长度不能超过500个字符")]
        public string Topic { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 生效开始时间
        /// </summary>
        public DateTime? EffectiveStartTime { get; set; }

        /// <summary>
        /// 生效结束时间
        /// </summary>
        public DateTime? EffectiveEndTime { get; set; }
    }

    /// <summary>
    /// 权限测试输入
    /// </summary>
    public class TestPermissionInput
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required(ErrorMessage = "客户端ID不能为空")]
        public string ClientId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        public string Topic { get; set; }

        /// <summary>
        /// 访问类型
        /// </summary>
        [Required(ErrorMessage = "访问类型不能为空")]
        public MqttAccessType AccessType { get; set; }
    }

    /// <summary>
    /// 权限测试输出
    /// </summary>
    public class PermissionTestOutput
    {
        /// <summary>
        /// 是否授权
        /// </summary>
        public bool IsAuthorized { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string FailureReason { get; set; }

        /// <summary>
        /// 匹配的规则
        /// </summary>
        public MatchedRuleOutput MatchedRule { get; set; }

        /// <summary>
        /// 所有匹配的规则
        /// </summary>
        public List<MatchedRuleOutput> AllMatchedRules { get; set; } = new List<MatchedRuleOutput>();

        /// <summary>
        /// 测试时间
        /// </summary>
        public DateTime TestTime { get; set; }
    }

    /// <summary>
    /// 匹配的规则输出
    /// </summary>
    public class MatchedRuleOutput
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 权限
        /// </summary>
        public string Permission { get; set; }

        /// <summary>
        /// 匹配的模式
        /// </summary>
        public string MatchedPattern { get; set; }

        /// <summary>
        /// 匹配类型
        /// </summary>
        public string MatchType { get; set; }
    }

    /// <summary>
    /// 导入结果输出
    /// </summary>
    public class ImportResultOutput
    {
        /// <summary>
        /// 导入成功的规则
        /// </summary>
        public List<AclRuleOutput> ImportedRules { get; set; } = new List<AclRuleOutput>();

        /// <summary>
        /// 导入失败的规则
        /// </summary>
        public List<string> FailedRules { get; set; } = new List<string>();

        /// <summary>
        /// 请求总数
        /// </summary>
        public int TotalRequested { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailCount { get; set; }

        /// <summary>
        /// 导入时间
        /// </summary>
        public DateTime ImportTime { get; set; }
    }
} 