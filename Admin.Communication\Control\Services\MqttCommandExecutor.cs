// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Control.Interfaces;
using Admin.Communication.Control.Models;
using Admin.Communication.Mqtt.Services;
using Admin.Communication.Mqtt.Abstractions;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Text;

namespace Admin.Communication.Control.Services;

/// <summary>
/// MQTT指令执行器
/// </summary>
public class MqttCommandExecutor(
    ISqlSugarClient db,
    IMqttBroker mqttBroker,
    IControlResponseHandler responseHandler,
    ILogger<MqttCommandExecutor> logger) : ICommandMediumExecutor
{
    private readonly ISqlSugarClient _db = db;
    private readonly IMqttBroker _mqttBroker = mqttBroker;
    private readonly IControlResponseHandler _responseHandler = responseHandler;
    private readonly ILogger<MqttCommandExecutor> _logger = logger;

    /// <summary>
    /// 是否支持指定的通讯介质
    /// </summary>
    /// <param name="communicationMedium">通讯介质类型</param>
    /// <returns>是否支持</returns>
    public bool CanHandle(int communicationMedium)
    {
        return communicationMedium == 1; // 1: MQTT
    }

    /// <summary>
    /// 执行指令
    /// </summary>
    /// <param name="command">指令实体</param>
    /// <param name="request">执行请求</param>
    /// <returns>执行结果</returns>
    public async Task<CommandExecuteResult> ExecuteAsync(ControlCmdEntity command, ExecuteCommandRequest request)
    {
        var correlationId = Guid.NewGuid().ToString();
        long recordId = 0;

        try
        {
            _logger.LogInformation("开始执行MQTT指令: CommandId={CommandId}, DeviceId={DeviceId}, CorrelationId={CorrelationId}", 
                command.Id, command.DeviceId, correlationId);

            // 1. 获取目标设备信息
            var targetDeviceId = request.DeviceId ?? command.DeviceId;
            var deviceInfo = await GetDeviceInfoAsync(targetDeviceId);
            if (deviceInfo == null)
            {
                return CommandExecuteResult.Failed($"设备不存在: DeviceId={targetDeviceId}");
            }

            // 2. 构建控制主题
            var controlTopic = BuildControlTopic(deviceInfo);

            // 3. 处理指令内容
            var processedContent = ProcessCommandContent(command.CmdContent, request.Parameters);

            // 4. 构建消息载荷
            var payload = BuildPayload(command, processedContent);

            // 5. 创建控制记录
            recordId = await CreateControlRecordAsync(command, request, processedContent, controlTopic);

            // 6. 注册响应等待器
            var waiter = new ControlResponseWaiter
            {
                CorrelationId = correlationId,
                ControlRecordId = recordId,
                DeviceId = GetResponseDeviceId(deviceInfo),
                SendTime = DateTime.Now,
                TimeoutMs = command.ResponseTime
            };
            _responseHandler.RegisterWaitingResponse(waiter);

            // 7. 发布MQTT消息（QoS2）
            try
            {
                await _mqttBroker.PublishAsync(controlTopic, payload, 2);
            }
            catch (Exception ex)
            {
                await UpdateControlRecordAsync(recordId, "发布失败", null);
                return CommandExecuteResult.Failed($"MQTT消息发布失败: {ex.Message}");
            }

            _logger.LogInformation("MQTT消息发布成功: Topic={Topic}, PayloadSize={Size}, CorrelationId={CorrelationId}", 
                controlTopic, payload.Length, correlationId);

            // 8. 等待响应
            var response = await _responseHandler.WaitForResponseAsync(correlationId, command.ResponseTime);

            // 9. 返回结果
            return CommandExecuteResult.Success(recordId, response.Result, response.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行MQTT指令时发生错误: CommandId={CommandId}, CorrelationId={CorrelationId}", 
                command.Id, correlationId);

            if (recordId > 0)
            {
                await UpdateControlRecordAsync(recordId, "执行异常", null);
            }

            return CommandExecuteResult.Failed($"执行异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备信息</returns>
    private async Task<DeviceInfo?> GetDeviceInfoAsync(long deviceId)
    {
        var device = await _db.Queryable<DeviceEntity>()
            .Where(d => d.Id == deviceId)
            .Select(d => new DeviceInfo
            {
                Id = d.Id,
                DeviceName = d.DeviceName,
                DeviceIdentityCode = d.DeviceIdentityCode,
                ParentId = d.ParentId
            })
            .FirstAsync();

        return device;
    }

    /// <summary>
    /// 构建控制主题
    /// </summary>
    /// <param name="deviceInfo">设备信息</param>
    /// <returns>控制主题</returns>
    private static string BuildControlTopic(DeviceInfo deviceInfo)
    {
        // 如果是网关子设备（DeviceIdentityCode为空且有ParentId），使用父设备ID
        var targetDeviceId = string.IsNullOrEmpty(deviceInfo.DeviceIdentityCode) && deviceInfo.ParentId.HasValue
            ? deviceInfo.ParentId.Value
            : deviceInfo.Id;

        return $"/devices/{targetDeviceId}/messages/control/down";
    }

    /// <summary>
    /// 获取响应设备ID
    /// </summary>
    /// <param name="deviceInfo">设备信息</param>
    /// <returns>响应设备ID</returns>
    private static long GetResponseDeviceId(DeviceInfo deviceInfo)
    {
        // 响应主题的设备ID与控制主题保持一致
        return string.IsNullOrEmpty(deviceInfo.DeviceIdentityCode) && deviceInfo.ParentId.HasValue
            ? deviceInfo.ParentId.Value
            : deviceInfo.Id;
    }

    /// <summary>
    /// 处理指令内容（替换参数占位符）
    /// </summary>
    /// <param name="cmdContent">指令内容</param>
    /// <param name="parameters">参数</param>
    /// <returns>处理后的内容</returns>
    private static string ProcessCommandContent(string cmdContent, Dictionary<string, object>? parameters)
    {
        if (parameters == null || parameters.Count == 0)
        {
            return cmdContent;
        }

        var result = cmdContent;
        foreach (var param in parameters)
        {
            var placeholder = $"{{{param.Key}}}";
            result = result.Replace(placeholder, param.Value?.ToString() ?? "");
        }

        return result;
    }

    /// <summary>
    /// 构建消息载荷
    /// </summary>
    /// <param name="command">指令实体</param>
    /// <param name="content">处理后的内容</param>
    /// <returns>消息载荷</returns>
    private static byte[] BuildPayload(ControlCmdEntity command, string content)
    {
        return command.Encode switch
        {
            1 => Encoding.UTF8.GetBytes(content), // JSON
            2 => Convert.FromHexString(content.Replace(" ", "").Replace("-", "")), // HEX
            _ => throw new NotSupportedException($"不支持的编码方式: {command.Encode}")
        };
    }

    /// <summary>
    /// 创建控制记录
    /// </summary>
    /// <param name="command">指令实体</param>
    /// <param name="request">执行请求</param>
    /// <param name="sendContent">发送内容</param>
    /// <param name="topic">主题</param>
    /// <returns>记录ID</returns>
    private async Task<long> CreateControlRecordAsync(ControlCmdEntity command, ExecuteCommandRequest request, 
        string sendContent, string topic)
    {
        var record = new HisControlSendEntity
        {
            CmdName = command.CmdName,
            SendContent = sendContent,
            SendTime = DateTime.Now,
            ControlType = 1, // 1: 手动控制
            Medium = "MQTT",
            MediumInfo = topic,
            Source = request.Source,
            Username = request.Username,
            ControlResult = "发送中" // 初始状态
        };

        return await _db.Insertable(record).ExecuteReturnSnowflakeIdAsync();
    }

    /// <summary>
    /// 更新控制记录
    /// </summary>
    /// <param name="recordId">记录ID</param>
    /// <param name="result">控制结果</param>
    /// <param name="responseContent">响应内容</param>
    private async Task UpdateControlRecordAsync(long recordId, string result, string? responseContent)
    {
        await _db.Updateable<HisControlSendEntity>()
            .SetColumns(it => new HisControlSendEntity
            {
                ControlResult = result,
                ResponseContent = responseContent
            })
            .Where(it => it.Id == recordId)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 设备信息
    /// </summary>
    private class DeviceInfo
    {
        public long Id { get; set; }
        public string DeviceName { get; set; }
        public string DeviceIdentityCode { get; set; }
        public long? ParentId { get; set; }
    }
}
