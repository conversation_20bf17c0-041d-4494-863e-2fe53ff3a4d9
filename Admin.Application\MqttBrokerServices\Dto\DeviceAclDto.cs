// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Mqtt.Configuration;
using System.ComponentModel.DataAnnotations;

namespace Admin.Application.MqttBrokerServices.Dto;

#region 设备ACL规则相关DTO

/// <summary>
/// 创建设备ACL规则请求DTO
/// </summary>
public class CreateDeviceAclRulesInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 自定义主题前缀（可选，默认使用设备ID）
    /// </summary>
    public string? TopicPrefix { get; set; }

    /// <summary>
    /// 是否允许订阅系统主题
    /// </summary>
    public bool AllowSystemTopics { get; set; } = false;

    /// <summary>
    /// 自定义允许的主题列表
    /// </summary>
    public List<string>? CustomAllowedTopics { get; set; }

    /// <summary>
    /// 自定义禁止的主题列表
    /// </summary>
    public List<string>? CustomDeniedTopics { get; set; }
}

/// <summary>
/// 设备ACL规则模板
/// </summary>
public class DeviceAclRuleTemplate
{
    /// <summary>
    /// 规则名称模板
    /// </summary>
    public string RuleNameTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 主题模板
    /// </summary>
    public string TopicTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 访问类型
    /// </summary>
    public MqttAccessType AccessType { get; set; }

    /// <summary>
    /// Payload编码 (1:JSON 2:HEX)
    /// </summary>
    public int DataFormat { get; set; } = 1;

    /// <summary>
    /// 是否允许
    /// </summary>
    public bool Allow { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 描述模板
    /// </summary>
    public string? DescriptionTemplate { get; set; }
}

/// <summary>
/// 设备ACL规则创建结果
/// </summary>
public class DeviceAclRulesResult
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// 创建成功的规则列表
    /// </summary>
    public List<CreatedAclRule> CreatedRules { get; set; } = new();

    /// <summary>
    /// 创建失败的规则列表
    /// </summary>
    public List<FailedAclRule> FailedRules { get; set; } = new();

    /// <summary>
    /// 总请求数
    /// </summary>
    public int TotalRequested { get; set; }

    /// <summary>
    /// 成功数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数
    /// </summary>
    public int FailCount { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 创建成功的ACL规则
/// </summary>
public class CreatedAclRule
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public long RuleId { get; set; }

    /// <summary>
    /// 规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 访问类型
    /// </summary>
    public MqttAccessType AccessType { get; set; }

    /// <summary>
    /// 是否允许
    /// </summary>
    public bool Allow { get; set; }
}

/// <summary>
/// 创建失败的ACL规则
/// </summary>
public class FailedAclRule
{
    /// <summary>
    /// 规则名称
    /// </summary>
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 访问类型
    /// </summary>
    public MqttAccessType AccessType { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string FailureReason { get; set; } = string.Empty;
}

/// <summary>
/// 批量删除设备ACL规则请求DTO
/// </summary>
public class DeleteDeviceAclRulesInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// 是否删除所有相关规则（包括自定义规则）
    /// </summary>
    public bool DeleteAllRelated { get; set; } = false;
}

/// <summary>
/// 设备ACL规则查询请求DTO
/// </summary>
public class QueryDeviceAclRulesInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public string? DeviceId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 主题过滤
    /// </summary>
    public string? TopicFilter { get; set; }

    /// <summary>
    /// 访问类型过滤
    /// </summary>
    public MqttAccessType? AccessType { get; set; }

    /// <summary>
    /// 是否只查询激活的规则
    /// </summary>
    public bool OnlyActive { get; set; } = true;
}

#endregion
