// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Modbus.Models;
using Admin.Multiplex.Contracts.Consts;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Volo.Abp.Application.Services;

namespace Admin.Application.ModbusServices;

/// <summary>
/// Modbus指令管理服务
/// 提供Modbus指令调度的管理和监控功能
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.SYSTEM)]
public class ModbusInstructionManagementService(
    ISqlSugarClient db,
    ILogger<ModbusInstructionManagementService> logger) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly ILogger<ModbusInstructionManagementService> _logger = logger;

    /// <summary>
    /// 获取Modbus设备列表
    /// </summary>
    /// <returns>Modbus设备列表</returns>
    public async Task<List<ModbusDeviceOutput>> GetModbusDevicesAsync()
    {
        var devices = await _db.Queryable<DeviceEntity>()
            .LeftJoin<ProductEntity>((d, p) => d.ModelId == p.Id)
            .LeftJoin<ProductModelEntity>((d, p, pm) => d.ModelId == pm.Id)
            .Where((d, p, pm) => p.ProtocolType == (int)ProtocolTypeEnum.Modbus &&
                               p.DataFormat == (int)DataFormatEnum.Hex)
            .Select((d, p, pm) => new
            {
                d.Id,
                d.DeviceName,
                d.ModbusAddr,
                d.DeviceIdentityCode,
                ProductName = p.ProductName,
                ModelName = pm.ModelName,
                ProtocolType = p.ProtocolType,
                DataFormat = p.DataFormat,
                d.CreateTime
            })
            .ToListAsync();

        return devices.Adapt<List<ModbusDeviceOutput>>();
    }

    /// <summary>
    /// 获取设备的指令列表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>指令列表</returns>
    public async Task<List<ModbusInstructionOutput>> GetDeviceInstructionsAsync(long deviceId)
    {
        var instructions = await _db.Queryable<DeviceInstructionEntity>()
            .Where(di => di.DeviceId == deviceId)
            .OrderBy(di => di.Id)
            .ToListAsync();

        return instructions.Adapt<List<ModbusInstructionOutput>>();
    }

    /// <summary>
    /// 更新指令参数
    /// </summary>
    /// <param name="input">更新参数</param>
    /// <returns>更新结果</returns>
    public async Task<ModbusInstructionOutput> UpdateInstructionAsync(UpdateModbusInstructionInput input)
    {
        var instruction = await _db.Queryable<DeviceInstructionEntity>()
            .Where(di => di.Id == input.Id)
            .FirstAsync();

        if (instruction == null)
        {
            throw PersistdValidateException.Message("指令不存在");
        }

        // 更新指令参数
        if (input.ReadInterval.HasValue)
            instruction.ReadInterval = input.ReadInterval.Value;
        
        if (input.ResponseTime.HasValue)
            instruction.ResponseTime = input.ResponseTime.Value;
        
        if (input.RetryCount.HasValue)
            instruction.RetryCount = input.RetryCount.Value;
        
        if (input.IsEnabled.HasValue)
            instruction.IsEnabled = input.IsEnabled.Value;

        await _db.Updateable(instruction).ExecuteCommandAsync();

        _logger.LogInformation("更新Modbus指令参数: DeviceId={DeviceId}, InstructionId={InstructionId}",
            instruction.DeviceId, instruction.Id);

        return instruction.Adapt<ModbusInstructionOutput>();
    }

    /// <summary>
    /// 启用/禁用指令
    /// </summary>
    /// <param name="instructionId">指令ID</param>
    /// <param name="enabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<bool> SetInstructionEnabledAsync(long instructionId, bool enabled)
    {
        var result = await _db.Updateable<DeviceInstructionEntity>()
            .SetColumns(di => di.IsEnabled == enabled)
            .Where(di => di.Id == instructionId)
            .ExecuteCommandAsync();

        _logger.LogInformation("设置指令状态: InstructionId={InstructionId}, Enabled={Enabled}",
            instructionId, enabled);

        return result > 0;
    }

    /// <summary>
    /// 批量启用/禁用设备的所有指令
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="enabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<int> SetDeviceInstructionsEnabledAsync(long deviceId, bool enabled)
    {
        var result = await _db.Updateable<DeviceInstructionEntity>()
            .SetColumns(di => di.IsEnabled == enabled)
            .Where(di => di.DeviceId == deviceId)
            .ExecuteCommandAsync();

        _logger.LogInformation("批量设置设备指令状态: DeviceId={DeviceId}, Enabled={Enabled}, Count={Count}",
            deviceId, enabled, result);

        return result;
    }
}

/// <summary>
/// Modbus设备输出DTO
/// </summary>
public class ModbusDeviceOutput
{
    public long Id { get; set; }
    public string DeviceName { get; set; } = string.Empty;
    public int ModbusAddr { get; set; }
    public string DeviceIdentityCode { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public string ModelName { get; set; } = string.Empty;
    public int ProtocolType { get; set; }
    public int DataFormat { get; set; }
    public DateTime CreateTime { get; set; }
}

/// <summary>
/// Modbus指令输出DTO
/// </summary>
public class ModbusInstructionOutput
{
    public long Id { get; set; }
    public long DeviceId { get; set; }
    public string InstructionName { get; set; } = string.Empty;
    public string send_str { get; set; } = string.Empty;
    public int Encode { get; set; }
    public int ReadInterval { get; set; }
    public int ResponseTime { get; set; }
    public int RetryCount { get; set; }
    public bool IsEnabled { get; set; }
    public DateTime CreateTime { get; set; }
}

/// <summary>
/// 更新Modbus指令输入DTO
/// </summary>
public class UpdateModbusInstructionInput
{
    public long Id { get; set; }
    public int? ReadInterval { get; set; }
    public int? ResponseTime { get; set; }
    public int? RetryCount { get; set; }
    public bool? IsEnabled { get; set; }
}
