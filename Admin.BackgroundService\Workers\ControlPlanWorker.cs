// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Control.Services;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Admin.BackgroundService.Workers;

/// <summary>
/// 控制计划后台任务
/// 定期检查并执行重复执行的控制计划
/// </summary>
public class ControlPlanWorker : AsyncPeriodicBackgroundWorkerBase
{
    public ControlPlanWorker(AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory)
        : base(timer, serviceScopeFactory)
    {
        Timer.Period = 60000; // 每分钟执行一次
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        Logger.LogDebug("开始检查控制计划");
        await CheckAndExecutePlansAsync();
    }

    /// <summary>
    /// 检查并执行控制计划
    /// </summary>
    private async Task CheckAndExecutePlansAsync()
    {
        try
        {
            using var scope = ServiceScopeFactory.CreateScope();
            var db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
            var planExecutor = scope.ServiceProvider.GetRequiredService<IControlPlanExecutor>();

            // 获取所有启用的重复执行计划
            var plans = await GetActiveRepeatPlansAsync(db);

            if (plans.Count == 0)
            {
                Logger.LogDebug("没有找到启用的重复执行控制计划");
                return;
            }

            Logger.LogDebug("找到 {Count} 个启用的重复执行控制计划", plans.Count);

            var executedCount = 0;
            foreach (var plan in plans)
            {
                try
                {
                    // 检查是否应该执行
                    var shouldExecute = await planExecutor.ShouldExecutePlanAsync(plan.Id);
                    if (!shouldExecute)
                    {
                        continue;
                    }

                    Logger.LogInformation("执行控制计划: PlanId={PlanId}, Name={Name}", plan.Id, plan.Name);

                    // 执行控制计划
                    var result = await planExecutor.ExecutePlanAsync(plan.Id, "定时任务");

                    Logger.LogInformation("控制计划执行结果: PlanId={PlanId}, Name={Name}, Total={Total}, Success={Success}, Failed={Failed}",
                        plan.Id, plan.Name, result.TotalCount, result.SuccessCount, result.FailedCount);

                    executedCount++;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "执行控制计划时发生错误: PlanId={PlanId}, Name={Name}", plan.Id, plan.Name);
                }
            }

            if (executedCount > 0)
            {
                Logger.LogInformation("本次检查执行了 {Count} 个控制计划", executedCount);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "检查控制计划时发生错误");
        }
    }

    /// <summary>
    /// 获取所有启用的重复执行计划
    /// </summary>
    /// <param name="db">数据库客户端</param>
    /// <returns>控制计划列表</returns>
    private async Task<List<ControlPlanEntity>> GetActiveRepeatPlansAsync(ISqlSugarClient db)
    {
        return await db.Queryable<ControlPlanEntity>()
            .Where(cp => cp.IsAppControl == 1)  // 启用状态
            .Where(cp => cp.TriggerType == 1)   // 重复执行
            .Where(cp => cp.ExecuteType != null) // 有执行方式配置
            .ToListAsync();
    }
}
