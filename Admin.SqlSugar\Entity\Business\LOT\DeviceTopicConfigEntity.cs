﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;
/// <summary>
/// 设备Topic配置表
/// </summary>
[SugarTable("DEVICE_TOPIC_CONFIG")]
public partial class DeviceTopicConfigEntity : BaseEntity
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public string DeviceId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "NAME")]
    public string Name { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    /// 必须以"/devices/{device_id}/custom/"开头
    [SugarColumn(ColumnName = "TOPIC")]
    public string Topic { get; set; }

    /// <summary>
    /// 访问类型(0=All, 1=Publish, 2=Subscribe)
    /// </summary>
    [SugarColumn(ColumnName = "ACCESS_TYPE")]
    public int AccessType { get; set; }

    /// <summary>
    /// Payload编码 (1:JSON 2:HEX)
    /// </summary>
    public int DataFormat { get; set; } = 1;

    /// <summary>
    /// 优先级
    /// </summary>
    [SugarColumn(ColumnName = "PRIORITY")]
    public int Priority { get; set; } = 100;

    /// <summary>
    /// 模板描述
    /// </summary>
    [SugarColumn(ColumnName = "DESCRIPTION")]
    public string Description { get; set; }
}
