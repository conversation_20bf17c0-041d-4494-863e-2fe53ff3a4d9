﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Models.Statistics;
/// <summary>
/// 连接统计信息
/// </summary>
public class ConnectionStatistics
{
    /// <summary>
    /// 当前连接数
    /// </summary>
    public int CurrentConnections { get; set; }

    /// <summary>
    /// 总连接数（累计）
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 总断开连接数（累计）
    /// </summary>
    public long TotalDisconnections { get; set; }

    /// <summary>
    /// 异常断开连接数
    /// </summary>
    public long AbnormalDisconnections { get; set; }

    /// <summary>
    /// 认证失败次数
    /// </summary>
    public long AuthenticationFailures { get; set; }

    /// <summary>
    /// 连接被拒绝次数
    /// </summary>
    public long ConnectionRejections { get; set; }

    /// <summary>
    /// 最大并发连接数
    /// </summary>
    public int MaxConcurrentConnections { get; set; }

    /// <summary>
    /// 平均连接持续时间（秒）
    /// </summary>
    public double AverageConnectionDuration { get; set; }

    /// <summary>
    /// 按IP分组的连接数
    /// </summary>
    public Dictionary<string, int> ConnectionsByIp { get; set; } = new Dictionary<string, int>();
}