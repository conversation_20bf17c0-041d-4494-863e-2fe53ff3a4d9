using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System;

namespace Admin.Communication.Mqtt.Configuration
{
    /// <summary>
    /// MQTT配置扩展方法
    /// </summary>
    public static class MqttConfigurationExtensions
    {
        /// <summary>
        /// 添加MQTT代理服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configure">配置选项</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddMqttBroker(this IServiceCollection services, Action<MqttBrokerOptions>? configure = null)
        {
            // 注册配置选项
            if (configure != null)
            {
                services.Configure(configure);
            }

            // 注册MQTT连接管理器
            services.TryAddSingleton<IMqttConnectionManager, MqttConnectionManager>();

            // 注册MQTT代理服务
            services.TryAddSingleton<IMqttBroker, MqttBrokerService>();

            return services;
        }

        /// <summary>
        /// 添加MQTT代理服务并自动启动
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configure">配置选项</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddMqttBrokerWithAutoStart(this IServiceCollection services, Action<MqttBrokerOptions>? configure = null)
        {
            // 添加MQTT代理服务
            services.AddMqttBroker(configure);

            // 注册托管服务，用于自动启动MQTT代理
            services.AddHostedService<MqttBrokerHostedService>();

            return services;
        }
    }
} 