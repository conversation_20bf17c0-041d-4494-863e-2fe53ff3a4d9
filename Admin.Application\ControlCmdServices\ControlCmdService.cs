// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ControlCmdServices.Dto;
using Admin.Communication.Control.Interfaces;
using Admin.Communication.Control.Models;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ControlCmdServices;

/// <summary>
/// 控制指令配置服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class ControlCmdService(ISqlSugarClient db, Repository<ControlCmdEntity> repository, ICommandExecutor commandExecutor) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<ControlCmdEntity> _repository = repository;
    private readonly ICommandExecutor _commandExecutor = commandExecutor;

    /// <summary>
    /// 分页查询控制指令
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页列表</returns>
    public async Task<PagedList<ControlCmdOutput>> GetPagedListAsync(ControlCmdPagedInput input)
    {
        var query = _db.Queryable<ControlCmdEntity>()
            .LeftJoin<DeviceEntity>((cc, d) => cc.DeviceId == d.Id)
            .WhereIF(!string.IsNullOrEmpty(input.CmdName), (cc, d) => cc.CmdName.Contains(input.CmdName!))
            .WhereIF(input.DeviceId.HasValue, (cc, d) => cc.DeviceId == input.DeviceId!.Value)
            .WhereIF(input.CommunicationMedium.HasValue, (cc, d) => cc.CommunicationMedium == input.CommunicationMedium!.Value)
            .WhereIF(input.Encode.HasValue, (cc, d) => cc.Encode == input.Encode!.Value)
            .WhereIF(input.IsAppControl.HasValue, (cc, d) => cc.IsAppControl == input.IsAppControl!.Value)
            .Select((cc, d) => new ControlCmdOutput
            {
                Id = cc.Id,
                CmdName = cc.CmdName,
                DeviceId = cc.DeviceId,
                DeviceName = d.DeviceName,
                CommunicationMedium = cc.CommunicationMedium,
                CommunicationMediumName = cc.CommunicationMedium == 1 ? "MQTT" : "未知",
                CmdContent = cc.CmdContent,
                Encode = cc.Encode,
                EncodeName = cc.Encode == 1 ? "JSON" : cc.Encode == 2 ? "HEX" : "未知",
                AreaId = cc.AreaId,
                DeviceCategory = cc.DeviceCategory,
                IsAppControl = cc.IsAppControl,
                IsAppControlName = cc.IsAppControl == 1 ? "是" : "否"
            })
            .OrderByDescending(cc => cc.CreateTime);

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 根据ID获取控制指令
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <returns>控制指令信息</returns>
    public async Task<ControlCmdOutput> GetByIdAsync(long id)
    {
        var result = await _db.Queryable<ControlCmdEntity>()
            .LeftJoin<DeviceEntity>((cc, d) => cc.DeviceId == d.Id)
            .Where((cc, d) => cc.Id == id)
            .Select((cc, d) => new ControlCmdOutput
            {
                Id = cc.Id,
                CmdName = cc.CmdName,
                DeviceId = cc.DeviceId,
                DeviceName = d.DeviceName,
                CommunicationMedium = cc.CommunicationMedium,
                CommunicationMediumName = cc.CommunicationMedium == 1 ? "MQTT" : "未知",
                CmdContent = cc.CmdContent,
                Encode = cc.Encode,
                EncodeName = cc.Encode == 1 ? "JSON" : cc.Encode == 2 ? "HEX" : "未知",
                AreaId = cc.AreaId,
                DeviceCategory = cc.DeviceCategory,
                IsAppControl = cc.IsAppControl,
                IsAppControlName = cc.IsAppControl == 1 ? "是" : "否"
            })
            .FirstAsync();

        if (result == null)
        {
            throw PersistdValidateException.Message("控制指令不存在");
        }

        return result;
    }

    /// <summary>
    /// 添加控制指令
    /// </summary>
    /// <param name="input">指令信息</param>
    /// <returns>指令信息</returns>
    public async Task<ControlCmdOutput> AddAsync(AddControlCmdInput input)
    {
        // 1. 验证设备是否存在
        await ValidateDeviceExistsAsync(input.DeviceId);

        // 2. 验证指令名称在同一设备下是否重复
        await ValidateCommandNameUniqueAsync(input.DeviceId, input.CmdName);

        // 3. 创建控制指令实体
        var entity = input.Adapt<ControlCmdEntity>();

        // 4. 保存到数据库
        var id = await _repository.InsertReturnSnowflakeIdAsync(entity);
        entity.Id = id;

        // 5. 返回结果
        return await GetByIdAsync(id);
    }

    /// <summary>
    /// 更新控制指令
    /// </summary>
    /// <param name="input">指令信息</param>
    /// <returns>指令信息</returns>
    public async Task<ControlCmdOutput> UpdateAsync(UpdateControlCmdInput input)
    {
        // 1. 验证指令是否存在
        var existingEntity = await _repository.GetByIdAsync(input.Id) ??
            throw PersistdValidateException.Message("控制指令不存在");

        // 2. 验证设备是否存在
        await ValidateDeviceExistsAsync(input.DeviceId);

        // 3. 验证指令名称在同一设备下是否重复（排除当前指令）
        await ValidateCommandNameUniqueAsync(input.DeviceId, input.CmdName, input.Id);

        // 4. 更新实体
        var entity = input.Adapt<ControlCmdEntity>();

        await _repository.UpdateAsync(entity);

        // 5. 返回结果
        return await GetByIdAsync(input.Id);
    }

    /// <summary>
    /// 删除控制指令
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("控制指令不存在");

        return await _repository.DeleteAsync(entity);
    }

    /// <summary>
    /// 根据设备ID获取控制指令列表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>控制指令列表</returns>
    public async Task<List<ControlCmdOutput>> GetByDeviceIdAsync(long deviceId)
    {
        // 验证设备是否存在
        await ValidateDeviceExistsAsync(deviceId);

        var result = await _db.Queryable<ControlCmdEntity>()
            .LeftJoin<DeviceEntity>((cc, d) => cc.DeviceId == d.Id)
            .Where((cc, d) => cc.DeviceId == deviceId)
            .Select((cc, d) => new ControlCmdOutput
            {
                Id = cc.Id,
                CmdName = cc.CmdName,
                DeviceId = cc.DeviceId,
                DeviceName = d.DeviceName,
                CommunicationMedium = cc.CommunicationMedium,
                CommunicationMediumName = cc.CommunicationMedium == 1 ? "MQTT" : "未知",
                CmdContent = cc.CmdContent,
                Encode = cc.Encode,
                EncodeName = cc.Encode == 1 ? "JSON" : cc.Encode == 2 ? "HEX" : "未知",
                AreaId = cc.AreaId,
                DeviceCategory = cc.DeviceCategory,
                IsAppControl = cc.IsAppControl,
                IsAppControlName = cc.IsAppControl == 1 ? "是" : "否"
            })
            .OrderByDescending(cc => cc.CreateTime)
            .ToListAsync();

        return result;
    }

    /// <summary>
    /// 验证设备是否存在
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    private async Task ValidateDeviceExistsAsync(long deviceId)
    {
        var exists = await _db.Queryable<DeviceEntity>()
            .Where(d => d.Id == deviceId)
            .AnyAsync();

        if (!exists)
        {
            throw PersistdValidateException.Message($"设备不存在: DeviceId={deviceId}");
        }
    }

    /// <summary>
    /// 验证指令名称在同一设备下是否唯一
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="cmdName">指令名称</param>
    /// <param name="excludeId">排除的指令ID（用于更新时排除自身）</param>
    private async Task ValidateCommandNameUniqueAsync(long deviceId, string cmdName, long? excludeId = null)
    {
        var query = _db.Queryable<ControlCmdEntity>()
            .Where(cc => cc.DeviceId == deviceId && cc.CmdName == cmdName);

        if (excludeId.HasValue)
        {
            query = query.Where(cc => cc.Id != excludeId.Value);
        }

        var exists = await query.AnyAsync();

        if (exists)
        {
            throw PersistdValidateException.Message($"该设备下已存在相同名称的控制指令: {cmdName}");
        }
    }

    /// <summary>
    /// 执行指令
    /// </summary>
    /// <param name="request">执行请求</param>
    /// <returns>执行结果</returns>
    public async Task<CommandExecuteResult> ExecuteCommandAsync(ExecuteCommandRequest request)
    {
        return await _commandExecutor.ExecuteAsync(request);
    }

    /// <summary>
    /// 批量执行指令
    /// </summary>
    /// <param name="request">批量执行请求</param>
    /// <returns>批量执行结果</returns>
    public async Task<BatchExecuteResult> BatchExecuteCommandAsync(BatchExecuteCommandRequest request)
    {
        return await _commandExecutor.BatchExecuteAsync(request);
    }

    /// <summary>
    /// 执行指定ID的指令
    /// </summary>
    /// <param name="commandId">指令ID</param>
    /// <param name="parameters">动态参数</param>
    /// <param name="source">来源</param>
    /// <param name="username">用户名</param>
    /// <returns>执行结果</returns>
    public async Task<CommandExecuteResult> ExecuteCommandByIdAsync(long commandId,
        Dictionary<string, object>? parameters = null, string? source = null, string? username = null)
    {
        var request = new ExecuteCommandRequest
        {
            CommandId = commandId,
            Parameters = parameters,
            Source = source ?? "手动控制",
            Username = username
        };

        return await _commandExecutor.ExecuteAsync(request);
    }
}
