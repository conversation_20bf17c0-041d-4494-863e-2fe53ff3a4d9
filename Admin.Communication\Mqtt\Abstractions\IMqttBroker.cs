using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Events;
using Admin.Communication.Mqtt.Models;

namespace Admin.Communication.Mqtt.Abstractions
{
    /// <summary>
    /// MQTT代理接口，定义MQTT代理/服务器的基本功能
    /// </summary>
    public interface IMqttBroker
    {
        /// <summary>
        /// 获取代理服务器当前状态
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 获取代理服务器监听端口
        /// </summary>
        int Port { get; }

        /// <summary>
        /// 启动MQTT代理服务器
        /// </summary>
        /// <param name="port">监听端口，默认为1883</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        Task StartAsync(int port = 1883, CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止MQTT代理服务器
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        Task StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取当前连接的客户端数量
        /// </summary>
        /// <returns>客户端数量</returns>
        int GetConnectedClientCount();

        /// <summary>
        /// 获取当前连接的客户端信息
        /// </summary>
        /// <returns>客户端信息列表</returns>
        IReadOnlyList<MqttClientInfo> GetConnectedClients();

        /// <summary>
        /// 获取指定主题的订阅者数量
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>订阅者数量</returns>
        int GetSubscriptionCount(string topic);

        /// <summary>
        /// 发布消息到指定主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息负载</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="retain">是否保留消息</param>
        /// <returns>发布任务</returns>
        Task PublishAsync(string topic, byte[] payload, int qos = 0, bool retain = false);

        /// <summary>
        /// 发布消息到指定主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息负载(字符串)</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="retain">是否保留消息</param>
        /// <returns>发布任务</returns>
        Task PublishAsync(string topic, string payload, int qos = 0, bool retain = false);

        /// <summary>
        /// 断开指定客户端的连接
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>断开连接任务</returns>
        Task DisconnectClientAsync(string clientId);

        /// <summary>
        /// 当客户端连接时触发
        /// </summary>
        event EventHandler<MqttClientConnectedEventArgs> ClientConnected;

        /// <summary>
        /// 当客户端断开连接时触发
        /// </summary>
        event EventHandler<MqttClientDisconnectedEventArgs> ClientDisconnected;

        /// <summary>
        /// 当收到消息时触发
        /// </summary>
        event EventHandler<MqttMessageReceivedEventArgs> MessageReceived;

        /// <summary>
        /// 当客户端订阅主题时触发
        /// </summary>
        event EventHandler<MqttSubscriptionEventArgs> ClientSubscribed;

        /// <summary>
        /// 当客户端取消订阅主题时触发
        /// </summary>
        event EventHandler<MqttSubscriptionEventArgs> ClientUnsubscribed;
    }
} 