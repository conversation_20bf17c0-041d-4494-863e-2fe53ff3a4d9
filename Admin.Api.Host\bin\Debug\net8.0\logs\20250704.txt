[15:20:46] [ERR]  
INSERT INTO `mqtt_subscriptions`  
           (`Id`,`SessionId`,`ClientId`,`TopicFilter`,`Qos`,`SubscribedTime`,`LastMatchTime`,`MatchCount`,`IsWildcard`,`Status`)
     VALUES
           (@Id,@SessionId,@ClientId,@TopicFilter,@Qos,@SubscribedTime,@LastMatchTime,@MatchCount,@IsWildcard,@Status) ;

[15:20:47] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存会话订阅信息时发生错误: ClientId=mqttx_a978efd2_1751613646000
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0-gateway/up' for key 'uk_session_topic'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveSessionSubscriptionsAsync(MqttSession session) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 1004

[15:30:41] [ERR]  
INSERT INTO `mqtt_subscriptions`  
           (`Id`,`SessionId`,`ClientId`,`TopicFilter`,`Qos`,`SubscribedTime`,`LastMatchTime`,`MatchCount`,`IsWildcard`,`Status`)
     VALUES
           (@Id,@SessionId,@ClientId,@TopicFilter,@Qos,@SubscribedTime,@LastMatchTime,@MatchCount,@IsWildcard,@Status) ;

[15:30:41] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存会话订阅信息时发生错误: ClientId=mqttx_a978efd2_1751614241000
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0-gateway/up' for key 'uk_session_topic'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveSessionSubscriptionsAsync(MqttSession session) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 1004

