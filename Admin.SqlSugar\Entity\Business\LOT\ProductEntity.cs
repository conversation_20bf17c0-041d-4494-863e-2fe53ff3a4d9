// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// IOT产品
/// </summary>
[SugarTable("LOT_PRODUCT")]
public partial class ProductEntity : BaseEntity
{
    /// <summary>
    /// 产品名称
    /// </summary>
    [SugarColumn(ColumnName = "PRODUCT_NAME")]
    public string ProductName { get; set; }

    /// <summary>
    /// 协议类型 (1:MQTT 2:Modbus)
    /// </summary>
    [SugarColumn(ColumnName = "PROTOCOL_TYPE")]
    public int ProtocolType { get; set; }

    /// <summary>
    /// 产品类型 (1:直连设备 2:网关设备 3:网关子设备)
    /// </summary>
    [SugarColumn(ColumnName = "PRODUCT_TYPE")]
    public int ProductType { get; set; } = 1;

    /// <summary>
    /// 数据格式 (1:JSON 2:HEX)
    /// </summary>
    [SugarColumn(ColumnName = "DATA_FORMAT")]
    public int DataFormat { get; set; }

    /// <summary>
    /// 产品描述
    /// </summary>
    [SugarColumn(ColumnName = "DESCRIPTION")]
    public string Description { get; set; }
} 