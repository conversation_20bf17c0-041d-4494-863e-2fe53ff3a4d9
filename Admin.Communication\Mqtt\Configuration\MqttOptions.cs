using Admin.Communication.Mqtt.Models;

namespace Admin.Communication.Mqtt.Configuration
{
    /// <summary>
    /// MQTT配置模型，包含代理服务器的配置
    /// </summary>
    public class MqttOptions
    {
        /// <summary>
        /// MQTT代理服务器配置
        /// </summary>
        public MqttBrokerOptions Broker { get; set; } = new MqttBrokerOptions();

        /// <summary>
        /// 默认协议版本
        /// </summary>
        public byte DefaultProtocolVersion { get; set; } = (byte)MqttProtocol.ProtocolVersion.V311;
    }
} 