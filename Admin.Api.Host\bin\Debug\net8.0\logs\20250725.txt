[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[09:12:23] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[09:12:23] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[09:12:23] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[09:12:23] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[09:12:23] [INF]  
项目当前环境为：Development

[09:12:23] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[09:12:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[09:12:24] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[09:12:24] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[09:12:24] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[09:12:24] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[09:12:24] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[09:12:24] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[09:12:31] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/v1/device-instruction/batch-delete" for actions - Admin.Application.DeviceServices.DeviceInstructionService.BatchDeleteAsync (Admin.Application),Admin.Application.DeviceServices.DeviceInstructionService.BatchDeleteAsync (Admin.Application). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

[09:13:00] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:13:00] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48311

[09:13:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[09:20:07] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[09:20:08] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[09:20:08] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[09:20:08] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[09:20:08] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[09:20:08] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[09:20:08] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[09:20:08] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[09:20:08] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[09:20:08] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[09:20:08] [INF]  
项目当前环境为：Development

[09:20:08] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[09:20:08] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[09:20:08] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[09:20:08] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[09:20:08] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[09:20:08] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[09:20:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[09:20:09] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:20:09] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48546

[09:20:09] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:20:13] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "POST api/v1/device-instruction/{id}/set-enabled" for actions - Admin.Application.DeviceServices.DeviceInstructionService.SetEnabledAsync (Admin.Application),Admin.Application.DeviceServices.DeviceInstructionService.SetEnabledAsync (Admin.Application). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[09:30:06] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[09:30:06] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[09:30:07] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[09:30:07] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[09:30:07] [INF]  
项目当前环境为：Development

[09:30:07] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[09:30:07] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[09:30:07] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[09:30:07] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[09:30:07] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[09:30:07] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[09:30:07] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[09:30:09] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:30:09] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:54344

[09:30:09] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:30:11] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "GET api/v1/model-property/paged-list" for actions - Admin.Application.ProductServices.ModelPropertyService.GetPagedListAsync (Admin.Application),Admin.Application.ProductServices.ModelPropertyService.GetPagedListAsync (Admin.Application). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[09:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[09:34:38] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[09:34:38] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[09:34:38] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[09:34:38] [INF]  
项目当前环境为：Development

[09:34:38] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[09:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[09:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[09:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[09:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[09:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[09:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[09:34:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:34:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:40347

[09:34:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:34:49] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "PUT api/v1/model-property" for actions - Admin.Application.ProductServices.ModelPropertyService.UpdateAsync (Admin.Application),Admin.Application.ProductServices.ModelPropertyService.UpdateAsync (Admin.Application). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[09:37:27] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[09:37:27] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[09:37:27] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[09:37:27] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[09:37:27] [INF]  
项目当前环境为：Development

[09:37:27] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[09:37:27] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[09:37:27] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[09:37:27] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[09:37:27] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[09:37:27] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[09:37:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[09:37:28] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:37:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:40500

[09:37:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:42:27] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库清理了 1 个过期持久会话

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:51461

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[09:45:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[09:45:27] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:45:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:51463

[09:45:27] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:32874

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[09:56:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[09:56:17] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[09:56:17] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:32876

[09:56:17] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:04:51] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:04:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:53996

[10:04:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:56089

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:07:04] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:07:05] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:07:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:56091

[10:07:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:55131

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:17:52] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:17:53] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:17:53] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:55133

[10:17:53] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:21:46] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:21:46] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:22:10] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:22:10] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:55180

[10:22:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:23:56] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:23:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:23:56] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:23:56] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:55203

[10:23:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:25:45] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:25:45] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:25:48] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:25:48] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:55226

[10:25:48] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:52217

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:26:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:26:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:26:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:52219

[10:26:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47245

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:29:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:29:06] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:29:06] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47247

[10:29:06] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:33:32] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:33:32] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:33:32] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:33:32] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47301

[10:33:32] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:33:45] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:33:45] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:33:47] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:33:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47305

[10:33:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:34:04] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:34:04] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:34:06] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:34:06] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47306

[10:34:06] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:35296

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:39:56] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:39:57] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:39:57] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:35297

[10:39:57] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:40:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: true

[10:40:08] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ClientDisconnect"

[10:40:09] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:40:09] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:56544

[10:40:09] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[10:40:09] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=e560c75199624160ac3f1a98b24cd159

[10:40:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:40:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:40:45] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:40:45] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:35298

[10:40:45] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:44:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: true

[10:44:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="NormalDisconnection"

[10:46:59] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:46:59] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:50508

[10:46:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[10:47:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=15b6809bf7e4454db53c9f05a963e5af

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33506

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[10:50:44] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[10:50:45] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:50:45] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33507

[10:50:45] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:54471

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:01:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:01:34] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:01:34] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:54473

[11:01:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:02:40] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:02:40] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:02:41] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:02:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:54478

[11:02:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:02:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:02:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:04:13] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:04:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:54479

[11:04:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:59780

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:12:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:12:14] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:12:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:59781

[11:12:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:12:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:12:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:12:21] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:12:21] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:59782

[11:12:21] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48222

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:23:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:23:11] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:23:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48223

[11:23:11] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36107

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:33:59] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:34:00] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:34:00] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36109

[11:34:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:34:23] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:34:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:34:23] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:34:23] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36112

[11:34:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:34:58] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:34:58] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:36:13] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:36:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36113

[11:36:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:37:39] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:37:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:37:40] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:37:40] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36114

[11:37:40] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:37:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:37:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:37:58] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:37:58] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36115

[11:37:58] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:38:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:38:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:38:17] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:38:17] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36116

[11:38:17] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:40:23] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:40:23] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:42:13] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:42:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:36117

[11:42:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33343

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:44:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:44:48] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:44:48] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33345

[11:44:48] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:45:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:45:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:45:21] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:45:21] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33349

[11:45:21] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:46:50] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:46:50] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:46:50] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:46:50] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33350

[11:46:50] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:46122

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[11:52:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[11:52:22] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:52:22] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:46123

[11:52:22] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:51356

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[12:03:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[12:03:13] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:03:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:51357

[12:03:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:53814

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[12:14:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[12:14:01] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:14:01] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:53815

[12:14:01] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33969

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[12:24:47] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[12:24:48] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:24:48] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33970

[12:24:48] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:27:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T12:25:57.7159618+08:00"

[12:27:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[12:27:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[12:27:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[12:27:32] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:27:32] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:55062

[12:27:32] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[12:27:32] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=5ec1203155704d648acbe4ddbe9808ee

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48637

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[12:35:35] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[12:35:36] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:35:36] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48638

[12:35:36] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:42967

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[12:46:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[12:46:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:46:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:42968

[12:46:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:46:21] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[12:46:21] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[12:46:24] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:46:24] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:42969

[12:46:24] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:53088

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[12:57:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[12:57:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[12:57:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:53089

[12:57:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:07:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T13:05:57.7165591+08:00"

[13:07:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[13:07:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[13:07:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[13:07:33] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:07:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:56401

[13:07:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[13:07:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=333d4233a8d443869b21043de9946599

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:60230

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[13:08:02] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[13:08:03] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:08:03] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:60231

[13:08:03] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:17:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T13:15:57.7182038+08:00"

[13:17:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[13:17:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[13:17:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[13:17:33] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:17:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:56771

[13:17:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[13:17:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=7a907983dc744455a64bd2ae1ee02c1f

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47199

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[13:18:51] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[13:18:52] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:18:52] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47200

[13:18:52] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:27:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T13:25:57.7209373+08:00"

[13:27:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[13:27:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[13:27:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[13:27:33] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:27:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:57087

[13:27:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[13:27:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=49f84a282b3e4a27b62a888085fe44cc

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:45432

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[13:29:40] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[13:29:41] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:29:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:45433

[13:29:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:50167

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[13:40:28] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[13:40:29] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:40:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:50168

[13:40:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47434

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[13:51:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[13:51:16] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:51:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47435

[13:51:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[13:52:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T13:50:57.7138283+08:00"

[13:52:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[13:52:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[13:52:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[13:52:33] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[13:52:33] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:57899

[13:52:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[13:52:33] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=6f27869efe39403f8cd10f7144e6811a

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47437

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:47436

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[14:01:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[14:02:04] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:02:04] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:33754

[14:02:04] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48899

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[14:12:53] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[14:12:54] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:12:54] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:48900

[14:12:54] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:56215

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[14:23:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[14:23:43] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:23:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:56216

[14:23:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:32:20] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[14:32:20] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[14:33:10] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:33:10] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:56217

[14:33:10] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:34:09] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[14:34:09] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[14:34:30] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:34:30] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:51051

[14:34:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:52:17] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[14:52:17] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[14:52:19] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:52:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:51052

[14:52:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:54:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T14:52:57.7065977+08:00"

[14:54:37] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[14:54:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[14:54:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[14:54:41] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:54:41] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:61128

[14:54:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[14:54:41] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=0dd3d060c989421186ecae92ab2d1e2d

[15:02:30] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: true

[15:02:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="NormalDisconnection"

[15:12:38] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[15:12:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:50903

[15:12:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[15:12:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=0d454de2dcb44fccb743d15d09f1f49a

[15:54:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T15:52:57.7173647+08:00"

[15:54:37] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[15:54:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[15:54:37] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[15:54:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[15:54:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:52818

[15:54:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[15:54:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=0fe1c35731874227899056ec77adc357

[16:24:35] [ERR]  
SELECT  `mi`.`ID` AS `Id` , `mi`.`MODEL_ID` AS `ModelId` , `pm`.`MODEL_NAME` AS `ModelName` , `mi`.`INSTRUCTION_NAME` AS `InstructionName` , `mi`.`FUNCTION_CODE` AS `FunctionCode` , `mi`.`START_ADDRESS` AS `StartAddress` , `mi`.`READ_COUNT` AS `ReadCount` , `mi`.`ENCODE` AS `Encode` , `mi`.`RESPONSE_TIME` AS `ResponseTime` , `mi`.`RETRY_COUNT` AS `RetryCount` , `mi`.`IS_ENABLED` AS `IsEnabled` , `mi`.`CREATE_TIME` AS `CreateTime` , `mi`.`UPDATE_TIME` AS `UpdateTime` , `mi`.`CREATE_BY` AS `CreateBy` , `mi`.`UPDATE_BY` AS `UpdateBy` , `mi`.`REMARK` AS `Remark`  FROM `LOT_MODEL_INSTRUCTION` `mi` Left JOIN `LOT_PRODUCT_MODEL` `pm` ON ( `mi`.`MODEL_ID` = `pm`.`ID` )     ORDER BY `mi`.`CREATE_TIME` DESC LIMIT 0,10

[16:24:35] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Unknown column 'mi.IS_ENABLED' in 'field list'",
  "details": "MySqlException: Unknown column 'mi.IS_ENABLED' in 'field list'\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)\r\n   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()\r\n   at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)\r\n   at Admin.SqlSugar.PagedListExtensions.ToPurestPagedListAsync[TResult](ISugarQueryable`1 queryable, Int32 pageIndex, Int32 pageSize) in D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\PagedListExtensions.cs:line 49\r\n   at Admin.Application.ProductServices.ModelInstructionService.GetPagedListAsync(ModelInstructionQueryInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ProductServices\\ModelInstructionService.cs:line 52\r\n   at lambda_method13446(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1054,
    "SqlState": "42S22"
  },
  "validationErrors": null
}


[16:29:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T16:27:57.7164647+08:00"

[16:29:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:29:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[16:29:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[16:29:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:29:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:54331

[16:29:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[16:29:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=54b3b44ae5f548d297c98ad47d580282

[16:39:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T16:37:57.7160895+08:00"

[16:39:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[16:39:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[16:39:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[16:39:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[16:39:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:54755

[16:39:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[16:39:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=cf876a73bb9440fabbb9895598cf9157

[17:04:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T17:02:57.7191052+08:00"

[17:04:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[17:04:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[17:04:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[17:04:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:04:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:55881

[17:04:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[17:04:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=b517484736b347f2b1395ea9e53feaa5

[17:26:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[17:26:08] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[17:26:08] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:26:08] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:51053

[17:26:08] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[17:39:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T17:37:57.7191909+08:00"

[17:39:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[17:39:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[17:39:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[17:39:42] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:39:42] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:57320

[17:39:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[17:39:42] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=203de9f5b3574d5caa550119c917ca3a

[17:59:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T17:57:57.7197664+08:00"

[17:59:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[17:59:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[17:59:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[17:59:43] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[17:59:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:58217

[17:59:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[17:59:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=a72f4fd23dc14fadbfdc668da06b58dd

[18:09:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-25T18:07:57.7153763+08:00"

[18:09:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[18:09:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[18:09:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[18:09:43] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[18:09:43] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:58610

[18:09:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[18:09:43] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=b9b6b5d8ce7649dc9a842bcd97ee86bb

