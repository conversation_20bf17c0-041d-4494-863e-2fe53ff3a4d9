// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity;

/// <summary>
/// 功能实体
/// </summary>
[SugarTable("CMS_FUNCTION")]
public partial class FunctionEntity : BaseEntity
{
    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "NAME")]
    public string Name { get; set; }
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "CODE")]
    public string Code { get; set; }
    /// <summary>
    /// 隶属于
    /// </summary>
    [SugarColumn(ColumnName = "PARENT_ID")]
    public long? ParentId { get; set; }
}