[00:00:32] [ERR]  
SELECT 1 FROM `LOT_DEVICE`   WHERE ( `DEVICE_ID` = @DeviceId0 )   LIMIT 0,1

[00:00:32] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Unknown column 'DEVICE_ID' in 'where clause'",
  "details": "MySqlException: Unknown column 'DEVICE_ID' in 'where clause'\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)\r\n   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()\r\n   at SqlSugar.QueryableProvider`1.AnyAsync()\r\n   at SqlSugar.QueryableProvider`1.AnyAsync(Expression`1 expression)\r\n   at Admin.Application.DeviceServices.DeviceService.AddAsync(AddDeviceInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\DeviceServices\\DeviceService.cs:line 66\r\n   at lambda_method2163(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1054,
    "SqlState": "42S22"
  },
  "validationErrors": null
}


[09:29:12] [ERR]  
INSERT INTO `LOT_DEVICE`  
           (`PRODUCT_ID`,`MODEL_ID`,`PARENT_ID`,`GROUP_KEY`,`DEVICE_IDENTITY_CODE`,`DEVICE_ID`,`DEVICE_NAME`,`DEVICE_DESCRIPTION`,`STATUS`,`IP_ADDRESS`,`DEVICE_SECRET`,`ACTIVATE_TIME`,`LAST_ONLINE_TIME`,`LAST_OFFLINE_TIME`,`LAST_DATA_TIME`,`IS_ENABLED`,`ID`,`CREATE_BY`,`CREATE_TIME`,`UPDATE_BY`,`UPDATE_TIME`,`REMARK`)
     VALUES
           (@PRODUCT_ID,@MODEL_ID,@PARENT_ID,@GROUP_KEY,@DEVICE_IDENTITY_CODE,@DEVICE_ID,@DEVICE_NAME,@DEVICE_DESCRIPTION,@STATUS,@IP_ADDRESS,@DEVICE_SECRET,@ACTIVATE_TIME,@LAST_ONLINE_TIME,@LAST_OFFLINE_TIME,@LAST_DATA_TIME,@IS_ENABLED,@ID,@CREATE_BY,@CREATE_TIME,@UPDATE_BY,@UPDATE_TIME,@REMARK) ;

[09:29:13] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Unknown column 'PARENT_ID' in 'field list'",
  "details": "MySqlException: Unknown column 'PARENT_ID' in 'field list'\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()\r\n   at SqlSugar.InsertableProvider`1.ExecuteReturnSnowflakeIdAsync()\r\n   at Admin.Application.DeviceServices.DeviceService.AddAsync(AddDeviceInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\DeviceServices\\DeviceService.cs:line 113\r\n   at lambda_method1403(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1054,
    "SqlState": "42S22"
  },
  "validationErrors": null
}


[09:31:00] [ERR]  
INSERT INTO `MQTT_USER`  
           (`USERNAME`,`PASSWORD`,`CLIENT_ID_LIMIT`,`EXPIRE_TIME`,`LAST_LOGIN_TIME`,`LAST_LOGIN_IP`,`DESCRIPTION`,`IS_ENABLED`,`ID`,`CREATE_BY`,`CREATE_TIME`,`UPDATE_BY`,`UPDATE_TIME`,`REMARK`)
     VALUES
           (@USERNAME,@PASSWORD,@CLIENT_ID_LIMIT,@EXPIRE_TIME,@LAST_LOGIN_TIME,@LAST_LOGIN_IP,@DESCRIPTION,@IS_ENABLED,@ID,@CREATE_BY,@CREATE_TIME,@UPDATE_BY,@UPDATE_TIME,@REMARK) ;

[09:31:00] [ERR] Admin.Communication.Mqtt.Services.MqttUserService 
创建设备MQTT用户时发生错误: DeviceId=V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678
MySqlConnector.MySqlException (0x80004005): Data too long for column 'USERNAME' at row 1
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttUserService.CreateDeviceUserAsync(String deviceId, String deviceSecret, String deviceName, Nullable`1 expireTime) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttUserService.cs:line 374

[09:31:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:31:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:32:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:32:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 没有订阅主题 'gateway/up' 的权限: 没有权限访问主题 'gateway/up'

[09:32:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 没有订阅主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:32:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:33:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:33:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:34:17] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 没有发布主题 '/devices/{device_id}/sys/properties/report' 的权限: 没有权限访问主题 '/devices/{device_id}/sys/properties/report'

[09:34:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:34:50] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:34:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:35:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:35:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:36:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:36:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:36:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:37:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:37:33] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:37:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:38:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:38:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:38:59] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:39:01] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:39:02] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:39:02] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:39:03] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[09:39:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:39:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:40:22] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:40:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:41:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:41:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:42:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:42:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:42:56] [ERR]  
SELECT  `dl`.`DEVICE_ID` AS `DeviceId` , `d`.`DEVICE_NAME` AS `DeviceName` , `d`.`DEVICE_ID` AS `DeviceCode` , `d`.`STATUS` AS `DeviceStatus` , `dl`.`DATA_TIME` AS `LastDataTime` , @constant3 AS `IsOnline` , TIMESTAMPDIFF(Minute,`dl`.`DATA_TIME`,@MethodConst5) AS `OfflineMinutes` , `dl`.`DATA_JSON` AS `DataJson`  FROM `LOT_DEVICE_DATA_LATEST` `dl` Left JOIN `LOT_DEVICE` `d` ON ( `dl`.`DEVICE_ID` = `d`.`ID` )   WHERE (TIMESTAMPDIFF(Minute,`dl`.`DATA_TIME`,@MethodConst1) > @Const2 ) 

[09:42:56] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Table 'cms.lot_device_data_latest' doesn't exist",
  "details": "MySqlException: Table 'cms.lot_device_data_latest' doesn't exist\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)\r\n   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()\r\n   at Admin.Application.DeviceServices.DeviceDataService.GetOfflineDevicesAsync(Int32 timeoutMinutes) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\DeviceServices\\DeviceDataService.cs:line 305\r\n   at lambda_method1858(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1146,
    "SqlState": "42S02"
  },
  "validationErrors": null
}


[09:43:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:43:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:44:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:44:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:45:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:45:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:46:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:46:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:47:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:47:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:48:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:48:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:49:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:49:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:50:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:50:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:51:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:51:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:52:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:52:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:53:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:53:54] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:54:24] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:54:54] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:55:24] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:55:54] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:56:24] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:56:54] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:57:24] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:57:54] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:58:24] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:58:54] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:59:24] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[09:59:54] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:00:24] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:03:52] [WRN] Admin.Communication.Mqtt.Services.MqttUserService 
删除MQTT用户失败: 用户 V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678 不存在

[10:10:04] [ERR]  
INSERT INTO `MQTT_USER`  
           (`USERNAME`,`PASSWORD`,`CLIENT_ID_LIMIT`,`EXPIRE_TIME`,`LAST_LOGIN_TIME`,`LAST_LOGIN_IP`,`DESCRIPTION`,`IS_ENABLED`,`ID`,`CREATE_BY`,`CREATE_TIME`,`UPDATE_BY`,`UPDATE_TIME`,`REMARK`)
     VALUES
           (@USERNAME,@PASSWORD,@CLIENT_ID_LIMIT,@EXPIRE_TIME,@LAST_LOGIN_TIME,@LAST_LOGIN_IP,@DESCRIPTION,@IS_ENABLED,@ID,@CREATE_BY,@CREATE_TIME,@UPDATE_BY,@UPDATE_TIME,@REMARK) ;

[10:10:04] [ERR] Admin.Communication.Mqtt.Services.MqttUserService 
创建设备MQTT用户时发生错误: DeviceId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-AnyPGeQ_MGcSsFM3QQjm13bheSOd29HhbhEA1OsDm_xBSWtYgy28UAOgoxtEBMzTIAAzwnh_ksM-KkOa-IMGSNZnhaL2
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0' for key 'PRIMARY'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttUserService.CreateDeviceUserAsync(String deviceId, String deviceSecret, String deviceName, Nullable`1 expireTime) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttUserService.cs:line 374

[10:10:04] [ERR]  
INSERT INTO `MQTT_ACL_RULE`  
           (`RULE_NAME`,`PRIORITY`,`ACCESS_TYPE`,`ALLOW`,`USERNAME`,`CLIENT_ID`,`IP_ADDRESS`,`TOPIC`,`DESCRIPTION`,`IS_ACTIVE`,`EFFECTIVE_START_TIME`,`EFFECTIVE_END_TIME`,`ID`,`CREATE_BY`,`CREATE_TIME`,`UPDATE_BY`,`UPDATE_TIME`,`REMARK`)
     VALUES
           (@RULE_NAME,@PRIORITY,@ACCESS_TYPE,@ALLOW,@USERNAME,@CLIENT_ID,@IP_ADDRESS,@TOPIC,@DESCRIPTION,@IS_ACTIVE,@EFFECTIVE_START_TIME,@EFFECTIVE_END_TIME,@ID,@CREATE_BY,@CREATE_TIME,@UPDATE_BY,@UPDATE_TIME,@REMARK) ;

[10:10:05] [ERR] Admin.Application.MqttBrokerServices.MqttAclManagementService 
创建设备ACL规则失败: Template=Device_{DeviceId}_Properties_Report
MySqlConnector.MySqlException (0x80004005): Data too long for column 'RULE_NAME' at row 1
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnSnowflakeIdAsync()
   at Admin.Application.MqttBrokerServices.MqttAclManagementService.CreateDeviceAclRulesAsync(CreateDeviceAclRulesInput input) in D:\code projects\purest-admin-main\api\Admin.Application\MqttBrokerServices\MqttAclManagementService.cs:line 642

[10:10:05] [ERR]  
INSERT INTO `MQTT_ACL_RULE`  
           (`RULE_NAME`,`PRIORITY`,`ACCESS_TYPE`,`ALLOW`,`USERNAME`,`CLIENT_ID`,`IP_ADDRESS`,`TOPIC`,`DESCRIPTION`,`IS_ACTIVE`,`EFFECTIVE_START_TIME`,`EFFECTIVE_END_TIME`,`ID`,`CREATE_BY`,`CREATE_TIME`,`UPDATE_BY`,`UPDATE_TIME`,`REMARK`)
     VALUES
           (@RULE_NAME,@PRIORITY,@ACCESS_TYPE,@ALLOW,@USERNAME,@CLIENT_ID,@IP_ADDRESS,@TOPIC,@DESCRIPTION,@IS_ACTIVE,@EFFECTIVE_START_TIME,@EFFECTIVE_END_TIME,@ID,@CREATE_BY,@CREATE_TIME,@UPDATE_BY,@UPDATE_TIME,@REMARK) ;

[10:10:05] [ERR] Admin.Application.MqttBrokerServices.MqttAclManagementService 
创建设备ACL规则失败: Template=Device_{DeviceId}_Gateway_Properties_Report
MySqlConnector.MySqlException (0x80004005): Data too long for column 'RULE_NAME' at row 1
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnSnowflakeIdAsync()
   at Admin.Application.MqttBrokerServices.MqttAclManagementService.CreateDeviceAclRulesAsync(CreateDeviceAclRulesInput input) in D:\code projects\purest-admin-main\api\Admin.Application\MqttBrokerServices\MqttAclManagementService.cs:line 642

[10:12:26] [ERR]  
INSERT INTO `MQTT_USER`  
           (`USERNAME`,`PASSWORD`,`CLIENT_ID_LIMIT`,`EXPIRE_TIME`,`LAST_LOGIN_TIME`,`LAST_LOGIN_IP`,`DESCRIPTION`,`IS_ENABLED`,`ID`,`CREATE_BY`,`CREATE_TIME`,`UPDATE_BY`,`UPDATE_TIME`,`REMARK`)
     VALUES
           (@USERNAME,@PASSWORD,@CLIENT_ID_LIMIT,@EXPIRE_TIME,@LAST_LOGIN_TIME,@LAST_LOGIN_IP,@DESCRIPTION,@IS_ENABLED,@ID,@CREATE_BY,@CREATE_TIME,@UPDATE_BY,@UPDATE_TIME,@REMARK) ;

[10:12:26] [ERR] Admin.Communication.Mqtt.Services.MqttUserService 
创建设备MQTT用户时发生错误: DeviceId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0' for key 'PRIMARY'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttUserService.CreateDeviceUserAsync(String deviceId, String deviceSecret, String deviceName, Nullable`1 expireTime) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttUserService.cs:line 374

[10:12:55] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:13:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:13:55] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:14:13] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 没有发布主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report' 的权限: 没有权限访问主题 '/devices/V-09GfEDsRC0brzRBPeFs8Ha2492G0R4ym3pqzt2fPMPomlVltanI678/sys/properties/report'

[10:14:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:14:34] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 没有发布主题 '/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report' 的权限: 没有权限访问主题 '/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report'

[10:14:55] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:14:56] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 没有发布主题 '/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report' 的权限: 没有权限访问主题 '/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report'

[10:15:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:15:55] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:16:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 gateWay 没有发布主题 'gateway/1' 的权限: 没有权限访问主题 'gateway/1'

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[10:33:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[10:33:12] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[10:33:12] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[10:33:13] [INF]  
项目当前环境为：Development

[10:33:13] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[10:33:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[10:33:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[10:33:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[10:33:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[10:33:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[10:33:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[10:33:14] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:33:14] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: gateWay, IP: ***********:25981

[10:33:14] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=gateWay, IP=***********, SessionPresent=true

[10:33:31] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[10:33:31] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:54080

[10:33:31] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[11:00:10] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[11:00:10] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[11:00:11] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[11:00:11] [INF]  
项目当前环境为：Development

[11:00:11] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[11:00:11] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[11:00:11] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[11:00:11] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report

[11:00:11] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[11:00:11] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[11:00:11] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[11:00:15] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:00:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: gateWay, IP: ***********:26007

[11:00:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=gateWay, IP=***********, SessionPresent=true

[11:00:20] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[11:00:20] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:55549

[11:00:20] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
开始处理设备消息: 主题=/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report, 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到设备: ID=696462312706117, 名称=1号温湿度, 模型ID=696308782780485

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
模型属性数量: 2

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 开始解析设备数据 ===

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备信息: ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 名称=1号温湿度, 分组Key=

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
原始JSON数据: 

[11:01:17] [WRN] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
JSON数据为空

[11:01:17] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备消息处理完成: 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
开始处理设备消息: 主题=/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report, 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到设备: ID=696462312706117, 名称=1号温湿度, 模型ID=696308782780485

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
模型属性数量: 2

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 开始解析设备数据 ===

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备信息: ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 名称=1号温湿度, 分组Key=

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
原始JSON数据: 

[11:01:56] [WRN] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
JSON数据为空

[11:01:56] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备消息处理完成: 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
开始处理设备消息: 主题=/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report, 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到设备: ID=696462312706117, 名称=1号温湿度, 模型ID=696308782780485

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
模型属性数量: 2

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 开始解析设备数据 ===

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备信息: ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 名称=1号温湿度, 分组Key=

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
原始JSON数据: {
  "wendu": 20.4,
  "shidu": 60.1
}

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
--- 解析直连设备数据 ---

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备: 1号温湿度(QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2)

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
✓ 属性匹配: 温度(wendu) = 20.4 [decimal小数] C

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
✓ 属性匹配: 湿度(shidu) = 60.1 [decimal小数] %

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 设备数据解析完成 ===

[11:03:48] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备消息处理完成: 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[17:39:15] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[17:39:15] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[17:39:16] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[17:39:16] [INF]  
项目当前环境为：Development

[17:39:16] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[17:39:16] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[17:39:16] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[17:39:16] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report

[17:39:16] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[17:39:16] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[17:39:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[17:44:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库清理了 1 个过期持久会话

[18:02:27] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[18:02:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:54302

[18:02:27] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 123123 已存在连接，替换旧连接

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: false

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="ReplacedByNewConnection"

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:60803

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: 123123, 是否正常断开: true

[18:13:16] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=123123, IP=*************, Reason="NormalDisconnection"

[18:13:17] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[18:13:17] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:60804

[18:13:17] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[23:34:38] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[23:34:38] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[23:34:38] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[23:34:38] [INF]  
项目当前环境为：Development

[23:34:38] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[23:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[23:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[23:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report

[23:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[23:34:38] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[23:34:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[23:34:43] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "GET api/v1/device-data/latest-property-data" for actions - Admin.Application.DeviceServices.DeviceDataService.GetLatestPropertyDataAsync (Admin.Application),Admin.Application.DeviceServices.DeviceDataService.GetLatestPropertyDataAsync (Admin.Application). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

[23:34:45] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "GET api/v1/device-data/latest-property-data" for actions - Admin.Application.DeviceServices.DeviceDataService.GetLatestPropertyDataAsync (Admin.Application),Admin.Application.DeviceServices.DeviceDataService.GetLatestPropertyDataAsync (Admin.Application). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

[23:34:52] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "GET api/v1/device-data/latest-property-data" for actions - Admin.Application.DeviceServices.DeviceDataService.GetLatestPropertyDataAsync (Admin.Application),Admin.Application.DeviceServices.DeviceDataService.GetLatestPropertyDataAsync (Admin.Application). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

[23:34:57] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在停止MQTT代理服务...

[23:34:57] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在停止MQTT代理服务...

[23:34:57] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已停止

[23:34:57] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功停止

[23:34:57] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
销毁消息处理器: DeviceDataHandler

[23:34:57] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在销毁...

[23:34:57] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器销毁完成

[23:34:57] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器销毁完成: DeviceDataHandler

[23:34:57] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[23:34:57] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在销毁MQTT消息分发器...

[23:34:57] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器已销毁

[23:34:57] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已释放

[23:34:57] [INF] WorkflowCore.Services.WorkflowHost 
Stopping background tasks

[23:34:57] [INF] WorkflowCore.Services.WorkflowHost 
Worker tasks stopped

