// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Admin.Application.MqttBrokerServices.Dto;

#region 请求 DTO

/// <summary>
/// 添加MQTT用户请求DTO
/// </summary>
public class AddMqttUserInput
{
    /// <summary>
    /// 用户名
    /// </summary>
    [Required(ErrorMessage = "用户名不能为空")]
    [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    [StringLength(100, ErrorMessage = "密码长度不能超过100个字符")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 客户端ID限制，多个用逗号分隔，为空表示不限制
    /// </summary>
    [StringLength(500, ErrorMessage = "客户端ID限制长度不能超过500个字符")]
    public string? ClientIdLimit { get; set; }

    /// <summary>
    /// 过期时间，为空表示永不过期
    /// </summary>
    public DateTime? ExpireTime { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [StringLength(200, ErrorMessage = "描述长度不能超过200个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 更新MQTT用户请求DTO
/// </summary>
public class UpdateMqttUserInput
{
    /// <summary>
    /// 密码（为空表示不修改密码）
    /// </summary>
    [StringLength(100, ErrorMessage = "密码长度不能超过100个字符")]
    public string? Password { get; set; }

    /// <summary>
    /// 客户端ID限制，多个用逗号分隔，为空表示不限制
    /// </summary>
    [StringLength(500, ErrorMessage = "客户端ID限制长度不能超过500个字符")]
    public string? ClientIdLimit { get; set; }

    /// <summary>
    /// 过期时间，为空表示永不过期
    /// </summary>
    public DateTime? ExpireTime { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [StringLength(200, ErrorMessage = "描述长度不能超过200个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// MQTT用户查询请求DTO
/// </summary>
public class MqttUserQueryInput : PaginationParams
{
    /// <summary>
    /// 用户名过滤
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 是否启用过滤
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 创建设备MQTT用户请求DTO
/// </summary>
public class CreateDeviceMqttUserInput
{
    /// <summary>
    /// 设备ID（作为用户名）
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// 设备密钥（作为密码）
    /// </summary>
    [Required(ErrorMessage = "设备密钥不能为空")]
    public string DeviceSecret { get; set; } = string.Empty;

    /// <summary>
    /// 设备名称（作为描述）
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 过期时间，为空表示永不过期
    /// </summary>
    public DateTime? ExpireTime { get; set; }
}

#endregion

#region 响应 DTO

/// <summary>
/// MQTT用户输出DTO
/// </summary>
public class MqttUserOutput
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 客户端ID限制
    /// </summary>
    public string? ClientIdLimit { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpireTime { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 最后登录IP
    /// </summary>
    public string? LastLoginIp { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 是否过期
    /// </summary>
    public bool IsExpired => ExpireTime.HasValue && ExpireTime.Value < DateTime.Now;

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusDescription
    {
        get
        {
            if (!IsEnabled) return "已禁用";
            if (IsExpired) return "已过期";
            return "正常";
        }
    }
}



#endregion
