// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// MQTT订阅信息
/// </summary>
[SugarTable("mqtt_subscriptions")]
public partial class MqttSubscriptionEntity
{
    /// <summary>
    /// 主键ID（雪花ID）
    /// </summary>
    [SugarColumn(ColumnName = "Id", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [SugarColumn(ColumnName = "ClientId")]
    public string ClientId { get; set; }

    /// <summary>
    /// 主题过滤器
    /// </summary>
    [SugarColumn(ColumnName = "TopicFilter")]
    public string TopicFilter { get; set; }

    /// <summary>
    /// 服务质量等级(0,1,2)
    /// </summary>
    [SugarColumn(ColumnName = "Qos")]
    public int Qos { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    [SugarColumn(ColumnName = "SubscribedTime")]
    public DateTime SubscribedTime { get; set; }

    /// <summary>
    /// 最后匹配时间
    /// </summary>
    [SugarColumn(ColumnName = "LastMatchTime")]
    public DateTime? LastMatchTime { get; set; }

    /// <summary>
    /// 匹配次数
    /// </summary>
    [SugarColumn(ColumnName = "MatchCount")]
    public long MatchCount { get; set; }

    /// <summary>
    /// 是否为通配符订阅(0-否, 1-是)
    /// </summary>
    [SugarColumn(ColumnName = "IsWildcard")]
    public bool IsWildcard { get; set; }

    /// <summary>
    /// 订阅状态(1-活跃, 2-暂停)
    /// </summary>
    [SugarColumn(ColumnName = "Status")]
    public int Status { get; set; }
} 