// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Application.ProductServices.Dto;

/// <summary>
/// 产品模型输入DTO
/// </summary>
public class ProductModelInput
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 设备组 (1:温湿度 2:漏水检测 3:空调 4:UPS 5:配电 6:开关 7:发电机 8:红外 9:门禁 10:传感器 11.冷通道)
    /// </summary>
    public int DeviceGroup { get; set; }



    /// <summary>
    /// 模型描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 产品模型更新输入DTO
/// </summary>
public class UpdateProductModelInput : ProductModelInput
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
/// 产品模型输出DTO
/// </summary>
public class ProductModelOutput
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 产品ID
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 设备组
    /// </summary>
    public int DeviceGroup { get; set; }

    /// <summary>
    /// 设备组名称
    /// </summary>
    public string DeviceGroupName { get; set; }



    /// <summary>
    /// 模型描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 产品模型查询输入DTO
/// </summary>
public class ProductModelQueryInput : PaginationParams
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long? ProductId { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 设备组
    /// </summary>
    public int? DeviceGroup { get; set; }



    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 产品模型简单输出DTO (用于下拉选择等场景)
/// </summary>
public class ProductModelSimpleOutput
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 产品ID
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 设备组
    /// </summary>
    public int DeviceGroup { get; set; }

    /// <summary>
    /// 设备组名称
    /// </summary>
    public string DeviceGroupName { get; set; }


}

/// <summary>
/// 产品模型详情输出DTO (包含属性信息)
/// </summary>
public class ProductModelDetailOutput : ProductModelOutput
{
    /// <summary>
    /// 属性列表
    /// </summary>
    public List<ProductPropertyOutput> Properties { get; set; } = [];
} 