// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using SqlSugar;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// MQTT待发送消息表
/// </summary>
[SugarTable("mqtt_pending_messages")]
public partial class MqttPendingMessageEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(ColumnName = "Id", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 消息ID
    /// </summary>
    [SugarColumn(ColumnName = "MessageId")]
    public string MessageId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [SugarColumn(ColumnName = "ClientId")]
    public string ClientId { get; set; }

    /// <summary>
    /// 消息主题
    /// </summary>
    [SugarColumn(ColumnName = "Topic")]
    public string Topic { get; set; }

    /// <summary>
    /// 消息负载
    /// </summary>
    [SugarColumn(ColumnName = "Payload")]
    public byte[] Payload { get; set; }

    /// <summary>
    /// 服务质量等级(0,1,2)
    /// </summary>
    [SugarColumn(ColumnName = "Qos")]
    public int Qos { get; set; }

    /// <summary>
    /// 是否为保留消息(0-否, 1-是)
    /// </summary>
    [SugarColumn(ColumnName = "Retain")]
    public bool Retain { get; set; }

    /// <summary>
    /// 是否为重复消息(0-否, 1-是)
    /// </summary>
    [SugarColumn(ColumnName = "Duplicate")]
    public bool Duplicate { get; set; }

    /// <summary>
    /// MQTT包ID（QoS>0时使用）
    /// </summary>
    [SugarColumn(ColumnName = "PacketId")]
    public int? PacketId { get; set; }

    /// <summary>
    /// 消息类型(1-PUBLISH, 2-PUBACK, 3-PUBREC, 4-PUBREL, 5-PUBCOMP)
    /// </summary>
    [SugarColumn(ColumnName = "MessageType")]
    public int MessageType { get; set; }

    /// <summary>
    /// 消息优先级(1-低, 2-普通, 3-高, 4-紧急)
    /// </summary>
    [SugarColumn(ColumnName = "Priority")]
    public int Priority { get; set; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    [SugarColumn(ColumnName = "CreatedTime")]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 计划发送时间
    /// </summary>
    [SugarColumn(ColumnName = "ScheduleTime")]
    public DateTime? ScheduleTime { get; set; }

    /// <summary>
    /// 消息过期时间
    /// </summary>
    [SugarColumn(ColumnName = "ExpiryTime")]
    public DateTime? ExpiryTime { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    [SugarColumn(ColumnName = "RetryCount")]
    public int RetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [SugarColumn(ColumnName = "MaxRetryCount")]
    public int MaxRetryCount { get; set; }

    /// <summary>
    /// 最后重试时间
    /// </summary>
    [SugarColumn(ColumnName = "LastRetryTime")]
    public DateTime? LastRetryTime { get; set; }

    /// <summary>
    /// 消息状态(1-待发送, 2-发送中, 3-已发送, 4-已确认, 5-发送失败, 6-已过期)
    /// </summary>
    [SugarColumn(ColumnName = "Status")]
    public int Status { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(ColumnName = "ErrorMessage")]
    public string ErrorMessage { get; set; }

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    [SugarColumn(ColumnName = "PublisherId")]
    public string PublisherId { get; set; }

    /// <summary>
    /// 消息确认状态(0-无需确认, 1-等待PUBACK, 2-等待PUBREC, 3-等待PUBREL, 4-等待PUBCOMP)
    /// </summary>
    [SugarColumn(ColumnName = "AckStatus")]
    public int AckStatus { get; set; }

    /// <summary>
    /// 消息确认时间
    /// </summary>
    [SugarColumn(ColumnName = "AckTime")]
    public DateTime? AckTime { get; set; }
} 