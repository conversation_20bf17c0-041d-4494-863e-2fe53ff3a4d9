{"openapi": "3.0.1", "info": {"title": "LOT系统"}, "paths": {"/api/v1/alarm-event/sync-system-alarm-events": {"post": {"tags": ["AlarmEvent"], "summary": "同步系统告警事件\r\n重新生成所有设备的告警事件，用于参数配置变更后的事件同步", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.AlarmServices.AlarmEventSyncResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AlarmServices.AlarmEventSyncResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AlarmServices.AlarmEventSyncResult"}}}}}}}, "/api/v1/alarm-history/pending-confirmation-alarms": {"get": {"tags": ["AlarmHistory"], "summary": "获取待确认告警", "parameters": [{"name": "DeviceId", "in": "query", "description": "设备ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "DeviceName", "in": "query", "description": "设备名称", "schema": {"type": "string"}}, {"name": "AlarmType", "in": "query", "description": "告警类型 (1:通讯失败, 2:参数超限)", "schema": {"type": "integer", "format": "int32"}}, {"name": "AlarmLevel", "in": "query", "description": "告警级别 (1：紧急 2：严重 3：一般 4：预警)", "schema": {"type": "integer", "format": "int32"}}, {"name": "AlarmStatus", "in": "query", "description": "告警状态 (1:待确认, 2:已确认, 3:待处理, 4:已处理)", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsReleased", "in": "query", "description": "是否已解除", "schema": {"type": "boolean"}}, {"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/alarm-history/confirmed-alarms": {"get": {"tags": ["AlarmHistory"], "summary": "获取已确认告警", "parameters": [{"name": "DeviceId", "in": "query", "description": "设备ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "DeviceName", "in": "query", "description": "设备名称", "schema": {"type": "string"}}, {"name": "AlarmType", "in": "query", "description": "告警类型 (1:通讯失败, 2:参数超限)", "schema": {"type": "integer", "format": "int32"}}, {"name": "AlarmLevel", "in": "query", "description": "告警级别 (1：紧急 2：严重 3：一般 4：预警)", "schema": {"type": "integer", "format": "int32"}}, {"name": "AlarmStatus", "in": "query", "description": "告警状态 (1:待确认, 2:已确认, 3:待处理, 4:已处理)", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsReleased", "in": "query", "description": "是否已解除", "schema": {"type": "boolean"}}, {"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/alarm-history/unconfirmed-but-released-alarms": {"get": {"tags": ["AlarmHistory"], "summary": "获取未确认但已解除的告警", "parameters": [{"name": "DeviceId", "in": "query", "description": "设备ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "DeviceName", "in": "query", "description": "设备名称", "schema": {"type": "string"}}, {"name": "AlarmType", "in": "query", "description": "告警类型 (1:通讯失败, 2:参数超限)", "schema": {"type": "integer", "format": "int32"}}, {"name": "AlarmLevel", "in": "query", "description": "告警级别 (1：紧急 2：严重 3：一般 4：预警)", "schema": {"type": "integer", "format": "int32"}}, {"name": "AlarmStatus", "in": "query", "description": "告警状态 (1:待确认, 2:已确认, 3:待处理, 4:已处理)", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsReleased", "in": "query", "description": "是否已解除", "schema": {"type": "boolean"}}, {"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/alarm-history/confirm-alarms": {"post": {"tags": ["AlarmHistory"], "summary": "确认告警", "requestBody": {"description": "确认输入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AlarmServices.Dto.ConfirmAlarmInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AlarmServices.Dto.ConfirmAlarmInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AlarmServices.Dto.ConfirmAlarmInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/control-cmd/paged-list": {"get": {"tags": ["ControlCmd"], "summary": "分页查询控制指令", "parameters": [{"name": "CmdName", "in": "query", "description": "指令名称 (模糊查询)", "schema": {"type": "string"}}, {"name": "DeviceId", "in": "query", "description": "设备ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "CommunicationMedium", "in": "query", "description": "通讯介质 (1:MQTT)", "schema": {"type": "integer", "format": "int32"}}, {"name": "Encode", "in": "query", "description": "编码方式 (1:<PERSON><PERSON><PERSON> 2:HEX)", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsAppControl", "in": "query", "description": "是否启用APP控制 (0:否 1:是)", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlCmdOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlCmdOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlCmdOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/control-cmd/{id}/by-id": {"get": {"tags": ["ControlCmd"], "summary": "根据ID获取控制指令", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}}}}}}, "/api/v1/control-cmd": {"post": {"tags": ["ControlCmd"], "summary": "添加控制指令", "requestBody": {"description": "指令信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.AddControlCmdInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.AddControlCmdInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.AddControlCmdInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}}}}}, "put": {"tags": ["ControlCmd"], "summary": "更新控制指令", "requestBody": {"description": "指令信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.UpdateControlCmdInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.UpdateControlCmdInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.UpdateControlCmdInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}}}}}}, "/api/v1/control-cmd/{id}": {"delete": {"tags": ["ControlCmd"], "summary": "删除控制指令", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/control-cmd/{deviceId}/by-device-id": {"get": {"tags": ["ControlCmd"], "summary": "根据设备ID获取控制指令列表", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}}}}}}}}, "/api/v1/control-cmd/execute-command": {"post": {"tags": ["ControlCmd"], "summary": "执行指令", "requestBody": {"description": "执行请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.ExecuteCommandRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.ExecuteCommandRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.ExecuteCommandRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.CommandExecuteResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.CommandExecuteResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.CommandExecuteResult"}}}}}}}, "/api/v1/control-cmd/batch-execute-command": {"post": {"tags": ["ControlCmd"], "summary": "批量执行指令", "requestBody": {"description": "批量执行请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.BatchExecuteCommandRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.BatchExecuteCommandRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.BatchExecuteCommandRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.BatchExecuteResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.BatchExecuteResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.BatchExecuteResult"}}}}}}}, "/api/v1/control-cmd/{commandId}/execute-command-by-id": {"post": {"tags": ["ControlCmd"], "summary": "执行指定ID的指令", "parameters": [{"name": "commandId", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "source", "in": "query", "description": "来源", "schema": {"type": "string"}}, {"name": "username", "in": "query", "description": "用户名", "schema": {"type": "string"}}], "requestBody": {"description": "动态参数", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.CommandExecuteResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.CommandExecuteResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.CommandExecuteResult"}}}}}}}, "/api/v1/control-plan/paged-list": {"get": {"tags": ["ControlPlan"], "summary": "分页查询控制计划", "parameters": [{"name": "Name", "in": "query", "description": "计划名称 (模糊查询)", "schema": {"type": "string"}}, {"name": "TriggerType", "in": "query", "description": "触发方式 (1:重复执行 2:条件执行)", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsAppControl", "in": "query", "description": "监控状态 (0:不启用 1:启用)", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlPlanOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlPlanOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlPlanOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/control-plan/{id}/by-id": {"get": {"tags": ["ControlPlan"], "summary": "根据ID获取控制计划", "parameters": [{"name": "id", "in": "path", "description": "计划ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}}}}}}, "/api/v1/control-plan": {"post": {"tags": ["ControlPlan"], "summary": "添加控制计划", "requestBody": {"description": "计划信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.AddControlPlanInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.AddControlPlanInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.AddControlPlanInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}}}}}, "put": {"tags": ["ControlPlan"], "summary": "更新控制计划", "requestBody": {"description": "计划信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.UpdateControlPlanInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.UpdateControlPlanInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.UpdateControlPlanInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}}}}}}}, "/api/v1/control-plan/{id}": {"delete": {"tags": ["ControlPlan"], "summary": "删除控制计划", "parameters": [{"name": "id", "in": "path", "description": "计划ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "添加设备", "requestBody": {"description": "设备信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceOutput"}}}}}}}, "/api/v1/device/{id}/by-id": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "根据ID获取设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceOutput"}}}}}}}, "/api/v1/device/query": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "分页查询设备", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceQueryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceQueryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceQueryInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/device/{id}": {"put": {"tags": ["<PERSON><PERSON>"], "summary": "更新设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "更新信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}, "delete": {"tags": ["<PERSON><PERSON>"], "summary": "删除设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device/{id}/enable": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "启用设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device/{id}/disable": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "禁用设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device/{id}/reset-secret": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "重置设备密钥", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/v1/device-data/history-data": {"get": {"tags": ["DeviceData"], "summary": "获取设备历史数据", "parameters": [{"name": "DeviceId", "in": "query", "description": "设备ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/device-data/{deviceId}/is-device-online": {"post": {"tags": ["DeviceData"], "summary": "检查设备是否在线", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "timeoutMinutes", "in": "query", "description": "超时时间（分钟）", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device-data/offline-devices": {"get": {"tags": ["DeviceData"], "summary": "获取离线设备列表", "parameters": [{"name": "timeoutMinutes", "in": "query", "description": "超时时间（分钟）", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput"}}}}}}}}, "/api/v1/device-data/{deviceId}/history-data": {"delete": {"tags": ["DeviceData"], "summary": "删除设备历史数据", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "beforeDate", "in": "query", "description": "删除此时间之前的数据", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/device-data/batch-delete-history-data": {"post": {"tags": ["DeviceData"], "summary": "批量删除历史数据", "parameters": [{"name": "beforeDate", "in": "query", "description": "删除此时间之前的数据", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/device-data/{deviceId}/device-latest-data": {"delete": {"tags": ["DeviceData"], "summary": "删除设备所有最新数据", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/device-instruction/paged-list": {"get": {"tags": ["DeviceInstruction"], "summary": "分页查询设备指令", "parameters": [{"name": "DeviceId", "in": "query", "description": "设备ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "InstructionName", "in": "query", "description": "指令名称", "schema": {"type": "string"}}, {"name": "IsEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/device-instruction/{id}/by-id": {"get": {"tags": ["DeviceInstruction"], "summary": "根据ID获取设备指令", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}}}}}}, "/api/v1/device-instruction/{deviceId}/by-device-id": {"get": {"tags": ["DeviceInstruction"], "summary": "根据设备ID获取指令列表", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}}}}}}}, "/api/v1/device-instruction": {"post": {"tags": ["DeviceInstruction"], "summary": "添加设备指令", "requestBody": {"description": "指令信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}}}}}}, "/api/v1/device-instruction/{id}": {"put": {"tags": ["DeviceInstruction"], "summary": "更新设备指令", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "更新信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}}}}}}, "delete": {"tags": ["DeviceInstruction"], "summary": "删除设备指令", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/device-instruction/batch-delete": {"post": {"tags": ["DeviceInstruction"], "summary": "批量删除设备指令", "requestBody": {"description": "指令ID列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/device-instruction/{id}/set-enabled": {"post": {"tags": ["DeviceInstruction"], "summary": "启用/禁用设备指令", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/device-instruction/{deviceId}/simple-list": {"get": {"tags": ["DeviceInstruction"], "summary": "获取设备指令简单列表（用于下拉选择等场景）", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "enabledOnly", "in": "query", "description": "是否只返回启用的指令", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput"}}}}}}}}, "/api/v1/device-para/query": {"post": {"tags": ["DevicePara"], "summary": "分页查询设备参数", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaQueryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaQueryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaQueryInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceParaOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceParaOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceParaOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/device-para/{parameterId}/by-id": {"get": {"tags": ["DevicePara"], "summary": "根据ID获取设备参数", "parameters": [{"name": "parameterId", "in": "path", "description": "参数ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}}}}}}, "/api/v1/device-para/{deviceId}/by-device-id": {"get": {"tags": ["DevicePara"], "summary": "根据设备ID获取参数列表", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}}}}}}}, "/api/v1/device-para/{deviceId}/simple-list-by-device-id": {"get": {"tags": ["DevicePara"], "summary": "根据设备ID获取简单参数列表（用于下拉选择）", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput"}}}}}}}}, "/api/v1/device-para": {"post": {"tags": ["DevicePara"], "summary": "添加设备参数", "requestBody": {"description": "设备参数信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceParaInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceParaInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceParaInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}}}}}, "put": {"tags": ["DevicePara"], "summary": "更新设备参数", "requestBody": {"description": "设备参数信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}}}}}}, "delete": {"tags": ["DevicePara"], "summary": "删除设备参数", "parameters": [{"name": "parameterId", "in": "query", "description": "参数ID", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device-para/batch-delete": {"post": {"tags": ["DevicePara"], "summary": "批量删除设备参数", "requestBody": {"description": "批量删除输入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device-para/set-monitor-status": {"post": {"tags": ["DevicePara"], "summary": "设置设备参数监控状态", "requestBody": {"description": "设置监控状态输入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/device-topic-config/paged-list": {"get": {"tags": ["DeviceTopicConfig"], "summary": "分页查询设备主题配置", "parameters": [{"name": "DeviceId", "in": "query", "description": "设备ID", "schema": {"type": "string"}}, {"name": "TemplateName", "in": "query", "description": "模板名称", "schema": {"type": "string"}}, {"name": "Topic", "in": "query", "description": "主题", "schema": {"type": "string"}}, {"name": "AccessType", "in": "query", "description": "访问类型(0=All, 1=Publish, 2=Subscribe)", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/device-topic-config/{id}/by-id": {"get": {"tags": ["DeviceTopicConfig"], "summary": "根据ID获取设备主题配置", "parameters": [{"name": "id", "in": "path", "description": "配置ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}}}}}}, "/api/v1/device-topic-config/{deviceId}/by-device-id": {"get": {"tags": ["DeviceTopicConfig"], "summary": "根据设备ID获取主题配置列表", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}}}}}}, "delete": {"tags": ["DeviceTopicConfig"], "summary": "根据设备ID删除所有主题配置", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/device-topic-config": {"post": {"tags": ["DeviceTopicConfig"], "summary": "添加设备主题配置", "requestBody": {"description": "配置信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}}}}}}}, "/api/v1/device-topic-config/{id}": {"delete": {"tags": ["DeviceTopicConfig"], "summary": "删除设备主题配置", "parameters": [{"name": "id", "in": "path", "description": "配置ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/device-topic-config/batch-delete": {"post": {"tags": ["DeviceTopicConfig"], "summary": "批量删除设备主题配置", "requestBody": {"description": "配置ID列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/modbus-test/send-test-command": {"post": {"tags": ["ModbusTest"], "summary": "手动发送Modbus指令", "requestBody": {"description": "测试输入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestResult"}}}}}}}, "/api/v1/modbus-test/simulate-device-response": {"post": {"tags": ["ModbusTest"], "summary": "模拟设备响应", "requestBody": {"description": "响应输入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusResponseInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusResponseInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusResponseInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusTestResult"}}}}}}}, "/api/v1/modbus-test/{deviceId}/device-instructions-for-test": {"get": {"tags": ["ModbusTest"], "summary": "获取设备指令列表（用于测试）", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.DeviceInstructionTestOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.DeviceInstructionTestOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.DeviceInstructionTestOutput"}}}}}}}}, "/api/v1/modbus-test/generate-modbus-command": {"post": {"tags": ["ModbusTest"], "summary": "生成标准Modbus RTU指令", "requestBody": {"description": "指令参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusCommandInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusCommandInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusCommandInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusCommandResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusCommandResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusCommandResult"}}}}}}}, "/api/v1/model-instruction/paged-list": {"get": {"tags": ["ModelInstruction"], "summary": "获取模型指令分页列表", "parameters": [{"name": "ModelId", "in": "query", "description": "模型ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "InstructionName", "in": "query", "description": "指令名称", "schema": {"type": "string"}}, {"name": "IsEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/model-instruction/{id}/by-id": {"get": {"tags": ["ModelInstruction"], "summary": "根据ID获取模型指令详情", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionOutput"}}}}}}}, "/api/v1/model-instruction": {"post": {"tags": ["ModelInstruction"], "summary": "创建模型指令", "requestBody": {"description": "创建输入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionOutput"}}}}}}, "put": {"tags": ["ModelInstruction"], "summary": "更新模型指令\r\n只能修改InstructionName、FunctionCode、StartAddress、ReadCount、Encode、ResponseTime、RetryCount、IsEnabled", "requestBody": {"description": "更新输入", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateModelInstructionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateModelInstructionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateModelInstructionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-instruction/{id}": {"delete": {"tags": ["ModelInstruction"], "summary": "删除模型指令", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-instruction/batch-delete": {"post": {"tags": ["ModelInstruction"], "summary": "批量删除模型指令", "requestBody": {"description": "指令ID列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-instruction/{modelId}/by-model-id": {"get": {"tags": ["ModelInstruction"], "summary": "根据模型ID获取指令列表", "parameters": [{"name": "modelId", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput"}}}}}}}}, "/api/v1/model-instruction/{id}/set-enabled": {"post": {"tags": ["ModelInstruction"], "summary": "设置指令启用状态", "parameters": [{"name": "id", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-instruction/simple-list": {"get": {"tags": ["ModelInstruction"], "summary": "获取所有启用的指令列表", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput"}}}}}}}}, "/api/v1/model-property/paged-list": {"get": {"tags": ["ModelProperty"], "summary": "获取模型属性分页列表", "parameters": [{"name": "ModelId", "in": "query", "description": "模型ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "InstructionId", "in": "query", "description": "指令ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "Key", "in": "query", "description": "参数标识符", "schema": {"type": "string"}}, {"name": "DataType", "in": "query", "description": "数据类型", "schema": {"type": "integer", "format": "int32"}}, {"name": "MonitorStatus", "in": "query", "description": "监控状态", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsSave", "in": "query", "description": "是否保存", "schema": {"type": "boolean"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/model-property/{id}/by-id": {"get": {"tags": ["ModelProperty"], "summary": "根据ID获取模型属性详情", "parameters": [{"name": "id", "in": "path", "description": "属性ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}}}}}}, "/api/v1/model-property/{id}/product-property-by-id": {"get": {"tags": ["ModelProperty"], "summary": "根据ID获取产品属性详情 (向后兼容)", "parameters": [{"name": "id", "in": "path", "description": "属性ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertyOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertyOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertyOutput"}}}}}}}, "/api/v1/model-property": {"post": {"tags": ["ModelProperty"], "summary": "添加模型属性", "requestBody": {"description": "属性信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}, "put": {"tags": ["ModelProperty"], "summary": "更新模型属性", "requestBody": {"description": "属性信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateModelPropertyInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateModelPropertyInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateModelPropertyInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-property/{id}": {"delete": {"tags": ["ModelProperty"], "summary": "删除模型属性", "parameters": [{"name": "id", "in": "path", "description": "属性ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-property/batch-delete": {"post": {"tags": ["ModelProperty"], "summary": "批量删除模型属性", "requestBody": {"description": "属性ID集合", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-property/{modelId}/by-model-id": {"get": {"tags": ["ModelProperty"], "summary": "根据模型ID获取属性列表", "parameters": [{"name": "modelId", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}}}}}}, "delete": {"tags": ["ModelProperty"], "summary": "根据模型ID删除所有属性", "parameters": [{"name": "modelId", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-property/{modelId}/product-properties-by-model-id": {"get": {"tags": ["ModelProperty"], "summary": "根据模型ID获取属性列表 (向后兼容)", "parameters": [{"name": "modelId", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertyOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertyOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertyOutput"}}}}}}}}, "/api/v1/model-property/{modelId}/simple-by-model-id": {"get": {"tags": ["ModelProperty"], "summary": "根据模型ID获取属性简单列表", "parameters": [{"name": "modelId", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput"}}}}}}}}, "/api/v1/model-property/{modelId}/product-property-simple-by-model-id": {"get": {"tags": ["ModelProperty"], "summary": "根据模型ID获取属性简单列表 (向后兼容)", "parameters": [{"name": "modelId", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput"}}}}}}}}, "/api/v1/model-property/batch-add": {"post": {"tags": ["ModelProperty"], "summary": "批量添加模型属性", "requestBody": {"description": "批量添加信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/model-property/simple-list": {"get": {"tags": ["ModelProperty"], "summary": "获取属性简单列表 (用于下拉选择)", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput"}}}}}}}}, "/api/v1/model-property/product-property-simple-list": {"get": {"tags": ["ModelProperty"], "summary": "获取属性简单列表 (用于下拉选择) (向后兼容)", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput"}}}}}}}}, "/api/v1/model-property/{instructionId}/by-instruction-id": {"get": {"tags": ["ModelProperty"], "summary": "根据指令ID获取属性列表", "parameters": [{"name": "instructionId", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}}}}}}}, "delete": {"tags": ["ModelProperty"], "summary": "根据指令ID删除所有属性", "parameters": [{"name": "instructionId", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product/paged-list": {"get": {"tags": ["Product"], "summary": "获取产品分页列表", "parameters": [{"name": "ProductName", "in": "query", "description": "产品名称", "schema": {"type": "string"}}, {"name": "ProtocolType", "in": "query", "description": "协议类型", "schema": {"type": "integer", "format": "int32"}}, {"name": "ProductType", "in": "query", "description": "产品类型", "schema": {"type": "integer", "format": "int32"}}, {"name": "DataFormat", "in": "query", "description": "数据格式", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/product/{id}/by-id": {"get": {"tags": ["Product"], "summary": "根据ID获取产品详情", "parameters": [{"name": "id", "in": "path", "description": "产品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductOutput"}}}}}}}, "/api/v1/product": {"post": {"tags": ["Product"], "summary": "添加产品", "requestBody": {"description": "产品信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}, "put": {"tags": ["Product"], "summary": "更新产品\r\n允许修改ProductName、Description", "requestBody": {"description": "产品信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateProductInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateProductInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateProductInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product/{id}": {"delete": {"tags": ["Product"], "summary": "删除产品", "parameters": [{"name": "id", "in": "path", "description": "产品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product/batch-delete": {"post": {"tags": ["Product"], "summary": "批量删除产品", "requestBody": {"description": "产品ID集合", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product/simple-list": {"get": {"tags": ["Product"], "summary": "获取产品简单列表 (用于下拉选择)", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}}}}}}, "/api/v1/product/by-protocol-type": {"get": {"tags": ["Product"], "summary": "根据协议类型获取产品列表", "parameters": [{"name": "protocolType", "in": "query", "description": "协议类型", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}}}}}}, "/api/v1/product/by-product-type": {"get": {"tags": ["Product"], "summary": "根据产品类型获取产品列表", "parameters": [{"name": "productType", "in": "query", "description": "产品类型", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductSimpleOutput"}}}}}}}}, "/api/v1/product-model/paged-list": {"get": {"tags": ["ProductModel"], "summary": "获取产品模型分页列表", "parameters": [{"name": "ProductId", "in": "query", "description": "产品ID", "schema": {"type": "integer", "format": "int64"}}, {"name": "ModelName", "in": "query", "description": "模型名称", "schema": {"type": "string"}}, {"name": "DeviceGroup", "in": "query", "description": "设备组", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductModelOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductModelOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductModelOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/product-model/{id}/by-id": {"get": {"tags": ["ProductModel"], "summary": "根据产品ID获取产品模型详情", "parameters": [{"name": "id", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelOutput"}}}}}}}, "/api/v1/product-model/{id}/detail-by-id": {"get": {"tags": ["ProductModel"], "summary": "获取产品模型详情（包含属性信息）", "parameters": [{"name": "id", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelDetailOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelDetailOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelDetailOutput"}}}}}}}, "/api/v1/product-model": {"post": {"tags": ["ProductModel"], "summary": "添加产品模型", "requestBody": {"description": "模型信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}, "put": {"tags": ["ProductModel"], "summary": "更新产品模型\r\n只能修改ModelName、Description、IsEnabled", "requestBody": {"description": "模型信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateProductModelInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateProductModelInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.UpdateProductModelInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product-model/{id}": {"delete": {"tags": ["ProductModel"], "summary": "删除产品模型", "parameters": [{"name": "id", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product-model/batch-delete": {"post": {"tags": ["ProductModel"], "summary": "批量删除产品模型", "requestBody": {"description": "模型ID集合", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product-model/{productId}/by-product-id": {"get": {"tags": ["ProductModel"], "summary": "根据产品ID获取模型列表", "parameters": [{"name": "productId", "in": "path", "description": "产品ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}}}}}}, "/api/v1/product-model/by-device-group": {"get": {"tags": ["ProductModel"], "summary": "根据设备组获取模型列表", "parameters": [{"name": "deviceGroup", "in": "query", "description": "设备组", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}}}}}}, "/api/v1/product-model/{id}/set-enabled": {"post": {"tags": ["ProductModel"], "summary": "启用/禁用模型", "parameters": [{"name": "id", "in": "path", "description": "模型ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/product-model/simple-list": {"get": {"tags": ["ProductModel"], "summary": "获取模型简单列表 (用于下拉选择)", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelSimpleOutput"}}}}}}}}}, "components": {"schemas": {"Admin.Application.AlarmServices.AlarmEventSyncResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "是否成功"}, "totalEventCount": {"type": "integer", "description": "生成的事件总数", "format": "int32"}, "message": {"type": "string", "description": "结果消息", "nullable": true}, "syncTime": {"type": "string", "description": "同步时间"}}, "additionalProperties": false, "description": "告警事件同步结果"}, "Admin.Application.AlarmServices.Dto.AlarmHistoryOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "告警ID", "format": "int64"}, "eventId": {"type": "string", "description": "事件ID", "nullable": true}, "deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "alarmDescription": {"type": "string", "description": "告警描述", "nullable": true}, "alarmValue": {"type": "number", "description": "告警值", "format": "double"}, "alarmLevel": {"type": "integer", "description": "告警级别", "format": "int32"}, "alarmLevelDescription": {"type": "string", "description": "告警级别描述", "nullable": true}, "alarmStatus": {"type": "integer", "description": "告警状态", "format": "int32"}, "alarmStatusDescription": {"type": "string", "description": "告警状态描述", "nullable": true}, "alarmTime": {"type": "string", "description": "告警时间"}, "confirmedPeople": {"type": "string", "description": "告警确认人", "nullable": true}, "confirmedTime": {"type": "string", "description": "确认时间", "nullable": true}, "releaseTime": {"type": "string", "description": "解除时间", "nullable": true}, "releaseValue": {"type": "number", "description": "解除数值", "format": "double", "nullable": true}, "releaseDescription": {"type": "string", "description": "解除描述", "nullable": true}, "isReleased": {"type": "boolean", "description": "是否已解除", "readOnly": true}, "durationMinutes": {"type": "number", "description": "告警持续时间（分钟）", "format": "double", "nullable": true, "readOnly": true}}, "additionalProperties": false, "description": "告警历史输出"}, "Admin.Application.AlarmServices.Dto.ConfirmAlarmInput": {"type": "object", "properties": {"alarmIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "告警ID列表", "nullable": true}, "confirmedPeople": {"type": "string", "description": "确认人", "nullable": true}}, "additionalProperties": false, "description": "确认告警输入"}, "Admin.Application.ControlCmdServices.Dto.AddControlCmdInput": {"required": ["cmdContent", "cmdName", "communicationMedium", "deviceId", "encode"], "type": "object", "properties": {"cmdName": {"maxLength": 100, "minLength": 0, "type": "string", "description": "指令名称"}, "deviceId": {"minimum": 1, "type": "integer", "description": "设备ID", "format": "int64"}, "communicationMedium": {"maximum": 1, "minimum": 1, "type": "integer", "description": "通讯介质 (1:MQTT)", "format": "int32"}, "cmdContent": {"minLength": 1, "type": "string", "description": "指令内容"}, "encode": {"maximum": 2, "minimum": 1, "type": "integer", "description": "编码方式 (1:<PERSON><PERSON><PERSON> 2:HEX)", "format": "int32"}, "areaId": {"type": "integer", "description": "区域ID (可选)", "format": "int32", "nullable": true}, "deviceCategory": {"type": "integer", "description": "设备分类 (可选)", "format": "int32", "nullable": true}, "isAppControl": {"maximum": 1, "minimum": 0, "type": "integer", "description": "是否启用APP控制 (0:否 1:是)", "format": "int32"}}, "additionalProperties": false, "description": "添加控制指令输入DTO"}, "Admin.Application.ControlCmdServices.Dto.AddControlPlanInput": {"required": ["cmdIds", "name", "triggerType"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string", "description": "计划名称"}, "triggerType": {"maximum": 2, "minimum": 1, "type": "integer", "description": "触发方式 (1:重复执行 2:条件执行)", "format": "int32"}, "executeType": {"type": "integer", "description": "执行方式 (1:时间点 2:间隔) - 重复执行时必填", "format": "int32", "nullable": true}, "timePoint": {"type": "string", "description": "时间点 (HH:mm，多个用逗号分隔) - 执行方式为时间点时必填", "nullable": true}, "interval": {"type": "integer", "description": "间隔 - 执行方式为间隔时必填", "format": "int32", "nullable": true}, "intervalUnit": {"type": "integer", "description": "间隔单位 (1:分钟 2:小时) - 执行方式为间隔时必填", "format": "int32", "nullable": true}, "method": {"type": "integer", "description": "方法 (1:大于参考值 2:小于参考值 3:等于参考值) - 条件执行时必填", "format": "int32", "nullable": true}, "parameterId": {"type": "integer", "description": "关联参数ID - 条件执行时必填", "format": "int64", "nullable": true}, "referenceValue": {"type": "number", "description": "参考值 - 条件执行时必填", "format": "double", "nullable": true}, "cmdIds": {"minLength": 1, "type": "string", "description": "指令ID集 (逗号分隔)"}, "cmdInterval": {"maximum": 2147483647, "minimum": 100, "type": "integer", "description": "指令间隔 (单位：毫秒)", "format": "int32"}, "isAppControl": {"maximum": 1, "minimum": 0, "type": "integer", "description": "监控状态 (0:不启用 1:启用)", "format": "int32"}}, "additionalProperties": false, "description": "添加控制计划输入DTO"}, "Admin.Application.ControlCmdServices.Dto.ControlCmdOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "指令ID", "format": "int64"}, "cmdName": {"type": "string", "description": "指令名称", "nullable": true}, "deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "communicationMedium": {"type": "integer", "description": "通讯介质 (1:MQTT)", "format": "int32"}, "communicationMediumName": {"type": "string", "description": "通讯介质名称", "nullable": true}, "cmdContent": {"type": "string", "description": "指令内容", "nullable": true}, "encode": {"type": "integer", "description": "编码方式 (1:<PERSON><PERSON><PERSON> 2:HEX)", "format": "int32"}, "encodeName": {"type": "string", "description": "编码方式名称", "nullable": true}, "areaId": {"type": "integer", "description": "区域ID", "format": "int32", "nullable": true}, "deviceCategory": {"type": "integer", "description": "设备分类", "format": "int32", "nullable": true}, "isAppControl": {"type": "integer", "description": "是否启用APP控制 (0:否 1:是)", "format": "int32"}, "isAppControlName": {"type": "string", "description": "是否启用APP控制名称", "nullable": true}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "控制指令输出DTO"}, "Admin.Application.ControlCmdServices.Dto.ControlPlanOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "计划ID", "format": "int64"}, "name": {"type": "string", "description": "计划名称", "nullable": true}, "triggerType": {"type": "integer", "description": "触发方式 (1:重复执行 2:条件执行)", "format": "int32"}, "triggerTypeName": {"type": "string", "description": "触发方式名称", "nullable": true}, "executeType": {"type": "integer", "description": "执行方式 (1:时间点 2:间隔)", "format": "int32", "nullable": true}, "executeTypeName": {"type": "string", "description": "执行方式名称", "nullable": true}, "timePoint": {"type": "string", "description": "时间点 (HH:mm，多个用逗号分隔)", "nullable": true}, "interval": {"type": "integer", "description": "间隔", "format": "int32", "nullable": true}, "intervalUnit": {"type": "integer", "description": "间隔单位 (1:分钟 2:小时)", "format": "int32", "nullable": true}, "intervalUnitName": {"type": "string", "description": "间隔单位名称", "nullable": true}, "method": {"type": "integer", "description": "方法 (1:大于参考值 2:小于参考值 3:等于参考值)", "format": "int32", "nullable": true}, "methodName": {"type": "string", "description": "方法名称", "nullable": true}, "parameterId": {"type": "integer", "description": "关联参数ID", "format": "int64", "nullable": true}, "referenceValue": {"type": "number", "description": "参考值", "format": "double", "nullable": true}, "cmdIds": {"type": "string", "description": "指令ID集", "nullable": true}, "cmdInterval": {"type": "integer", "description": "指令间隔 (单位：毫秒)", "format": "int32"}, "isAppControl": {"type": "integer", "description": "监控状态 (0:不启用 1:启用)", "format": "int32"}, "isAppControlName": {"type": "string", "description": "监控状态名称", "nullable": true}}, "additionalProperties": false, "description": "控制计划输出DTO"}, "Admin.Application.ControlCmdServices.Dto.UpdateControlCmdInput": {"required": ["cmdContent", "cmdName", "communicationMedium", "deviceId", "encode", "id"], "type": "object", "properties": {"id": {"minimum": 1, "type": "integer", "description": "指令ID", "format": "int64"}, "cmdName": {"maxLength": 100, "minLength": 0, "type": "string", "description": "指令名称"}, "deviceId": {"minimum": 1, "type": "integer", "description": "设备ID", "format": "int64"}, "communicationMedium": {"maximum": 1, "minimum": 1, "type": "integer", "description": "通讯介质 (1:MQTT)", "format": "int32"}, "cmdContent": {"minLength": 1, "type": "string", "description": "指令内容"}, "encode": {"maximum": 2, "minimum": 1, "type": "integer", "description": "编码方式 (1:<PERSON><PERSON><PERSON> 2:HEX)", "format": "int32"}, "areaId": {"type": "integer", "description": "区域ID (可选)", "format": "int32", "nullable": true}, "deviceCategory": {"type": "integer", "description": "设备分类 (可选)", "format": "int32", "nullable": true}, "isAppControl": {"maximum": 1, "minimum": 0, "type": "integer", "description": "是否启用APP控制 (0:否 1:是)", "format": "int32"}}, "additionalProperties": false, "description": "更新控制指令输入DTO"}, "Admin.Application.ControlCmdServices.Dto.UpdateControlPlanInput": {"required": ["cmdIds", "id", "name", "triggerType"], "type": "object", "properties": {"id": {"minimum": 1, "type": "integer", "description": "计划ID", "format": "int64"}, "name": {"maxLength": 100, "minLength": 0, "type": "string", "description": "计划名称"}, "triggerType": {"maximum": 2, "minimum": 1, "type": "integer", "description": "触发方式 (1:重复执行 2:条件执行)", "format": "int32"}, "executeType": {"type": "integer", "description": "执行方式 (1:时间点 2:间隔) - 重复执行时必填", "format": "int32", "nullable": true}, "timePoint": {"type": "string", "description": "时间点 (HH:mm，多个用逗号分隔) - 执行方式为时间点时必填", "nullable": true}, "interval": {"type": "integer", "description": "间隔 - 执行方式为间隔时必填", "format": "int32", "nullable": true}, "intervalUnit": {"type": "integer", "description": "间隔单位 (1:分钟 2:小时) - 执行方式为间隔时必填", "format": "int32", "nullable": true}, "method": {"type": "integer", "description": "方法 (1:大于参考值 2:小于参考值 3:等于参考值) - 条件执行时必填", "format": "int32", "nullable": true}, "parameterId": {"type": "integer", "description": "关联参数ID - 条件执行时必填", "format": "int64", "nullable": true}, "referenceValue": {"type": "number", "description": "参考值 - 条件执行时必填", "format": "double", "nullable": true}, "cmdIds": {"minLength": 1, "type": "string", "description": "指令ID集 (逗号分隔)"}, "cmdInterval": {"maximum": 2147483647, "minimum": 100, "type": "integer", "description": "指令间隔 (单位：毫秒)", "format": "int32"}, "isAppControl": {"maximum": 1, "minimum": 0, "type": "integer", "description": "监控状态 (0:不启用 1:启用)", "format": "int32"}}, "additionalProperties": false, "description": "更新控制计划输入DTO"}, "Admin.Application.DeviceServices.Dto.AddDeviceInput": {"required": ["deviceName", "modelId"], "type": "object", "properties": {"modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "parentId": {"type": "integer", "description": "父设备ID (0表示无父设备)", "format": "int64"}, "groupKey": {"maxLength": 50, "type": "string", "description": "分组标识 (网关类型产品使用，对应JSON中的分组key，如\"IO\"、\"wenshidu\")\r\n直连设备此字段为空", "nullable": true}, "deviceId": {"pattern": "^[a-zA-Z0-9_-]{4,128}$", "type": "string", "description": "设备ID，用于唯一标识一个设备。如果填写该参数，平台将设备ID设置为该参数值；\r\n设备ID长度为4至128个字符，只允许字母、数字、下划线（_）、连接符（-）的组合。\r\n如果不填写该参数，设备ID由物联网平台分配获得，生成规则为guid + _ + IdentityCode拼接而成。\r\n网关子设备为空字符串", "nullable": true}, "deviceIdentityCode": {"pattern": "^[a-zA-Z0-9_-]{4,64}$", "type": "string", "description": "设备标识码 (通常使用IMEI、MAC地址或Serial No作为设备标识码)\r\n设备标识码长度为4至64个字符，只允许字母、数字、下划线（_）、连接符（-）的组合\r\n网关子设备为空字符串", "nullable": true}, "deviceName": {"maxLength": 100, "minLength": 1, "type": "string", "description": "设备名称"}, "deviceDescription": {"maxLength": 500, "type": "string", "description": "设备描述", "nullable": true}, "ipAddress": {"pattern": "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", "type": "string", "description": "设备IP地址", "nullable": true}, "modbusAddr": {"maximum": 247, "minimum": 1, "type": "integer", "description": "设备串口地址 (Modbus设备使用)", "format": "int32"}}, "additionalProperties": false, "description": "添加设备输入DTO"}, "Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput": {"required": ["deviceId", "instructionName", "sendStr"], "type": "object", "properties": {"deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "instructionName": {"maxLength": 100, "minLength": 1, "type": "string", "description": "指令名称"}, "sendStr": {"maxLength": 500, "minLength": 1, "type": "string", "description": "发送指令 (例如 010300000002C40B)"}, "encode": {"maximum": 2, "minimum": 1, "type": "integer", "description": "编码 (1:HEX 2:ASCII)", "format": "int32"}, "responseTime": {"maximum": 60000, "minimum": 100, "type": "integer", "description": "响应时间(毫秒)", "format": "int32"}, "retryCount": {"maximum": 10, "minimum": 0, "type": "integer", "description": "重试次数", "format": "int32"}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "添加设备指令输入DTO"}, "Admin.Application.DeviceServices.Dto.AddDeviceParaInput": {"required": ["dataType", "deviceId", "instructionId", "key", "name"], "type": "object", "properties": {"deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "instructionId": {"type": "integer", "description": "指令ID", "format": "int64"}, "name": {"maxLength": 100, "minLength": 1, "type": "string", "description": "参数名称"}, "sort": {"maximum": 2147483647, "minimum": 0, "type": "integer", "description": "参数序号", "format": "int32"}, "key": {"maxLength": 200, "minLength": 1, "type": "string", "description": "参数标识符 (JSON中的路径，支持多层嵌套，如: \"IO.input.channel1\" 或 \"sensors.temperature\")"}, "dataType": {"maximum": 5, "minimum": 1, "type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "enumDescription": {"maxLength": 500, "type": "string", "description": "枚举说明", "nullable": true}, "unit": {"maxLength": 50, "type": "string", "description": "单位", "nullable": true}, "divisionFactor": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "倍率", "format": "int32"}, "decimalPlaces": {"maximum": 10, "minimum": 0, "type": "integer", "description": "小数位数", "format": "int32"}, "correctionScale": {"maximum": 1000, "minimum": 0.001, "type": "number", "description": "校正比例（默认为1）", "format": "double"}, "correctionAmplitude": {"type": "number", "description": "校正幅度（默认为0）", "format": "double"}, "alarmUpperLimit": {"type": "number", "description": "报警上限", "format": "double"}, "alarmUpperLimitClearValue": {"type": "number", "description": "报警上限解除值", "format": "double"}, "alarmLowerLimit": {"type": "number", "description": "报警下限", "format": "double"}, "alarmLowerLimitClearValue": {"type": "number", "description": "报警下限解除值", "format": "double"}, "warningUpperLimit": {"type": "number", "description": "预警上限", "format": "double"}, "warningUpperLimitClearValue": {"type": "number", "description": "预警上限解除值", "format": "double"}, "warningLowerLimit": {"type": "number", "description": "预警下限", "format": "double"}, "warningLowerLimitClearValue": {"type": "number", "description": "预警下限解除值", "format": "double"}, "isSave": {"type": "boolean", "description": "是否保存"}, "saveAmplitude": {"minimum": 0, "type": "number", "description": "保存幅度", "format": "double"}, "saveAmplitudeType": {"maximum": 1, "minimum": 0, "type": "integer", "description": "保存幅度类型（0: 数值 1：百分比）", "format": "int32"}, "saveInterval": {"maximum": 2147483647, "minimum": 1000, "type": "integer", "description": "保存间隔(毫秒)", "format": "int32"}, "monitorStatus": {"maximum": 1, "minimum": 0, "type": "integer", "description": "监控状态（0: 不启用 1：启用）", "format": "int32"}, "description": {"maxLength": 500, "type": "string", "description": "参数描述", "nullable": true}}, "additionalProperties": false, "description": "添加设备参数输入DTO"}, "Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput": {"required": ["deviceId", "templateName", "topic"], "type": "object", "properties": {"deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "设备ID"}, "templateName": {"maxLength": 100, "minLength": 1, "type": "string", "description": "模板名称"}, "topic": {"maxLength": 500, "minLength": 1, "type": "string", "description": "主题"}, "accessType": {"maximum": 2, "minimum": 0, "type": "integer", "description": "访问类型(0=All, 1=Publish, 2=Subscribe)", "format": "int32"}, "dataFormat": {"maximum": 2, "minimum": 1, "type": "integer", "description": "Payload编码 (1:JSON 2:HEX)", "format": "int32"}, "priority": {"maximum": 999, "minimum": 1, "type": "integer", "description": "优先级", "format": "int32"}, "description": {"maxLength": 500, "type": "string", "description": "模板描述", "nullable": true}}, "additionalProperties": false, "description": "添加设备主题配置输入DTO"}, "Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput": {"required": ["parameterIds"], "type": "object", "properties": {"parameterIds": {"minItems": 1, "type": "array", "items": {"type": "integer", "format": "int64"}, "description": "参数ID列表"}}, "additionalProperties": false, "description": "批量删除设备参数输入DTO"}, "Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID", "format": "int64"}, "deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "propertyName": {"type": "string", "description": "属性名称", "nullable": true}, "propertyValue": {"type": "string", "description": "属性值", "nullable": true}, "dataTime": {"type": "string", "description": "数据时间戳"}}, "additionalProperties": false, "description": "设备历史数据输出"}, "Admin.Application.DeviceServices.Dto.DeviceInstructionOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "指令ID", "format": "int64"}, "deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "instructionName": {"type": "string", "description": "指令名称", "nullable": true}, "sendStr": {"type": "string", "description": "发送指令", "nullable": true}, "encode": {"type": "integer", "description": "编码 (1:HEX 2:ASCII)", "format": "int32"}, "responseTime": {"type": "integer", "description": "响应时间(毫秒)", "format": "int32"}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32"}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间"}, "updateBy": {"type": "integer", "description": "修改人", "format": "int64", "nullable": true}, "updateTime": {"type": "string", "description": "修改时间", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "设备指令输出DTO"}, "Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "指令ID", "format": "int64"}, "instructionName": {"type": "string", "description": "指令名称", "nullable": true}, "sendStr": {"type": "string", "description": "发送指令", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "设备指令简单输出DTO (用于下拉选择等场景)"}, "Admin.Application.DeviceServices.Dto.DeviceOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID", "format": "int64"}, "productId": {"type": "integer", "description": "产品ID", "format": "int64"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "protocolType": {"type": "integer", "description": "设备协议 (1:MQTT 2:Modbus)", "format": "int32"}, "dataFormat": {"type": "integer", "description": "设备数据格式 (1:<PERSON><PERSON><PERSON> 2:二进制码流)", "format": "int32"}, "deviceType": {"type": "integer", "description": "设备属性 (1:直连设备 2:网关设备 3:网关子设备)", "format": "int32"}, "parentId": {"type": "integer", "description": "父设备ID", "format": "int64"}, "groupKey": {"type": "string", "description": "分组标识", "nullable": true}, "deviceId": {"type": "string", "description": "设备ID，用于唯一标识一个设备\r\n网关子设备为空字符串", "nullable": true}, "deviceIdentityCode": {"type": "string", "description": "设备标识码 (网关子设备为空字符串)", "nullable": true}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "deviceDescription": {"type": "string", "description": "设备描述", "nullable": true}, "status": {"type": "integer", "description": "设备状态 (0:离线 1:在线)", "format": "int32"}, "ipAddress": {"type": "string", "description": "设备IP地址", "nullable": true}, "modbusAddr": {"type": "integer", "description": "设备串口地址 (Modbus设备使用)", "format": "int32"}, "deviceSecret": {"type": "string", "description": "设备密钥 (网关子设备为null)", "nullable": true}, "lastDataTime": {"type": "string", "description": "最后数据上报时间", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间"}, "updateBy": {"type": "integer", "description": "修改人", "format": "int64", "nullable": true}, "updateTime": {"type": "string", "description": "修改时间", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "设备输出DTO"}, "Admin.Application.DeviceServices.Dto.DeviceParaOutput": {"type": "object", "properties": {"parameterId": {"type": "integer", "description": "参数ID", "format": "int64"}, "deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "instructionId": {"type": "integer", "description": "指令ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "sort": {"type": "integer", "description": "参数序号", "format": "int32"}, "key": {"type": "string", "description": "参数标识符 (JSON中的路径，支持多层嵌套，如: \"IO.input.channel1\" 或 \"sensors.temperature\")", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "enumDescription": {"type": "string", "description": "枚举说明", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "divisionFactor": {"type": "integer", "description": "倍率", "format": "int32"}, "decimalPlaces": {"type": "integer", "description": "小数位数", "format": "int32"}, "correctionScale": {"type": "number", "description": "校正比例", "format": "double"}, "correctionAmplitude": {"type": "number", "description": "校正幅度", "format": "double"}, "alarmUpperLimit": {"type": "number", "description": "报警上限", "format": "double"}, "alarmUpperLimitClearValue": {"type": "number", "description": "报警上限解除值", "format": "double"}, "alarmLowerLimit": {"type": "number", "description": "报警下限", "format": "double"}, "alarmLowerLimitClearValue": {"type": "number", "description": "报警下限解除值", "format": "double"}, "warningUpperLimit": {"type": "number", "description": "预警上限", "format": "double"}, "warningUpperLimitClearValue": {"type": "number", "description": "预警上限解除值", "format": "double"}, "warningLowerLimit": {"type": "number", "description": "预警下限", "format": "double"}, "warningLowerLimitClearValue": {"type": "number", "description": "预警下限解除值", "format": "double"}, "isSave": {"type": "boolean", "description": "是否保存"}, "saveAmplitude": {"type": "number", "description": "保存幅度", "format": "double"}, "saveAmplitudeType": {"type": "integer", "description": "保存幅度类型（0: 数值 1：百分比）", "format": "int32"}, "saveInterval": {"type": "integer", "description": "保存间隔(毫秒)", "format": "int32"}, "monitorStatus": {"type": "integer", "description": "监控状态（0: 不启用 1：启用）", "format": "int32"}, "description": {"type": "string", "description": "参数描述", "nullable": true}}, "additionalProperties": false, "description": "设备参数输出DTO"}, "Admin.Application.DeviceServices.Dto.DeviceParaQueryInput": {"type": "object", "properties": {"pageIndex": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "pageSize": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}, "deviceId": {"type": "integer", "description": "设备ID", "format": "int64", "nullable": true}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32", "nullable": true}, "monitorStatus": {"type": "integer", "description": "监控状态（0: 不启用 1：启用）", "format": "int32", "nullable": true}, "isSave": {"type": "boolean", "description": "是否保存", "nullable": true}}, "additionalProperties": false, "description": "设备参数查询输入DTO"}, "Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput": {"type": "object", "properties": {"parameterId": {"type": "integer", "description": "参数ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "key": {"type": "string", "description": "参数标识符", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "unit": {"type": "string", "description": "单位", "nullable": true}, "monitorStatus": {"type": "integer", "description": "监控状态（0: 不启用 1：启用）", "format": "int32"}}, "additionalProperties": false, "description": "设备参数简单输出DTO（用于下拉选择等场景）"}, "Admin.Application.DeviceServices.Dto.DeviceQueryInput": {"type": "object", "properties": {"pageIndex": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "pageSize": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "status": {"type": "integer", "description": "设备状态 (0:离线 1:在线)", "format": "int32", "nullable": true}, "productId": {"type": "integer", "description": "产品ID", "format": "int64", "nullable": true}, "parentId": {"type": "integer", "description": "父设备ID", "format": "int64", "nullable": true}, "groupKey": {"type": "string", "description": "分组标识", "nullable": true}, "deviceIdentityCode": {"type": "string", "description": "设备标识码", "nullable": true}, "protocolType": {"type": "integer", "description": "设备协议类型", "format": "int32", "nullable": true}, "dataFormat": {"type": "integer", "description": "设备数据格式", "format": "int32", "nullable": true}, "deviceType": {"type": "integer", "description": "设备属性类型", "format": "int32", "nullable": true}}, "additionalProperties": false, "description": "设备查询输入DTO"}, "Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput": {"type": "object", "properties": {"deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "deviceCode": {"type": "string", "description": "设备编码", "nullable": true}, "deviceStatus": {"type": "integer", "description": "设备状态 (0:离线 1:在线)", "format": "int32"}, "lastDataTime": {"type": "string", "description": "最后数据时间", "nullable": true}, "isOnline": {"type": "boolean", "description": "是否在线"}, "offlineMinutes": {"type": "integer", "description": "离线时长（分钟）", "format": "int32"}, "dataJson": {"type": "string", "description": "设备数据JSON", "nullable": true}}, "additionalProperties": false, "description": "设备状态概览输出"}, "Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "配置ID", "format": "int64"}, "deviceId": {"type": "string", "description": "设备ID", "nullable": true}, "templateName": {"type": "string", "description": "模板名称", "nullable": true}, "topic": {"type": "string", "description": "主题", "nullable": true}, "accessType": {"type": "integer", "description": "访问类型(0=All, 1=Publish, 2=Subscribe)", "format": "int32"}, "accessTypeDesc": {"type": "string", "description": "访问类型描述", "nullable": true, "readOnly": true}, "dataFormat": {"type": "integer", "description": "Payload编码 (1:JSON 2:HEX)", "format": "int32"}, "dataFormatDesc": {"type": "string", "description": "数据格式描述", "nullable": true, "readOnly": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "description": {"type": "string", "description": "模板描述", "nullable": true}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间"}, "updateBy": {"type": "integer", "description": "修改人", "format": "int64", "nullable": true}, "updateTime": {"type": "string", "description": "修改时间", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "设备主题配置输出DTO"}, "Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput": {"required": ["monitorStatus", "parameterIds"], "type": "object", "properties": {"parameterIds": {"minItems": 1, "type": "array", "items": {"type": "integer", "format": "int64"}, "description": "参数ID列表"}, "monitorStatus": {"maximum": 1, "minimum": 0, "type": "integer", "description": "监控状态（0: 不启用 1：启用）", "format": "int32"}}, "additionalProperties": false, "description": "设置设备参数监控状态输入DTO"}, "Admin.Application.DeviceServices.Dto.UpdateDeviceInput": {"required": ["dataFormat", "deviceName", "deviceType", "modelId", "productId", "protocolType"], "type": "object", "properties": {"productId": {"type": "integer", "description": "产品ID", "format": "int64"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "protocolType": {"maximum": 2, "minimum": 1, "type": "integer", "description": "设备协议 (1:MQTT 2:Modbus)", "format": "int32"}, "dataFormat": {"maximum": 2, "minimum": 1, "type": "integer", "description": "设备数据格式 (1:J<PERSON>N 2:HEX)", "format": "int32"}, "deviceType": {"maximum": 3, "minimum": 1, "type": "integer", "description": "设备属性 (1:直连设备 2:网关设备 3:网关子设备)", "format": "int32"}, "parentId": {"type": "integer", "description": "父设备ID (0表示无父设备)", "format": "int64"}, "groupKey": {"maxLength": 50, "type": "string", "description": "分组标识 (网关类型产品使用，对应JSON中的分组key，如\"IO\"、\"wenshidu\")\r\n直连设备此字段为空", "nullable": true}, "deviceId": {"pattern": "^[a-zA-Z0-9_-]{4,128}$", "type": "string", "description": "设备ID，用于唯一标识一个设备。\r\n设备ID长度为4至128个字符，只允许字母、数字、下划线（_）、连接符（-）的组合。\r\n网关子设备为空字符串", "nullable": true}, "deviceIdentityCode": {"pattern": "^[a-zA-Z0-9_-]{4,64}$", "type": "string", "description": "设备标识码 (通常使用IMEI、MAC地址或Serial No作为设备标识码)\r\n设备标识码长度为4至64个字符，只允许字母、数字、下划线（_）、连接符（-）的组合\r\n网关子设备为空字符串", "nullable": true}, "deviceName": {"maxLength": 100, "minLength": 1, "type": "string", "description": "设备名称"}, "deviceDescription": {"maxLength": 500, "type": "string", "description": "设备描述", "nullable": true}, "ipAddress": {"pattern": "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", "type": "string", "description": "设备IP地址", "nullable": true}, "modbusAddr": {"maximum": 247, "minimum": 1, "type": "integer", "description": "设备串口地址 (Modbus设备使用)", "format": "int32"}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "更新设备输入DTO"}, "Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput": {"required": ["deviceId", "instructionName", "sendStr"], "type": "object", "properties": {"deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "instructionName": {"maxLength": 100, "minLength": 1, "type": "string", "description": "指令名称"}, "sendStr": {"maxLength": 500, "minLength": 1, "type": "string", "description": "发送指令 (例如 010300000002C40B)"}, "encode": {"maximum": 2, "minimum": 1, "type": "integer", "description": "编码 (1:HEX 2:ASCII)", "format": "int32"}, "responseTime": {"maximum": 60000, "minimum": 100, "type": "integer", "description": "响应时间(毫秒)", "format": "int32"}, "retryCount": {"maximum": 10, "minimum": 0, "type": "integer", "description": "重试次数", "format": "int32"}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "remark": {"maxLength": 500, "type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "更新设备指令输入DTO"}, "Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput": {"required": ["dataType", "deviceId", "instructionId", "key", "name", "parameterId"], "type": "object", "properties": {"parameterId": {"type": "integer", "description": "参数ID", "format": "int64"}, "deviceId": {"type": "integer", "description": "设备ID", "format": "int64"}, "instructionId": {"type": "integer", "description": "指令ID", "format": "int64"}, "name": {"maxLength": 100, "minLength": 1, "type": "string", "description": "参数名称"}, "sort": {"maximum": 2147483647, "minimum": 0, "type": "integer", "description": "参数序号", "format": "int32"}, "key": {"maxLength": 200, "minLength": 1, "type": "string", "description": "参数标识符 (JSON中的路径，支持多层嵌套，如: \"IO.input.channel1\" 或 \"sensors.temperature\")"}, "dataType": {"maximum": 5, "minimum": 1, "type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "enumDescription": {"maxLength": 500, "type": "string", "description": "枚举说明", "nullable": true}, "unit": {"maxLength": 50, "type": "string", "description": "单位", "nullable": true}, "divisionFactor": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "倍率", "format": "int32"}, "decimalPlaces": {"maximum": 10, "minimum": 0, "type": "integer", "description": "小数位数", "format": "int32"}, "correctionScale": {"maximum": 1000, "minimum": 0.001, "type": "number", "description": "校正比例（默认为1）", "format": "double"}, "correctionAmplitude": {"type": "number", "description": "校正幅度（默认为0）", "format": "double"}, "alarmUpperLimit": {"type": "number", "description": "报警上限", "format": "double"}, "alarmUpperLimitClearValue": {"type": "number", "description": "报警上限解除值", "format": "double"}, "alarmLowerLimit": {"type": "number", "description": "报警下限", "format": "double"}, "alarmLowerLimitClearValue": {"type": "number", "description": "报警下限解除值", "format": "double"}, "warningUpperLimit": {"type": "number", "description": "预警上限", "format": "double"}, "warningUpperLimitClearValue": {"type": "number", "description": "预警上限解除值", "format": "double"}, "warningLowerLimit": {"type": "number", "description": "预警下限", "format": "double"}, "warningLowerLimitClearValue": {"type": "number", "description": "预警下限解除值", "format": "double"}, "isSave": {"type": "boolean", "description": "是否保存"}, "saveAmplitude": {"minimum": 0, "type": "number", "description": "保存幅度", "format": "double"}, "saveAmplitudeType": {"maximum": 1, "minimum": 0, "type": "integer", "description": "保存幅度类型（0: 数值 1：百分比）", "format": "int32"}, "saveInterval": {"maximum": 2147483647, "minimum": 1000, "type": "integer", "description": "保存间隔(毫秒)", "format": "int32"}, "monitorStatus": {"maximum": 1, "minimum": 0, "type": "integer", "description": "监控状态（0: 不启用 1：启用）", "format": "int32"}, "description": {"maxLength": 500, "type": "string", "description": "参数描述", "nullable": true}}, "additionalProperties": false, "description": "更新设备参数输入DTO"}, "Admin.Application.ModbusServices.DeviceInstructionTestOutput": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "instructionName": {"type": "string", "nullable": true}, "command": {"type": "string", "nullable": true}, "readInterval": {"type": "integer", "format": "int32"}, "responseTime": {"type": "integer", "format": "int32"}, "retryCount": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}}, "additionalProperties": false, "description": "设备指令测试输出"}, "Admin.Application.ModbusServices.ModbusCommandInput": {"type": "object", "properties": {"slaveAddress": {"type": "integer", "format": "int32"}, "functionCode": {"type": "integer", "format": "int32"}, "startAddress": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "description": "Modbus指令输入"}, "Admin.Application.ModbusServices.ModbusCommandResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "command": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Modbus指令生成结果"}, "Admin.Application.ModbusServices.ModbusResponseInput": {"type": "object", "properties": {"deviceId": {"type": "integer", "format": "int64"}, "responseData": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Modbus响应输入"}, "Admin.Application.ModbusServices.ModbusTestInput": {"type": "object", "properties": {"deviceId": {"type": "integer", "format": "int64"}, "command": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Modbus测试输入"}, "Admin.Application.ModbusServices.ModbusTestResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "topic": {"type": "string", "nullable": true}, "command": {"type": "string", "nullable": true}, "sendTime": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Modbus测试结果"}, "Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput": {"type": "object", "properties": {"modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyInput"}, "description": "属性列表", "nullable": true}}, "additionalProperties": false, "description": "批量添加模型属性输入DTO"}, "Admin.Application.ProductServices.Dto.ModelInstructionInput": {"type": "object", "properties": {"modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "instructionName": {"type": "string", "description": "指令名称", "nullable": true}, "functionCode": {"type": "integer", "description": "功能码", "format": "int32"}, "startAddress": {"type": "integer", "description": "起始地址", "format": "int32"}, "readCount": {"type": "integer", "description": "读取数量", "format": "int32"}, "encode": {"type": "integer", "description": "编码 (1:HEX 2:ASCII)", "format": "int32"}, "responseTime": {"type": "integer", "description": "响应时间(毫秒)", "format": "int32"}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32"}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "模型指令输入DTO"}, "Admin.Application.ProductServices.Dto.ModelInstructionOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "指令ID", "format": "int64"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "modelName": {"type": "string", "description": "模型名称", "nullable": true}, "instructionName": {"type": "string", "description": "指令名称", "nullable": true}, "functionCode": {"type": "integer", "description": "功能码", "format": "int32"}, "functionCodeName": {"type": "string", "description": "功能码名称", "nullable": true}, "startAddress": {"type": "integer", "description": "起始地址", "format": "int32"}, "readCount": {"type": "integer", "description": "读取数量", "format": "int32"}, "encode": {"type": "integer", "description": "编码", "format": "int32"}, "encodeName": {"type": "string", "description": "编码名称", "nullable": true}, "responseTime": {"type": "integer", "description": "响应时间(毫秒)", "format": "int32"}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32"}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间", "nullable": true}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "updateBy": {"type": "integer", "description": "更新人", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "模型指令输出DTO"}, "Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "指令ID", "format": "int64"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "instructionName": {"type": "string", "description": "指令名称", "nullable": true}, "functionCode": {"type": "integer", "description": "功能码", "format": "int32"}, "functionCodeName": {"type": "string", "description": "功能码名称", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "模型指令简单输出DTO (用于下拉选择等场景)"}, "Admin.Application.ProductServices.Dto.ModelPropertyInput": {"type": "object", "properties": {"modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "instructionId": {"type": "integer", "description": "指令ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "sort": {"type": "integer", "description": "参数序号", "format": "int32"}, "key": {"type": "string", "description": "参数标识符 (JSON中的路径，支持多层嵌套，如: \"IO.input.channel1\" 或 \"sensors.temperature\")", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "enumDescription": {"type": "string", "description": "枚举说明\r\n示例: 0=运行;1=停止\r\n0=浮冲;1=均冲;2=放电", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "divisionFactor": {"type": "integer", "description": "倍率", "format": "int32"}, "decimalPlaces": {"type": "integer", "description": "小数位数", "format": "int32"}, "correctionScale": {"type": "number", "description": "校正比例\r\n解析值*校正比例+校正幅度，默认为1", "format": "double"}, "correctionAmplitude": {"type": "number", "description": "校正幅度\r\n默认为0", "format": "double"}, "alarmUpperLimit": {"type": "number", "description": "报警上限", "format": "double"}, "alarmUpperLimitClearValue": {"type": "number", "description": "报警上限解除值", "format": "double"}, "alarmLowerLimit": {"type": "number", "description": "报警下限", "format": "double"}, "alarmLowerLimitClearValue": {"type": "number", "description": "报警下限解除值", "format": "double"}, "warningUpperLimit": {"type": "number", "description": "预警上限", "format": "double"}, "warningUpperLimitClearValue": {"type": "number", "description": "预警上限解除值", "format": "double"}, "warningLowerLimit": {"type": "number", "description": "预警下限", "format": "double"}, "warningLowerLimitClearValue": {"type": "number", "description": "预警下限解除值", "format": "double"}, "isSave": {"type": "boolean", "description": "是否保存"}, "saveAmplitude": {"type": "number", "description": "保存幅度\r\n与上次保存值的偏差超过幅度，触发保存", "format": "double"}, "saveAmplitudeType": {"type": "integer", "description": "保存幅度类型\r\n0: 数值 1：百分比\r\n默认数值", "format": "int32"}, "saveInterval": {"type": "integer", "description": "保存间隔(毫秒)", "format": "int32"}, "monitorStatus": {"type": "integer", "description": "监控状态\r\n0: 不启用 1：启用", "format": "int32"}, "description": {"type": "string", "description": "参数描述", "nullable": true}}, "additionalProperties": false, "description": "模型属性输入DTO"}, "Admin.Application.ProductServices.Dto.ModelPropertyOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "属性ID", "format": "int64"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "instructionId": {"type": "integer", "description": "指令ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "sort": {"type": "integer", "description": "参数序号", "format": "int32"}, "key": {"type": "string", "description": "参数标识符 (JSON中的路径，支持多层嵌套，如: \"IO.input.channel1\" 或 \"sensors.temperature\")", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "dataTypeName": {"type": "string", "description": "数据类型名称", "nullable": true}, "enumDescription": {"type": "string", "description": "枚举说明\r\n示例: 0=运行;1=停止\r\n0=浮冲;1=均冲;2=放电", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "divisionFactor": {"type": "integer", "description": "倍率", "format": "int32"}, "decimalPlaces": {"type": "integer", "description": "小数位数", "format": "int32"}, "correctionScale": {"type": "number", "description": "校正比例\r\n解析值*校正比例+校正幅度，默认为1", "format": "double"}, "correctionAmplitude": {"type": "number", "description": "校正幅度\r\n默认为0", "format": "double"}, "alarmUpperLimit": {"type": "number", "description": "报警上限", "format": "double"}, "alarmUpperLimitClearValue": {"type": "number", "description": "报警上限解除值", "format": "double"}, "alarmLowerLimit": {"type": "number", "description": "报警下限", "format": "double"}, "alarmLowerLimitClearValue": {"type": "number", "description": "报警下限解除值", "format": "double"}, "warningUpperLimit": {"type": "number", "description": "预警上限", "format": "double"}, "warningUpperLimitClearValue": {"type": "number", "description": "预警上限解除值", "format": "double"}, "warningLowerLimit": {"type": "number", "description": "预警下限", "format": "double"}, "warningLowerLimitClearValue": {"type": "number", "description": "预警下限解除值", "format": "double"}, "isSave": {"type": "boolean", "description": "是否保存"}, "saveAmplitude": {"type": "number", "description": "保存幅度\r\n与上次保存值的偏差超过幅度，触发保存", "format": "double"}, "saveAmplitudeType": {"type": "integer", "description": "保存幅度类型\r\n0: 数值 1：百分比\r\n默认数值", "format": "int32"}, "saveInterval": {"type": "integer", "description": "保存间隔(毫秒)", "format": "int32"}, "monitorStatus": {"type": "integer", "description": "监控状态\r\n0: 不启用 1：启用", "format": "int32"}, "description": {"type": "string", "description": "参数描述", "nullable": true}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间", "nullable": true}, "createBy": {"type": "string", "description": "创建人", "nullable": true}, "updateBy": {"type": "string", "description": "更新人", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "模型属性输出DTO"}, "Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "属性ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "key": {"type": "string", "description": "参数标识符", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型", "format": "int32"}, "dataTypeName": {"type": "string", "description": "数据类型名称", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}}, "additionalProperties": false, "description": "模型属性简单输出DTO (用于下拉选择等场景)"}, "Admin.Application.ProductServices.Dto.ProductInput": {"type": "object", "properties": {"productName": {"type": "string", "description": "产品名称", "nullable": true}, "protocolType": {"type": "integer", "description": "协议类型 (1:MQTT 2:Modbus)", "format": "int32"}, "productType": {"type": "integer", "description": "产品类型 (1:直连设备 2:网关设备 3:网关子设备)", "format": "int32"}, "dataFormat": {"type": "integer", "description": "数据格式 (1:J<PERSON>N 2:HEX)", "format": "int32"}, "description": {"type": "string", "description": "产品描述", "nullable": true}}, "additionalProperties": false, "description": "产品输入DTO"}, "Admin.Application.ProductServices.Dto.ProductModelDetailOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "模型ID", "format": "int64"}, "productId": {"type": "integer", "description": "产品ID", "format": "int64"}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "modelName": {"type": "string", "description": "模型名称", "nullable": true}, "deviceGroup": {"type": "integer", "description": "设备组", "format": "int32"}, "deviceGroupName": {"type": "string", "description": "设备组名称", "nullable": true}, "description": {"type": "string", "description": "模型描述", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductPropertyOutput"}, "description": "属性列表", "nullable": true}}, "additionalProperties": false, "description": "产品模型详情输出DTO (包含属性信息)"}, "Admin.Application.ProductServices.Dto.ProductModelInput": {"type": "object", "properties": {"productId": {"type": "integer", "description": "产品ID", "format": "int64"}, "modelName": {"type": "string", "description": "模型名称", "nullable": true}, "deviceGroup": {"type": "integer", "description": "设备组 (1:温湿度 2:漏水检测 3:空调 4:UPS 5:配电 6:开关 7:发电机 8:红外 9:门禁 10:传感器 11.冷通道)", "format": "int32"}, "description": {"type": "string", "description": "模型描述", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "产品模型输入DTO"}, "Admin.Application.ProductServices.Dto.ProductModelOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "模型ID", "format": "int64"}, "productId": {"type": "integer", "description": "产品ID", "format": "int64"}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "modelName": {"type": "string", "description": "模型名称", "nullable": true}, "deviceGroup": {"type": "integer", "description": "设备组", "format": "int32"}, "deviceGroupName": {"type": "string", "description": "设备组名称", "nullable": true}, "description": {"type": "string", "description": "模型描述", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "产品模型输出DTO"}, "Admin.Application.ProductServices.Dto.ProductModelSimpleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "模型ID", "format": "int64"}, "productId": {"type": "integer", "description": "产品ID", "format": "int64"}, "modelName": {"type": "string", "description": "模型名称", "nullable": true}, "deviceGroup": {"type": "integer", "description": "设备组", "format": "int32"}, "deviceGroupName": {"type": "string", "description": "设备组名称", "nullable": true}}, "additionalProperties": false, "description": "产品模型简单输出DTO (用于下拉选择等场景)"}, "Admin.Application.ProductServices.Dto.ProductOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID", "format": "int64"}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "protocolType": {"type": "integer", "description": "协议类型", "format": "int32"}, "protocolTypeName": {"type": "string", "description": "协议类型名称", "nullable": true}, "productType": {"type": "integer", "description": "产品类型", "format": "int32"}, "productTypeName": {"type": "string", "description": "产品类型名称", "nullable": true}, "dataFormat": {"type": "integer", "description": "数据格式", "format": "int32"}, "dataFormatName": {"type": "string", "description": "数据格式名称", "nullable": true}, "description": {"type": "string", "description": "产品描述", "nullable": true}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间", "nullable": true}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "updateBy": {"type": "integer", "description": "更新人", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "产品输出DTO"}, "Admin.Application.ProductServices.Dto.ProductPropertyOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "属性ID", "format": "int64"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "instructionId": {"type": "integer", "description": "指令ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "sort": {"type": "integer", "description": "参数序号", "format": "int32"}, "key": {"type": "string", "description": "参数标识符 (JSON中的路径，支持多层嵌套，如: \"IO.input.channel1\" 或 \"sensors.temperature\")", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "dataTypeName": {"type": "string", "description": "数据类型名称", "nullable": true}, "enumDescription": {"type": "string", "description": "枚举说明\r\n示例: 0=运行;1=停止\r\n0=浮冲;1=均冲;2=放电", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "divisionFactor": {"type": "integer", "description": "倍率", "format": "int32"}, "decimalPlaces": {"type": "integer", "description": "小数位数", "format": "int32"}, "correctionScale": {"type": "number", "description": "校正比例\r\n解析值*校正比例+校正幅度，默认为1", "format": "double"}, "correctionAmplitude": {"type": "number", "description": "校正幅度\r\n默认为0", "format": "double"}, "alarmUpperLimit": {"type": "number", "description": "报警上限", "format": "double"}, "alarmUpperLimitClearValue": {"type": "number", "description": "报警上限解除值", "format": "double"}, "alarmLowerLimit": {"type": "number", "description": "报警下限", "format": "double"}, "alarmLowerLimitClearValue": {"type": "number", "description": "报警下限解除值", "format": "double"}, "warningUpperLimit": {"type": "number", "description": "预警上限", "format": "double"}, "warningUpperLimitClearValue": {"type": "number", "description": "预警上限解除值", "format": "double"}, "warningLowerLimit": {"type": "number", "description": "预警下限", "format": "double"}, "warningLowerLimitClearValue": {"type": "number", "description": "预警下限解除值", "format": "double"}, "isSave": {"type": "boolean", "description": "是否保存"}, "saveAmplitude": {"type": "number", "description": "保存幅度\r\n与上次保存值的偏差超过幅度，触发保存", "format": "double"}, "saveAmplitudeType": {"type": "integer", "description": "保存幅度类型\r\n0: 数值 1：百分比\r\n默认数值", "format": "int32"}, "saveInterval": {"type": "integer", "description": "保存间隔(毫秒)", "format": "int32"}, "monitorStatus": {"type": "integer", "description": "监控状态\r\n0: 不启用 1：启用", "format": "int32"}, "description": {"type": "string", "description": "参数描述", "nullable": true}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间", "nullable": true}, "createBy": {"type": "string", "description": "创建人", "nullable": true}, "updateBy": {"type": "string", "description": "更新人", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "产品属性输出DTO (向后兼容别名)"}, "Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "属性ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "key": {"type": "string", "description": "参数标识符", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型", "format": "int32"}, "dataTypeName": {"type": "string", "description": "数据类型名称", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}}, "additionalProperties": false, "description": "产品属性简单输出DTO (向后兼容别名)"}, "Admin.Application.ProductServices.Dto.ProductSimpleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID", "format": "int64"}, "productName": {"type": "string", "description": "产品名称", "nullable": true}, "protocolType": {"type": "integer", "description": "协议类型", "format": "int32"}, "productType": {"type": "integer", "description": "产品类型", "format": "int32"}, "dataFormat": {"type": "integer", "description": "数据格式", "format": "int32"}}, "additionalProperties": false, "description": "产品简单输出DTO (用于下拉选择等场景)"}, "Admin.Application.ProductServices.Dto.UpdateModelInstructionInput": {"type": "object", "properties": {"modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "instructionName": {"type": "string", "description": "指令名称", "nullable": true}, "functionCode": {"type": "integer", "description": "功能码", "format": "int32"}, "startAddress": {"type": "integer", "description": "起始地址", "format": "int32"}, "readCount": {"type": "integer", "description": "读取数量", "format": "int32"}, "encode": {"type": "integer", "description": "编码 (1:HEX 2:ASCII)", "format": "int32"}, "responseTime": {"type": "integer", "description": "响应时间(毫秒)", "format": "int32"}, "retryCount": {"type": "integer", "description": "重试次数", "format": "int32"}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "id": {"type": "integer", "description": "指令ID", "format": "int64"}}, "additionalProperties": false, "description": "模型指令更新输入DTO"}, "Admin.Application.ProductServices.Dto.UpdateModelPropertyInput": {"type": "object", "properties": {"modelId": {"type": "integer", "description": "模型ID", "format": "int64"}, "instructionId": {"type": "integer", "description": "指令ID", "format": "int64"}, "name": {"type": "string", "description": "参数名称", "nullable": true}, "sort": {"type": "integer", "description": "参数序号", "format": "int32"}, "key": {"type": "string", "description": "参数标识符 (JSON中的路径，支持多层嵌套，如: \"IO.input.channel1\" 或 \"sensors.temperature\")", "nullable": true}, "dataType": {"type": "integer", "description": "数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）", "format": "int32"}, "enumDescription": {"type": "string", "description": "枚举说明\r\n示例: 0=运行;1=停止\r\n0=浮冲;1=均冲;2=放电", "nullable": true}, "unit": {"type": "string", "description": "单位", "nullable": true}, "divisionFactor": {"type": "integer", "description": "倍率", "format": "int32"}, "decimalPlaces": {"type": "integer", "description": "小数位数", "format": "int32"}, "correctionScale": {"type": "number", "description": "校正比例\r\n解析值*校正比例+校正幅度，默认为1", "format": "double"}, "correctionAmplitude": {"type": "number", "description": "校正幅度\r\n默认为0", "format": "double"}, "alarmUpperLimit": {"type": "number", "description": "报警上限", "format": "double"}, "alarmUpperLimitClearValue": {"type": "number", "description": "报警上限解除值", "format": "double"}, "alarmLowerLimit": {"type": "number", "description": "报警下限", "format": "double"}, "alarmLowerLimitClearValue": {"type": "number", "description": "报警下限解除值", "format": "double"}, "warningUpperLimit": {"type": "number", "description": "预警上限", "format": "double"}, "warningUpperLimitClearValue": {"type": "number", "description": "预警上限解除值", "format": "double"}, "warningLowerLimit": {"type": "number", "description": "预警下限", "format": "double"}, "warningLowerLimitClearValue": {"type": "number", "description": "预警下限解除值", "format": "double"}, "isSave": {"type": "boolean", "description": "是否保存"}, "saveAmplitude": {"type": "number", "description": "保存幅度\r\n与上次保存值的偏差超过幅度，触发保存", "format": "double"}, "saveAmplitudeType": {"type": "integer", "description": "保存幅度类型\r\n0: 数值 1：百分比\r\n默认数值", "format": "int32"}, "saveInterval": {"type": "integer", "description": "保存间隔(毫秒)", "format": "int32"}, "monitorStatus": {"type": "integer", "description": "监控状态\r\n0: 不启用 1：启用", "format": "int32"}, "description": {"type": "string", "description": "参数描述", "nullable": true}, "id": {"type": "integer", "description": "属性ID", "format": "int64"}}, "additionalProperties": false, "description": "模型属性更新输入DTO"}, "Admin.Application.ProductServices.Dto.UpdateProductInput": {"type": "object", "properties": {"productName": {"type": "string", "description": "产品名称", "nullable": true}, "protocolType": {"type": "integer", "description": "协议类型 (1:MQTT 2:Modbus)", "format": "int32"}, "productType": {"type": "integer", "description": "产品类型 (1:直连设备 2:网关设备 3:网关子设备)", "format": "int32"}, "dataFormat": {"type": "integer", "description": "数据格式 (1:J<PERSON>N 2:HEX)", "format": "int32"}, "description": {"type": "string", "description": "产品描述", "nullable": true}, "id": {"type": "integer", "description": "产品ID", "format": "int64"}}, "additionalProperties": false, "description": "产品更新输入DTO"}, "Admin.Application.ProductServices.Dto.UpdateProductModelInput": {"type": "object", "properties": {"productId": {"type": "integer", "description": "产品ID", "format": "int64"}, "modelName": {"type": "string", "description": "模型名称", "nullable": true}, "deviceGroup": {"type": "integer", "description": "设备组 (1:温湿度 2:漏水检测 3:空调 4:UPS 5:配电 6:开关 7:发电机 8:红外 9:门禁 10:传感器 11.冷通道)", "format": "int32"}, "description": {"type": "string", "description": "模型描述", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "id": {"type": "integer", "description": "模型ID", "format": "int64"}}, "additionalProperties": false, "description": "产品模型更新输入DTO"}, "Admin.Communication.Control.Models.BatchExecuteCommandRequest": {"required": ["commandIds"], "type": "object", "properties": {"commandIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "intervalMs": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "source": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Admin.Communication.Control.Models.BatchExecuteResult": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "failedCount": {"type": "integer", "format": "int32"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Communication.Control.Models.CommandExecuteResult"}, "nullable": true}}, "additionalProperties": false}, "Admin.Communication.Control.Models.CommandExecuteResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean"}, "controlRecordId": {"type": "integer", "format": "int64"}, "errorMessage": {"type": "string", "nullable": true}, "controlResult": {"type": "string", "nullable": true}, "responseContent": {"type": "string", "nullable": true}, "executeTime": {"type": "string"}}, "additionalProperties": false}, "Admin.Communication.Control.Models.ExecuteCommandRequest": {"required": ["commandId"], "type": "object", "properties": {"commandId": {"minimum": 1, "type": "integer", "format": "int64"}, "deviceId": {"type": "integer", "format": "int64", "nullable": true}, "parameters": {"type": "object", "additionalProperties": {}, "nullable": true}, "source": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.AlarmServices.Dto.AlarmHistoryOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlCmdOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlCmdOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlPlanOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ControlCmdServices.Dto.ControlPlanOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceInstructionOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceParaOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceParaOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelInstructionOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ModelPropertyOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductModelOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductModelOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProductServices.Dto.ProductOutput"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "ModelInstruction", "description": "模型指令服务"}, {"name": "ModelProperty", "description": "模型属性服务"}, {"name": "ProductModel", "description": "产品模型服务"}, {"name": "Product", "description": "产品服务"}, {"name": "ModbusTest", "description": "Modbus测试服务\r\n提供Modbus指令测试和调试功能"}, {"name": "DeviceData", "description": "设备数据服务"}, {"name": "DeviceInstruction", "description": "设备指令服务"}, {"name": "DevicePara", "description": "设备参数管理服务"}, {"name": "<PERSON><PERSON>", "description": "设备管理服务"}, {"name": "DeviceTopicConfig", "description": "设备主题配置服务"}, {"name": "ControlCmd", "description": "控制指令配置服务"}, {"name": "ControlPlan", "description": "控制计划服务"}, {"name": "AlarmEvent", "description": "告警事件服务"}, {"name": "AlarmHistory", "description": "告警历史服务"}, {"name": "ModelInstruction", "description": "模型指令服务"}, {"name": "ModelProperty", "description": "模型属性服务"}, {"name": "ProductModel", "description": "产品模型服务"}, {"name": "Product", "description": "产品服务"}, {"name": "ModbusTest", "description": "Modbus测试服务\r\n提供Modbus指令测试和调试功能"}, {"name": "DeviceData", "description": "设备数据服务"}, {"name": "DeviceInstruction", "description": "设备指令服务"}, {"name": "DevicePara", "description": "设备参数管理服务"}, {"name": "<PERSON><PERSON>", "description": "设备管理服务"}, {"name": "DeviceTopicConfig", "description": "设备主题配置服务"}, {"name": "ControlCmd", "description": "控制指令配置服务"}, {"name": "ControlPlan", "description": "控制计划服务"}, {"name": "AlarmEvent", "description": "告警事件服务"}, {"name": "AlarmHistory", "description": "告警历史服务"}, {"name": "ModelInstruction", "description": "模型指令服务"}, {"name": "ModelProperty", "description": "模型属性服务"}, {"name": "ProductModel", "description": "产品模型服务"}, {"name": "Product", "description": "产品服务"}, {"name": "ModbusTest", "description": "Modbus测试服务\r\n提供Modbus指令测试和调试功能"}, {"name": "DeviceData", "description": "设备数据服务"}, {"name": "DeviceInstruction", "description": "设备指令服务"}, {"name": "DevicePara", "description": "设备参数管理服务"}, {"name": "<PERSON><PERSON>", "description": "设备管理服务"}, {"name": "DeviceTopicConfig", "description": "设备主题配置服务"}, {"name": "ControlCmd", "description": "控制指令配置服务"}, {"name": "ControlPlan", "description": "控制计划服务"}, {"name": "AlarmEvent", "description": "告警事件服务"}, {"name": "AlarmHistory", "description": "告警历史服务"}]}