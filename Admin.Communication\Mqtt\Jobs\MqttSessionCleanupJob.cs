// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Mqtt.Abstractions;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Admin.Multiplex.Contracts.Enums.Mqtt;

namespace Admin.Communication.Mqtt.Jobs;

/// <summary>
/// MQTT会话清理后台作业参数
/// </summary>
public class MqttSessionCleanupArgs
{
    /// <summary>
    /// 清理类型
    /// </summary>
    public MqttCleanupType CleanupType { get; set; }
    
    /// <summary>
    /// 过期时间（小时）
    /// </summary>
    public int ExpirationHours { get; set; } = 24;
}

/// <summary>
/// MQTT清理类型
/// </summary>
public enum MqttCleanupType
{
    /// <summary>
    /// 清理过期会话
    /// </summary>
    ExpiredSessions,
    
    /// <summary>
    /// 清理过期消息
    /// </summary>
    ExpiredMessages,
    
    /// <summary>
    /// 清理失败消息
    /// </summary>
    FailedMessages,
    
    /// <summary>
    /// 全部清理
    /// </summary>
    All
}

/// <summary>
/// MQTT会话清理后台作业
/// 定期清理过期的会话、消息和订阅信息
/// </summary>
public class MqttSessionCleanupJob(
    ISqlSugarClient db,
    IMqttConnectionManager connectionManager,
    ILogger<MqttSessionCleanupJob> logger) : AsyncBackgroundJob<MqttSessionCleanupArgs>, ITransientDependency
{
    private readonly ISqlSugarClient _db = db;
    private readonly IMqttConnectionManager _connectionManager = connectionManager;
    private readonly ILogger<MqttSessionCleanupJob> _logger = logger;

    public override async Task ExecuteAsync(MqttSessionCleanupArgs args)
    {
        try
        {
            _logger.LogDebug("开始执行MQTT会话清理任务: CleanupType={CleanupType}, ExpirationHours={ExpirationHours}", 
                args.CleanupType, args.ExpirationHours);

            var cutoffTime = DateTime.Now.AddHours(-args.ExpirationHours);

            switch (args.CleanupType)
            {
                case MqttCleanupType.ExpiredSessions:
                    await CleanupExpiredSessionsAsync(cutoffTime);
                    break;
                    
                case MqttCleanupType.ExpiredMessages:
                    await CleanupExpiredMessagesAsync(cutoffTime);
                    break;
                    
                case MqttCleanupType.FailedMessages:
                    await CleanupFailedMessagesAsync(cutoffTime);
                    break;
                    
                case MqttCleanupType.All:
                    await CleanupExpiredSessionsAsync(cutoffTime);
                    await CleanupExpiredMessagesAsync(cutoffTime);
                    await CleanupFailedMessagesAsync(cutoffTime);
                    break;
            }

            _logger.LogDebug("MQTT会话清理任务执行完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行MQTT会话清理任务时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 清理过期会话
    /// </summary>
    private async Task CleanupExpiredSessionsAsync(DateTime cutoffTime)
    {
        try
        {
            // 清理内存中的过期会话
            await _connectionManager.CleanupExpiredSessionsAsync();

            // 清理数据库中的过期会话
            var expiredSessionCount = await _db.Deleteable<MqttSessionEntity>()
                .Where(x => x.LastActivityTime < cutoffTime && x.State != 1) // State 1 = Active
                .ExecuteCommandAsync();

            if (expiredSessionCount > 0)
            {
                _logger.LogInformation("清理了 {Count} 个过期会话", expiredSessionCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期会话时发生错误");
        }
    }

    /// <summary>
    /// 清理过期消息
    /// </summary>
    private async Task CleanupExpiredMessagesAsync(DateTime cutoffTime)
    {
        try
        {
            // 清理内存中的过期消息
            await _connectionManager.CleanupExpiredPendingMessagesAsync();

            // 清理数据库中的过期消息
            var expiredMessageCount = await _db.Deleteable<MqttPendingMessageEntity>()
                .Where(x => x.ExpiryTime.HasValue && x.ExpiryTime.Value < DateTime.Now)
                .ExecuteCommandAsync();

            if (expiredMessageCount > 0)
            {
                _logger.LogInformation("清理了 {Count} 个过期消息", expiredMessageCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期消息时发生错误");
        }
    }

    /// <summary>
    /// 清理失败消息
    /// </summary>
    private async Task CleanupFailedMessagesAsync(DateTime cutoffTime)
    {
        try
        {
            // 清理数据库中的失败消息（超过一定时间的）
            var failedMessageCount = await _db.Deleteable<MqttPendingMessageEntity>()
                .Where(x => x.Status == (int)MqttMessageStatusEnum.SendFailed && x.CreatedTime < cutoffTime)
                .ExecuteCommandAsync();

            if (failedMessageCount > 0)
            {
                _logger.LogInformation("清理了 {Count} 个失败消息", failedMessageCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理失败消息时发生错误");
        }
    }
}
