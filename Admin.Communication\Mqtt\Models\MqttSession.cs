using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Admin.Communication.Mqtt.Models
{
    /// <summary>
    /// MQTT会话
    /// </summary>
    public class MqttSession
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 是否为清除会话
        /// </summary>
        public bool CleanSession { get; set; }

        /// <summary>
        /// 会话创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; }

        /// <summary>
        /// 会话过期时间
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 是否为持久会话
        /// </summary>
        public bool IsPersistent => !CleanSession;

        /// <summary>
        /// 会话状态
        /// </summary>
        public SessionState State { get; set; } = SessionState.Active;

        /// <summary>
        /// 订阅信息
        /// </summary>
        private readonly ConcurrentDictionary<string, MqttSubscription> _subscriptions = new ConcurrentDictionary<string, MqttSubscription>();

        /// <summary>
        /// 待发送的消息队列（用于离线消息）
        /// </summary>
        private readonly ConcurrentQueue<MqttMessage> _pendingMessages = new ConcurrentQueue<MqttMessage>();

        /// <summary>
        /// 待确认的消息（QoS > 0）
        /// </summary>
        private readonly ConcurrentDictionary<ushort, MqttMessage> _awaitingAckMessages = new ConcurrentDictionary<ushort, MqttMessage>();

        /// <summary>
        /// 待完成的消息（QoS 2）
        /// </summary>
        private readonly ConcurrentDictionary<ushort, MqttMessage> _awaitingCompMessages = new ConcurrentDictionary<ushort, MqttMessage>();

        /// <summary>
        /// 会话统计信息
        /// </summary>
        public SessionStatistics Statistics { get; set; } = new SessionStatistics();

        /// <summary>
        /// 创建新的MQTT会话
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="cleanSession">是否为清除会话</param>
        public MqttSession(string clientId, bool cleanSession = true)
        {
            SessionId = Guid.NewGuid().ToString("N");
            ClientId = clientId ?? throw new ArgumentNullException(nameof(clientId));
            CleanSession = cleanSession;
            CreatedTime = DateTime.Now;
            LastActivityTime = DateTime.Now;
            
            // 持久会话设置过期时间（默认24小时）
            if (IsPersistent)
            {
                ExpiryTime = DateTime.Now.AddHours(24);
            }
        }

        #region 订阅管理

        /// <summary>
        /// 添加订阅
        /// </summary>
        /// <param name="subscription">订阅信息</param>
        public void AddSubscription(MqttSubscription subscription)
        {
            if (subscription == null) throw new ArgumentNullException(nameof(subscription));
            
            _subscriptions[subscription.TopicFilter] = subscription;
            UpdateActivity();
            Statistics.SubscriptionCount = _subscriptions.Count;
        }

        /// <summary>
        /// 移除订阅
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveSubscription(string topicFilter)
        {
            if (string.IsNullOrEmpty(topicFilter)) return false;
            
            var removed = _subscriptions.TryRemove(topicFilter, out _);
            if (removed)
            {
                UpdateActivity();
                Statistics.SubscriptionCount = _subscriptions.Count;
            }
            return removed;
        }

        /// <summary>
        /// 获取所有订阅
        /// </summary>
        /// <returns>订阅列表</returns>
        public IReadOnlyList<MqttSubscription> GetSubscriptions()
        {
            return _subscriptions.Values.ToList();
        }

        /// <summary>
        /// 获取订阅信息
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>订阅信息，如果不存在则返回null</returns>
        public MqttSubscription GetSubscription(string topicFilter)
        {
            _subscriptions.TryGetValue(topicFilter, out var subscription);
            return subscription;
        }

        /// <summary>
        /// 检查是否订阅了指定主题
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>是否已订阅</returns>
        public bool IsSubscribed(string topicFilter)
        {
            return _subscriptions.ContainsKey(topicFilter);
        }

        /// <summary>
        /// 清除所有订阅
        /// </summary>
        public void ClearSubscriptions()
        {
            _subscriptions.Clear();
            UpdateActivity();
            Statistics.SubscriptionCount = 0;
        }

        #endregion

        #region 消息管理

        /// <summary>
        /// 添加待发送消息
        /// </summary>
        /// <param name="message">消息</param>
        public void EnqueueMessage(MqttMessage message)
        {
            if (message == null) throw new ArgumentNullException(nameof(message));
            
            _pendingMessages.Enqueue(message);
            UpdateActivity();
            Statistics.PendingMessageCount = _pendingMessages.Count;
        }

        /// <summary>
        /// 获取下一个待发送消息
        /// </summary>
        /// <returns>消息，如果队列为空则返回null</returns>
        public MqttMessage DequeueMessage()
        {
            var hasMessage = _pendingMessages.TryDequeue(out var message);
            if (hasMessage)
            {
                UpdateActivity();
                Statistics.PendingMessageCount = _pendingMessages.Count;
            }
            return message;
        }

        /// <summary>
        /// 获取待发送消息数量
        /// </summary>
        /// <returns>消息数量</returns>
        public int GetPendingMessageCount()
        {
            return _pendingMessages.Count;
        }

        /// <summary>
        /// 获取所有待发送消息（用于持久化）
        /// </summary>
        /// <returns>待发送消息列表</returns>
        public IReadOnlyList<MqttMessage> GetPendingMessages()
        {
            return _pendingMessages.ToList();
        }

        /// <summary>
        /// 清除待发送消息
        /// </summary>
        public void ClearPendingMessages()
        {
            while (_pendingMessages.TryDequeue(out _)) { }
            UpdateActivity();
            Statistics.PendingMessageCount = 0;
        }

        /// <summary>
        /// 添加待确认消息
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="message">消息</param>
        public void AddAwaitingAckMessage(ushort messageId, MqttMessage message)
        {
            _awaitingAckMessages[messageId] = message;
            UpdateActivity();
        }

        /// <summary>
        /// 移除待确认消息
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <returns>被移除的消息</returns>
        public MqttMessage RemoveAwaitingAckMessage(ushort messageId)
        {
            _awaitingAckMessages.TryRemove(messageId, out var message);
            UpdateActivity();
            return message;
        }

        /// <summary>
        /// 添加待完成消息（QoS 2）
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="message">消息</param>
        public void AddAwaitingCompMessage(ushort messageId, MqttMessage message)
        {
            _awaitingCompMessages[messageId] = message;
            UpdateActivity();
        }

        /// <summary>
        /// 移除待完成消息
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <returns>被移除的消息</returns>
        public MqttMessage RemoveAwaitingCompMessage(ushort messageId)
        {
            _awaitingCompMessages.TryRemove(messageId, out var message);
            UpdateActivity();
            return message;
        }

        /// <summary>
        /// 获取所有待确认消息（用于持久化）
        /// </summary>
        /// <returns>待确认消息字典</returns>
        public IReadOnlyDictionary<ushort, MqttMessage> GetAwaitingAckMessages()
        {
            return _awaitingAckMessages.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// 获取所有待完成消息（用于持久化）
        /// </summary>
        /// <returns>待完成消息字典</returns>
        public IReadOnlyDictionary<ushort, MqttMessage> GetAwaitingCompMessages()
        {
            return _awaitingCompMessages.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// 恢复待确认消息（从持久化存储恢复）
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="message">消息</param>
        public void RestoreAwaitingAckMessage(ushort messageId, MqttMessage message)
        {
            _awaitingAckMessages[messageId] = message;
            // 不更新活动时间，因为这是恢复操作
        }

        /// <summary>
        /// 恢复待完成消息（从持久化存储恢复）
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="message">消息</param>
        public void RestoreAwaitingCompMessage(ushort messageId, MqttMessage message)
        {
            _awaitingCompMessages[messageId] = message;
            // 不更新活动时间，因为这是恢复操作
        }

        #endregion

        #region 会话状态管理

        /// <summary>
        /// 更新活动时间
        /// </summary>
        public void UpdateActivity()
        {
            LastActivityTime = DateTime.Now;
        }

        /// <summary>
        /// 检查会话是否过期
        /// </summary>
        /// <returns>是否过期</returns>
        public bool IsExpired()
        {
            return ExpiryTime.HasValue && DateTime.Now > ExpiryTime.Value;
        }

        /// <summary>
        /// 设置会话过期时间
        /// </summary>
        /// <param name="expiryInterval">过期间隔（秒）</param>
        public void SetExpiryInterval(uint expiryInterval)
        {
            if (expiryInterval == 0)
            {
                ExpiryTime = null; // 永不过期
            }
            else
            {
                ExpiryTime = DateTime.Now.AddSeconds(expiryInterval);
            }
        }

        /// <summary>
        /// 激活会话
        /// </summary>
        public void Activate()
        {
            State = SessionState.Active;
            UpdateActivity();
        }

        /// <summary>
        /// 暂停会话
        /// </summary>
        public void Suspend()
        {
            State = SessionState.Suspended;
            UpdateActivity();
        }

        /// <summary>
        /// 终止会话
        /// </summary>
        public void Terminate()
        {
            State = SessionState.Terminated;
            UpdateActivity();
        }

        /// <summary>
        /// 清理会话资源
        /// </summary>
        public void Cleanup()
        {
            if (CleanSession)
            {
                ClearSubscriptions();
                ClearPendingMessages();
                _awaitingAckMessages.Clear();
                _awaitingCompMessages.Clear();
            }
            
            State = SessionState.Terminated;
            UpdateActivity();
        }

        #endregion

        /// <summary>
        /// 获取会话摘要信息
        /// </summary>
        /// <returns>会话摘要</returns>
        public SessionSummary GetSummary()
        {
            return new SessionSummary
            {
                SessionId = SessionId,
                ClientId = ClientId,
                CleanSession = CleanSession,
                State = State,
                CreatedTime = CreatedTime,
                LastActivityTime = LastActivityTime,
                ExpiryTime = ExpiryTime,
                SubscriptionCount = _subscriptions.Count,
                PendingMessageCount = _pendingMessages.Count,
                AwaitingAckCount = _awaitingAckMessages.Count,
                AwaitingCompCount = _awaitingCompMessages.Count,
                Statistics = Statistics
            };
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Session[{SessionId}] ClientId: {ClientId}, CleanSession: {CleanSession}, " +
                   $"State: {State}, Subscriptions: {_subscriptions.Count}, " +
                   $"PendingMessages: {_pendingMessages.Count}";
        }
    }

    /// <summary>
    /// 会话状态
    /// </summary>
    public enum SessionState
    {
        /// <summary>
        /// 活跃状态
        /// </summary>
        Active,

        /// <summary>
        /// 暂停状态
        /// </summary>
        Suspended,

        /// <summary>
        /// 已终止
        /// </summary>
        Terminated
    }

    /// <summary>
    /// 会话统计信息
    /// </summary>
    public class SessionStatistics
    {
        /// <summary>
        /// 订阅数量
        /// </summary>
        public int SubscriptionCount { get; set; }

        /// <summary>
        /// 待发送消息数量
        /// </summary>
        public int PendingMessageCount { get; set; }

        /// <summary>
        /// 已发送消息数量
        /// </summary>
        public long SentMessageCount { get; set; }

        /// <summary>
        /// 已接收消息数量
        /// </summary>
        public long ReceivedMessageCount { get; set; }

        /// <summary>
        /// 消息发送失败数量
        /// </summary>
        public long FailedMessageCount { get; set; }

        /// <summary>
        /// 最后发送消息时间
        /// </summary>
        public DateTime? LastSentMessageTime { get; set; }

        /// <summary>
        /// 最后接收消息时间
        /// </summary>
        public DateTime? LastReceivedMessageTime { get; set; }

        /// <summary>
        /// 记录发送消息
        /// </summary>
        public void RecordSentMessage()
        {
            SentMessageCount++;
            LastSentMessageTime = DateTime.Now;
        }

        /// <summary>
        /// 记录接收消息
        /// </summary>
        public void RecordReceivedMessage()
        {
            ReceivedMessageCount++;
            LastReceivedMessageTime = DateTime.Now;
        }

        /// <summary>
        /// 记录失败消息
        /// </summary>
        public void RecordFailedMessage()
        {
            FailedMessageCount++;
        }
    }

    /// <summary>
    /// 会话摘要信息
    /// </summary>
    public class SessionSummary
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 是否为清除会话
        /// </summary>
        public bool CleanSession { get; set; }

        /// <summary>
        /// 会话状态
        /// </summary>
        public SessionState State { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 订阅数量
        /// </summary>
        public int SubscriptionCount { get; set; }

        /// <summary>
        /// 待发送消息数量
        /// </summary>
        public int PendingMessageCount { get; set; }

        /// <summary>
        /// 待确认消息数量
        /// </summary>
        public int AwaitingAckCount { get; set; }

        /// <summary>
        /// 待完成消息数量
        /// </summary>
        public int AwaitingCompCount { get; set; }

        /// <summary>
        /// 统计信息
        /// </summary>
        public SessionStatistics Statistics { get; set; }

        /// <summary>
        /// 会话持续时间
        /// </summary>
        public TimeSpan Duration => DateTime.Now - CreatedTime;

        /// <summary>
        /// 是否过期
        /// </summary>
        public bool IsExpired => ExpiryTime.HasValue && DateTime.Now > ExpiryTime.Value;
    }

    /// <summary>
    /// 待发送消息统计信息
    /// </summary>
    public class PendingMessageStatistics
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 待发送消息数量
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 发送中消息数量
        /// </summary>
        public int SendingCount { get; set; }

        /// <summary>
        /// 已发送消息数量
        /// </summary>
        public int SentCount { get; set; }

        /// <summary>
        /// 已确认消息数量
        /// </summary>
        public int AcknowledgedCount { get; set; }

        /// <summary>
        /// 发送失败消息数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 过期消息数量
        /// </summary>
        public int ExpiredCount { get; set; }

        /// <summary>
        /// 总消息数量
        /// </summary>
        public int TotalCount { get; set; }
    }
} 