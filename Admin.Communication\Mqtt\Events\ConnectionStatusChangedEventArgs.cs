﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Multiplex.Contracts.Enums.Mqtt;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Events;
/// <summary>
/// 连接状态变化事件参数
/// </summary>
public class ConnectionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 旧状态
    /// </summary>
    public ConnectionStatusEnum OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public ConnectionStatusEnum NewStatus { get; set; }

    /// <summary>
    /// 状态变化时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}
