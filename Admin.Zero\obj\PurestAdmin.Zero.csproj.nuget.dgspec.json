{"format": 1, "restore": {"D:\\code projects\\purest-admin-main\\api\\PurestAdmin.Zero\\PurestAdmin.Zero.csproj": {}}, "projects": {"D:\\code projects\\purest-admin-main\\api\\PurestAdmin.Zero\\PurestAdmin.Zero.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code projects\\purest-admin-main\\api\\PurestAdmin.Zero\\PurestAdmin.Zero.csproj", "projectName": "PurestAdmin.Zero", "projectPath": "D:\\code projects\\purest-admin-main\\api\\PurestAdmin.Zero\\PurestAdmin.Zero.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code projects\\purest-admin-main\\api\\PurestAdmin.Zero\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj": {"projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj"}, "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj": {"projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj"}, "D:\\code projects\\purest-admin-main\\api\\Admin.Workflow\\Admin.Workflow.csproj": {"projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Workflow\\Admin.Workflow.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.Castle.Core": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.Timing": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}