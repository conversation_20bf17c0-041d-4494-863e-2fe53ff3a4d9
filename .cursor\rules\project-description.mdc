---
description: 
globs: 
alwaysApply: true
---
## 项目描述
项目基于.Net8开发，由abp精简而来，沿用了abp framework的大部分功能，包括依赖注入、验证、授权等。结构重新划分后，使用更方便。
本项目旨在实现一个对分散式数据机房的单点式物联网集中监控管理系统，将分散的设备、动力、环境、安防等的监控数据通过网关层mqtt集中采集、处理和展示的平台，支持集中告警管理，支持短信、邮件、电话等方式。

## 项目结构说明
- **Admin.Api.Host** - 项目入口、启动层，所有关于接口的设置都放在这里,比如请求中间件、授权策略等
- **Admin.Application** - 应用层，会自动根据规则生成api接口，业务层
- **Admin.Core** - 核心层，此层不参与任何业务内容，负责基础功能的实现，以及组件的封装、扩展等
- **Admin.Multiplex** - 复合层，多元化的层，作用：为application和host层提供价值，放置一些后台功能以及复用的功能等
- **Admin.Multiplex.Contracts** - 复合层的契约层，相当于复合层方法的抽象层，也不完全都是契约，也会存放常量或者枚举等
- **Admin.SqlSugar** - ORM层，本项目使用了SqlSugar
- **Admin.Communication** - 通信层, 此层是mqtt架构层，支持mqtt3.1、mqtt3.1.1协议
- **Admin.Workflow** - 工作流层，处理业务流程
- **Admin.BackgroundService** - 后台服务层，处理定时任务和后台作业
- **Admin.Zero** - 零代码层，提供代码生成和模板功能
- 根据开发拓展持续封装新层

## 技术栈详情

### 核心框架
- **.NET 8** - 主要开发框架
- **ABP Framework (精简版)** - 提供依赖注入、验证、授权等基础功能
- **ASP.NET Core** - Web API框架
- **SqlSugar** - ORM框架，支持多数据库
- **MySQL** - 主数据库

### 工具库
- **Mapster** - 对象映射工具
- **Serilog** - 日志框架
- **Swagger/OpenAPI** - API文档生成
- **SignalR** - 实时通信

## 开发规范与最佳实践

### 1. 服务层开发规范

#### 构造函数模式
使用C# 12主构造函数语法：

#### 依赖注入
- 继承`ApplicationService`的服务会自动注册到DI容器
- 无需手动在模块中注册应用层服务
- 使用接口定义服务契约

#### 返回类型规范
- **分页查询**: 直接返回`PagedList<T>`，不使用Result包装
- **单个对象**: 直接返回具体DTO类型
- **操作结果**: 返回具体的Output DTO

### 2. DTO命名规范
- **输入DTO**: 使用`Input`后缀 (如：`AddUserInput`, `UpdateUserInput`)
- **输出DTO**: 使用`Output`后缀 (如：`UserOutput`, `UserListOutput`)
- **查询DTO**: 继承`PaginationParams`进行分页查询

### 3. 对象映射规范
使用Mapster进行对象映射：
```csharp
// Entity -> DTO
var output = entity.Adapt<UserOutput>();

// 分页映射
var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
return pagedList.Adapt<PagedList<UserOutput>>();
```

### 4. 异常处理规范
使用项目统一的异常处理：
```csharp
// 验证异常
throw PersistdValidateException.Message("错误信息");

// 空结果异常
var entity = await repository.GetByIdAsync(id) ?? 
    throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
```

### 5. API控制器规范
- 应用层服务会自动生成API控制器
- 使用`[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.XXX)]`进行分组
- 路由前缀为`v1`

### 6. 数据库实体规范
- 继承`BaseEntity`获得标准审计字段（如果需要审计）
- 使用雪花ID作为主键
- 标准字段：`Id`, `CreateBy`, `CreateTime`, `UpdateBy`, `UpdateTime`, `Remark`
