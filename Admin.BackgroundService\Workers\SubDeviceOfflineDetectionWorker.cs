// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Admin.BackgroundService.Workers;

/// <summary>
/// 网关子设备离线检测后台服务
/// 注意：此功能已移除，后续再开发
/// </summary>
public class SubDeviceOfflineDetectionWorker(ILogger<SubDeviceOfflineDetectionWorker> logger) : ISingletonDependency
{
    private readonly ILogger<SubDeviceOfflineDetectionWorker> _logger = logger;

    public Task StartAsync()
    {
        _logger.LogInformation("网关子设备离线检测功能已移除，后续再开发");
        return Task.CompletedTask;
    }
}
