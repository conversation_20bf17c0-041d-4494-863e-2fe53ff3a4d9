{"openapi": "3.0.1", "info": {"title": "工作流管理"}, "paths": {"/api/v1/definition/paged-list": {"get": {"tags": ["Definition"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "模板名称", "schema": {"type": "string"}}, {"name": "Version", "in": "query", "description": "版本", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsLocked", "in": "query", "description": "是否锁定", "schema": {"type": "boolean"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/definition/{id}": {"get": {"tags": ["Definition"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}}}}}}, "put": {"tags": ["Definition"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.PutWfDefinitionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.PutWfDefinitionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.PutWfDefinitionInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Definition"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/definition": {"post": {"tags": ["Definition"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/definition/{id}/lock": {"post": {"tags": ["Definition"], "summary": "锁定", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/definition/definitions": {"get": {"tags": ["Definition"], "summary": "已注册流程集合", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}}}}}}}}, "/api/v1/instance/self-paged-list": {"get": {"tags": ["Instance"], "summary": "我的流程", "parameters": [{"name": "WorkflowStatus", "in": "query", "description": "工作流状态", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.InstanceOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.InstanceOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.InstanceOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/instance/auditing-paged-list": {"get": {"tags": ["Instance"], "summary": "待办事项", "parameters": [{"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.WaitingAuditingOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.WaitingAuditingOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.WaitingAuditingOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/instance/{id}/start": {"post": {"tags": ["Instance"], "summary": "开始流程", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/instance/{id}/terminate": {"post": {"tags": ["Instance"], "summary": "终止流程", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/instance/{id}/auditing": {"post": {"tags": ["Instance"], "summary": "审批流程", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.AuditingInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.AuditingInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.AuditingInput"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/instance/{id}/instance-detail": {"get": {"tags": ["Instance"], "summary": "获取实例详情", "parameters": [{"name": "id", "in": "path", "description": "实例Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.InstanceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.InstanceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.InstanceOutput"}}}}}}}}, "components": {"schemas": {"Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.InstanceOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.InstanceOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.WaitingAuditingOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.WaitingAuditingOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.Workflow.Services.InstanceDtos.AuditingInput": {"type": "object", "properties": {"stepId": {"type": "integer", "description": "审批步骤", "format": "int32"}, "isAgree": {"type": "boolean", "description": "是否同意"}, "auditingOpinion": {"type": "string", "description": "审批意见", "nullable": true}}, "additionalProperties": false, "description": "审批输入DTO"}, "Admin.Workflow.Services.InstanceDtos.AuditingRecordOutput": {"type": "object", "properties": {"executionPointerId": {"type": "integer", "description": "步骤id", "format": "int64"}, "auditingTime": {"type": "string", "description": "审批时间"}, "auditor": {"type": "integer", "description": "审批人", "format": "int64"}, "auditorName": {"type": "string", "description": "审批人", "nullable": true}, "auditingOpinion": {"type": "string", "description": "审批意见", "nullable": true}, "isAgree": {"type": "boolean", "description": "是否同意"}}, "additionalProperties": false}, "Admin.Workflow.Services.InstanceDtos.ExecutionPointerOutput": {"type": "object", "properties": {"stepId": {"type": "integer", "format": "int32"}, "stepName": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "startTime": {"type": "string"}, "endTime": {"type": "string", "nullable": true}, "auditingRecords": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.AuditingRecordOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.Workflow.Services.InstanceDtos.InstanceOutput": {"type": "object", "properties": {"persistenceId": {"type": "integer", "description": "主键Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间"}, "description": {"type": "string", "description": "流程描述", "nullable": true}, "data": {"type": "string", "description": "数据", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}, "status": {"type": "integer", "description": "流程状态", "format": "int32"}, "version": {"type": "integer", "description": "版本", "format": "int32"}, "definition": {"$ref": "#/components/schemas/Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput"}, "currentNodeName": {"type": "string", "description": "当前节点名称", "nullable": true, "readOnly": true}, "currentNodeStatusString": {"type": "string", "description": "当前节点状态", "nullable": true, "readOnly": true}, "executionPointers": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.ExecutionPointerOutput"}, "description": "审批节点", "nullable": true}}, "additionalProperties": false, "description": "待审批数据"}, "Admin.Workflow.Services.InstanceDtos.WaitingAuditingOutput": {"type": "object", "properties": {"persistenceId": {"type": "integer", "description": "主键Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间"}, "createByName": {"type": "string", "description": "流程发起人", "nullable": true}, "description": {"type": "string", "description": "流程描述", "nullable": true}, "version": {"type": "integer", "description": "版本", "format": "int32"}, "stepId": {"type": "integer", "description": "步骤Id", "format": "int32", "readOnly": true}, "executionPointers": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Workflow.Services.InstanceDtos.ExecutionPointerOutput"}, "description": "审批步骤", "nullable": true}}, "additionalProperties": false, "description": "待审批数据"}, "Admin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput": {"required": ["designsContent", "formContent", "isLocked", "name", "version"], "type": "object", "properties": {"remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 20, "minLength": 1, "type": "string", "description": "名称"}, "designsContent": {"minLength": 1, "type": "string", "description": "设计器内容"}, "formContent": {"minLength": 1, "type": "string", "description": "表单内容"}, "version": {"type": "integer", "description": "版本", "format": "int32", "default": 1}, "isLocked": {"type": "boolean", "description": "是否锁定"}}, "additionalProperties": false}, "Admin.Workflow.Services.WfDefiniationDtos.PutWfDefinitionInput": {"required": ["designsContent", "formContent", "isLocked", "name", "version"], "type": "object", "properties": {"remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 20, "minLength": 1, "type": "string", "description": "名称"}, "designsContent": {"minLength": 1, "type": "string", "description": "设计器内容"}, "formContent": {"minLength": 1, "type": "string", "description": "表单内容"}, "version": {"type": "integer", "description": "版本", "format": "int32", "default": 1}, "isLocked": {"type": "boolean", "description": "是否锁定"}}, "additionalProperties": false}, "Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "definitionId": {"type": "string", "description": "定义ID", "nullable": true}, "workflowContent": {"type": "string", "description": "流程内容", "nullable": true}, "designsContent": {"type": "string", "description": "设计器内容", "nullable": true}, "formContent": {"type": "string", "description": "表单内容", "nullable": true}, "version": {"type": "integer", "description": "版本", "format": "int32"}, "isLocked": {"type": "boolean", "description": "是否锁定"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "Definition", "description": "工作流定义服务"}, {"name": "Instance", "description": "流程实例服务"}]}