﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Models.Results;
/// <summary>
/// 授权结果
/// </summary>
public class AuthorizationResult
{
    /// <summary>
    /// 是否授权成功
    /// </summary>
    public bool IsAuthorized { get; set; }

    /// <summary>
    /// 授权失败原因
    /// </summary>
    public string FailureReason { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static AuthorizationResult Success()
    {
        return new AuthorizationResult { IsAuthorized = true };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static AuthorizationResult Failure(string reason)
    {
        return new AuthorizationResult
        {
            IsAuthorized = false,
            FailureReason = reason
        };
    }
}
