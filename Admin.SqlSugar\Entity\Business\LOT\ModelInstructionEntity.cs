﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 模型指令表
/// modbus RTU 指令
/// </summary>
[SugarTable("LOT_MODEL_INSTRUCTION")]
public partial class ModelInstructionEntity : BaseEntity
{
    /// <summary>
    /// 模型id
    /// </summary>
    [SugarColumn(ColumnName = "MODEL_ID")]
    public long ModelId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    [SugarColumn(ColumnName = "INSTRUCTION_NAME")]
    public string InstructionName { get; set; }

    /// <summary>
    /// 功能码
    /// </summary>
    [SugarColumn(ColumnName = "FUNCTION_CODE")]
    public int FunctionCode { get; set; }

    /// <summary>
    /// 起始地址
    /// </summary>
    [SugarColumn(ColumnName = "START_ADDRESS")]
    public int StartAddress { get; set; }

    /// <summary>
    /// 读取数量
    /// </summary>
    [SugarColumn(ColumnName = "READ_COUNT")]
    public int ReadCount { get; set; }

    /// <summary>
    /// 编码 1:HEX 2:ASCII
    /// </summary>
    [SugarColumn(ColumnName = "ENCODE")]
    public int Encode { get; set; } = 1; 

    /// <summary>
    /// 指令顺序
    /// </summary>
    [SugarColumn(ColumnName = "ORDER")]
    public int InstructionOrder { get; set; } = 1; // 默认1，表示第一个指令

    /// <summary>
    /// 读取间隔(毫秒)
    /// </summary>
    [SugarColumn(ColumnName = "READ_INTERVAL")]
    public int ReadInterval { get; set; } = 10000;

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    [SugarColumn(ColumnName = "RESPONSE_TIME")]
    public int ResponseTime { get; set; } = 2000;

    /// <summary>
    /// 重试次数
    /// </summary>
    [SugarColumn(ColumnName = "RETRY_COUNT")]
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnName = "IS_ENABLED")]
    public bool IsEnabled { get; set; } = true;
}
