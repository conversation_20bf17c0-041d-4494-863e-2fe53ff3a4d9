namespace Admin.Multiplex.Contracts.Enums.Mqtt
{
    /// <summary>
    /// 连接拒绝代码
    /// </summary>
    public enum ConnectionRejectCodeEnum
    {
        /// <summary>
        /// 超出最大连接数
        /// </summary>
        MaxConnectionsExceeded,

        /// <summary>
        /// 超出IP最大连接数
        /// </summary>
        MaxConnectionsPerIpExceeded,

        /// <summary>
        /// 客户端在黑名单中
        /// </summary>
        ClientBlacklisted,

        /// <summary>
        /// IP在黑名单中
        /// </summary>
        IpBlacklisted,

        /// <summary>
        /// 连接频率过高
        /// </summary>
        ConnectionRateLimitExceeded,

        /// <summary>
        /// 服务器维护中
        /// </summary>
        ServerMaintenance
    }
} 