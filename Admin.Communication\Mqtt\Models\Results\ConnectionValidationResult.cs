﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Multiplex.Contracts.Enums.Mqtt;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Models.Results;
/// <summary>
/// 连接验证结果
/// </summary>
public class ConnectionValidationResult
{
    /// <summary>
    /// 是否允许连接
    /// </summary>
    public bool IsAllowed { get; set; }

    /// <summary>
    /// 拒绝原因
    /// </summary>
    public string RejectReason { get; set; }

    /// <summary>
    /// 拒绝代码
    /// </summary>
    public ConnectionRejectCodeEnum RejectCode { get; set; }

    /// <summary>
    /// 创建允许结果
    /// </summary>
    public static ConnectionValidationResult Allow()
    {
        return new ConnectionValidationResult { IsAllowed = true };
    }

    /// <summary>
    /// 创建拒绝结果
    /// </summary>
    public static ConnectionValidationResult Reject(ConnectionRejectCodeEnum code, string reason)
    {
        return new ConnectionValidationResult
        {
            IsAllowed = false,
            RejectCode = code,
            RejectReason = reason
        };
    }
}