using System;
using System.Collections.Generic;
using System.Linq;

namespace Admin.Communication.Mqtt.Protocol
{
    /// <summary>
    /// MQTT主题匹配器
    /// </summary>
    public class MqttTopicMatcher
    {
        /// <summary>
        /// 检查主题是否匹配主题过滤器
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>是否匹配</returns>
        public static bool IsMatch(string topic, string topicFilter)
        {
            if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(topicFilter))
            {
                return false;
            }

            // 检查主题和主题过滤器是否有效
            if (!IsValidTopic(topic) || !IsValidTopicFilter(topicFilter))
            {
                return false;
            }

            // 如果主题过滤器只包含多级通配符，则匹配所有主题
            if (topicFilter == "#")
            {
                return true;
            }

            // 将主题和主题过滤器分割为层级
            string[] topicLevels = topic.Split('/');
            string[] filterLevels = topicFilter.Split('/');

            // 如果主题过滤器以多级通配符结尾，则移除最后一个层级
            bool hasMultiLevelWildcard = false;
            if (filterLevels.Length > 0 && filterLevels[filterLevels.Length - 1] == "#")
            {
                hasMultiLevelWildcard = true;
                Array.Resize(ref filterLevels, filterLevels.Length - 1);
            }

            // 如果主题层级少于主题过滤器层级，则不匹配
            if (topicLevels.Length < filterLevels.Length)
            {
                return false;
            }

            // 如果主题层级多于主题过滤器层级，并且没有多级通配符，则不匹配
            if (topicLevels.Length > filterLevels.Length && !hasMultiLevelWildcard)
            {
                return false;
            }

            // 逐层比较
            for (int i = 0; i < filterLevels.Length; i++)
            {
                string filterLevel = filterLevels[i];
                string topicLevel = topicLevels[i];

                // 如果是单级通配符，则匹配任何层级
                if (filterLevel == "+")
                {
                    continue;
                }

                // 如果不是通配符，则必须完全匹配
                if (filterLevel != topicLevel)
                {
                    return false;
                }
            }

            // 如果有多级通配符，则匹配剩余的所有层级
            // 如果没有多级通配符，则主题层级数必须等于主题过滤器层级数
            return hasMultiLevelWildcard || topicLevels.Length == filterLevels.Length;
        }

        /// <summary>
        /// 查找匹配主题的主题过滤器
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="topicFilters">主题过滤器集合</param>
        /// <returns>匹配的主题过滤器列表</returns>
        public static List<string> FindMatchingFilters(string topic, IEnumerable<string> topicFilters)
        {
            if (string.IsNullOrEmpty(topic) || topicFilters == null)
            {
                return new List<string>();
            }

            return topicFilters
                .Where(filter => IsMatch(topic, filter))
                .ToList();
        }

        /// <summary>
        /// 检查主题是否有效
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>是否有效</returns>
        public static bool IsValidTopic(string topic)
        {
            if (string.IsNullOrEmpty(topic))
            {
                return false;
            }

            // 主题不能包含通配符
            if (topic.Contains("+") || topic.Contains("#"))
            {
                return false;
            }

            // 主题不能以/结尾（但可以以/开头）
            if (topic.EndsWith("/"))
            {
                return false;
            }

            // 主题不能包含连续的/
            if (topic.Contains("//"))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查主题过滤器是否有效
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>是否有效</returns>
        public static bool IsValidTopicFilter(string topicFilter)
        {
            if (string.IsNullOrEmpty(topicFilter))
            {
                return false;
            }

            // 主题过滤器不能以/结尾（但可以以/开头）
            if (topicFilter.EndsWith("/"))
            {
                return false;
            }

            // 主题过滤器不能包含连续的/
            if (topicFilter.Contains("//"))
            {
                return false;
            }

            // 多级通配符只能出现在主题过滤器的末尾，并且必须单独占据一个层级
            int multiLevelIndex = topicFilter.IndexOf("#");
            if (multiLevelIndex != -1)
            {
                // 如果多级通配符不在末尾，则无效
                if (multiLevelIndex != topicFilter.Length - 1)
                {
                    return false;
                }

                // 如果多级通配符前面不是/，并且不是唯一的字符，则无效
                if (multiLevelIndex > 0 && topicFilter[multiLevelIndex - 1] != '/')
                {
                    return false;
                }
            }

            // 单级通配符必须单独占据一个层级
            string[] levels = topicFilter.Split('/');
            foreach (string level in levels)
            {
                if (level.Contains("+") && level != "+")
                {
                    return false;
                }
            }

            return true;
        }
    }
} 