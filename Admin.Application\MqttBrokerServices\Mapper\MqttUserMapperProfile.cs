// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.MqttBrokerServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;
using Mapster;

namespace Admin.Application.MqttBrokerServices.Mapper;

/// <summary>
/// MQTT用户映射配置
/// </summary>
public class MqttUserMapperProfile : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // AddMqttUserInput -> MqttUserEntity
        config.ForType<AddMqttUserInput, MqttUserEntity>()
            .IgnoreNullValues(true)
            .Ignore(dest => dest.CreateTime)
            .Ignore(dest => dest.CreateBy)
            .Ignore(dest => dest.UpdateTime)
            .Ignore(dest => dest.UpdateBy)
            .Ignore(dest => dest.LastLoginTime)
            .Ignore(dest => dest.LastLoginIp);

        // UpdateMqttUserInput -> MqttUserEntity
        config.ForType<UpdateMqttUserInput, MqttUserEntity>()
            .IgnoreNullValues(true)
            .Ignore(dest => dest.Username)
            .Ignore(dest => dest.CreateTime)
            .Ignore(dest => dest.CreateBy)
            .Ignore(dest => dest.UpdateTime)
            .Ignore(dest => dest.UpdateBy)
            .Ignore(dest => dest.LastLoginTime)
            .Ignore(dest => dest.LastLoginIp);

        // MqttUserEntity -> MqttUserOutput
        config.ForType<MqttUserEntity, MqttUserOutput>()
            .IgnoreNullValues(true);
    }
}
