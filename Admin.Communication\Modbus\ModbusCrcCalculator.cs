// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Communication.Modbus;

/// <summary>
/// Modbus CRC16 校验码计算工具类
/// </summary>
public static class ModbusCrcCalculator
{
    /// <summary>
    /// 计算CRC16校验码（Modbus标准）
    /// 使用多项式：0xA001，初始值：0xFFFF
    /// </summary>
    /// <param name="data">数据字节数组</param>
    /// <returns>CRC16校验码</returns>
    public static ushort CalculateCrc16(byte[] data)
    {
        if (data == null)
            throw new ArgumentNullException(nameof(data));

        ushort crc = 0xFFFF;
        
        foreach (byte b in data)
        {
            crc ^= b;
            for (int i = 0; i < 8; i++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= 0xA001;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }
        
        return crc;
    }

    /// <summary>
    /// 计算CRC16校验码并返回字节数组（低字节在前）
    /// </summary>
    /// <param name="data">数据字节数组</param>
    /// <returns>CRC16校验码的字节数组 [低字节, 高字节]</returns>
    public static byte[] CalculateCrc16Bytes(byte[] data)
    {
        var crc = CalculateCrc16(data);
        return new byte[] 
        { 
            (byte)(crc & 0xFF),      // 低字节
            (byte)(crc >> 8)         // 高字节
        };
    }

    /// <summary>
    /// 验证数据的CRC16校验码是否正确
    /// </summary>
    /// <param name="dataWithCrc">包含CRC16的完整数据（CRC16在末尾两个字节）</param>
    /// <returns>校验是否通过</returns>
    public static bool VerifyCrc16(byte[] dataWithCrc)
    {
        if (dataWithCrc == null || dataWithCrc.Length < 3)
            return false;

        // 分离数据和CRC
        var dataLength = dataWithCrc.Length - 2;
        var data = new byte[dataLength];
        Array.Copy(dataWithCrc, 0, data, 0, dataLength);

        var receivedCrc = (ushort)(dataWithCrc[dataLength] | (dataWithCrc[dataLength + 1] << 8));
        var calculatedCrc = CalculateCrc16(data);

        return receivedCrc == calculatedCrc;
    }

    /// <summary>
    /// 为数据添加CRC16校验码
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>添加了CRC16校验码的完整数据</returns>
    public static byte[] AppendCrc16(byte[] data)
    {
        if (data == null)
            throw new ArgumentNullException(nameof(data));

        var crcBytes = CalculateCrc16Bytes(data);
        var result = new byte[data.Length + 2];
        
        Array.Copy(data, 0, result, 0, data.Length);
        result[data.Length] = crcBytes[0];     // 低字节
        result[data.Length + 1] = crcBytes[1]; // 高字节
        
        return result;
    }
}
