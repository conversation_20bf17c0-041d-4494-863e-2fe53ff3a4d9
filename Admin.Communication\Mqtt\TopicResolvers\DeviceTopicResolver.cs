using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.DataParsers;
using Admin.Communication.Mqtt.TopicFilters;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Admin.Communication.Mqtt.TopicResolvers
{
    /// <summary>
    /// 设备主题解析器 - 根据业务逻辑重新设计
    /// </summary>
    public class DeviceTopicResolver : ITopicResolver
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<DeviceTopicResolver> _logger;
        private readonly PresetTopicManager _presetTopicManager;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="db">数据库客户端</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="presetTopicManager">预置主题管理器</param>
        public DeviceTopicResolver(
            ISqlSugarClient db,
            ILogger<DeviceTopicResolver> logger,
            PresetTopicManager presetTopicManager)
        {
            _db = db;
            _logger = logger;
            _presetTopicManager = presetTopicManager;
        }

        /// <summary>
        /// 解析主题并获取设备解析上下文
        /// </summary>
        /// <param name="topic">MQTT主题</param>
        /// <param name="deviceId">设备ID</param>
        /// <returns>主题解析结果</returns>
        public async Task<TopicResolveResult> ResolveAsync(string topic, string deviceId)
        {
            try
            {
                _logger.LogDebug("开始解析主题: {Topic}, 设备ID: {DeviceId}", topic, deviceId);

                // 1. 检查是否为预置主题
                var presetTemplate = _presetTopicManager.MatchPresetTopic(topic);
                if (presetTemplate != null)
                {
                    return await ResolvePresetTopicAsync(topic, deviceId, presetTemplate);
                }

                // 2. 检查是否为自定义主题
                var customTopicConfig = await GetCustomTopicConfigAsync(deviceId, topic);
                if (customTopicConfig != null)
                {
                    return await ResolveCustomTopicAsync(topic, deviceId, customTopicConfig);
                }

                // 3. 未知主题
                _logger.LogWarning("未知主题类型: {Topic}, 设备ID: {DeviceId}", topic, deviceId);
                return TopicResolveResult.Failure($"未知主题类型: {topic}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析主题时发生异常: {Topic}, 设备ID: {DeviceId}", topic, deviceId);
                return TopicResolveResult.Failure($"解析异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析预置主题
        /// </summary>
        private async Task<TopicResolveResult> ResolvePresetTopicAsync(string topic, string deviceId, PresetTopicTemplate template)
        {
            _logger.LogInformation("解析预置主题: {TemplateName}, 设备ID: {DeviceId}", template.Name, deviceId);

            // 1. 验证设备ID
            var extractedDeviceId = _presetTopicManager.ExtractDeviceId(topic, template);
            if (extractedDeviceId != deviceId)
            {
                return TopicResolveResult.Failure($"主题中的设备ID({extractedDeviceId})与消息设备ID({deviceId})不一致");
            }

            // 2. 查询设备信息
            var device = await GetDeviceByDeviceIdAsync(deviceId);
            if (device == null)
            {
                return TopicResolveResult.Failure($"未找到设备: {deviceId}");
            }

            // 3. 根据预置主题类型确定设备类型和数据格式
            DataFormatEnum dataFormat;
            bool isGatewayDevice;

            if (template.IsDirectDevice)
            {
                // 直连设备：根据主题编码使用JSON还是HEX
                dataFormat = template.DataFormat;
                isGatewayDevice = false;
                _logger.LogDebug("预置主题-直连设备: 数据格式={DataFormat}", dataFormat);
            }
            else if (template.IsGatewayDevice)
            {
                // 网关设备：根据主题编码使用JSON还是HEX
                dataFormat = template.DataFormat;
                isGatewayDevice = true;
                _logger.LogDebug("预置主题-网关设备: 数据格式={DataFormat}", dataFormat);
            }
            else
            {
                // 其他预置主题：使用模板定义的数据格式
                dataFormat = template.DataFormat;
                isGatewayDevice = false;
                _logger.LogDebug("预置主题-其他类型: 数据格式={DataFormat}", dataFormat);
            }

            // 4. 构建解析上下文
            var parseContext = await BuildParseContextAsync(device, topic, isGatewayDevice, null);
            if (parseContext == null)
            {
                return TopicResolveResult.Failure("构建解析上下文失败");
            }

            return TopicResolveResult.Success(TopicType.Preset, dataFormat, parseContext);
        }

        /// <summary>
        /// 解析自定义主题
        /// </summary>
        private async Task<TopicResolveResult> ResolveCustomTopicAsync(string topic, string deviceId, DeviceTopicConfigEntity config)
        {
            _logger.LogInformation("解析自定义主题: 设备ID={DeviceId}, 主题={Topic}, 配置ID={ConfigId}",
                deviceId, topic, config.Id);

            // 1. 查询设备信息
            var device = await GetDeviceByDeviceIdAsync(deviceId);
            if (device == null)
            {
                return TopicResolveResult.Failure($"未找到设备: {deviceId}");
            }

            // 2. 获取数据格式
            var dataFormat = (DataFormatEnum)config.DataFormat;
            _logger.LogDebug("自定义主题数据格式: {DataFormat}", dataFormat);

            // 3. 构建解析上下文
            var parseContext = await BuildParseContextAsync(device, topic, false, config);
            if (parseContext == null)
            {
                return TopicResolveResult.Failure("构建解析上下文失败");
            }

            return TopicResolveResult.Success(TopicType.Custom, dataFormat, parseContext);
        }

        /// <summary>
        /// 构建设备解析上下文
        /// </summary>
        private async Task<DeviceParseContext?> BuildParseContextAsync(
            DeviceEntity device,
            string topic,
            bool isGatewayDevice,
            DeviceTopicConfigEntity? topicConfig)
        {
            try
            {
                // 1. 查询产品模型信息
                var productModel = await GetProductModelAsync(device.ModelId);
                if (productModel == null)
                {
                    _logger.LogError("未找到产品模型: {ModelId}", device.ModelId);
                    return null;
                }

                // 2. 查询产品信息
                var product = await GetProductByIdAsync(productModel.ProductId);
                if (product == null)
                {
                    _logger.LogError("未找到产品信息: {ProductId}", productModel.ProductId);
                    return null;
                }

                // 3. 查询设备参数（根据设备类型）
                List<ModelPropertyEntity> modelProperties;
                if (isGatewayDevice)
                {
                    // 网关设备：获取网关设备和子设备的设备参数
                    modelProperties = await GetGatewayDevicePropertiesAsync(device.Id);
                }
                else
                {
                    // 直连设备：直接查询设备参数表
                    modelProperties = await GetDirectDevicePropertiesAsync(device.ModelId);
                }

                // 4. 创建解析上下文
                var parseContext = new DeviceParseContext
                {
                    Device = device,
                    ProductModel = productModel,
                    Product = product,
                    ModelProperties = modelProperties,
                    Topic = topic,
                    IsGatewayDevice = isGatewayDevice,
                    TopicConfig = topicConfig
                };

                _logger.LogDebug("构建解析上下文成功: 设备={DeviceId}, 属性数量={PropertyCount}, 网关设备={IsGateway}",
                    device.DeviceId, modelProperties.Count, isGatewayDevice);

                return parseContext;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建解析上下文时发生异常: 设备={DeviceId}", device.DeviceId);
                return null;
            }
        }

        /// <summary>
        /// 获取直连设备的设备参数
        /// </summary>
        private async Task<List<ModelPropertyEntity>> GetDirectDevicePropertiesAsync(long modelId)
        {
            try
            {
                var properties = await _db.Queryable<ModelPropertyEntity>()
                    .Where(p => p.ModelId == modelId)
                    .OrderBy(p => p.Sort)
                    .ToListAsync();

                _logger.LogDebug("查询直连设备参数: ModelId={ModelId}, 数量={Count}", modelId, properties.Count);
                return properties ?? new List<ModelPropertyEntity>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询直连设备参数失败: ModelId={ModelId}", modelId);
                return new List<ModelPropertyEntity>();
            }
        }

        /// <summary>
        /// 获取网关设备和子设备的设备参数
        /// </summary>
        private async Task<List<ModelPropertyEntity>> GetGatewayDevicePropertiesAsync(long gatewayDeviceId)
        {
            try
            {
                // 1. 查询网关设备的参数
                var gatewayDevice = await _db.Queryable<DeviceEntity>()
                    .Where(d => d.Id == gatewayDeviceId)
                    .FirstAsync();

                if (gatewayDevice == null)
                {
                    _logger.LogWarning("未找到网关设备: {GatewayDeviceId}", gatewayDeviceId);
                    return new List<ModelPropertyEntity>();
                }

                var allProperties = new List<ModelPropertyEntity>();

                // 2. 获取网关设备自身的参数
                var gatewayProperties = await _db.Queryable<ModelPropertyEntity>()
                    .Where(p => p.ModelId == gatewayDevice.ModelId)
                    .OrderBy(p => p.Sort)
                    .ToListAsync();

                if (gatewayProperties != null)
                {
                    allProperties.AddRange(gatewayProperties);
                }

                // 3. 查询子设备
                var subDevices = await _db.Queryable<DeviceEntity>()
                    .Where(d => d.ParentId == gatewayDeviceId)
                    .ToListAsync();

                // 4. 获取所有子设备的参数
                foreach (var subDevice in subDevices ?? new List<DeviceEntity>())
                {
                    var subDeviceProperties = await _db.Queryable<ModelPropertyEntity>()
                        .Where(p => p.ModelId == subDevice.ModelId)
                        .OrderBy(p => p.Sort)
                        .ToListAsync();

                    if (subDeviceProperties != null)
                    {
                        allProperties.AddRange(subDeviceProperties);
                    }
                }

                _logger.LogDebug("查询网关设备参数: 网关ID={GatewayId}, 子设备数={SubDeviceCount}, 总参数数={TotalProperties}",
                    gatewayDeviceId, subDevices?.Count ?? 0, allProperties.Count);

                return allProperties;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询网关设备参数失败: GatewayDeviceId={GatewayDeviceId}", gatewayDeviceId);
                return new List<ModelPropertyEntity>();
            }
        }

        /// <summary>
        /// 根据设备ID查询设备信息
        /// </summary>
        private async Task<DeviceEntity?> GetDeviceByDeviceIdAsync(string deviceId)
        {
            try
            {
                return await _db.Queryable<DeviceEntity>()
                    .Where(d => d.DeviceId == deviceId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询设备失败: DeviceId={DeviceId}", deviceId);
                return null;
            }
        }

        /// <summary>
        /// 根据模型ID查询产品模型
        /// </summary>
        private async Task<ProductModelEntity?> GetProductModelAsync(long modelId)
        {
            try
            {
                return await _db.Queryable<ProductModelEntity>()
                    .Where(m => m.Id == modelId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询产品模型失败: ModelId={ModelId}", modelId);
                return null;
            }
        }

        /// <summary>
        /// 根据产品ID查询产品信息
        /// </summary>
        private async Task<ProductEntity?> GetProductByIdAsync(long productId)
        {
            try
            {
                return await _db.Queryable<ProductEntity>()
                    .Where(p => p.Id == productId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询产品失败: ProductId={ProductId}", productId);
                return null;
            }
        }

        /// <summary>
        /// 根据模型ID查询模型属性
        /// </summary>
        private async Task<List<ModelPropertyEntity>> GetModelPropertiesAsync(long modelId)
        {
            try
            {
                var properties = await _db.Queryable<ModelPropertyEntity>()
                    .Where(p => p.ModelId == modelId)
                    .ToListAsync();

                return properties ?? new List<ModelPropertyEntity>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询模型属性失败: ModelId={ModelId}", modelId);
                return new List<ModelPropertyEntity>();
            }
        }

        /// <summary>
        /// 获取自定义主题配置
        /// </summary>
        private async Task<DeviceTopicConfigEntity?> GetCustomTopicConfigAsync(string deviceId, string topic)
        {
            try
            {
                // 查询设备主题配置表，进行主题字段匹配（索引）
                var config = await _db.Queryable<DeviceTopicConfigEntity>()
                    .Where(c => c.DeviceId == deviceId && c.Topic == topic)
                    .FirstAsync();

                if (config != null)
                {
                    _logger.LogDebug("找到自定义主题配置: DeviceId={DeviceId}, Topic={Topic}, ConfigId={ConfigId}",
                        deviceId, topic, config.Id);
                }

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "查询自定义主题配置失败: DeviceId={DeviceId}, Topic={Topic}", deviceId, topic);
                return null;
            }
        }
    }
}
