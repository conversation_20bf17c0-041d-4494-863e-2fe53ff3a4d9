// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Alarm.Services;
using Admin.Multiplex.Contracts.Consts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;

namespace Admin.Application.AlarmServices;

/// <summary>
/// 告警事件服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class AlarmEventService(AlarmEventGeneratorService alarmEventGenerator) : ApplicationService
{
    private readonly AlarmEventGeneratorService _alarmEventGenerator = alarmEventGenerator;

    /// <summary>
    /// 同步系统告警事件
    /// 重新生成所有设备的告警事件，用于参数配置变更后的事件同步
    /// </summary>
    /// <returns>同步结果</returns>
    public async Task<AlarmEventSyncResult> SyncSystemAlarmEventsAsync()
    {
        try
        {
            Logger.LogInformation("开始同步系统告警事件");
            
            var totalEventCount = await _alarmEventGenerator.GenerateAlarmEventsForAllDevicesAsync();
            
            Logger.LogInformation("系统告警事件同步完成，共生成 {EventCount} 个事件", totalEventCount);
            
            return new AlarmEventSyncResult
            {
                Success = true,
                TotalEventCount = totalEventCount,
                Message = $"同步成功，共生成 {totalEventCount} 个告警事件"
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "同步系统告警事件时发生异常");
            
            return new AlarmEventSyncResult
            {
                Success = false,
                TotalEventCount = 0,
                Message = $"同步失败：{ex.Message}"
            };
        }
    }
}

/// <summary>
/// 告警事件同步结果
/// </summary>
public class AlarmEventSyncResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 生成的事件总数
    /// </summary>
    public int TotalEventCount { get; set; }

    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 同步时间
    /// </summary>
    public DateTime SyncTime { get; set; } = DateTime.Now;
}
