namespace Admin.Communication.Mqtt.Models
{
    /// <summary>
    /// MQTT订阅选项
    /// </summary>
    public class MqttSubscription
    {
        /// <summary>
        /// 主题过滤器
        /// </summary>
        public string TopicFilter { get; set; }
        
        /// <summary>
        /// 服务质量等级
        /// </summary>
        public byte QualityOfService { get; set; }
        
        /// <summary>
        /// 使用主题过滤器和QoS等级初始化订阅选项
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <param name="qos">服务质量等级</param>
        public MqttSubscription(string topicFilter, byte qos)
        {
            TopicFilter = topicFilter;
            QualityOfService = qos;
        }
        
        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{TopicFilter} (QoS: {QualityOfService})";
        }
        
        /// <summary>
        /// 隐式转换为元组
        /// </summary>
        /// <param name="subscription">订阅选项</param>
        /// <returns>元组</returns>
        public static implicit operator (string TopicFilter, byte QoS)(MqttSubscription subscription)
        {
            return (subscription.TopicFilter, subscription.QualityOfService);
        }
    }
} 