// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Modbus.Models;

namespace Admin.Communication.Modbus;

/// <summary>
/// Modbus指令示例
/// 提供常用的Modbus RTU指令示例和使用说明
/// </summary>
public static class ModbusInstructionExample
{
    /// <summary>
    /// 温湿度传感器指令示例
    /// </summary>
    public static class TemperatureHumiditySensor
    {
        /// <summary>
        /// 读取温湿度数据
        /// 设备地址：01，功能码：03，起始地址：0000，数量：0002
        /// </summary>
        public static string ReadTemperatureHumidity => "010300000002C40B";

        /// <summary>
        /// 示例响应数据
        /// 01 03 04 02 15 12 54 CRC16
        /// 温度：0x0215 = 533 (53.3°C)
        /// 湿度：0x1254 = 4692 (46.92%RH)
        /// </summary>
        public static string ExampleResponse => "01030402151254";

        /// <summary>
        /// 指令配置建议
        /// </summary>
        public static InstructionScheduleState GetRecommendedConfig()
        {
            return new InstructionScheduleState
            {
                InstructionName = "读取温湿度",
                Command = ReadTemperatureHumidity,
                ReadInterval = 10000,  // 10秒间隔
                ResponseTime = 2000,   // 2秒超时
                RetryCount = 3,        // 重试3次
                Status = InstructionExecutionStatus.Ready
            };
        }
    }

    /// <summary>
    /// 电力监测设备指令示例
    /// </summary>
    public static class PowerMonitor
    {
        /// <summary>
        /// 读取电压电流
        /// 设备地址：01，功能码：03，起始地址：0000，数量：0004
        /// </summary>
        public static string ReadVoltageCurrent => "010300000004440A";

        /// <summary>
        /// 读取功率数据
        /// 设备地址：01，功能码：03，起始地址：0010，数量：0002
        /// </summary>
        public static string ReadPower => "010300100002C407";

        /// <summary>
        /// 指令配置建议
        /// </summary>
        public static List<InstructionScheduleState> GetRecommendedConfigs()
        {
            return new List<InstructionScheduleState>
            {
                new InstructionScheduleState
                {
                    InstructionName = "读取电压电流",
                    Command = ReadVoltageCurrent,
                    ReadInterval = 5000,   // 5秒间隔
                    ResponseTime = 3000,   // 3秒超时
                    RetryCount = 3,
                    Status = InstructionExecutionStatus.Ready
                },
                new InstructionScheduleState
                {
                    InstructionName = "读取功率数据",
                    Command = ReadPower,
                    ReadInterval = 10000,  // 10秒间隔
                    ResponseTime = 3000,   // 3秒超时
                    RetryCount = 3,
                    Status = InstructionExecutionStatus.Ready
                }
            };
        }
    }

    /// <summary>
    /// 生成标准Modbus RTU读取指令
    /// </summary>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="functionCode">功能码</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="quantity">数量</param>
    /// <returns>十六进制指令字符串</returns>
    public static string GenerateReadCommand(byte slaveAddress, byte functionCode, ushort startAddress, ushort quantity)
    {
        var command = new List<byte>
        {
            slaveAddress,
            functionCode,
            (byte)(startAddress >> 8),
            (byte)(startAddress & 0xFF),
            (byte)(quantity >> 8),
            (byte)(quantity & 0xFF)
        };

        // 计算CRC16
        var crc = CalculateCRC16(command.ToArray());
        command.Add((byte)(crc & 0xFF));
        command.Add((byte)(crc >> 8));

        return Convert.ToHexString(command.ToArray());
    }

    /// <summary>
    /// 解析Modbus RTU响应
    /// </summary>
    /// <param name="response">响应数据</param>
    /// <returns>解析结果</returns>
    public static ModbusResponseParseResult ParseResponse(byte[] response)
    {
        if (response.Length < 5)
        {
            return new ModbusResponseParseResult
            {
                Success = false,
                ErrorMessage = "响应数据长度不足"
            };
        }

        var slaveAddress = response[0];
        var functionCode = response[1];
        var dataLength = response[2];

        if (response.Length < 3 + dataLength + 2)
        {
            return new ModbusResponseParseResult
            {
                Success = false,
                ErrorMessage = "响应数据长度与声明不符"
            };
        }

        var data = new byte[dataLength];
        Array.Copy(response, 3, data, 0, dataLength);

        var values = new List<ushort>();
        for (int i = 0; i < dataLength; i += 2)
        {
            if (i + 1 < dataLength)
            {
                var value = (ushort)((data[i] << 8) | data[i + 1]);
                values.Add(value);
            }
        }

        return new ModbusResponseParseResult
        {
            Success = true,
            SlaveAddress = slaveAddress,
            FunctionCode = functionCode,
            DataLength = dataLength,
            Values = values
        };
    }

    /// <summary>
    /// 计算CRC16校验码
    /// </summary>
    private static ushort CalculateCRC16(byte[] data)
    {
        ushort crc = 0xFFFF;
        for (int i = 0; i < data.Length; i++)
        {
            crc ^= data[i];
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= 0xA001;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }
        return crc;
    }
}

/// <summary>
/// Modbus响应解析结果
/// </summary>
public class ModbusResponseParseResult
{
    public bool Success { get; set; }
    public byte SlaveAddress { get; set; }
    public byte FunctionCode { get; set; }
    public byte DataLength { get; set; }
    public List<ushort> Values { get; set; } = new();
    public string? ErrorMessage { get; set; }
}
