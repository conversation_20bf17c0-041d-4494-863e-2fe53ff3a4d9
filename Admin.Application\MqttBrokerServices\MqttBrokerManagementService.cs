using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Mvc;
using Admin.Application.MqttBrokerServices.Dto;
using Admin.Multiplex.Contracts.Consts;
using Admin.Core.ExceptionExtensions;
using Admin.Multiplex.Contracts.Enums;
using System.Net;
using System.ComponentModel.DataAnnotations;

namespace Admin.Application.MqttBrokerServices
{
    /// <summary>
    /// MQTT代理服务管理应用层服务接口
    /// </summary>
    public interface IMqttBrokerManagementService
    {
        /// <summary>
        /// 启动代理服务
        /// </summary>
        Task<BrokerServiceOutput> StartAsync(StartBrokerInput input, CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止代理服务
        /// </summary>
        Task<BrokerServiceOutput> StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 重启代理服务
        /// </summary>
        Task<BrokerServiceOutput> RestartAsync(RestartBrokerInput input, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取代理服务状态
        /// </summary>
        Task<BrokerStatusOutput> GetStatusAsync();

        /// <summary>
        /// 获取代理服务配置
        /// </summary>
        Task<BrokerConfigurationOutput> GetConfigurationAsync();

        /// <summary>
        /// 更新代理服务配置
        /// </summary>
        Task<UpdateConfigurationResponse> UpdateConfigurationAsync(UpdateConfigurationRequest request);
    }

    /// <summary>
    /// MQTT代理服务管理应用层服务
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.MQTT)]
    public class MqttBrokerManagementService(
        IMqttBroker mqttBroker,
        ILogger<MqttBrokerManagementService> logger,
        IOptionsMonitor<MqttBrokerOptions> optionsMonitor) : ApplicationService, IMqttBrokerManagementService
    {
        private readonly IMqttBroker _mqttBroker = mqttBroker ?? throw new ArgumentNullException(nameof(mqttBroker));
        private readonly ILogger<MqttBrokerManagementService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly IOptionsMonitor<MqttBrokerOptions> _optionsMonitor = optionsMonitor ?? throw new ArgumentNullException(nameof(optionsMonitor));
        private DateTime? _startTime;
        private DateTime? _stopTime;

        // 需要重启的配置项
        private static readonly HashSet<string> _restartRequiredSettings = new()
        {
            "broker.port", "broker.enabletls", "broker.certificatepath", "broker.bindaddress"
        };

        /// <summary>
        /// 启动代理服务
        /// </summary>
        public async Task<BrokerServiceOutput> StartAsync(StartBrokerInput input, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("准备启动MQTT代理服务，端口: {Port}", input.Port);

            if (_mqttBroker.IsRunning)
            {
                _logger.LogWarning("MQTT代理服务已在运行中");
                return new BrokerServiceOutput
                {
                    Message = "MQTT代理服务已在运行中",
                    Port = _mqttBroker.Port,
                    IsRunning = true,
                    StartTime = _startTime
                };
            }

            // 验证端口参数
            var options = _optionsMonitor.CurrentValue;
            var port = input.Port > 0 ? input.Port : options.Port;
            if (port <= 0 || port > 65535)
            {
                throw PersistdValidateException.Message($"无效的端口号: {port}，端口号必须在1-65535之间");
            }

            try
            {
                // 应用配置更新（如果有提供）
                if (input.Configuration != null)
                {
                    ApplyConfigurationUpdates(input.Configuration);
                }

                // 启动代理服务
                await _mqttBroker.StartAsync(port, cancellationToken);
                _startTime = DateTime.UtcNow;

                _logger.LogInformation("MQTT代理服务启动成功，端口: {Port}", port);

                return new BrokerServiceOutput
                {
                    Message = "MQTT代理服务已启动",
                    Port = port,
                    IsRunning = true,
                    StartTime = _startTime
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动MQTT代理服务时发生错误");
                throw PersistdValidateException.Message($"启动MQTT代理服务失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止代理服务
        /// </summary>
        public async Task<BrokerServiceOutput> StopAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("准备停止MQTT代理服务");

            if (!_mqttBroker.IsRunning)
            {
                _logger.LogWarning("MQTT代理服务未在运行");
                return new BrokerServiceOutput
                {
                    Message = "MQTT代理服务未在运行",
                    IsRunning = false,
                    StopTime = _stopTime
                };
            }

            try
            {
                await _mqttBroker.StopAsync(cancellationToken);
                _stopTime = DateTime.UtcNow;

                _logger.LogInformation("MQTT代理服务停止成功");

                return new BrokerServiceOutput
                {
                    Message = "MQTT代理服务已停止",
                    IsRunning = false,
                    StopTime = _stopTime
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止MQTT代理服务时发生错误");
                throw PersistdValidateException.Message($"停止MQTT代理服务失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重启代理服务
        /// </summary>
        public async Task<BrokerServiceOutput> RestartAsync(RestartBrokerInput input, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("准备重启MQTT代理服务");

            try
            {
                var wasRunning = _mqttBroker.IsRunning;
                var previousUptime = wasRunning && _startTime.HasValue 
                    ? DateTime.UtcNow - _startTime.Value 
                    : TimeSpan.Zero;

                // 优雅停止
                if (wasRunning && input.GracefulShutdown)
                {
                    using var shutdownCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    shutdownCts.CancelAfter(TimeSpan.FromSeconds(input.ShutdownTimeout));
                    
                    await _mqttBroker.StopAsync(shutdownCts.Token);
                    _logger.LogInformation("代理服务已优雅停止");
                }
                else if (wasRunning)
                {
                    await _mqttBroker.StopAsync(cancellationToken);
                }

                // 重新启动
                var options = _optionsMonitor.CurrentValue;
                var port = options.Port;
                await _mqttBroker.StartAsync(port, cancellationToken);
                var restartTime = DateTime.UtcNow;
                _startTime = restartTime;

                _logger.LogInformation("MQTT代理服务重启成功，端口: {Port}", port);

                return new BrokerServiceOutput
                {
                    Message = "MQTT代理服务重启成功",
                    RestartTime = restartTime,
                    PreviousUptime = previousUptime.ToString(@"hh\:mm\:ss"),
                    Port = port,
                    IsRunning = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重启MQTT代理服务时发生错误");
                throw PersistdValidateException.Message($"重启MQTT代理服务失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取代理服务状态
        /// </summary>
        public async Task<BrokerStatusOutput> GetStatusAsync()
        {
            try
            {
                var isRunning = _mqttBroker.IsRunning;
                var uptime = isRunning && _startTime.HasValue 
                    ? DateTime.UtcNow - _startTime.Value 
                    : TimeSpan.Zero;

                var options = _optionsMonitor.CurrentValue;

                return new BrokerStatusOutput
                {
                    IsRunning = isRunning,
                    Port = _mqttBroker.Port,
                    StartTime = _startTime,
                    Uptime = uptime.ToString(@"hh\:mm\:ss"),
                    Version = "1.0.0", // 可以从程序集信息获取
                    ConnectedClientCount = _mqttBroker.GetConnectedClientCount(),
                    Configuration = new BrokerStatusConfigurationOutput
                    {
                        MaxConnections = options.MaxConnections,
                        MaxConnectionsPerIp = options.MaxConnectionsPerIp,
                        ConnectionTimeout = options.ConnectionTimeout,
                        EnableRetainedMessages = options.EnableRetainedMessages,
                        EnableStatistics = options.EnableStatistics,
                        AllowAnonymousAccess = options.AllowAnonymousAccess,
                        UseTls = options.UseTls,
                        MaxMessageSize = options.MaxMessageSize,
                        MaxTopicLength = options.MaxTopicLength
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取代理服务状态时发生错误");
                throw PersistdValidateException.Message($"获取代理服务状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取代理服务配置
        /// </summary>
        public async Task<BrokerConfigurationOutput> GetConfigurationAsync()
        {
            try
            {
                var currentOptions = _optionsMonitor.CurrentValue;

                return new BrokerConfigurationOutput
                {
                    Broker = new BrokerConfigurationSectionOutput
                    {
                        Port = currentOptions.Port,
                        MaxConnections = currentOptions.MaxConnections,
                        MaxConnectionsPerIp = currentOptions.MaxConnectionsPerIp,
                        ConnectionTimeout = currentOptions.ConnectionTimeout,
                        UseTls = currentOptions.UseTls,
                        CertificatePath = currentOptions.CertificatePath,
                        AllowAnonymousAccess = currentOptions.AllowAnonymousAccess,
                        EnableRetainedMessages = currentOptions.EnableRetainedMessages,
                        EnableStatistics = currentOptions.EnableStatistics,
                        MaxTopicLength = currentOptions.MaxTopicLength,
                        MaxMessageSize = currentOptions.MaxMessageSize,
                        BindAddress = currentOptions.BindAddress?.ToString() ?? "0.0.0.0",
                        EnableWebSockets = currentOptions.EnableWebSockets,
                        WebSocketPort = currentOptions.WebSocketPort,
                        WebSocketPath = currentOptions.WebSocketPath
                    },
                    Authentication = new AuthenticationConfigurationSectionOutput
                    {
                        DefaultUsername = currentOptions.Username,
                        DefaultPassword = "***masked***", // 不返回真实密码
                        RequireAuthentication = !currentOptions.AllowAnonymousAccess,
                        UserCount = currentOptions.Users?.Count ?? 0
                    },
                    Acl = new AclConfigurationSectionOutput
                    {
                        EnableAcl = currentOptions.AclRules?.Any() == true,
                        RuleCount = currentOptions.AclRules?.Count ?? 0
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取代理服务配置时发生错误");
                throw PersistdValidateException.Message($"获取代理服务配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新代理服务配置
        /// </summary>
        public async Task<UpdateConfigurationResponse> UpdateConfigurationAsync(UpdateConfigurationRequest request)
        {
            try
            {
                _logger.LogInformation("开始更新MQTT配置");

                // 验证配置
                ValidateConfiguration(request);

                // 模拟配置更新过程
                var changedSettings = await SimulateConfigurationUpdatesAsync(request);
                var requiresRestart = changedSettings.Any(setting => _restartRequiredSettings.Contains(setting.ToLower()));

                var response = new UpdateConfigurationResponse
                {
                    UpdatedTime = DateTime.UtcNow,
                    RequiresRestart = requiresRestart,
                    ChangedSettings = changedSettings
                };

                _logger.LogInformation("MQTT配置更新成功，更改了 {Count} 项设置，需要重启: {RequiresRestart}", 
                    changedSettings.Count, requiresRestart);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新MQTT配置失败");
                throw PersistdValidateException.Message($"更新配置失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 应用配置更新
        /// </summary>
        private void ApplyConfigurationUpdates(BrokerStartConfigurationInput config)
        {
            var options = _optionsMonitor.CurrentValue;
            
            if (config.MaxConnections.HasValue)
                options.MaxConnections = config.MaxConnections.Value;
            
            if (config.MaxConnectionsPerIp.HasValue)
                options.MaxConnectionsPerIp = config.MaxConnectionsPerIp.Value;
            
            if (config.ConnectionTimeout.HasValue)
                options.ConnectionTimeout = config.ConnectionTimeout.Value;
            
            if (config.EnableTls.HasValue)
                options.UseTls = config.EnableTls.Value;
            
            if (config.AllowAnonymousAccess.HasValue)
                options.AllowAnonymousAccess = config.AllowAnonymousAccess.Value;
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        private void ValidateConfiguration(UpdateConfigurationRequest request)
        {
            var errors = new List<string>();

            // 验证代理配置
            if (request.Broker != null)
            {
                ValidateBrokerConfiguration(request.Broker, errors);
            }

            // 验证认证配置
            if (request.Authentication != null)
            {
                ValidateAuthenticationConfiguration(request.Authentication, errors);
            }

            // 验证ACL配置
            if (request.Acl != null)
            {
                ValidateAclConfiguration(request.Acl, errors);
            }

            if (errors.Any())
            {
                throw PersistdValidateException.Message($"配置验证失败: {string.Join(", ", errors)}");
            }
        }

        private void ValidateBrokerConfiguration(BrokerConfigurationUpdateDto broker, List<string> errors)
        {
            if (broker.Port.HasValue && (broker.Port.Value <= 0 || broker.Port.Value > 65535))
            {
                errors.Add("端口号必须在1-65535之间");
            }

            if (broker.MaxConnections.HasValue && broker.MaxConnections.Value <= 0)
            {
                errors.Add("最大连接数必须大于0");
            }

            if (broker.MaxConnectionsPerIp.HasValue && broker.MaxConnectionsPerIp.Value <= 0)
            {
                errors.Add("单个IP最大连接数必须大于0");
            }

            if (broker.ConnectionTimeout.HasValue && broker.ConnectionTimeout.Value <= 0)
            {
                errors.Add("连接超时时间必须大于0");
            }

            if (broker.EnableTls == true && string.IsNullOrEmpty(broker.CertificatePath))
            {
                errors.Add("启用TLS时必须提供证书路径");
            }

            if (!string.IsNullOrEmpty(broker.BindAddress) && !IPAddress.TryParse(broker.BindAddress, out _))
            {
                errors.Add("绑定地址格式无效");
            }
        }

        private void ValidateAuthenticationConfiguration(AuthenticationConfigurationUpdateDto auth, List<string> errors)
        {
            // 认证配置验证逻辑
            if (auth.RequireAuthentication == true && string.IsNullOrEmpty(auth.DefaultUsername))
            {
                errors.Add("启用认证时必须设置默认用户名");
            }
        }

        private void ValidateAclConfiguration(AclConfigurationUpdateDto acl, List<string> errors)
        {
            if (!string.IsNullOrEmpty(acl.DefaultPolicy) && 
                acl.DefaultPolicy != "allow" && acl.DefaultPolicy != "deny")
            {
                errors.Add("默认策略必须是 'allow' 或 'deny'");
            }
        }

        private async Task<List<string>> SimulateConfigurationUpdatesAsync(UpdateConfigurationRequest request)
        {
            var changedSettings = new List<string>();
            var currentOptions = _optionsMonitor.CurrentValue;

            // 模拟代理配置更新
            if (request.Broker != null)
            {
                changedSettings.AddRange(SimulateBrokerConfigurationUpdates(currentOptions, request.Broker));
            }

            // 模拟认证配置更新
            if (request.Authentication != null)
            {
                changedSettings.AddRange(SimulateAuthenticationConfigurationUpdates(currentOptions, request.Authentication));
            }

            // 模拟ACL配置更新
            if (request.Acl != null)
            {
                changedSettings.AddRange(SimulateAclConfigurationUpdates(currentOptions, request.Acl));
            }

            // 模拟异步操作
            await Task.Delay(100);

            return changedSettings;
        }

        private List<string> SimulateBrokerConfigurationUpdates(MqttBrokerOptions options, BrokerConfigurationUpdateDto broker)
        {
            var changedSettings = new List<string>();

            if (broker.Port.HasValue && options.Port != broker.Port.Value)
            {
                changedSettings.Add("broker.port");
            }

            if (broker.MaxConnections.HasValue && options.MaxConnections != broker.MaxConnections.Value)
            {
                changedSettings.Add("broker.maxConnections");
            }

            if (broker.MaxConnectionsPerIp.HasValue && options.MaxConnectionsPerIp != broker.MaxConnectionsPerIp.Value)
            {
                changedSettings.Add("broker.maxConnectionsPerIp");
            }

            if (broker.ConnectionTimeout.HasValue && options.ConnectionTimeout != broker.ConnectionTimeout.Value)
            {
                changedSettings.Add("broker.connectionTimeout");
            }

            if (broker.EnableTls.HasValue && options.UseTls != broker.EnableTls.Value)
            {
                changedSettings.Add("broker.enableTls");
            }

            if (!string.IsNullOrEmpty(broker.CertificatePath) && options.CertificatePath != broker.CertificatePath)
            {
                changedSettings.Add("broker.certificatePath");
            }

            if (broker.AllowAnonymousAccess.HasValue && options.AllowAnonymousAccess != broker.AllowAnonymousAccess.Value)
            {
                changedSettings.Add("broker.allowAnonymousAccess");
            }

            if (broker.EnableRetainedMessages.HasValue && options.EnableRetainedMessages != broker.EnableRetainedMessages.Value)
            {
                changedSettings.Add("broker.enableRetainedMessages");
            }

            if (broker.EnableStatistics.HasValue && options.EnableStatistics != broker.EnableStatistics.Value)
            {
                changedSettings.Add("broker.enableStatistics");
            }

            if (broker.MaxTopicLength.HasValue && options.MaxTopicLength != broker.MaxTopicLength.Value)
            {
                changedSettings.Add("broker.maxTopicLength");
            }

            if (broker.MaxMessageSize.HasValue && options.MaxMessageSize != broker.MaxMessageSize.Value)
            {
                changedSettings.Add("broker.maxMessageSize");
            }

            if (!string.IsNullOrEmpty(broker.BindAddress) && options.BindAddress?.ToString() != broker.BindAddress)
            {
                changedSettings.Add("broker.bindAddress");
            }

            return changedSettings;
        }

        private List<string> SimulateAuthenticationConfigurationUpdates(MqttBrokerOptions options, AuthenticationConfigurationUpdateDto auth)
        {
            var changedSettings = new List<string>();

            if (!string.IsNullOrEmpty(auth.DefaultUsername) && options.Username != auth.DefaultUsername)
            {
                changedSettings.Add("authentication.defaultUsername");
            }

            if (!string.IsNullOrEmpty(auth.DefaultPassword) && options.Password != auth.DefaultPassword)
            {
                changedSettings.Add("authentication.defaultPassword");
            }

            if (auth.RequireAuthentication.HasValue && options.AllowAnonymousAccess == auth.RequireAuthentication.Value)
            {
                changedSettings.Add("authentication.requireAuthentication");
            }

            return changedSettings;
        }

        private List<string> SimulateAclConfigurationUpdates(MqttBrokerOptions options, AclConfigurationUpdateDto acl)
        {
            var changedSettings = new List<string>();

            if (acl.EnableAcl.HasValue && (options.AclRules?.Any() == true) != acl.EnableAcl.Value)
            {
                changedSettings.Add("acl.enableAcl");
            }

            if (!string.IsNullOrEmpty(acl.DefaultPolicy))
            {
                changedSettings.Add("acl.defaultPolicy");
            }

            return changedSettings;
        }

        #endregion
    }
} 