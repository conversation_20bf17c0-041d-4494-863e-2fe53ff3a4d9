﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Models.Results;
/// <summary>
/// 连接结果
/// </summary>
public class ConnectionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 是否有现有连接被替换
    /// </summary>
    public bool ExistingConnectionReplaced { get; set; }

    /// <summary>
    /// 会话是否存在
    /// </summary>
    public bool SessionPresent { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static ConnectionResult Success(bool sessionPresent = false, bool existingReplaced = false)
    {
        return new ConnectionResult
        {
            IsSuccess = true,
            SessionPresent = sessionPresent,
            ExistingConnectionReplaced = existingReplaced,
            Message = "连接成功"
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static ConnectionResult Failure(string message)
    {
        return new ConnectionResult
        {
            IsSuccess = false,
            Message = message
        };
    }
}
