﻿{
  "AllowedHosts": "*",
  "SnowflakeIdOptions": {
    "WorkId": "1"
  },
  "FileSystemOptions": {
    "LimitSize": 1024, //单文件最大值
    "Path": "E:/文件上传测试" //为空时,默认当前程序运行目录/FileSystem下
  },
  "EnableGlobalUnitOfWork": true,
  "JwtOptions": {
    "SecretKey": "49BA59ABBE56E05749BA59ABBE56E057",
    "ExpiredMinutes": 60,
    "RefreshMinutes": 30 //RefreshMinutes必须小于ExpiredMinutes，如果为0则取消无感刷新
  },
  "ConnectionOption1s": [
    {
      "DbType": "Mysql",
      "ConnectionString": "Data Source=localhost;Database=purest;User ID=root;Password=******;pooling=true;port=3306;sslmode=none;CharSet=utf8;AllowPublicKeyRetrieval=True;"
    }
  ],
  "OAuth2Options": [
    {
      "Name": "gitee",
      "ClientId": "ClientId",
      "ClientSecret": "ClientSecret",
      "RedirectUri": "http://************:8848/oauth-callback"
    },
    {
      "Name": "github",
      "ClientId": "ClientId",
      "ClientSecret": "ClientSecret",
      "RedirectUri": "http://************:8848/oauth-callback"
    }
  ],
  "Mqtt": {
    "DataProcessing": {
      "CustomTopicPrefix": "/custom/",
      "ForwardDataTopicsInProduction": false,
      "ForwardAllTopicsInDevelopment": true
    }
  }
}
