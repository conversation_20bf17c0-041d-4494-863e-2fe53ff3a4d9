using System.ComponentModel.DataAnnotations;

namespace Admin.Application.MqttBrokerServices.Dto
{
    /// <summary>
    /// 连接信息DTO
    /// </summary>
    public class ConnectionInfoDto
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// IP地址和端口
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime ConnectedTime { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; }

        /// <summary>
        /// 保持连接时间(秒)
        /// </summary>
        public int KeepAlive { get; set; }

        /// <summary>
        /// 是否使用清除会话标志
        /// </summary>
        public bool CleanSession { get; set; }

        /// <summary>
        /// 协议版本
        /// </summary>
        public string ProtocolVersion { get; set; } = string.Empty;

        /// <summary>
        /// 连接状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 已订阅的主题列表
        /// </summary>
        public List<string> SubscribedTopics { get; set; } = new();
    }

    /// <summary>
    /// 连接详细信息DTO
    /// </summary>
    public class ConnectionDetailDto : ConnectionInfoDto
    {
        /// <summary>
        /// 连接持续时间（格式化字符串）
        /// </summary>
        public string ConnectionDuration { get; set; } = string.Empty;

        /// <summary>
        /// 订阅详情列表
        /// </summary>
        public List<SubscriptionDetailDto> SubscriptionDetails { get; set; } = new();

        /// <summary>
        /// 连接统计信息
        /// </summary>
        public ClientConnectionStatisticsDto Statistics { get; set; } = new();
    }

    /// <summary>
    /// 订阅详情DTO
    /// </summary>
    public class SubscriptionDetailDto
    {
        /// <summary>
        /// 主题过滤器
        /// </summary>
        public string TopicFilter { get; set; } = string.Empty;

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int Qos { get; set; }

        /// <summary>
        /// 订阅时间
        /// </summary>
        public DateTime SubscribedTime { get; set; }
    }

    /// <summary>
    /// 客户端连接统计信息DTO
    /// </summary>
    public class ClientConnectionStatisticsDto
    {
        /// <summary>
        /// 发送的消息数量
        /// </summary>
        public long MessagesSent { get; set; }

        /// <summary>
        /// 接收的消息数量
        /// </summary>
        public long MessagesReceived { get; set; }

        /// <summary>
        /// 传输的字节数
        /// </summary>
        public long BytesTransferred { get; set; }

        /// <summary>
        /// 最后消息时间
        /// </summary>
        public DateTime? LastMessageTime { get; set; }
    }

    /// <summary>
    /// 连接查询请求DTO
    /// </summary>
    public class GetConnectionsRequest : PagedRequestDto
    {
        /// <summary>
        /// 客户端ID过滤
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 用户名过滤
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// IP地址过滤
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// 连接状态过滤
        /// </summary>
        public string? Status { get; set; }
    }

    /// <summary>
    /// 连接列表响应DTO
    /// </summary>
    public class GetConnectionsResponse : PagedResponseDto<ConnectionInfoDto>
    {
        public GetConnectionsResponse(List<ConnectionInfoDto> connections, int totalCount, int page, int pageSize)
            : base(connections, totalCount, page, pageSize)
        {
        }
    }

    /// <summary>
    /// 断开连接请求DTO
    /// </summary>
    public class DisconnectConnectionRequest
    {
        /// <summary>
        /// 断开原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 是否发送断开消息
        /// </summary>
        public bool SendDisconnectMessage { get; set; } = true;
    }

    /// <summary>
    /// 批量断开连接请求DTO
    /// </summary>
    public class BatchDisconnectRequest
    {
        /// <summary>
        /// 客户端ID列表
        /// </summary>
        [Required]
        public List<string> ClientIds { get; set; } = new();

        /// <summary>
        /// 断开原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 是否发送断开消息
        /// </summary>
        public bool SendDisconnectMessage { get; set; } = true;
    }

    /// <summary>
    /// 断开连接结果DTO
    /// </summary>
    public class DisconnectResult
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 断开时间
        /// </summary>
        public DateTime? DisconnectTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? Error { get; set; }
    }

    /// <summary>
    /// 批量断开连接响应DTO
    /// </summary>
    public class BatchDisconnectResponse
    {
        /// <summary>
        /// 请求总数
        /// </summary>
        public int TotalRequested { get; set; }

        /// <summary>
        /// 成功断开的连接数
        /// </summary>
        public int SuccessfulDisconnects { get; set; }

        /// <summary>
        /// 失败的断开连接数
        /// </summary>
        public int FailedDisconnects { get; set; }

        /// <summary>
        /// 详细结果
        /// </summary>
        public List<DisconnectResult> Results { get; set; } = new();
    }

    /// <summary>
    /// 基础分页请求DTO
    /// </summary>
    public abstract class PagedRequestDto
    {
        /// <summary>
        /// 页码（默认：1）
        /// </summary>
        [Range(1, int.MaxValue)]
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小（默认：20）
        /// </summary>
        [Range(1, 100)]
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// 基础分页响应DTO
    /// </summary>
    public abstract class PagedResponseDto<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Items { get; set; }

        /// <summary>
        /// 分页信息
        /// </summary>
        public PaginationDto Pagination { get; set; }

        protected PagedResponseDto(List<T> items, int totalCount, int page, int pageSize)
        {
            Items = items;
            Pagination = new PaginationDto
            {
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
    }

    /// <summary>
    /// 分页信息DTO
    /// </summary>
    public class PaginationDto
    {
        /// <summary>
        /// 当前页
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 连接管理操作结果
    /// </summary>
    public class ConnectionManagementResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public static ConnectionManagementResult Success(string message, object? data = null)
        {
            return new ConnectionManagementResult
            {
                IsSuccess = true,
                Message = message,
                Data = data
            };
        }

        public static ConnectionManagementResult Error(string errorMessage)
        {
            return new ConnectionManagementResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }
} 