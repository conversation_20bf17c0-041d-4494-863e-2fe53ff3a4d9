// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.AlarmServices.Dto;
using Admin.Core.ExceptionExtensions;
using Admin.Multiplex.Contracts.Consts;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using Volo.Abp.Application.Services;

/*
 * 告警配置说明：
 *
 * 1. 参数告警配置规则：
 *    - MonitorStatus: 总开关（0=禁用，1=启用）
 *    - AlarmUpperLimit/AlarmLowerLimit: 告警阈值（null=不检测）
 *    - WarningUpperLimit/WarningLowerLimit: 预警阈值（null=不检测）
 *    - 清除阈值: 独立的清除阈值，避免告警抖动
 *
 * 2. 配置示例：
 *    // 只监控数据，不告警不预警
 *    MonitorStatus = 1, AlarmUpperLimit = null, AlarmLowerLimit = null,
 *    WarningUpperLimit = null, WarningLowerLimit = null
 *
 *    // 只要预警，不要告警
 *    MonitorStatus = 1, AlarmUpperLimit = null, AlarmLowerLimit = null,
 *    WarningUpperLimit = 80, WarningLowerLimit = 10
 *
 *    // 只检测上限，不检测下限
 *    MonitorStatus = 1, AlarmUpperLimit = 100, AlarmLowerLimit = null,
 *    WarningUpperLimit = 90, WarningLowerLimit = null
 *
 * 3. 告警标题规则：
 *    - 通讯失败: "{设备名} 通讯失败" (例如: "一号精密空调 通讯失败")
 *    - 参数超限: "{设备名} {参数名} {上限/下限}{告警/预警}" (例如: "7号温湿度 湿度 下限告警")
 */

namespace Admin.Application.AlarmServices;

/// <summary>
/// 告警历史服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class AlarmHistoryService(ISqlSugarClient db) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;

    /// <summary>
    /// 获取待确认告警
    /// </summary>
    /// <param name="input">分页参数</param>
    /// <returns>待确认告警列表</returns>
    public async Task<PagedList<AlarmHistoryOutput>> GetPendingConfirmationAlarmsAsync(AlarmHistoryPagedInput input)
    {
        var query = _db.Queryable<AlarmHistoryEntity>()
            .LeftJoin<AlarmEventEntity>((a, e) => a.EventId == e.Id)
            .LeftJoin<DeviceEntity>((a, e, d) => a.DeviceId == d.Id)
            .Where((a, e, d) => a.AlarmStatus == (int)AlarmStatusEnum.PendingConfirmation)
            .OrderByDescending((a, e, d) => a.AlarmTime)
            .Select((a, e, d) => new AlarmHistoryOutput
            {
                Id = a.Id,
                EventId = a.EventId,
                DeviceId = a.DeviceId,
                DeviceName = d.DeviceName,
                AlarmDescription = a.AlarmDescription,
                AlarmValue = a.AlarmValue,
                AlarmLevel = e.Level,
                AlarmLevelDescription = GetAlarmLevelDescription(e.Level),
                AlarmStatus = a.AlarmStatus,
                AlarmStatusDescription = "待确认",
                AlarmTime = a.AlarmTime,
                ConfirmedPeople = a.ConfirmedPeople,
                ConfirmedTime = a.ConfirmedTime,
                ReleaseTime = a.ReleaseTime,
                ReleaseValue = a.ReleaseValue,
                ReleaseDescription = a.ReleaseDescription
            });

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 获取已确认告警
    /// </summary>
    /// <param name="input">分页参数</param>
    /// <returns>已确认告警列表</returns>
    public async Task<PagedList<AlarmHistoryOutput>> GetConfirmedAlarmsAsync(AlarmHistoryPagedInput input)
    {
        var query = _db.Queryable<AlarmHistoryEntity>()
            .LeftJoin<AlarmEventEntity>((a, e) => a.EventId == e.Id)
            .LeftJoin<DeviceEntity>((a, e, d) => a.DeviceId == d.Id)
            .Where((a, e, d) => a.AlarmStatus == (int)AlarmStatusEnum.Confirmed)
            .OrderByDescending((a, e, d) => a.AlarmTime)
            .Select((a, e, d) => new AlarmHistoryOutput
            {
                Id = a.Id,
                EventId = a.EventId,
                DeviceId = a.DeviceId,
                DeviceName = d.DeviceName,
                AlarmDescription = a.AlarmDescription,
                AlarmValue = a.AlarmValue,
                AlarmLevel = e.Level,
                AlarmLevelDescription = GetAlarmLevelDescription(e.Level),
                AlarmStatus = a.AlarmStatus,
                AlarmStatusDescription = "已确认",
                AlarmTime = a.AlarmTime,
                ConfirmedPeople = a.ConfirmedPeople,
                ConfirmedTime = a.ConfirmedTime,
                ReleaseTime = a.ReleaseTime,
                ReleaseValue = a.ReleaseValue,
                ReleaseDescription = a.ReleaseDescription
            });

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 获取未确认但已解除的告警
    /// </summary>
    /// <param name="input">分页参数</param>
    /// <returns>未确认但已解除的告警列表</returns>
    public async Task<PagedList<AlarmHistoryOutput>> GetUnconfirmedButReleasedAlarmsAsync(AlarmHistoryPagedInput input)
    {
        var query = _db.Queryable<AlarmHistoryEntity>()
            .LeftJoin<AlarmEventEntity>((a, e) => a.EventId == e.Id)
            .LeftJoin<DeviceEntity>((a, e, d) => a.DeviceId == d.Id)
            .Where((a, e, d) => a.AlarmStatus == (int)AlarmStatusEnum.PendingConfirmation && a.ReleaseTime != null)
            .OrderByDescending((a, e, d) => a.AlarmTime)
            .Select((a, e, d) => new AlarmHistoryOutput
            {
                Id = a.Id,
                EventId = a.EventId,
                DeviceId = a.DeviceId,
                DeviceName = d.DeviceName,
                AlarmDescription = a.AlarmDescription,
                AlarmValue = a.AlarmValue,
                AlarmLevel = e.Level,
                AlarmLevelDescription = GetAlarmLevelDescription(e.Level),
                AlarmStatus = a.AlarmStatus,
                AlarmStatusDescription = "待确认",
                AlarmTime = a.AlarmTime,
                ConfirmedPeople = a.ConfirmedPeople,
                ConfirmedTime = a.ConfirmedTime,
                ReleaseTime = a.ReleaseTime,
                ReleaseValue = a.ReleaseValue,
                ReleaseDescription = a.ReleaseDescription
            });

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 确认告警
    /// </summary>
    /// <param name="input">确认输入</param>
    /// <returns>确认结果</returns>
    public async Task<bool> ConfirmAlarmsAsync(ConfirmAlarmInput input)
    {
        if (input.AlarmIds == null || input.AlarmIds.Count == 0)
            throw PersistdValidateException.Message("请选择要确认的告警");

        if (string.IsNullOrEmpty(input.ConfirmedPeople))
            throw PersistdValidateException.Message("请提供确认人信息");

        var confirmTime = DateTime.Now;

        var result = await _db.Updateable<AlarmHistoryEntity>()
            .SetColumns(a => new AlarmHistoryEntity
            {
                AlarmStatus = (int)AlarmStatusEnum.Confirmed,
                ConfirmedPeople = input.ConfirmedPeople,
                ConfirmedTime = confirmTime
            })
            .Where(a => input.AlarmIds.Contains(a.Id) && a.AlarmStatus == (int)AlarmStatusEnum.PendingConfirmation)
            .ExecuteCommandAsync();

        return result > 0;
    }



    #region 私有方法

    /// <summary>
    /// 获取告警级别描述
    /// </summary>
    private string GetAlarmLevelDescription(int alarmLevel)
    {
        return alarmLevel switch
        {
            1 => "紧急",
            2 => "严重",
            3 => "一般",
            4 => "预警",
            _ => "未知"
        };
    }

    #endregion
}
