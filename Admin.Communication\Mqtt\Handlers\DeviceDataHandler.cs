using System;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Microsoft.Extensions.Logging;
using Admin.SqlSugar.Entity.Business.LOT;
using Admin.SqlSugar;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using SqlSugar;
using Admin.Communication.Mqtt.DataParsers;
using Admin.Communication.Mqtt.TopicResolvers;
using Admin.Communication.Mqtt.TopicFilters;
using Admin.Communication.Modbus;
using Admin.Multiplex.Contracts.Enums;
using Admin.Communication.Alarm.Detectors;
using Admin.Communication.Alarm.Services;

namespace Admin.Communication.Mqtt.Handlers
{
    /// <summary>
    /// 设备数据处理器 - 可扩展架构版本
    /// 使用策略模式和工厂模式实现可扩展的数据解析架构
    /// 支持内置主题和配置主题，支持多种数据格式（JSON、HEX等）
    ///
    /// 架构组件：
    /// - TopicResolver: 解析主题类型和数据格式
    /// - DataParserFactory: 根据数据格式创建对应解析器
    /// - IDataParser: 数据解析器接口，支持JSON、HEX等格式
    ///
    /// 支持的主题模式：
    /// - /devices/{device_id}/sys/properties/report (直连设备)
    /// - /devices/{device_id}/sys/gateway/sub_devices/properties/report (网关设备)
    /// - 配置主题（通过DeviceTopicConfig表配置）
    /// </summary>
    public class DeviceDataHandler : BaseMessageHandler
    {
        private readonly ISqlSugarClient _db;
        private readonly ITopicResolver _topicResolver;
        private readonly IDataParserFactory _dataParserFactory;
        private readonly ITopicFilter _topicFilter;
        private readonly IModbusResponseHandler _modbusResponseHandler;
        private readonly ParameterAlarmDetector _parameterAlarmDetector;
        private readonly AlarmService _alarmService;

        /// <summary>
        /// 处理器名称
        /// </summary>
        public override string Name => "DeviceDataHandler";

        /// <summary>
        /// 处理器描述
        /// </summary>
        public override string Description => "设备数据处理器，专门处理设备上报的数据消息";

        /// <summary>
        /// 初始化设备数据处理器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="db">数据库客户端</param>
        /// <param name="topicResolver">主题解析器</param>
        /// <param name="dataParserFactory">数据解析器工厂</param>
        /// <param name="topicFilter">主题过滤器</param>
        /// <param name="modbusResponseHandler">Modbus响应处理器</param>
        /// <param name="parameterAlarmDetector">参数告警检测器</param>
        /// <param name="alarmService">告警服务</param>
        public DeviceDataHandler(ILogger<DeviceDataHandler> logger, ISqlSugarClient db,
            ITopicResolver topicResolver, IDataParserFactory dataParserFactory, ITopicFilter topicFilter,
            IModbusResponseHandler modbusResponseHandler, ParameterAlarmDetector parameterAlarmDetector,
            AlarmService alarmService) : base(logger)
        {
            _db = db;
            _topicResolver = topicResolver;
            _dataParserFactory = dataParserFactory;
            _topicFilter = topicFilter;
            _modbusResponseHandler = modbusResponseHandler;
            _parameterAlarmDetector = parameterAlarmDetector;
            _alarmService = alarmService;

            // 根据业务注释，处理以下主题：
            // /devices/{device_id}/sys/properties/report - 直连设备
            // /devices/{device_id}/sys/gateway/sub_devices/properties/report - 网关设备
            // /devices/{device_id}/modbus/command/up - Modbus响应
            SupportedTopicPatterns = new[]
            {
                "/devices/+/sys/properties/report",
                "/devices/+/sys/gateway/sub_devices/properties/report",
                "/devices/+/modbus/command/up"
            };

            // 只处理发布消息
            SupportedMessageTypes = new[] { typeof(MqttPublishMessage) };

            // 设置较高优先级，优先处理设备数据
            Priority = 10;
        }

        /// <summary>
        /// 处理设备数据消息的核心逻辑
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>处理结果</returns>
        protected override async Task<MqttMessageHandleResult> ProcessMessageAsync(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return MqttMessageHandleResult.Failure("消息类型不是发布消息");
            }

            try
            {
                var payloadSize = publishMessage.Payload?.Length ?? 0;
                _logger.LogDebug("处理设备数据消息: 主题={Topic}, 载荷大小={PayloadSize}字节",
                    publishMessage.Topic, payloadSize);

                // 直接处理设备消息
                await ProcessDeviceMessageAsync(publishMessage, context);

                return MqttMessageHandleResult.Success();
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "设备数据JSON格式错误: 主题={Topic}", publishMessage.Topic);
                return MqttMessageHandleResult.Failure("设备数据JSON格式错误", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备数据时发生异常: 主题={Topic}", publishMessage.Topic);
                return MqttMessageHandleResult.Failure($"处理设备数据异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化时的自定义逻辑
        /// </summary>
        /// <returns>初始化任务</returns>
        protected override async Task OnInitializeAsync()
        {
            _logger.LogInformation("设备数据处理器正在初始化...");
            _logger.LogInformation("支持的主题模式: {Patterns}", string.Join(", ", SupportedTopicPatterns));

            // 这里可以添加初始化逻辑，比如：
            // - 连接到数据库
            // - 初始化缓存
            // - 加载配置

            await Task.CompletedTask;
            _logger.LogInformation("设备数据处理器初始化完成");
        }

        /// <summary>
        /// 销毁时的自定义逻辑
        /// </summary>
        /// <returns>销毁任务</returns>
        protected override async Task OnDisposeAsync()
        {
            _logger.LogInformation("设备数据处理器正在销毁...");
            
            // 这里可以添加清理逻辑，比如：
            // - 关闭数据库连接
            // - 清理缓存
            // - 保存状态
            
            await Task.CompletedTask;
            _logger.LogInformation("设备数据处理器销毁完成");
        }

        #region 私有方法


        /// <summary>
        /// 统一处理设备消息 - 使用新的可扩展架构
        /// </summary>
        /// <param name="publishMessage">原始消息</param>
        /// <param name="context">消息上下文</param>
        /// <returns>处理任务</returns>
        private async Task ProcessDeviceMessageAsync(MqttPublishMessage publishMessage, MqttMessageContext context)
        {
            var topic = publishMessage.Topic;
            var deviceId = ExtractDeviceIdFromTopic(topic);

            if (string.IsNullOrEmpty(deviceId))
            {
                _logger.LogWarning("无法从主题中提取设备ID: {Topic}", topic);
                return;
            }

            _logger.LogInformation("开始处理设备消息: 主题={Topic}, 设备ID={DeviceId}", topic, deviceId);

            try
            {
                // 检查是否是Modbus响应主题
                if (topic.Contains("/modbus/command/up"))
                {
                    await HandleModbusResponseAsync(deviceId, publishMessage.Payload);
                    return;
                }

                // 1. 使用主题过滤器判断是否需要处理
                var filterResult = await _topicFilter.ShouldProcessAsync(topic, deviceId);
                if (filterResult.ShouldProcess == false)
                {
                    _logger.LogDebug("主题过滤器决定不处理此消息: {Reason}, 转发={ShouldForward}",
                        filterResult.Reason, filterResult.ShouldForward);

                    // TODO: 根据ShouldForward决定是否转发给客户端
                    // if (filterResult.ShouldForward) { /* 转发逻辑 */ }
                    return;
                }

                _logger.LogInformation("主题过滤通过: 类型={TopicType}, 原因={Reason}",
                    filterResult.TopicType, filterResult.Reason);

                // 2. 使用主题解析器解析主题并获取上下文
                var resolveResult = await _topicResolver.ResolveAsync(topic, deviceId);
                if (!resolveResult.IsSuccess || resolveResult.ParseContext == null)
                {
                    _logger.LogWarning("主题解析失败: {ErrorMessage}", resolveResult.ErrorMessage);
                    return;
                }

                var parseContext = resolveResult.ParseContext;
                _logger.LogInformation("主题解析成功: 类型={TopicType}, 数据格式={DataFormat}",
                    resolveResult.TopicType, resolveResult.DataFormat);

                // 3. 根据数据格式获取对应的解析器
                var parser = _dataParserFactory.GetParser(resolveResult.DataFormat);
                if (parser == null)
                {
                    _logger.LogWarning("未找到支持的数据解析器: 数据格式={DataFormat}", resolveResult.DataFormat);
                    return;
                }

                _logger.LogDebug("使用解析器: {ParserName} ({ParserDescription})", parser.Name, parser.Description);

                // 4. 检查解析器是否可以处理当前载荷
                if (!parser.CanParse(publishMessage.Payload ?? [], parseContext))
                {
                    _logger.LogWarning("解析器无法处理当前载荷: 解析器={ParserName}", parser.Name);
                    return;
                }

                // 5. 执行数据解析
                var parseResult = await parser.ParseAsync(publishMessage.Payload ?? [], parseContext);
                if (!parseResult.IsSuccess)
                {
                    _logger.LogError("数据解析失败: {ErrorMessage}", parseResult.ErrorMessage);
                    return;
                }

                _logger.LogInformation("数据解析成功: 解析出{Count}个属性, 耗时={ElapsedMs}ms",
                    parseResult.DataItems.Count, parseResult.ElapsedMilliseconds);

                // 6. 保存解析结果
                await SaveDeviceDataAsync(parseResult.DataItems, parseContext);

                _logger.LogInformation("设备消息处理完成: 设备ID={DeviceId}", deviceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备消息时发生异常: 设备ID={DeviceId}", deviceId);
                throw;
            }
        }

        /// <summary>
        /// 保存设备数据
        /// </summary>
        /// <param name="dataItems">数据项列表</param>
        /// <param name="parseContext">解析上下文</param>
        /// <returns>保存任务</returns>
        private async Task SaveDeviceDataAsync(List<DeviceDataItem> dataItems, DeviceParseContext parseContext)
        {
            if (dataItems == null || dataItems.Count == 0)
            {
                _logger.LogDebug("没有数据项需要保存");
                return;
            }

            var savedCount = 0;
            var historyDataList = new List<DeviceDataHistoryEntity>();

            foreach (var dataItem in dataItems)
            {
                try
                {
                    // 保存到设备实时数据表
                    var saveResult = await SaveDeviceLatestDataAsync(
                        parseContext.Device.Id,
                        parseContext.ProductModel.Id,
                        dataItem.PropertyId ?? 0,
                        dataItem.PropertyName,
                        dataItem.DataType,
                        dataItem.Value?.ToString() ?? string.Empty,
                        dataItem.Unit,
                        dataItem.DataStatus);

                    if (saveResult)
                    {
                        savedCount++;

                        // 同时添加到历史数据列表
                        historyDataList.Add(new DeviceDataHistoryEntity
                        {
                            DeviceId = parseContext.Device.Id,
                            PropertyName = dataItem.PropertyName,
                            DataType = dataItem.DataType,
                            PropertyValue = dataItem.Value?.ToString() ?? string.Empty,
                            DataTime = dataItem.DataTime
                        });

                        if (dataItem.IsDefined)
                        {
                            _logger.LogDebug("✓ 保存已定义属性: {PropertyName} = {Value} [{DataType}] {Unit}",
                                dataItem.PropertyName, dataItem.Value, GetDataTypeName(dataItem.DataType), dataItem.Unit);
                        }
                        else
                        {
                            _logger.LogDebug("✓ 保存未定义属性: {PropertyName} = {Value}",
                                dataItem.PropertyName, dataItem.Value);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("✗ 保存属性数据失败: {PropertyName}", dataItem.PropertyName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存数据项失败: {PropertyName}", dataItem.PropertyName);
                }
            }

            // 批量保存历史数据
            if (historyDataList.Count > 0)
            {
                await BatchSaveHistoryDataAsync(historyDataList);
                _logger.LogInformation("✓ 批量保存历史数据: {Count} 条", historyDataList.Count);
            }

            // 新增：告警检测逻辑
            await DetectParameterAlarmsAsync(dataItems, parseContext);

            _logger.LogInformation("设备数据保存完成: 成功保存 {SavedCount}/{TotalCount} 个属性",
                savedCount, dataItems.Count);
        }

        /// <summary>
        /// 获取数据类型名称
        /// </summary>
        /// <param name="dataType">数据类型代码</param>
        /// <returns>数据类型名称</returns>
        private static string GetDataTypeName(int dataType)
        {
            return dataType switch
            {
                1 => "int整形",
                2 => "long长整型",
                3 => "decimal小数",
                4 => "string字符串",
                5 => "datetime日期时间",
                6 => "JSON结构体",
                7 => "enum枚举",
                8 => "boolean布尔",
                9 => "stringList数组",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 检测参数告警
        /// </summary>
        /// <param name="dataItems">数据项列表</param>
        /// <param name="parseContext">解析上下文</param>
        private async Task DetectParameterAlarmsAsync(List<DeviceDataItem> dataItems, DeviceParseContext parseContext)
        {
            try
            {
                foreach (var dataItem in dataItems)
                {
                    // 只检测数值类型的数据
                    if (dataItem.Value == null || !dataItem.IsDefined)
                        continue;

                    // 尝试转换为数值类型
                    if (!decimal.TryParse(dataItem.Value.ToString(), out decimal numericValue))
                        continue;

                    // 获取设备参数配置
                    var deviceParameter = await GetDeviceParameterAsync(parseContext.Device.Id, dataItem.PropertyKey);
                    if (deviceParameter?.MonitorStatus != 1)
                        continue;

                    // 进行告警检测
                    var alarmResult = _parameterAlarmDetector.DetectParameterAlarm(deviceParameter, numericValue);

                    if (alarmResult.HasAlarm)
                    {
                        // 生成或更新告警事件
                        await _alarmService.ProcessParameterAlarmAsync(alarmResult, parseContext.Device, deviceParameter);
                    }
                    else
                    {
                        // 检查是否需要清除现有告警
                        await _alarmService.CheckParameterAlarmClearAsync(parseContext.Device.Id, deviceParameter.ParameterId, numericValue, deviceParameter);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检测参数告警时发生异常: DeviceId={DeviceId}", parseContext.Device.Id);
            }
        }

        /// <summary>
        /// 获取设备参数配置
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="propertyKey">属性键</param>
        /// <returns>设备参数配置</returns>
        private async Task<DeviceParaEntity> GetDeviceParameterAsync(long deviceId, string propertyKey)
        {
            try
            {
                return await _db.Queryable<DeviceParaEntity>()
                    .Where(p => p.DeviceId == deviceId && p.JsonKey == propertyKey)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogDebug("获取设备参数配置失败: DeviceId={DeviceId}, PropertyKey={PropertyKey}, Error={Error}",
                    deviceId, propertyKey, ex.Message);
                return null;
            }
        }



        /// <summary>
        /// 从主题中提取设备ID
        /// 支持的主题格式：
        /// - /devices/{device_id}/sys/properties/report (直连设备)
        /// - /devices/{device_id}/sys/gateway/sub_devices/properties/report (网关设备)
        /// </summary>
        /// <param name="topic">MQTT主题</param>
        /// <returns>设备ID</returns>
        private static string? ExtractDeviceIdFromTopic(string topic)
        {
            if (string.IsNullOrEmpty(topic))
            {
                return null;
            }

            var parts = topic.Split('/');

            // /devices/{device_id}/sys/properties/report (直连设备)
            // /devices/{device_id}/sys/gateway/sub_devices/properties/report (网关设备)
            if (parts.Length >= 4 && parts[1] == "devices")
            {
                return parts[2]; // 返回设备ID
            }

            return null;
        }

        /// <summary>
        /// 保存或更新设备最新数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="modelId">模型ID</param>
        /// <param name="propertyId">属性ID</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="dataValue">数据值</param>
        /// <param name="unit">单位</param>
        /// <param name="dataStatus">数据状态</param>
        /// <returns>是否成功</returns>
        private async Task<bool> SaveDeviceLatestDataAsync(long deviceId, long modelId, long propertyId,
            string propertyName, int dataType, string dataValue, string unit = "", int dataStatus = 1)
        {
            try
            {
                var existingData = await _db.Queryable<DeviceDataLatestEntity>()
                    .Where(x => x.DeviceId == deviceId && x.PropertyId == propertyId)
                    .FirstAsync();

                if (existingData != null)
                {
                    // 更新现有数据
                    existingData.DataValue = dataValue;
                    existingData.DataStatus = dataStatus;
                    existingData.UpdateTime = DateTime.Now;

                    var updateResult = await _db.Updateable(existingData).ExecuteCommandAsync();
                    return updateResult > 0;
                }
                else
                {
                    // 插入新数据
                    var newData = new DeviceDataLatestEntity
                    {
                        DeviceId = deviceId,
                        ModelId = modelId,
                        PropertyId = propertyId,
                        PropertyName = propertyName,
                        DataType = dataType,
                        DataValue = dataValue,
                        Unit = unit,
                        DataStatus = dataStatus,
                        UpdateTime = DateTime.Now
                    };

                    var insertResult = await _db.Insertable(newData).ExecuteCommandAsync();
                    return insertResult > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存设备最新数据失败: DeviceId={DeviceId}, PropertyId={PropertyId}",
                    deviceId, propertyId);
                return false;
            }
        }

        /// <summary>
        /// 批量保存设备历史数据
        /// </summary>
        /// <param name="historyDataList">历史数据列表</param>
        /// <returns>保存的记录数</returns>
        private async Task<int> BatchSaveHistoryDataAsync(List<DeviceDataHistoryEntity> historyDataList)
        {
            try
            {
                if (historyDataList?.Count == 0) return 0;

                var result = await _db.Insertable(historyDataList).ExecuteCommandAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量保存设备历史数据失败: 数据条数={Count}", historyDataList?.Count ?? 0);
                return 0;
            }
        }

        /// <summary>
        /// 处理Modbus响应消息
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="payload">响应负载</param>
        /// <returns>处理任务</returns>
        private async Task HandleModbusResponseAsync(string deviceId, byte[] payload)
        {
            try
            {
                _logger.LogDebug("处理Modbus响应: DeviceId={DeviceId}, PayloadSize={PayloadSize}",
                    deviceId, payload.Length);

                // 委托给Modbus响应处理器
                await _modbusResponseHandler.HandleModbusResponseAsync(deviceId, payload);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理Modbus响应时发生错误: DeviceId={DeviceId}", deviceId);
            }
        }
        #endregion
    }
}