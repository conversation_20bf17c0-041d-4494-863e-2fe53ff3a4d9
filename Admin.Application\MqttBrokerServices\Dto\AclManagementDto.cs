using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Admin.Communication.Mqtt.Configuration;
using Admin.Multiplex.Contracts;

namespace Admin.Application.MqttBrokerServices.Dto
{
    #region 请求 DTO

    /// <summary>
    /// 获取ACL规则请求DTO
    /// </summary>
    public class GetAclRulesRequest : PaginationParams
    {
        /// <summary>
        /// 规则名称过滤
        /// </summary>
        public string? RuleName { get; set; }

        /// <summary>
        /// 访问类型过滤
        /// </summary>
        public string? AccessType { get; set; }

        /// <summary>
        /// 用户名过滤
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 客户端ID过滤
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 主题过滤
        /// </summary>
        public string? Topic { get; set; }

        /// <summary>
        /// 权限过滤 (allow/deny)
        /// </summary>
        public string? Permission { get; set; }

        /// <summary>
        /// 是否激活过滤
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 标签过滤
        /// </summary>
        public string? Tags { get; set; }
    }

    /// <summary>
    /// 添加ACL规则请求DTO
    /// </summary>
    public class AddAclRuleRequest
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        [Required(ErrorMessage = "规则名称不能为空")]
        [StringLength(100, ErrorMessage = "规则名称长度不能超过100个字符")]
        public string RuleName { get; set; } = string.Empty;

        /// <summary>
        /// 优先级 (数值越大优先级越高)
        /// </summary>
        [Range(1, 1000, ErrorMessage = "优先级必须在1-1000之间")]
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 访问类型
        /// </summary>
        [Required(ErrorMessage = "访问类型不能为空")]
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public MqttAccessType AccessType { get; set; }

        /// <summary>
        /// 权限 (true=允许, false=拒绝)
        /// </summary>
        public bool Allow { get; set; } = true;

        /// <summary>
        /// 用户名 (为空表示适用于所有用户)
        /// </summary>
        [StringLength(100, ErrorMessage = "用户名长度不能超过100个字符")]
        public string? Username { get; set; }

        /// <summary>
        /// 客户端ID (为空表示适用于所有客户端)
        /// </summary>
        [StringLength(100, ErrorMessage = "客户端ID长度不能超过100个字符")]
        public string? ClientId { get; set; }

        /// <summary>
        /// IP地址 (为空表示适用于所有IP)
        /// </summary>
        [StringLength(45, ErrorMessage = "IP地址长度不能超过45个字符")]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 主题 (支持通配符)
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        [StringLength(500, ErrorMessage = "主题长度不能超过500个字符")]
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 规则描述
        /// </summary>
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 生效开始时间
        /// </summary>
        public DateTime? EffectiveStartTime { get; set; }

        /// <summary>
        /// 生效结束时间
        /// </summary>
        public DateTime? EffectiveEndTime { get; set; }
    }

    /// <summary>
    /// 更新ACL规则请求DTO
    /// </summary>
    public class UpdateAclRuleRequest
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        [Required(ErrorMessage = "规则名称不能为空")]
        [StringLength(100, ErrorMessage = "规则名称长度不能超过100个字符")]
        public string RuleName { get; set; } = string.Empty;

        /// <summary>
        /// 优先级 (数值越大优先级越高)
        /// </summary>
        [Range(1, 1000, ErrorMessage = "优先级必须在1-1000之间")]
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 访问类型
        /// </summary>
        [Required(ErrorMessage = "访问类型不能为空")]
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public MqttAccessType AccessType { get; set; }

        /// <summary>
        /// 权限 (true=允许, false=拒绝)
        /// </summary>
        public bool Allow { get; set; } = true;

        /// <summary>
        /// 用户名 (为空表示适用于所有用户)
        /// </summary>
        [StringLength(100, ErrorMessage = "用户名长度不能超过100个字符")]
        public string? Username { get; set; }

        /// <summary>
        /// 客户端ID (为空表示适用于所有客户端)
        /// </summary>
        [StringLength(100, ErrorMessage = "客户端ID长度不能超过100个字符")]
        public string? ClientId { get; set; }

        /// <summary>
        /// IP地址 (为空表示适用于所有IP)
        /// </summary>
        [StringLength(45, ErrorMessage = "IP地址长度不能超过45个字符")]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 主题 (支持通配符)
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        [StringLength(500, ErrorMessage = "主题长度不能超过500个字符")]
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 规则描述
        /// </summary>
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 生效开始时间
        /// </summary>
        public DateTime? EffectiveStartTime { get; set; }

        /// <summary>
        /// 生效结束时间
        /// </summary>
        public DateTime? EffectiveEndTime { get; set; }
    }

    /// <summary>
    /// 测试权限请求DTO
    /// </summary>
    public class TestPermissionRequest
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required(ErrorMessage = "客户端ID不能为空")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 访问类型
        /// </summary>
        [Required(ErrorMessage = "访问类型不能为空")]
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public MqttAccessType AccessType { get; set; }
    }

    #endregion

    #region 响应 DTO

    /// <summary>
    /// ACL规则DTO
    /// </summary>
    public class AclRuleDto
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = string.Empty;

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 访问类型
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public MqttAccessType AccessType { get; set; }

        /// <summary>
        /// 权限
        /// </summary>
        public string Permission => Allow ? "allow" : "deny";

        /// <summary>
        /// 是否允许
        /// </summary>
        public bool Allow { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 规则描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 生效开始时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? EffectiveStartTime { get; set; }

        /// <summary>
        /// 生效结束时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? EffectiveEndTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public long CreateBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public long? UpdateBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 权限测试结果DTO
    /// </summary>
    public class PermissionTestResultDto
    {
        /// <summary>
        /// 是否被授权
        /// </summary>
        public bool IsAuthorized { get; set; }

        /// <summary>
        /// 匹配的规则
        /// </summary>
        public MatchedRuleDto? MatchedRule { get; set; }

        /// <summary>
        /// 测试时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime TestTime { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string? FailureReason { get; set; }

        /// <summary>
        /// 所有匹配的规则
        /// </summary>
        public List<MatchedRuleDto> AllMatchedRules { get; set; } = new();
    }

    /// <summary>
    /// 匹配的规则DTO
    /// </summary>
    public class MatchedRuleDto
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 权限
        /// </summary>
        public string Permission { get; set; } = string.Empty;

        /// <summary>
        /// 匹配的模式
        /// </summary>
        public string? MatchedPattern { get; set; }

        /// <summary>
        /// 匹配类型
        /// </summary>
        public string? MatchType { get; set; }
    }

    /// <summary>
    /// ACL规则列表响应DTO
    /// </summary>
    public class GetAclRulesResponse : PagedResponseDto<AclRuleDto>
    {
        public GetAclRulesResponse(List<AclRuleDto> rules, int totalCount, int page, int pageSize)
            : base(rules, totalCount, page, pageSize)
        {
        }
    }

    #endregion

    #region 结果类

    /// <summary>
    /// ACL管理结果基类
    /// </summary>
    public class AclManagementResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public object? Data { get; set; }

        public static AclManagementResult Success(object? data = null)
        {
            return new AclManagementResult
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static AclManagementResult Error(string errorMessage)
        {
            return new AclManagementResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// ACL规则操作结果
    /// </summary>
    public class AclRuleResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 规则数据
        /// </summary>
        public AclRuleDto? Data { get; set; }

        public static AclRuleResult Success(AclRuleDto data)
        {
            return new AclRuleResult
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static AclRuleResult Error(string errorMessage)
        {
            return new AclRuleResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 权限测试结果
    /// </summary>
    public class PermissionTestResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 测试结果数据
        /// </summary>
        public PermissionTestResultDto? Data { get; set; }

        public static PermissionTestResult Success(PermissionTestResultDto data)
        {
            return new PermissionTestResult
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static PermissionTestResult Error(string errorMessage)
        {
            return new PermissionTestResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    #endregion
} 