using System;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Models.Results;
using Admin.Communication.Mqtt.Services;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.Examples
{
    /// <summary>
    /// JSON格式验证器，验证MQTT消息载荷是否为有效的JSON格式
    /// </summary>
    public class JsonFormatValidator : BaseMqttMessageValidator
    {
        public override string Name => "JsonFormatValidator";

        public JsonFormatValidator(ILogger<JsonFormatValidator> logger) : base(logger)
        {
            Priority = 10; // 高优先级，首先验证格式
        }

        public override bool CanValidate(MqttMessage message, MqttMessageContext context)
        {
            // 只验证发布消息且有载荷的消息
            return message is MqttPublishMessage publishMessage && 
                   publishMessage.Payload != null && 
                   publishMessage.Payload.Length > 0;
        }

        public override async Task<MqttMessageValidationResult> ValidateAsync(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return MqttMessageValidationResult.Success();
            }

            try
            {
                var jsonString = Encoding.UTF8.GetString(publishMessage.Payload);
                
                // 尝试解析JSON
                using var document = JsonDocument.Parse(jsonString);
                
                _logger.LogDebug("JSON格式验证通过: {Topic}", publishMessage.Topic);
                return MqttMessageValidationResult.Success();
            }
            catch (JsonException ex)
            {
                _logger.LogWarning("JSON格式验证失败: {Topic}, 错误: {Error}", publishMessage.Topic, ex.Message);
                return MqttMessageValidationResult.Failure("消息载荷不是有效的JSON格式", "INVALID_JSON");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JSON格式验证时发生异常: {Topic}", publishMessage.Topic);
                return MqttMessageValidationResult.Failure($"验证异常: {ex.Message}", "VALIDATION_ERROR");
            }
        }
    }

    /// <summary>
    /// 设备数据验证器，验证设备数据的完整性和有效性
    /// </summary>
    public class DeviceDataValidator : BaseMqttMessageValidator
    {
        public override string Name => "DeviceDataValidator";

        public DeviceDataValidator(ILogger<DeviceDataValidator> logger) : base(logger)
        {
            Priority = 20; // 在JSON格式验证之后
        }

        public override bool CanValidate(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return false;
            }

            // 只验证设备数据相关的主题
            return publishMessage.Topic.Contains("/data") || 
                   publishMessage.Topic.Contains("/telemetry") ||
                   publishMessage.Topic.Contains("/sensor");
        }

        public override async Task<MqttMessageValidationResult> ValidateAsync(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return MqttMessageValidationResult.Success();
            }

            try
            {
                var jsonString = Encoding.UTF8.GetString(publishMessage.Payload);
                var deviceData = JsonSerializer.Deserialize<DeviceDataModel>(jsonString, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                var result = new MqttMessageValidationResult { IsValid = true };

                // 验证设备ID
                if (string.IsNullOrEmpty(deviceData.DeviceId))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "设备ID不能为空";
                    result.ErrorCode = "MISSING_DEVICE_ID";
                    result.Details["Field"] = "DeviceId";
                }

                // 验证时间戳
                if (deviceData.Timestamp == default)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "时间戳不能为空";
                    result.ErrorCode = "MISSING_TIMESTAMP";
                    result.Details["Field"] = "Timestamp";
                }

                // 验证数据值
                if (deviceData.Value == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "数据值不能为空";
                    result.ErrorCode = "MISSING_VALUE";
                    result.Details["Field"] = "Value";
                }

                // 验证时间戳是否过期（超过5分钟）
                if (deviceData.Timestamp < DateTime.Now.AddMinutes(-5))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "数据时间戳过期";
                    result.ErrorCode = "EXPIRED_TIMESTAMP";
                    result.Details["Timestamp"] = deviceData.Timestamp;
                    result.Details["MaxAge"] = "5 minutes";
                }

                if (result.IsValid)
                {
                    _logger.LogDebug("设备数据验证通过: {DeviceId}", deviceData.DeviceId);
                }
                else
                {
                    _logger.LogWarning("设备数据验证失败: {DeviceId}, 错误: {Error}", 
                        deviceData.DeviceId, result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设备数据验证时发生异常: {Topic}", publishMessage.Topic);
                return MqttMessageValidationResult.Failure($"验证异常: {ex.Message}", "VALIDATION_ERROR");
            }
        }
    }

    /// <summary>
    /// 消息加密转换器，对敏感数据进行加密处理
    /// </summary>
    public class MessageEncryptionTransformer : BaseMqttMessageTransformer
    {
        public override string Name => "MessageEncryptionTransformer";

        public MessageEncryptionTransformer(ILogger<MessageEncryptionTransformer> logger) : base(logger)
        {
            Priority = 10; // 高优先级，优先加密
        }

        public override bool CanTransform(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return false;
            }

            // 只对包含敏感信息的主题进行加密
            return publishMessage.Topic.Contains("/secure/") || 
                   publishMessage.Topic.Contains("/private/") ||
                   context.Properties.ContainsKey("RequireEncryption");
        }

        public override async Task<MqttMessage> TransformAsync(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return message;
            }

            try
            {
                _logger.LogDebug("对消息进行加密转换: {Topic}", publishMessage.Topic);

                // 这里实现实际的加密逻辑
                // 为了示例，我们使用简单的Base64编码
                var originalPayload = publishMessage.Payload;
                var encryptedPayload = Convert.ToBase64String(originalPayload);
                var encryptedBytes = Encoding.UTF8.GetBytes(encryptedPayload);

                // 创建新的发布消息
                var encryptedMessage = new MqttPublishMessage
                {
                    Topic = publishMessage.Topic,
                    Payload = encryptedBytes,
                    QualityOfService = 2,
                    Retain = publishMessage.Retain
                };

                // 添加加密标记
                context.Properties["IsEncrypted"] = true;
                context.Properties["EncryptionMethod"] = "Base64";

                _logger.LogDebug("消息加密完成: {Topic}", publishMessage.Topic);
                return encryptedMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "消息加密时发生异常: {Topic}", publishMessage.Topic);
                return message; // 加密失败时返回原消息
            }
        }
    }

    /// <summary>
    /// 消息压缩转换器，对大载荷消息进行压缩
    /// </summary>
    public class MessageCompressionTransformer : BaseMqttMessageTransformer
    {
        private const int CompressionThreshold = 1024; // 1KB

        public override string Name => "MessageCompressionTransformer";

        public MessageCompressionTransformer(ILogger<MessageCompressionTransformer> logger) : base(logger)
        {
            Priority = 20; // 在加密之后进行压缩
        }

        public override bool CanTransform(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return false;
            }

            // 只对超过阈值的消息进行压缩
            return publishMessage.Payload != null && 
                   publishMessage.Payload.Length > CompressionThreshold;
        }

        public override async Task<MqttMessage> TransformAsync(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return message;
            }

            try
            {
                _logger.LogDebug("对消息进行压缩转换: {Topic}, 原始大小: {OriginalSize}字节", 
                    publishMessage.Topic, publishMessage.Payload.Length);

                // 这里实现实际的压缩逻辑
                // 为了示例，我们使用简单的模拟压缩
                var compressedPayload = await CompressAsync(publishMessage.Payload);

                // 创建新的发布消息
                var compressedMessage = new MqttPublishMessage
                {
                    Topic = publishMessage.Topic,
                    Payload = compressedPayload,
                    QualityOfService = 2,
                    Retain = publishMessage.Retain
                };

                // 添加压缩标记
                context.Properties["IsCompressed"] = true;
                context.Properties["CompressionMethod"] = "Simulated";
                context.Properties["OriginalSize"] = publishMessage.Payload.Length;
                context.Properties["CompressedSize"] = compressedPayload.Length;

                _logger.LogDebug("消息压缩完成: {Topic}, 压缩后大小: {CompressedSize}字节, 压缩率: {CompressionRatio:P2}", 
                    publishMessage.Topic, compressedPayload.Length, 
                    1.0 - (double)compressedPayload.Length / publishMessage.Payload.Length);

                return compressedMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "消息压缩时发生异常: {Topic}", publishMessage.Topic);
                return message; // 压缩失败时返回原消息
            }
        }

        private async Task<byte[]> CompressAsync(byte[] data)
        {
            // 模拟压缩处理
            await Task.Delay(10); // 模拟压缩耗时
            
            // 实际应用中应该使用真正的压缩算法，如GZip、Deflate等
            // 这里为了示例，简单地返回原数据（实际不压缩）
            return data;
        }
    }

    /// <summary>
    /// 消息路由转换器，根据规则修改消息主题
    /// </summary>
    public class MessageRoutingTransformer : BaseMqttMessageTransformer
    {
        public override string Name => "MessageRoutingTransformer";

        public MessageRoutingTransformer(ILogger<MessageRoutingTransformer> logger) : base(logger)
        {
            Priority = 5; // 最高优先级，首先进行路由转换
        }

        public override bool CanTransform(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return false;
            }

            // 检查是否需要路由转换
            return publishMessage.Topic.StartsWith("legacy/") ||
                   context.Properties.ContainsKey("RouteToTopic");
        }

        public override async Task<MqttMessage> TransformAsync(MqttMessage message, MqttMessageContext context)
        {
            if (!(message is MqttPublishMessage publishMessage))
            {
                return message;
            }

            try
            {
                var originalTopic = publishMessage.Topic;
                var newTopic = originalTopic;

                // 应用路由规则
                if (originalTopic.StartsWith("legacy/"))
                {
                    // 将legacy主题转换为新格式
                    newTopic = originalTopic.Replace("legacy/", "v2/");
                }

                if (context.Properties.TryGetValue("RouteToTopic", out var routeToTopic))
                {
                    newTopic = routeToTopic.ToString();
                }

                if (newTopic != originalTopic)
                {
                    _logger.LogDebug("消息路由转换: {OriginalTopic} -> {NewTopic}", originalTopic, newTopic);

                    // 创建新的发布消息
                    var routedMessage = new MqttPublishMessage
                    {
                        Topic = newTopic,
                        Payload = publishMessage.Payload,
                        QualityOfService = 2,
                        Retain = publishMessage.Retain
                    };

                    // 记录路由历史
                    context.Properties["OriginalTopic"] = originalTopic;
                    context.Properties["IsRouted"] = true;

                    return routedMessage;
                }

                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "消息路由转换时发生异常: {Topic}", publishMessage.Topic);
                return message; // 路由失败时返回原消息
            }
        }
    }

    /// <summary>
    /// 设备数据模型（用于验证）
    /// </summary>
    public class DeviceDataModel
    {
        public string DeviceId { get; set; }
        public string DataType { get; set; }
        public object Value { get; set; }
        public string Unit { get; set; }
        public DateTime Timestamp { get; set; }
        public string Status { get; set; }
    }
} 