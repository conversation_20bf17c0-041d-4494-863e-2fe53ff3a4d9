// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Multiplex.Contracts;
using System.ComponentModel.DataAnnotations;

namespace Admin.Application.ControlCmdServices.Dto;

/// <summary>
/// 添加控制指令输入DTO
/// </summary>
public class AddControlCmdInput
{
    /// <summary>
    /// 指令名称
    /// </summary>
    [Required(ErrorMessage = "指令名称不能为空")]
    [StringLength(100, ErrorMessage = "指令名称长度不能超过100个字符")]
    public string CmdName { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    [Range(1, long.MaxValue, ErrorMessage = "设备ID必须大于0")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 通讯介质 (1:MQTT)
    /// </summary>
    [Required(ErrorMessage = "通讯介质不能为空")]
    [Range(1, 1, ErrorMessage = "通讯介质只支持MQTT(1)")]
    public int CommunicationMedium { get; set; } = 1;

    /// <summary>
    /// 指令内容
    /// </summary>
    [Required(ErrorMessage = "指令内容不能为空")]
    public string CmdContent { get; set; }

    /// <summary>
    /// 编码方式 (1:JSON 2:HEX)
    /// </summary>
    [Required(ErrorMessage = "编码方式不能为空")]
    [Range(1, 2, ErrorMessage = "编码方式只支持JSON(1)或HEX(2)")]
    public int Encode { get; set; }

    /// <summary>
    /// 区域ID (可选)
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// 设备分类 (可选)
    /// </summary>
    public int? DeviceCategory { get; set; }

    /// <summary>
    /// 是否启用APP控制 (0:否 1:是)
    /// </summary>
    [Range(0, 1, ErrorMessage = "是否启用APP控制只能为0或1")]
    public int IsAppControl { get; set; } = 1;
}

/// <summary>
/// 更新控制指令输入DTO
/// </summary>
public class UpdateControlCmdInput
{
    /// <summary>
    /// 指令ID
    /// </summary>
    [Required(ErrorMessage = "指令ID不能为空")]
    [Range(1, long.MaxValue, ErrorMessage = "指令ID必须大于0")]
    public long Id { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    [Required(ErrorMessage = "指令名称不能为空")]
    [StringLength(100, ErrorMessage = "指令名称长度不能超过100个字符")]
    public string CmdName { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    [Range(1, long.MaxValue, ErrorMessage = "设备ID必须大于0")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 通讯介质 (1:MQTT)
    /// </summary>
    [Required(ErrorMessage = "通讯介质不能为空")]
    [Range(1, 1, ErrorMessage = "通讯介质只支持MQTT(1)")]
    public int CommunicationMedium { get; set; } = 1;

    /// <summary>
    /// 指令内容
    /// </summary>
    [Required(ErrorMessage = "指令内容不能为空")]
    public string CmdContent { get; set; }

    /// <summary>
    /// 编码方式 (1:JSON 2:HEX)
    /// </summary>
    [Required(ErrorMessage = "编码方式不能为空")]
    [Range(1, 2, ErrorMessage = "编码方式只支持JSON(1)或HEX(2)")]
    public int Encode { get; set; }

    /// <summary>
    /// 区域ID (可选)
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// 设备分类 (可选)
    /// </summary>
    public int? DeviceCategory { get; set; }

    /// <summary>
    /// 是否启用APP控制 (0:否 1:是)
    /// </summary>
    [Range(0, 1, ErrorMessage = "是否启用APP控制只能为0或1")]
    public int IsAppControl { get; set; } = 1;
}

/// <summary>
/// 控制指令分页查询输入DTO
/// </summary>
public class ControlCmdPagedInput : PaginationParams
{
    /// <summary>
    /// 指令名称 (模糊查询)
    /// </summary>
    public string? CmdName { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long? DeviceId { get; set; }

    /// <summary>
    /// 通讯介质 (1:MQTT)
    /// </summary>
    public int? CommunicationMedium { get; set; }

    /// <summary>
    /// 编码方式 (1:JSON 2:HEX)
    /// </summary>
    public int? Encode { get; set; }

    /// <summary>
    /// 是否启用APP控制 (0:否 1:是)
    /// </summary>
    public int? IsAppControl { get; set; }
}

/// <summary>
/// 控制指令输出DTO
/// </summary>
public class ControlCmdOutput
{
    /// <summary>
    /// 指令ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string CmdName { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 通讯介质 (1:MQTT)
    /// </summary>
    public int CommunicationMedium { get; set; }

    /// <summary>
    /// 通讯介质名称
    /// </summary>
    public string CommunicationMediumName { get; set; }

    /// <summary>
    /// 指令内容
    /// </summary>
    public string CmdContent { get; set; }

    /// <summary>
    /// 编码方式 (1:JSON 2:HEX)
    /// </summary>
    public int Encode { get; set; }

    /// <summary>
    /// 编码方式名称
    /// </summary>
    public string EncodeName { get; set; }

    /// <summary>
    /// 区域ID
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// 设备分类
    /// </summary>
    public int? DeviceCategory { get; set; }

    /// <summary>
    /// 是否启用APP控制 (0:否 1:是)
    /// </summary>
    public int IsAppControl { get; set; }

    /// <summary>
    /// 是否启用APP控制名称
    /// </summary>
    public string IsAppControlName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}
