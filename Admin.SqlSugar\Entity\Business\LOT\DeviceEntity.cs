// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 设备实体
/// </summary>
[SugarTable("LOT_DEVICE")]
public partial class DeviceEntity : BaseEntity
{
    /// <summary>
    /// 模型ID
    /// </summary>
    [SugarColumn(ColumnName = "MODEL_ID")]
    public long ModelId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_NAME")]
    public string DeviceName { get; set; }

    /// <summary>
    /// 父设备id
    /// </summary>
    [SugarColumn(ColumnName = "PARENT_ID")]
    public int ParentId { get; set; }

    /// <summary>
    /// 自定义发布主题 (为空则使用预置主题)
    [SugarColumn(ColumnName = "CUSTOM_PUBLISH_TOPIC")]
    public string? CustomPublishTopic { get; set; }



    /// <summary>
    /// 通常使用IMEI、MAC地址或Serial No作为设备标识码
    /// 设备标识码长度为4至32个字符，只允许字母、数字、下划线（_）、连接符（-）的组合。
    /// 网关子设备为""
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_IDENTITY_CODE")]
    public string? DeviceIdentityCode { get; set; }

    /// <summary>
    /// 设备ID。如果填写该参数，平台将设备ID设置为该参数值；
    /// 设备ID长度为4至64个字符，只允许字母、数字、下划线（）、连接符（-）的组合。
    /// 如果不填写该参数，设备ID由物联网平台生成,规则为guid + _ + IdentityCode拼接而成。 
    /// 网关子设备为""
    /// is mqtt clientId
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public string DeviceId { get; set; }

    /// <summary>
    /// 设备IP地址
    /// </summary>
    [SugarColumn(ColumnName = "IP_ADDRESS")]
    public string? IpAddress { get; set; }

    /// <summary>
    /// 从机地址
    /// </summary>
    [SugarColumn(ColumnName = "MODBUS_ADDR")]
    public int ModbusAddr { get; set; }

    /// <summary>
    /// 设备状态 (0:离线 1:在线)
    /// </summary>
    [SugarColumn(ColumnName = "STATUS")]
    public int Status { get; set; } = 0;

    /// <summary>
    /// 最后数据上报时间
    /// </summary>
    [SugarColumn(ColumnName = "LAST_DATA_TIME")]
    public DateTime? LastDataTime { get; set; }

    /// <summary>
    /// 设备描述
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_DESCRIPTION")]
    public string DeviceDescription { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnName = "IS_ENABLED")]
    public bool IsEnabled { get; set; } = true;
} 