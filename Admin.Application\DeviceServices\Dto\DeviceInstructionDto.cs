using System.ComponentModel.DataAnnotations;

namespace Admin.Application.DeviceServices.Dto;

/// <summary>
/// 设备指令查询输入DTO
/// </summary>
public class DeviceInstructionQueryInput : PaginationParams
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long? DeviceId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string? InstructionName { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 添加设备指令输入DTO
/// </summary>
public class AddDeviceInstructionInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    [Required(ErrorMessage = "指令名称不能为空")]
    [MaxLength(100, ErrorMessage = "指令名称最大长度为100个字符")]
    public string InstructionName { get; set; }

    /// <summary>
    /// 发送指令 (例如 010300000002C40B)
    /// </summary>
    [Required(ErrorMessage = "发送指令不能为空")]
    [MaxLength(500, ErrorMessage = "发送指令最大长度为500个字符")]
    public string SendStr { get; set; }

    /// <summary>
    /// 编码 (1:HEX 2:ASCII)
    /// </summary>
    [Range(1, 2, ErrorMessage = "编码值必须在1-2之间")]
    public int Encode { get; set; } = 1;

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    [Range(100, 60000, ErrorMessage = "响应时间范围为100-60000毫秒")]
    public int ResponseTime { get; set; } = 2000;

    /// <summary>
    /// 重试次数
    /// </summary>
    [Range(0, 10, ErrorMessage = "重试次数范围为0-10次")]
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注最大长度为500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 更新设备指令输入DTO
/// </summary>
public class UpdateDeviceInstructionInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    [Required(ErrorMessage = "指令名称不能为空")]
    [MaxLength(100, ErrorMessage = "指令名称最大长度为100个字符")]
    public string InstructionName { get; set; }

    /// <summary>
    /// 发送指令 (例如 010300000002C40B)
    /// </summary>
    [Required(ErrorMessage = "发送指令不能为空")]
    [MaxLength(500, ErrorMessage = "发送指令最大长度为500个字符")]
    public string SendStr { get; set; }

    /// <summary>
    /// 编码 (1:HEX 2:ASCII)
    /// </summary>
    [Range(1, 2, ErrorMessage = "编码值必须在1-2之间")]
    public int Encode { get; set; } = 1;

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    [Range(100, 60000, ErrorMessage = "响应时间范围为100-60000毫秒")]
    public int ResponseTime { get; set; } = 2000;

    /// <summary>
    /// 重试次数
    /// </summary>
    [Range(0, 10, ErrorMessage = "重试次数范围为0-10次")]
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注最大长度为500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 设备指令输出DTO
/// </summary>
public class DeviceInstructionOutput
{
    /// <summary>
    /// 指令ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string InstructionName { get; set; }

    /// <summary>
    /// 发送指令
    /// </summary>
    public string SendStr { get; set; }

    /// <summary>
    /// 编码 (1:HEX 2:ASCII)
    /// </summary>
    public int Encode { get; set; }

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    public int ResponseTime { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public long CreateBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 修改人
    /// </summary>
    public long? UpdateBy { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 设备指令简单输出DTO (用于下拉选择等场景)
/// </summary>
public class DeviceInstructionSimpleOutput
{
    /// <summary>
    /// 指令ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string InstructionName { get; set; }

    /// <summary>
    /// 发送指令
    /// </summary>
    public string SendStr { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}




