using Admin.Communication.Mqtt.Models;
using System;
using System.Buffers.Binary;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Protocol
{
    /// <summary>
    /// MQTT包解析器
    /// </summary>
    public class MqttPacketParser
    {
        /// <summary>
        /// 从流中读取MQTT消息
        /// </summary>
        /// <param name="stream">网络流</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>MQTT消息</returns>
        public async Task<MqttMessage> ParsePacketAsync(Stream stream, CancellationToken cancellationToken = default)
        {
            // 读取固定头部第一个字节
            int firstByte = await ReadByteAsync(stream, cancellationToken);
            if (firstByte == -1)
            {
                throw new EndOfStreamException("读取MQTT包固定头部第一个字节时到达流的末尾");
            }

            // 解析消息类型和标志
            byte messageTypeByte = (byte)((firstByte >> 4) & 0x0F);
            byte flags = (byte)(firstByte & 0x0F);

            // 检查消息类型是否有效
            if (!Enum.IsDefined(typeof(MqttProtocol.MessageType), messageTypeByte))
            {
                throw new MqttProtocolException($"无效的MQTT消息类型: {messageTypeByte}");
            }

            var messageType = (MqttProtocol.MessageType)messageTypeByte;

            // 读取剩余长度
            int remainingLength = await ReadRemainingLengthAsync(stream, cancellationToken);

            // 读取可变头部和负载
            byte[] packetData = await ReadBytesAsync(stream, remainingLength, cancellationToken);

            // 解析不同类型的消息
            return ParseMessage(messageType, flags, packetData);
        }

        /// <summary>
        /// 读取剩余长度
        /// </summary>
        private async Task<int> ReadRemainingLengthAsync(Stream stream, CancellationToken cancellationToken)
        {
            int multiplier = 1;
            int value = 0;
            int digit;
            int bytesRead = 0;

            do
            {
                digit = await ReadByteAsync(stream, cancellationToken);
                if (digit == -1)
                {
                    throw new EndOfStreamException("读取MQTT包剩余长度时到达流的末尾");
                }

                value += (digit & 0x7F) * multiplier;
                multiplier *= 128;
                bytesRead++;

                if (bytesRead > 4)
                {
                    throw new MqttProtocolException("MQTT包剩余长度超过4个字节");
                }
            }
            while ((digit & 0x80) != 0);

            return value;
        }

        /// <summary>
        /// 读取一个字节
        /// </summary>
        private async Task<int> ReadByteAsync(Stream stream, CancellationToken cancellationToken)
        {
            byte[] buffer = new byte[1];
            int bytesRead = await stream.ReadAsync(buffer, 0, 1, cancellationToken);
            return bytesRead == 0 ? -1 : buffer[0];
        }

        /// <summary>
        /// 读取指定长度的字节
        /// </summary>
        private async Task<byte[]> ReadBytesAsync(Stream stream, int count, CancellationToken cancellationToken)
        {
            byte[] buffer = new byte[count];
            int bytesRead = 0;
            int totalBytesRead = 0;

            while (totalBytesRead < count)
            {
                bytesRead = await stream.ReadAsync(buffer, totalBytesRead, count - totalBytesRead, cancellationToken);
                if (bytesRead == 0)
                {
                    throw new EndOfStreamException($"读取MQTT包数据时到达流的末尾，期望读取 {count} 字节，实际读取 {totalBytesRead} 字节");
                }

                totalBytesRead += bytesRead;
            }

            return buffer;
        }

        /// <summary>
        /// 解析消息
        /// </summary>
        protected virtual  MqttMessage ParseMessage(MqttProtocol.MessageType messageType, byte flags, byte[] data)
        {
            using (var reader = new BinaryReader(new MemoryStream(data)))
            {
                switch (messageType)
                {
                    case MqttProtocol.MessageType.Connect:
                        return ParseConnectMessage(reader);
                    case MqttProtocol.MessageType.ConnAck:
                        return ParseConnAckMessage(reader);
                    case MqttProtocol.MessageType.Publish:
                        return ParsePublishMessage(reader, flags, data.Length);
                    case MqttProtocol.MessageType.PubAck:
                        return ParsePubAckMessage(reader);
                    case MqttProtocol.MessageType.PubRec:
                        return ParsePubRecMessage(reader);
                    case MqttProtocol.MessageType.PubRel:
                        return ParsePubRelMessage(reader);
                    case MqttProtocol.MessageType.PubComp:
                        return ParsePubCompMessage(reader);
                    case MqttProtocol.MessageType.Subscribe:
                        return ParseSubscribeMessage(reader);
                    case MqttProtocol.MessageType.SubAck:
                        return ParseSubAckMessage(reader);
                    case MqttProtocol.MessageType.Unsubscribe:
                        return ParseUnsubscribeMessage(reader);
                    case MqttProtocol.MessageType.UnsubAck:
                        return ParseUnsubAckMessage(reader);
                    case MqttProtocol.MessageType.PingReq:
                        return new MqttPingReqMessage();
                    case MqttProtocol.MessageType.PingResp:
                        return new MqttPingRespMessage();
                    case MqttProtocol.MessageType.Disconnect:
                        return new MqttDisconnectMessage();
                    default:
                        throw new MqttProtocolException($"不支持的MQTT消息类型: {messageType}");
                }
            }
        }

        /// <summary>
        /// 解析CONNECT消息
        /// </summary>
        private MqttConnectMessage ParseConnectMessage(BinaryReader reader)
        {
            var message = new MqttConnectMessage();

            // 读取协议名称
            string protocolName = ReadString(reader);
            if (protocolName != "MQTT" && protocolName != "MQIsdp")
            {
                throw new MqttProtocolException($"不支持的协议名称: {protocolName}");
            }

            // 读取协议版本
            message.ProtocolVersion = reader.ReadByte();

            // 读取连接标志
            byte connectFlags = reader.ReadByte();
            message.CleanSession = (connectFlags & MqttProtocol.ConnectFlags.CleanSession) != 0;
            message.HasWill = (connectFlags & MqttProtocol.ConnectFlags.Will) != 0;
            message.WillQoS = (byte)((connectFlags & (MqttProtocol.ConnectFlags.WillQoS1 | MqttProtocol.ConnectFlags.WillQoS2)) >> 3);
            message.WillRetain = (connectFlags & MqttProtocol.ConnectFlags.WillRetain) != 0;
            bool hasPassword = (connectFlags & MqttProtocol.ConnectFlags.Password) != 0;
            bool hasUsername = (connectFlags & MqttProtocol.ConnectFlags.Username) != 0;

            // 读取保持连接时间
            message.KeepAlive = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());

            // 读取客户端ID
            message.ClientId = ReadString(reader);

            // 读取遗嘱消息
            if (message.HasWill)
            {
                message.WillTopic = ReadString(reader);
                message.WillMessage = ReadBinaryData(reader);
            }

            // 读取用户名
            if (hasUsername)
            {
                message.Username = ReadString(reader);
            }

            // 读取密码
            if (hasPassword)
            {
                message.Password = ReadString(reader);
            }

            return message;
        }

        /// <summary>
        /// 解析CONNACK消息
        /// </summary>
        private MqttConnAckMessage ParseConnAckMessage(BinaryReader reader)
        {
            var message = new MqttConnAckMessage();

            // 读取会话存在标志
            byte acknowledgeFlags = reader.ReadByte();
            message.SessionPresent = (acknowledgeFlags & 0x01) != 0;

            // 读取返回码
            byte returnCode = reader.ReadByte();
            message.ReturnCode = (MqttProtocol.ConnectReturnCode)returnCode;

            return message;
        }

        /// <summary>
        /// 解析PUBLISH消息
        /// </summary>
        private MqttPublishMessage ParsePublishMessage(BinaryReader reader, byte flags, int dataLength)
        {
            var message = new MqttPublishMessage();

            // 解析标志
            message.IsDuplicate = (flags & MqttProtocol.FixedHeaderFlags.DupFlag) != 0;
            message.QualityOfService = (byte)((flags & (MqttProtocol.FixedHeaderFlags.QoS1Flag | MqttProtocol.FixedHeaderFlags.QoS2Flag)) >> 1);
            message.Retain = (flags & MqttProtocol.FixedHeaderFlags.RetainFlag) != 0;

            // 读取主题
            message.Topic = ReadString(reader);

            // 读取消息ID (仅当QoS > 0时)
            if (message.QualityOfService > 0)
            {
                message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            }

            // 读取负载
            int payloadLength = dataLength - (int)reader.BaseStream.Position;
            if (payloadLength > 0)
            {
                message.Payload = reader.ReadBytes(payloadLength);
            }
            else
            {
                message.Payload = Array.Empty<byte>();
            }

            return message;
        }

        /// <summary>
        /// 解析PUBACK消息
        /// </summary>
        private MqttPubAckMessage ParsePubAckMessage(BinaryReader reader)
        {
            var message = new MqttPubAckMessage();
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            return message;
        }

        /// <summary>
        /// 解析PUBREC消息
        /// </summary>
        protected MqttPubRecMessage ParsePubRecMessage(BinaryReader reader)
        {
            var message = new MqttPubRecMessage();
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            return message;
        }

        /// <summary>
        /// 解析PUBREL消息
        /// </summary>
        protected MqttPubRelMessage ParsePubRelMessage(BinaryReader reader)
        {
            var message = new MqttPubRelMessage();
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            return message;
        }

        /// <summary>
        /// 解析PUBCOMP消息
        /// </summary>
        protected MqttPubCompMessage ParsePubCompMessage(BinaryReader reader)
        {
            var message = new MqttPubCompMessage();
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            return message;
        }

        /// <summary>
        /// 解析SUBSCRIBE消息
        /// </summary>
        protected MqttSubscribeMessage ParseSubscribeMessage(BinaryReader reader)
        {
            var message = new MqttSubscribeMessage();

            // 读取消息ID
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());

            // 读取主题过滤器
            var subscriptions = new List<MqttSubscription>();
            while (reader.BaseStream.Position < reader.BaseStream.Length)
            {
                string topic = ReadString(reader);
                byte qos = (byte)(reader.ReadByte() & 0x03); // 只取低2位
                subscriptions.Add(new MqttSubscription(topic, qos));
            }

            message.Subscriptions = subscriptions.ToArray();
            return message;
        }

        /// <summary>
        /// 解析SUBACK消息
        /// </summary>
        protected MqttSubAckMessage ParseSubAckMessage(BinaryReader reader)
        {
            var message = new MqttSubAckMessage();

            // 读取消息ID
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());

            // 读取返回码
            var returnCodes = new List<byte>();
            while (reader.BaseStream.Position < reader.BaseStream.Length)
            {
                returnCodes.Add(reader.ReadByte());
            }

            message.ReturnCodes = returnCodes.ToArray();
            return message;
        }

        /// <summary>
        /// 解析UNSUBSCRIBE消息
        /// </summary>
        protected MqttUnsubscribeMessage ParseUnsubscribeMessage(BinaryReader reader)
        {
            var message = new MqttUnsubscribeMessage();

            // 读取消息ID
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());

            // 读取主题过滤器
            var topicFilters = new List<string>();
            while (reader.BaseStream.Position < reader.BaseStream.Length)
            {
                topicFilters.Add(ReadString(reader));
            }

            message.TopicFilters = topicFilters.ToArray();
            return message;
        }

        /// <summary>
        /// 解析UNSUBACK消息
        /// </summary>
        protected MqttUnsubAckMessage ParseUnsubAckMessage(BinaryReader reader)
        {
            var message = new MqttUnsubAckMessage();
            message.MessageId = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            return message;
        }

        /// <summary>
        /// 读取字符串
        /// </summary>
        protected string ReadString(BinaryReader reader)
        {
            ushort length = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            byte[] data = reader.ReadBytes(length);
            return Encoding.UTF8.GetString(data);
        }

        /// <summary>
        /// 读取二进制数据
        /// </summary>
        protected byte[] ReadBinaryData(BinaryReader reader)
        {
            ushort length = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());
            return reader.ReadBytes(length);
        }
    }

    /// <summary>
    /// MQTT协议异常
    /// </summary>
    public class MqttProtocolException : Exception
    {
        /// <summary>
        /// 初始化MQTT协议异常
        /// </summary>
        /// <param name="message">异常消息</param>
        public MqttProtocolException(string message) : base(message)
        {
        }

        /// <summary>
        /// 初始化MQTT协议异常
        /// </summary>
        /// <param name="message">异常消息</param>
        /// <param name="innerException">内部异常</param>
        public MqttProtocolException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
} 