using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Admin.Multiplex.Contracts.Enums;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.TopicFilters
{
    /// <summary>
    /// 预置主题管理器
    /// </summary>
    public class PresetTopicManager
    {
        private readonly ILogger<PresetTopicManager> _logger;
        private readonly List<PresetTopicTemplate> _presetTopics;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public PresetTopicManager(ILogger<PresetTopicManager> logger)
        {
            _logger = logger;
            _presetTopics = InitializePresetTopics();
        }

        /// <summary>
        /// 检查是否为预置主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>匹配的预置主题模板</returns>
        public PresetTopicTemplate? MatchPresetTopic(string topic)
        {
            foreach (var template in _presetTopics)
            {
                if (template.IsMatch(topic))
                {
                    _logger.LogDebug("主题 {Topic} 匹配预置模板: {TemplateName}", topic, template.Name);
                    return template;
                }
            }

            return null;
        }

        /// <summary>
        /// 从主题中提取设备ID
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="template">匹配的模板</param>
        /// <returns>设备ID</returns>
        public string? ExtractDeviceId(string topic, PresetTopicTemplate template)
        {
            var match = Regex.Match(topic, template.RegexPattern);
            if (match.Success && match.Groups.Count > 1)
            {
                return match.Groups[1].Value;
            }

            return null;
        }

        /// <summary>
        /// 初始化预置主题模板
        /// </summary>
        private List<PresetTopicTemplate> InitializePresetTopics()
        {
            return new List<PresetTopicTemplate>
            {
                // 1. 设备消息上报
                new PresetTopicTemplate
                {
                    Name = "设备消息上报",
                    TopicPattern = "/devices/{device_id}/messages/control/up",
                    RegexPattern = @"^/devices/([^/]+)/messages/control/up$",
                    DataFormat = DataFormatEnum.Json,
                    AccessType = "Publish",
                    Priority = 100,
                    Description = "设备消息上报"
                },

                // 2. 平台下发消息给设备
                new PresetTopicTemplate
                {
                    Name = "平台下发消息给设备",
                    TopicPattern = "/devices/{device_id}/messages/control/down",
                    RegexPattern = @"^/devices/([^/]+)/messages/control/down$",
                    DataFormat = DataFormatEnum.Json,
                    AccessType = "Subscribe",
                    Priority = 101,
                    Description = "平台下发消息给设备"
                },

                // 3. 平台下发消息指令给设备
                new PresetTopicTemplate
                {
                    Name = "平台下发消息指令给设备",
                    TopicPattern = "/devices/{device_id}/messages/control/command/down",
                    RegexPattern = @"^/devices/([^/]+)/messages/control/command/down$",
                    DataFormat = DataFormatEnum.Hex,
                    AccessType = "Subscribe",
                    Priority = 102,
                    Description = "平台下发消息指令给设备"
                },

                // 4. 设备消息上报响应指令
                new PresetTopicTemplate
                {
                    Name = "设备消息上报响应指令",
                    TopicPattern = "/devices/{device_id}/messages/control/commands/up",
                    RegexPattern = @"^/devices/([^/]+)/messages/control/commands/up$",
                    DataFormat = DataFormatEnum.Hex,
                    AccessType = "Publish",
                    Priority = 103,
                    Description = "设备消息上报响应指令"
                },

                // 5. 设备上报属性数据 (直连设备)
                new PresetTopicTemplate
                {
                    Name = "设备上报属性数据",
                    TopicPattern = "/devices/{device_id}/properties/report",
                    RegexPattern = @"^/devices/([^/]+)/properties/report$",
                    DataFormat = DataFormatEnum.Json,
                    AccessType = "Publish",
                    Priority = 104,
                    Description = "设备上报属性数据",
                    IsDirectDevice = true
                },

                // 6. 网关批量上报属性数据 (网关设备)
                new PresetTopicTemplate
                {
                    Name = "网关批量上报属性数据",
                    TopicPattern = "/devices/{device_id}/gateway/sub_devices/properties/report",
                    RegexPattern = @"^/devices/([^/]+)/gateway/sub_devices/properties/report$",
                    DataFormat = DataFormatEnum.Json,
                    AccessType = "Publish",
                    Priority = 105,
                    Description = "网关批量上报属性数据",
                    IsGatewayDevice = true
                }
            };
        }
    }

    /// <summary>
    /// 预置主题模板
    /// </summary>
    public class PresetTopicTemplate
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 主题模式
        /// </summary>
        public string TopicPattern { get; set; } = string.Empty;

        /// <summary>
        /// 正则表达式模式
        /// </summary>
        public string RegexPattern { get; set; } = string.Empty;

        /// <summary>
        /// 数据格式
        /// </summary>
        public DataFormatEnum DataFormat { get; set; }

        /// <summary>
        /// 访问类型
        /// </summary>
        public string AccessType { get; set; } = string.Empty;

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否为直连设备主题
        /// </summary>
        public bool IsDirectDevice { get; set; }

        /// <summary>
        /// 是否为网关设备主题
        /// </summary>
        public bool IsGatewayDevice { get; set; }

        /// <summary>
        /// 检查主题是否匹配此模板
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>是否匹配</returns>
        public bool IsMatch(string topic)
        {
            return Regex.IsMatch(topic, RegexPattern);
        }
    }
}
