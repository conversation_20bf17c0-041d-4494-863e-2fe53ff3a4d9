﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 告警事件实体
/// </summary>
[SugarTable("LOT_ALARM_EVENT")]
public partial class AlarmEventEntity   
{
    /// <summary>
    /// 事件id
    /// </summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public string Id { get; set; }

    /// <summary>
    /// 事件名称
    /// </summary>
    [SugarColumn(ColumnName = "NAME")]
    public string Name { get; set; }

    /// <summary>
    /// 告警等级 (1:紧急 2:严重 3：一般 4：预警)
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_LEVEL")]
    public int Level { get; set; }

    /// <summary>
    /// 告警类型
    /// 预留属性
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_TYPE")]
    public int AlarmType { get; set; }

    /// <summary>
    /// 监控状态
    /// 0: 不启用 1：启用
    /// </summary>
    [SugarColumn(ColumnName = "MONITOR_STATUS")]
    public int MonitorStatus { get; set; }  
}
