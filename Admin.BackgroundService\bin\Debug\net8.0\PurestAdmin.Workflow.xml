<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PurestAdmin.Workflow</name>
    </assembly>
    <members>
        <member name="T:PurestAdmin.Workflow.Services.DefinitionService">
            <summary>
            工作流定义服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.#ctor(SqlSugar.ISqlSugarClient,WorkflowCore.Interface.IDefinitionLoader)">
            <summary>
            工作流定义服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.GetPagedListAsync(PurestAdmin.Workflow.Services.WfDefiniationDtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.AddAsync(PurestAdmin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.PutAsync(System.Int64,PurestAdmin.Workflow.Services.WfDefiniationDtos.PutWfDefinitionInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.LockAsync(System.Int64)">
            <summary>
            锁定
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.GetDefinitionsAsync">
            <summary>
            已注册流程集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.DefinitionService.GetWorkflowContent(PurestAdmin.SqlSugar.Entity.WfDefinitionEntity)">
            <summary>
            转换
            </summary>
            <param name="entity"></param>
        </member>
        <member name="T:PurestAdmin.Workflow.Services.InstanceDtos.AuditingInput">
            <summary>
            审批输入DTO
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingInput.StepId">
            <summary>
            审批步骤
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingInput.IsAgree">
            <summary>
            是否同意
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingInput.AuditingOpinion">
            <summary>
            审批意见
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingRecordOutput.ExecutionPointerId">
            <summary>
            步骤id
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingRecordOutput.AuditingTime">
            <summary>
            审批时间
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingRecordOutput.Auditor">
            <summary>
            审批人
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingRecordOutput.AuditorName">
            <summary>
            审批人
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingRecordOutput.AuditingOpinion">
            <summary>
            审批意见
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.AuditingRecordOutput.IsAgree">
            <summary>
            是否同意
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.GetSelfPagedListInput.WorkflowStatus">
            <summary>
            工作流状态
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceDetailOutput.FormContent">
            <summary>
            表单内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceDetailOutput.FormData">
            <summary>
            流程数据
            </summary>
        </member>
        <member name="T:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput">
            <summary>
            待审批数据
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.PersistenceId">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.Description">
            <summary>
            流程描述
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.Data">
            <summary>
            数据
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.Status">
            <summary>
            流程状态
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.Definition">
            <summary>
            流程设计
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.CurrentNodeName">
            <summary>
            当前节点名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.CurrentNodeStatusString">
            <summary>
            当前节点状态
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.InstanceOutput.ExecutionPointers">
            <summary>
            审批节点
            </summary>
        </member>
        <member name="T:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput">
            <summary>
            待审批数据
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput.PersistenceId">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput.CreateByName">
            <summary>
            流程发起人
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput.Description">
            <summary>
            流程描述
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput.StepId">
            <summary>
            步骤Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.InstanceDtos.WaitingAuditingOutput.ExecutionPointers">
            <summary>
            审批步骤
            </summary>
        </member>
        <member name="T:PurestAdmin.Workflow.Services.InstanceService">
            <summary>
            流程实例服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.InstanceService.#ctor(WorkflowCore.Interface.IWorkflowHost,SqlSugar.ISqlSugarClient,PurestAdmin.Multiplex.Contracts.IAdminUser.ICurrentUser,Volo.Abp.BackgroundJobs.IBackgroundJobManager)">
            <summary>
            流程实例服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.InstanceService.GetSelfPagedListAsync(PurestAdmin.Workflow.Services.InstanceDtos.GetSelfPagedListInput)">
            <summary>
            我的流程
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.InstanceService.GetAuditingPagedListAsync(PurestAdmin.Workflow.Services.InstanceDtos.GetWaitingPagedListInput)">
            <summary>
            待办事项
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.InstanceService.StartAsync(System.Int64,PurestAdmin.Workflow.DataTypes.GeneralAuditingDefinition)">
            <summary>
            开始流程
            </summary>
            <param name="id"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.InstanceService.TerminateAsync(System.Int64)">
            <summary>
            终止流程
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.InstanceService.AuditingAsync(System.Int64,PurestAdmin.Workflow.Services.InstanceDtos.AuditingInput)">
            <summary>
            审批流程
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Workflow.Services.InstanceService.GetInstanceDetailAsync(System.Int64)">
            <summary>
            获取实例详情
            </summary>
            <param name="id">实例Id</param>
            <returns></returns>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput.DesignsContent">
            <summary>
            设计器内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput.FormContent">
            <summary>
            表单内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput.IsLocked">
            <summary>
            是否锁定
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.GetPagedListInput.Name">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.GetPagedListInput.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.GetPagedListInput.IsLocked">
            <summary>
            是否锁定
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.DefinitionId">
            <summary>
            定义ID
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.WorkflowContent">
            <summary>
            流程内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.DesignsContent">
            <summary>
            设计器内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.FormContent">
            <summary>
            表单内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput.IsLocked">
            <summary>
            是否锁定
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Steps.GeneralAuditingStep.Auditor">
            <summary>
            审批人
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Steps.GeneralAuditingStep.AuditorName">
            <summary>
            审批人名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Steps.GeneralAuditingStep.AuditorType">
            <summary>
            审批人类型
            </summary>
        </member>
        <member name="P:PurestAdmin.Workflow.Steps.GeneralAuditingStep.AuditingStepType">
            <summary>
            审批步骤类型
            </summary>
        </member>
    </members>
</doc>
