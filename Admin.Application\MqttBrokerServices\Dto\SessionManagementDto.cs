using System.ComponentModel.DataAnnotations;

namespace Admin.Application.MqttBrokerServices.Dto
{
    /// <summary>
    /// 会话信息DTO
    /// </summary>
    public class SessionInfoDto
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 是否为清除会话
        /// </summary>
        public bool CleanSession { get; set; }

        /// <summary>
        /// 会话状态
        /// </summary>
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 订阅数量
        /// </summary>
        public int SubscriptionCount { get; set; }

        /// <summary>
        /// 待发送消息数量
        /// </summary>
        public int PendingMessageCount { get; set; }

        /// <summary>
        /// 待确认消息数量
        /// </summary>
        public int AwaitingAckCount { get; set; }

        /// <summary>
        /// 待完成消息数量
        /// </summary>
        public int AwaitingCompCount { get; set; }

        /// <summary>
        /// 会话持续时间(格式化字符串)
        /// </summary>
        public string Duration { get; set; } = string.Empty;

        /// <summary>
        /// 是否过期
        /// </summary>
        public bool IsExpired { get; set; }

        /// <summary>
        /// 是否为持久会话
        /// </summary>
        public bool IsPersistent { get; set; }
    }

    /// <summary>
    /// 会话详情DTO
    /// </summary>
    public class SessionDetailDto : SessionInfoDto
    {
        /// <summary>
        /// 订阅详情列表
        /// </summary>
        public List<SessionSubscriptionDto> Subscriptions { get; set; } = new();

        /// <summary>
        /// 待发送消息列表
        /// </summary>
        public List<PendingMessageDto> PendingMessages { get; set; } = new();

        /// <summary>
        /// 会话统计信息
        /// </summary>
        public SessionStatisticsDto Statistics { get; set; } = new();
    }

    /// <summary>
    /// 会话订阅信息DTO
    /// </summary>
    public class SessionSubscriptionDto
    {
        /// <summary>
        /// 主题过滤器
        /// </summary>
        public string TopicFilter { get; set; } = string.Empty;

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int Qos { get; set; }

        /// <summary>
        /// 订阅时间
        /// </summary>
        public DateTime SubscribedTime { get; set; }
    }

    /// <summary>
    /// 待发送消息DTO
    /// </summary>
    public class PendingMessageDto
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public int MessageId { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int Qos { get; set; }

        /// <summary>
        /// 消息载荷(Base64编码)
        /// </summary>
        public string Payload { get; set; } = string.Empty;

        /// <summary>
        /// 入队时间
        /// </summary>
        public DateTime EnqueuedTime { get; set; }

        /// <summary>
        /// 消息大小(字节)
        /// </summary>
        public int PayloadSize { get; set; }
    }

    /// <summary>
    /// 会话统计信息DTO
    /// </summary>
    public class SessionStatisticsDto
    {
        /// <summary>
        /// 订阅数量
        /// </summary>
        public int SubscriptionCount { get; set; }

        /// <summary>
        /// 待发送消息数量
        /// </summary>
        public int PendingMessageCount { get; set; }

        /// <summary>
        /// 已发送消息数量
        /// </summary>
        public long SentMessageCount { get; set; }

        /// <summary>
        /// 已接收消息数量
        /// </summary>
        public long ReceivedMessageCount { get; set; }

        /// <summary>
        /// 消息发送失败数量
        /// </summary>
        public long FailedMessageCount { get; set; }

        /// <summary>
        /// 最后发送消息时间
        /// </summary>
        public DateTime? LastSentMessageTime { get; set; }

        /// <summary>
        /// 最后接收消息时间
        /// </summary>
        public DateTime? LastReceivedMessageTime { get; set; }
    }

    /// <summary>
    /// 会话查询请求DTO
    /// </summary>
    public class GetSessionsRequest : PagedRequestDto
    {
        /// <summary>
        /// 客户端ID过滤
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 会话状态过滤
        /// </summary>
        public string? State { get; set; }

        /// <summary>
        /// 是否持久会话过滤
        /// </summary>
        public bool? Persistent { get; set; }

        /// <summary>
        /// 是否过期过滤
        /// </summary>
        public bool? Expired { get; set; }
    }

    /// <summary>
    /// 会话列表响应DTO
    /// </summary>
    public class GetSessionsResponse : PagedResponseDto<SessionInfoDto>
    {
        public GetSessionsResponse(List<SessionInfoDto> sessions, int totalCount, int page, int pageSize)
            : base(sessions, totalCount, page, pageSize)
        {
        }
    }

    /// <summary>
    /// 会话状态更新请求DTO
    /// </summary>
    public class UpdateSessionStateRequest
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 操作原因
        /// </summary>
        public string? Reason { get; set; }
    }

    /// <summary>
    /// 会话状态更新响应DTO
    /// </summary>
    public class UpdateSessionStateResponse
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 原状态
        /// </summary>
        public string OldState { get; set; } = string.Empty;

        /// <summary>
        /// 新状态
        /// </summary>
        public string NewState { get; set; } = string.Empty;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 操作原因
        /// </summary>
        public string? Reason { get; set; }
    }

    /// <summary>
    /// 会话清理响应DTO
    /// </summary>
    public class CleanupSessionResponse
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 清理时间
        /// </summary>
        public DateTime CleanupTime { get; set; }

        /// <summary>
        /// 删除的订阅数量
        /// </summary>
        public int DeletedSubscriptions { get; set; }

        /// <summary>
        /// 删除的待发送消息数量
        /// </summary>
        public int DeletedPendingMessages { get; set; }

        /// <summary>
        /// 删除的待确认消息数量
        /// </summary>
        public int DeletedAwaitingAckMessages { get; set; }

        /// <summary>
        /// 删除的待完成消息数量
        /// </summary>
        public int DeletedAwaitingCompMessages { get; set; }
    }

    /// <summary>
    /// 批量会话操作请求DTO
    /// </summary>
    public class BatchSessionOperationRequest
    {
        /// <summary>
        /// 会话ID列表
        /// </summary>
        [Required]
        public List<string> SessionIds { get; set; } = new();

        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// 操作原因
        /// </summary>
        public string? Reason { get; set; }
    }

    /// <summary>
    /// 批量会话操作结果DTO
    /// </summary>
    public class SessionOperationResult
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? OperationTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? Error { get; set; }
    }

    /// <summary>
    /// 批量会话操作响应DTO
    /// </summary>
    public class BatchSessionOperationResponse
    {
        /// <summary>
        /// 请求操作的总数
        /// </summary>
        public int TotalRequested { get; set; }

        /// <summary>
        /// 成功操作的数量
        /// </summary>
        public int SuccessfulOperations { get; set; }

        /// <summary>
        /// 失败操作的数量
        /// </summary>
        public int FailedOperations { get; set; }

        /// <summary>
        /// 操作结果列表
        /// </summary>
        public List<SessionOperationResult> Results { get; set; } = new();
    }

    /// <summary>
    /// 会话管理操作结果
    /// </summary>
    public class SessionManagementResult
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 返回数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static SessionManagementResult Success(string message, object? data = null)
        {
            return new SessionManagementResult
            {
                IsSuccess = true,
                Message = message,
                Data = data
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static SessionManagementResult Error(string errorMessage)
        {
            return new SessionManagementResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Message = "操作失败"
            };
        }
    }
} 