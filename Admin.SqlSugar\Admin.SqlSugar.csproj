﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoWarn>1701;1702;1591;8618;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Serilog" Version="4.0.1" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.166" />
    <PackageReference Include="Volo.Abp" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.Autofac" Version="8.2.1" />
    <PackageReference Include="Volo.Abp.Timing" Version="8.2.1" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="SqlSugar;" />
  </ItemGroup>

</Project>
