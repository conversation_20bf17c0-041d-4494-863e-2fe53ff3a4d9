// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using SqlSugar;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// MQTT用户实体
/// </summary>
[SugarTable("MQTT_USER")]
public class MqttUserEntity : BaseEntity
{
    /// <summary>
    /// 用户名
    /// </summary>
    [SugarColumn(ColumnName = "USERNAME", Length = 50, IsNullable = false)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [SugarColumn(ColumnName = "PASSWORD", Length = 100, IsNullable = false)]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 客户端ID限制，多个用逗号分隔，为空表示不限制
    /// </summary>
    [SugarColumn(ColumnName = "CLIENT_ID_LIMIT", Length = 500, IsNullable = true)]
    public string? ClientIdLimit { get; set; }

    /// <summary>
    /// 过期时间，为空表示永不过期
    /// </summary>
    [SugarColumn(ColumnName = "EXPIRE_TIME", IsNullable = true)]
    public DateTime? ExpireTime { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    [SugarColumn(ColumnName = "LAST_LOGIN_TIME", IsNullable = true)]
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 最后登录IP
    /// </summary>
    [SugarColumn(ColumnName = "LAST_LOGIN_IP", Length = 50, IsNullable = true)]
    public string? LastLoginIp { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnName = "DESCRIPTION", Length = 200, IsNullable = true)]
    public string? Description { get; set; }

    /// <summary>
    /// 是否启用 (1=启用, 0=禁用)
    /// </summary>
    [SugarColumn(ColumnName = "IS_ENABLED")]
    public bool IsEnabled { get; set; } = true;
}