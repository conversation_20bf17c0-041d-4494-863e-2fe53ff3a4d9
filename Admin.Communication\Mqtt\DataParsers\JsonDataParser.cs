using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Admin.Multiplex.Contracts.Enums;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.DataParsers
{
    /// <summary>
    /// JSON数据解析器
    /// </summary>
    public class JsonDataParser : IDataParser
    {
        private readonly ILogger<JsonDataParser> _logger;

        /// <summary>
        /// 支持的数据格式
        /// </summary>
        public DataFormatEnum SupportedFormat => DataFormatEnum.Json;

        /// <summary>
        /// 解析器名称
        /// </summary>
        public string Name => "JsonDataParser";

        /// <summary>
        /// 解析器描述
        /// </summary>
        public string Description => "JSON格式数据解析器，支持直连设备和网关设备的JSON数据解析";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public JsonDataParser(ILogger<JsonDataParser> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 是否可以解析指定的载荷数据
        /// </summary>
        /// <param name="payload">载荷数据</param>
        /// <param name="context">解析上下文</param>
        /// <returns>是否可以解析</returns>
        public bool CanParse(byte[] payload, DeviceParseContext context)
        {
            if (payload == null || payload.Length == 0)
                return false;

            try
            {
                var jsonString = Encoding.UTF8.GetString(payload);
                if (string.IsNullOrWhiteSpace(jsonString))
                    return false;

                // 尝试解析JSON以验证格式
                using var document = JsonDocument.Parse(jsonString);
                return document.RootElement.ValueKind == JsonValueKind.Object;
            }
            catch (JsonException)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查JSON格式时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 异步解析设备数据
        /// </summary>
        /// <param name="payload">载荷数据</param>
        /// <param name="context">解析上下文</param>
        /// <returns>解析结果</returns>
        public async Task<DeviceDataParseResult> ParseAsync(byte[] payload, DeviceParseContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _logger.LogDebug("开始解析JSON数据: 设备={DeviceId}, 载荷大小={PayloadSize}字节", 
                    context.Device.DeviceId, payload?.Length ?? 0);

                if (!CanParse(payload, context))
                {
                    return DeviceDataParseResult.Failure("载荷数据不是有效的JSON格式");
                }

                var jsonString = Encoding.UTF8.GetString(payload!);
                using var document = JsonDocument.Parse(jsonString);
                var rootElement = document.RootElement;

                List<DeviceDataItem> dataItems;

                if (context.IsGatewayDevice)
                {
                    // 网关设备：处理嵌套JSON
                    dataItems = await ParseGatewayDeviceDataAsync(rootElement, context);
                }
                else
                {
                    // 直连设备：直接解析JSON
                    dataItems = await ParseDirectDeviceDataAsync(rootElement, context);
                }

                stopwatch.Stop();
                
                _logger.LogInformation("JSON数据解析完成: 设备={DeviceId}, 解析出{Count}个属性, 耗时={ElapsedMs}ms",
                    context.Device.DeviceId, dataItems.Count, stopwatch.ElapsedMilliseconds);

                return DeviceDataParseResult.Success(dataItems, jsonString);
            }
            catch (JsonException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "JSON格式错误: 设备={DeviceId}", context.Device.DeviceId);
                return DeviceDataParseResult.Failure("JSON格式错误", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "解析JSON数据时发生异常: 设备={DeviceId}", context.Device.DeviceId);
                return DeviceDataParseResult.Failure($"解析异常: {ex.Message}", ex);
            }
            finally
            {
                if (stopwatch.IsRunning)
                    stopwatch.Stop();
            }
        }

        /// <summary>
        /// 解析直连设备数据
        /// </summary>
        private async Task<List<DeviceDataItem>> ParseDirectDeviceDataAsync(JsonElement rootElement, DeviceParseContext context)
        {
            _logger.LogDebug("解析直连设备JSON数据: {DeviceName}({DeviceId})", 
                context.Device.DeviceName, context.Device.DeviceId);

            var dataItems = new List<DeviceDataItem>();

            // 遍历JSON中的所有属性
            foreach (var jsonProperty in rootElement.EnumerateObject())
            {
                var propertyKey = jsonProperty.Name;
                var valueElement = jsonProperty.Value;

                // 查找对应的模型属性定义
                var modelProperty = context.ModelProperties.FirstOrDefault(p => p.JsonKey == propertyKey);
                
                var dataItem = CreateDataItem(propertyKey, valueElement, modelProperty);
                dataItems.Add(dataItem);

                if (modelProperty != null)
                {
                    _logger.LogDebug("✓ 已定义属性: {PropertyName}({PropertyKey}) = {Value} [{DataType}] {Unit}",
                        modelProperty.Name, propertyKey, dataItem.Value,
                        GetDataTypeName(modelProperty.DataType), modelProperty.Unit ?? "");
                }
                else
                {
                    _logger.LogWarning("⚠ 未定义属性: {PropertyKey} = {Value}", propertyKey, dataItem.Value);
                }
            }

            // 检查模型中定义但JSON中缺失的属性
            foreach (var modelProperty in context.ModelProperties)
            {
                if (!rootElement.TryGetProperty(modelProperty.JsonKey, out _))
                {
                    _logger.LogWarning("✗ JSON数据缺失属性: {PropertyName}({PropertyKey})",
                        modelProperty.Name, modelProperty.JsonKey);
                }
            }

            await Task.CompletedTask;
            return dataItems;
        }

        /// <summary>
        /// 解析网关设备数据
        /// </summary>
        private async Task<List<DeviceDataItem>> ParseGatewayDeviceDataAsync(JsonElement rootElement, DeviceParseContext context)
        {
            _logger.LogDebug("解析网关设备JSON数据: {DeviceName}({DeviceId})",
                context.Device.DeviceName, context.Device.DeviceId);

            var dataItems = new List<DeviceDataItem>();

            // 网关设备直接解析所有分组
            _logger.LogDebug("网关设备解析所有分组");
            dataItems.AddRange(await ParseAllGroupsAsync(rootElement, context));

            return dataItems;
        }

        /// <summary>
        /// 解析单个分组的数据
        /// </summary>
        private async Task<List<DeviceDataItem>> ParseSingleGroupAsync(JsonElement groupElement, string groupKey, DeviceParseContext context)
        {
            _logger.LogDebug("解析分组 [{GroupKey}]", groupKey);

            var dataItems = new List<DeviceDataItem>();

            foreach (var jsonProperty in groupElement.EnumerateObject())
            {
                var propertyKey = jsonProperty.Name;
                var valueElement = jsonProperty.Value;

                var modelProperty = context.ModelProperties.FirstOrDefault(p => p.JsonKey == propertyKey);
                var dataItem = CreateDataItem(propertyKey, valueElement, modelProperty);
                dataItems.Add(dataItem);

                if (modelProperty != null)
                {
                    _logger.LogDebug("  ✓ {PropertyName}({PropertyKey}) = {Value} [{DataType}] {Unit}",
                        modelProperty.Name, propertyKey, dataItem.Value,
                        GetDataTypeName(modelProperty.DataType), modelProperty.Unit ?? "");
                }
                else
                {
                    _logger.LogWarning("  ⚠ 未定义属性: {PropertyKey} = {Value}", propertyKey, dataItem.Value);
                }
            }

            await Task.CompletedTask;
            return dataItems;
        }

        /// <summary>
        /// 解析所有分组的数据
        /// </summary>
        private async Task<List<DeviceDataItem>> ParseAllGroupsAsync(JsonElement rootElement, DeviceParseContext context)
        {
            var dataItems = new List<DeviceDataItem>();

            foreach (var groupProperty in rootElement.EnumerateObject())
            {
                var groupKey = groupProperty.Name;
                var groupElement = groupProperty.Value;

                if (groupElement.ValueKind == JsonValueKind.Object)
                {
                    _logger.LogDebug("发现分组: {GroupKey}", groupKey);
                    var groupItems = await ParseSingleGroupAsync(groupElement, groupKey, context);
                    dataItems.AddRange(groupItems);
                }
                else
                {
                    _logger.LogDebug("跳过非对象属性: {PropertyKey} = {Value}", groupKey, groupElement.GetRawText());
                }
            }

            return dataItems;
        }

        /// <summary>
        /// 创建数据项
        /// </summary>
        private DeviceDataItem CreateDataItem(string propertyKey, JsonElement valueElement, Admin.SqlSugar.Entity.Business.LOT.ModelPropertyEntity? modelProperty)
        {
            var dataItem = new DeviceDataItem
            {
                PropertyKey = propertyKey,
                IsDefined = modelProperty != null,
                DataTime = DateTime.Now
            };

            if (modelProperty != null)
            {
                dataItem.PropertyId = modelProperty.Id;
                dataItem.PropertyName = modelProperty.Name;
                dataItem.DataType = modelProperty.DataType;
                dataItem.Unit = modelProperty.Unit ?? string.Empty;
                dataItem.Value = ExtractValueFromJsonElement(valueElement, modelProperty.DataType);
            }
            else
            {
                dataItem.PropertyName = propertyKey;
                dataItem.DataType = 4; // 默认字符串类型
                dataItem.Unit = string.Empty;
                dataItem.Value = valueElement.GetRawText();
            }

            return dataItem;
        }

        /// <summary>
        /// 从JSON元素中提取值
        /// </summary>
        private object? ExtractValueFromJsonElement(JsonElement element, int dataType)
        {
            try
            {
                return dataType switch
                {
                    1 => element.GetInt32(),                    // int整形
                    2 => element.GetInt64(),                    // long长整型
                    3 => element.GetDecimal(),                  // decimal小数
                    4 => element.GetString(),                   // string字符串
                    5 => element.GetDateTime(),                 // datetime日期时间
                    6 => element.GetRawText(),                  // JSON结构体
                    7 => element.GetString(),                   // enum枚举
                    8 => element.GetBoolean(),                  // boolean布尔
                    9 => element.GetRawText(),                  // stringList数组
                    _ => element.GetString()                    // 默认字符串
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取JSON值失败，数据类型: {DataType}", dataType);
                return element.GetRawText();
            }
        }

        /// <summary>
        /// 获取数据类型名称
        /// </summary>
        private static string GetDataTypeName(int dataType)
        {
            return dataType switch
            {
                1 => "int整形",
                2 => "long长整型",
                3 => "decimal小数",
                4 => "string字符串",
                5 => "datetime日期时间",
                6 => "JSON结构体",
                7 => "enum枚举",
                8 => "boolean布尔",
                9 => "stringList数组",
                _ => "未知类型"
            };
        }
    }
}
