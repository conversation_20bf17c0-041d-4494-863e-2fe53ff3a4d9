// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity;

/// <summary>
/// 事件
/// </summary>
[SugarTable("CMS_WF_EVENT")]
public partial class WfEventEntity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "PERSISTENCE_ID", IsPrimaryKey = true)]
    public long PersistenceId { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "EVENT_ID")]
    public string EventId { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "EVENT_NAME")]
    public string EventName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "EVENT_KEY")]
    public string EventKey { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "EVENT_DATA")]
    public string EventData { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "EVENT_TIME")]
    public DateTime EventTime { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [SugarColumn(ColumnName = "IS_PROCESSED")]
    public bool IsProcessed { get; set; }
}