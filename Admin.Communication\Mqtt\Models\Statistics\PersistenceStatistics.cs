// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Communication.Mqtt.Models.Statistics;

/// <summary>
/// 持久化统计信息
/// </summary>
public class PersistenceStatistics
{
    /// <summary>
    /// 是否启用持久化
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 持久会话数量
    /// </summary>
    public int PersistentSessionCount { get; set; }

    /// <summary>
    /// 总订阅数量
    /// </summary>
    public int TotalSubscriptionCount { get; set; }

    /// <summary>
    /// 待发送消息数量
    /// </summary>
    public int PendingMessageCount { get; set; }

    /// <summary>
    /// 发送中消息数量
    /// </summary>
    public int SendingMessageCount { get; set; }

    /// <summary>
    /// 已发送消息数量
    /// </summary>
    public int SentMessageCount { get; set; }

    /// <summary>
    /// 已确认消息数量
    /// </summary>
    public int AcknowledgedMessageCount { get; set; }

    /// <summary>
    /// 发送失败消息数量
    /// </summary>
    public int FailedMessageCount { get; set; }

    /// <summary>
    /// 过期消息数量
    /// </summary>
    public int ExpiredMessageCount { get; set; }

    /// <summary>
    /// 总消息数量
    /// </summary>
    public int TotalMessageCount { get; set; }
}
