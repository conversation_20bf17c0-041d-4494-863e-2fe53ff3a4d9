// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Multiplex.Contracts;
using System.ComponentModel.DataAnnotations;

namespace Admin.Application.ControlCmdServices.Dto;

/// <summary>
/// 控制计划分页查询输入DTO
/// </summary>
public class ControlPlanPagedInput : PaginationParams
{
    /// <summary>
    /// 计划名称 (模糊查询)
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 触发方式 (1:重复执行 2:条件执行)
    /// </summary>
    public int? TriggerType { get; set; }

    /// <summary>
    /// 监控状态 (0:不启用 1:启用)
    /// </summary>
    public int? IsAppControl { get; set; }
}

/// <summary>
/// 添加控制计划输入DTO
/// </summary>
public class AddControlPlanInput
{
    /// <summary>
    /// 计划名称
    /// </summary>
    [Required(ErrorMessage = "计划名称不能为空")]
    [StringLength(100, ErrorMessage = "计划名称长度不能超过100个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 触发方式 (1:重复执行 2:条件执行)
    /// </summary>
    [Required(ErrorMessage = "触发方式不能为空")]
    [Range(1, 2, ErrorMessage = "触发方式只能为1(重复执行)或2(条件执行)")]
    public int TriggerType { get; set; }

    /// <summary>
    /// 执行方式 (1:时间点 2:间隔) - 重复执行时必填
    /// </summary>
    public int? ExecuteType { get; set; }

    /// <summary>
    /// 时间点 (HH:mm，多个用逗号分隔) - 执行方式为时间点时必填
    /// </summary>
    public string? TimePoint { get; set; }

    /// <summary>
    /// 间隔 - 执行方式为间隔时必填
    /// </summary>
    public int? Interval { get; set; }

    /// <summary>
    /// 间隔单位 (1:分钟 2:小时) - 执行方式为间隔时必填
    /// </summary>
    public int? IntervalUnit { get; set; }

    /// <summary>
    /// 方法 (1:大于参考值 2:小于参考值 3:等于参考值) - 条件执行时必填
    /// </summary>
    public int? Method { get; set; }

    /// <summary>
    /// 关联参数ID - 条件执行时必填
    /// </summary>
    public long? ParameterId { get; set; }

    /// <summary>
    /// 参考值 - 条件执行时必填
    /// </summary>
    public decimal? ReferenceValue { get; set; }

    /// <summary>
    /// 指令ID集 (逗号分隔)
    /// </summary>
    [Required(ErrorMessage = "指令ID集不能为空")]
    public string CmdIds { get; set; }

    /// <summary>
    /// 指令间隔 (单位：毫秒)
    /// </summary>
    [Range(100, int.MaxValue, ErrorMessage = "指令间隔不能小于100毫秒")]
    public int CmdInterval { get; set; } = 1000;

    /// <summary>
    /// 监控状态 (0:不启用 1:启用)
    /// </summary>
    [Range(0, 1, ErrorMessage = "监控状态只能为0或1")]
    public int IsAppControl { get; set; } = 1;
}

/// <summary>
/// 更新控制计划输入DTO
/// </summary>
public class UpdateControlPlanInput
{
    /// <summary>
    /// 计划ID
    /// </summary>
    [Required(ErrorMessage = "计划ID不能为空")]
    [Range(1, long.MaxValue, ErrorMessage = "计划ID必须大于0")]
    public long Id { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    [Required(ErrorMessage = "计划名称不能为空")]
    [StringLength(100, ErrorMessage = "计划名称长度不能超过100个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 触发方式 (1:重复执行 2:条件执行)
    /// </summary>
    [Required(ErrorMessage = "触发方式不能为空")]
    [Range(1, 2, ErrorMessage = "触发方式只能为1(重复执行)或2(条件执行)")]
    public int TriggerType { get; set; }

    /// <summary>
    /// 执行方式 (1:时间点 2:间隔) - 重复执行时必填
    /// </summary>
    public int? ExecuteType { get; set; }

    /// <summary>
    /// 时间点 (HH:mm，多个用逗号分隔) - 执行方式为时间点时必填
    /// </summary>
    public string? TimePoint { get; set; }

    /// <summary>
    /// 间隔 - 执行方式为间隔时必填
    /// </summary>
    public int? Interval { get; set; }

    /// <summary>
    /// 间隔单位 (1:分钟 2:小时) - 执行方式为间隔时必填
    /// </summary>
    public int? IntervalUnit { get; set; }

    /// <summary>
    /// 方法 (1:大于参考值 2:小于参考值 3:等于参考值) - 条件执行时必填
    /// </summary>
    public int? Method { get; set; }

    /// <summary>
    /// 关联参数ID - 条件执行时必填
    /// </summary>
    public long? ParameterId { get; set; }

    /// <summary>
    /// 参考值 - 条件执行时必填
    /// </summary>
    public decimal? ReferenceValue { get; set; }

    /// <summary>
    /// 指令ID集 (逗号分隔)
    /// </summary>
    [Required(ErrorMessage = "指令ID集不能为空")]
    public string CmdIds { get; set; }

    /// <summary>
    /// 指令间隔 (单位：毫秒)
    /// </summary>
    [Range(100, int.MaxValue, ErrorMessage = "指令间隔不能小于100毫秒")]
    public int CmdInterval { get; set; } = 1000;

    /// <summary>
    /// 监控状态 (0:不启用 1:启用)
    /// </summary>
    [Range(0, 1, ErrorMessage = "监控状态只能为0或1")]
    public int IsAppControl { get; set; } = 1;
}

/// <summary>
/// 控制计划输出DTO
/// </summary>
public class ControlPlanOutput
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 触发方式 (1:重复执行 2:条件执行)
    /// </summary>
    public int TriggerType { get; set; }

    /// <summary>
    /// 触发方式名称
    /// </summary>
    public string TriggerTypeName { get; set; }

    /// <summary>
    /// 执行方式 (1:时间点 2:间隔)
    /// </summary>
    public int? ExecuteType { get; set; }

    /// <summary>
    /// 执行方式名称
    /// </summary>
    public string? ExecuteTypeName { get; set; }

    /// <summary>
    /// 时间点 (HH:mm，多个用逗号分隔)
    /// </summary>
    public string? TimePoint { get; set; }

    /// <summary>
    /// 间隔
    /// </summary>
    public int? Interval { get; set; }

    /// <summary>
    /// 间隔单位 (1:分钟 2:小时)
    /// </summary>
    public int? IntervalUnit { get; set; }

    /// <summary>
    /// 间隔单位名称
    /// </summary>
    public string? IntervalUnitName { get; set; }

    /// <summary>
    /// 方法 (1:大于参考值 2:小于参考值 3:等于参考值)
    /// </summary>
    public int? Method { get; set; }

    /// <summary>
    /// 方法名称
    /// </summary>
    public string? MethodName { get; set; }

    /// <summary>
    /// 关联参数ID
    /// </summary>
    public long? ParameterId { get; set; }

    /// <summary>
    /// 参考值
    /// </summary>
    public decimal? ReferenceValue { get; set; }

    /// <summary>
    /// 指令ID集
    /// </summary>
    public string CmdIds { get; set; }

    /// <summary>
    /// 指令间隔 (单位：毫秒)
    /// </summary>
    public int CmdInterval { get; set; }

    /// <summary>
    /// 监控状态 (0:不启用 1:启用)
    /// </summary>
    public int IsAppControl { get; set; }

    /// <summary>
    /// 监控状态名称
    /// </summary>
    public string IsAppControlName { get; set; }
}
