---
type: "always_apply"
---

# 开发规范与编码规则

## 项目概述
本项目是基于.NET 8开发的分散式数据机房单点式物联网集中监控管理系统，基于ABP Framework精简版构建，支持MQTT协议的设备数据采集和集中告警管理。

## 架构层次规范

### 层次职责
- **Admin.Api.Host**: 项目入口层，配置中间件、授权策略等
- **Admin.Application**: 应用层，自动生成API接口，处理业务逻辑
- **Admin.Core**: 核心层，基础功能实现和组件封装
- **Admin.Multiplex**: 复合层，后台功能和复用功能
- **Admin.Multiplex.Contracts**: 复合层契约，抽象层和常量定义
- **Admin.SqlSugar**: ORM层，数据访问
- **Admin.Communication**: 通信层，MQTT架构支持
- **Admin.Workflow**: 工作流层，业务流程处理
- **Admin.BackgroundService**: 后台服务层，定时任务和后台作业
- **Admin.Zero**: 零代码层，代码生成和模板功能

## 编码规范

### 1. 服务类开发规范

#### 构造函数模式
使用C# 12主构造函数语法：
```csharp
public class ExampleService(ISqlSugarClient db, Repository<Entity> repository) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<Entity> _repository = repository;
}
```

#### 依赖注入规则
- 继承`ApplicationService`的服务自动注册到DI容器
- 无需手动在模块中注册应用层服务
- 必须使用接口定义服务契约
- 优先使用构造函数注入

### 2. 返回类型规范

#### 分页查询
```csharp
// 直接返回PagedList<T>，不使用Result包装
public async Task<PagedList<UserOutput>> GetPagedListAsync(UserPagedInput input)
{
    var query = _repository.AsQueryable();
    var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
    return pagedList.Adapt<PagedList<UserOutput>>();
}
```

#### 单个对象查询
```csharp
// 直接返回具体DTO类型
public async Task<UserOutput> GetByIdAsync(long id)
{
    var entity = await _repository.GetByIdAsync(id) ?? 
        throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
    return entity.Adapt<UserOutput>();
}
```

#### 操作结果
```csharp
// 返回具体的Output DTO
public async Task<UserOutput> CreateAsync(AddUserInput input)
{
    var entity = input.Adapt<User>();
    await _repository.InsertAsync(entity);
    return entity.Adapt<UserOutput>();
}
```

### 3. DTO命名规范

#### 输入DTO
- 使用`Input`后缀：`AddUserInput`, `UpdateUserInput`, `DeleteUserInput`
- 查询DTO继承`PaginationParams`：`UserPagedInput`

#### 输出DTO
- 使用`Output`后缀：`UserOutput`, `UserListOutput`
- 列表输出使用`PagedList<T>`包装

#### 示例
```csharp
public class AddUserInput
{
    public string Name { get; set; }
    public string Email { get; set; }
}

public class UserPagedInput : PaginationParams
{
    public string? Name { get; set; }
    public string? Email { get; set; }
}

public class UserOutput
{
    public long Id { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public DateTime CreateTime { get; set; }
}
```

### 4. 对象映射规范

#### 使用Mapster进行映射
```csharp
// Entity -> DTO
var output = entity.Adapt<UserOutput>();

// Input -> Entity
var entity = input.Adapt<User>();

// 分页映射
var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
return pagedList.Adapt<PagedList<UserOutput>>();

// 批量映射
var outputs = entities.Adapt<List<UserOutput>>();
```

### 5. 异常处理规范

#### 验证异常
```csharp
// 自定义错误信息
throw PersistdValidateException.Message("用户名不能为空");

// 使用错误枚举
throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

// 参数验证
if (string.IsNullOrEmpty(input.Name))
    throw PersistdValidateException.Message("用户名不能为空");
```

#### 空结果处理
```csharp
var entity = await _repository.GetByIdAsync(id) ?? 
    throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
```

### 6. API控制器规范

#### 自动生成规则
- 应用层服务自动生成API控制器
- 使用`[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.XXX)]`进行分组
- 路由前缀统一为`v1`

#### 分组示例
```csharp
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.System)]
public class UserService : ApplicationService
{
    // 服务方法
}
```

### 7. 数据库实体规范

#### 基础实体
```csharp
public class User : BaseEntity
{
    public string Name { get; set; }
    public string Email { get; set; }
    // BaseEntity提供: Id, CreateBy, CreateTime, UpdateBy, UpdateTime, Remark
}
```

#### 主键规范
- 使用雪花ID作为主键
- 类型为`long`
- 字段名为`Id`

#### 审计字段
- `CreateBy`: 创建人
- `CreateTime`: 创建时间
- `UpdateBy`: 更新人
- `UpdateTime`: 更新时间
- `Remark`: 备注

### 8. 日志规范

#### 使用Serilog

### 9. MQTT通信规范

#### 消息处理
- 使用Admin.Communication层处理MQTT消息
- 支持MQTT 3.1和3.1.1协议
- 消息格式统一使用JSON

### 10. 后台服务规范

#### 定时任务
- 使用Admin.BackgroundService层
- 继承相应的后台服务基类
- 配置任务调度策略

## 技术栈使用规范

### 核心组件
- **.NET 8**: 主要开发框架
- **SqlSugar**: ORM框架，支持多数据库
- **Mapster**: 对象映射
- **Serilog**: 日志记录
- **SignalR**: 实时通信

### 数据库
- **MySQL**: 主数据库
- 使用SqlSugar进行数据访问
- 支持多数据库切换

## 代码质量要求

### 1. 命名规范
- 类名使用PascalCase
- 方法名使用PascalCase
- 属性名使用PascalCase
- 字段名使用camelCase，私有字段加下划线前缀
- 常量使用UPPER_CASE

### 2. 注释规范
- 公共API必须添加XML文档注释
- 复杂业务逻辑添加行内注释
- 使用中文注释说明业务含义

### 3. 性能要求
- 数据库查询使用异步方法
- 大数据量操作使用分页
- 合理使用缓存机制

### 4. 安全要求
- 输入参数必须验证
- 敏感信息不记录到日志
- 使用授权策略控制访问权限
