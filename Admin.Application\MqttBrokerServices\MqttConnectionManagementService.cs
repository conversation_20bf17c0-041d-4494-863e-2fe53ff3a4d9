using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using Admin.Application.MqttBrokerServices.Dto;
using Admin.Multiplex.Contracts.Enums.Mqtt;

namespace Admin.Application.MqttBrokerServices
{
    /// <summary>
    /// MQTT连接管理服务接口
    /// </summary>
    public interface IMqttConnectionManagementService
    {
        /// <summary>
        /// 获取所有连接
        /// </summary>
        Task<ConnectionManagementResult> GetConnectionsAsync(GetConnectionsRequest request);

        /// <summary>
        /// 获取单个连接详情
        /// </summary>
        Task<ConnectionManagementResult> GetConnectionDetailAsync(string clientId);

        /// <summary>
        /// 断开指定连接
        /// </summary>
        Task<ConnectionManagementResult> DisconnectConnectionAsync(string clientId, DisconnectConnectionRequest request);

        /// <summary>
        /// 批量断开连接
        /// </summary>
        Task<ConnectionManagementResult> BatchDisconnectAsync(BatchDisconnectRequest request);
    }

    /// <summary>
    /// MQTT连接管理服务
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.MQTT)]
    public class MqttConnectionManagementService(
        IMqttBroker mqttBroker,
        IMqttConnectionManager connectionManager,
        ILogger<MqttConnectionManagementService> logger) : ApplicationService, IMqttConnectionManagementService
    {
        private readonly IMqttBroker _mqttBroker = mqttBroker ?? throw new ArgumentNullException(nameof(mqttBroker));
        private readonly IMqttConnectionManager _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        private readonly ILogger<MqttConnectionManagementService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// 获取所有连接
        /// </summary>
        public async Task<ConnectionManagementResult> GetConnectionsAsync(GetConnectionsRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return ConnectionManagementResult.Error("MQTT代理服务未运行");
                }

                // 获取所有客户端信息
                var allClients = _mqttBroker.GetConnectedClients();
                
                // 应用过滤条件
                var filteredClients = allClients.AsEnumerable();

                if (!string.IsNullOrEmpty(request.ClientId))
                {
                    filteredClients = filteredClients.Where(c => 
                        c.ClientId.Contains(request.ClientId, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(request.Username))
                {
                    filteredClients = filteredClients.Where(c => 
                        !string.IsNullOrEmpty(c.Username) && 
                        c.Username.Contains(request.Username, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(request.IpAddress))
                {
                    filteredClients = filteredClients.Where(c => 
                        c.IpAddress.Contains(request.IpAddress, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(request.Status))
                {
                    // 由于所有返回的客户端都是已连接状态，这里只处理"Connected"状态
                    if (!request.Status.Equals("Connected", StringComparison.OrdinalIgnoreCase))
                    {
                        filteredClients = Enumerable.Empty<MqttClientInfo>();
                    }
                }

                var totalCount = filteredClients.Count();

                // 应用分页
                var pagedClients = filteredClients
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // 转换为DTO
                var connectionDtos = pagedClients.Select(MapToConnectionInfo).ToList();

                var response = new GetConnectionsResponse(connectionDtos, totalCount, request.Page, request.PageSize);

                _logger.LogDebug("获取连接列表成功，总数: {TotalCount}, 当前页: {Page}, 每页大小: {PageSize}", 
                    totalCount, request.Page, request.PageSize);

                return ConnectionManagementResult.Success("获取连接列表成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取连接列表时发生错误");
                return ConnectionManagementResult.Error($"获取连接列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取单个连接详情
        /// </summary>
        public async Task<ConnectionManagementResult> GetConnectionDetailAsync(string clientId)
        {
            try
            {
                if (string.IsNullOrEmpty(clientId))
                {
                    return ConnectionManagementResult.Error("客户端ID不能为空");
                }

                if (!_mqttBroker.IsRunning)
                {
                    return ConnectionManagementResult.Error("MQTT代理服务未运行");
                }

                // 检查连接是否存在
                if (!_connectionManager.IsConnected(clientId))
                {
                    return ConnectionManagementResult.Error($"客户端 {clientId} 未连接");
                }

                // 获取客户端信息
                var clientInfo = _mqttBroker.GetConnectedClients()
                    .FirstOrDefault(c => c.ClientId == clientId);

                if (clientInfo == null)
                {
                    return ConnectionManagementResult.Error($"未找到客户端 {clientId} 的信息");
                }

                // 获取连接对象
                var connection = _connectionManager.GetConnection(clientId);
                if (connection == null)
                {
                    return ConnectionManagementResult.Error($"未找到客户端 {clientId} 的连接对象");
                }

                // 构建详细信息
                var detail = MapToConnectionDetail(clientInfo, connection);

                _logger.LogDebug("获取客户端 {ClientId} 详情成功", clientId);

                return ConnectionManagementResult.Success("获取连接详情成功", detail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户端 {ClientId} 详情时发生错误", clientId);
                return ConnectionManagementResult.Error($"获取连接详情失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 断开指定连接
        /// </summary>
        public async Task<ConnectionManagementResult> DisconnectConnectionAsync(string clientId, DisconnectConnectionRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(clientId))
                {
                    return ConnectionManagementResult.Error("客户端ID不能为空");
                }

                if (!_mqttBroker.IsRunning)
                {
                    return ConnectionManagementResult.Error("MQTT代理服务未运行");
                }

                // 检查连接是否存在
                if (!_connectionManager.IsConnected(clientId))
                {
                    return ConnectionManagementResult.Error($"客户端 {clientId} 未连接");
                }

                _logger.LogInformation("准备断开客户端连接: {ClientId}, 原因: {Reason}", 
                    clientId, request.Reason ?? "管理员操作");

                // 断开连接
                var reason = string.IsNullOrEmpty(request.Reason) ?
                    DisconnectReasonEnum.ServerDisconnect :
                    DisconnectReasonEnum.ServerDisconnect;

                await _connectionManager.RemoveConnectionAsync(clientId, reason, request.SendDisconnectMessage);

                var result = new DisconnectResult
                {
                    ClientId = clientId,
                    Success = true,
                    DisconnectTime = DateTime.UtcNow
                };

                _logger.LogInformation("客户端 {ClientId} 连接已断开", clientId);

                return ConnectionManagementResult.Success("连接断开成功", result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开客户端 {ClientId} 连接时发生错误", clientId);

                var result = new DisconnectResult
                {
                    ClientId = clientId,
                    Success = false,
                    Error = ex.Message
                };

                return ConnectionManagementResult.Success("断开连接操作已执行", result);
            }
        }

        /// <summary>
        /// 批量断开连接
        /// </summary>
        public async Task<ConnectionManagementResult> BatchDisconnectAsync(BatchDisconnectRequest request)
        {
            try
            {
                if (request.ClientIds == null || !request.ClientIds.Any())
                {
                    return ConnectionManagementResult.Error("客户端ID列表不能为空");
                }

                if (!_mqttBroker.IsRunning)
                {
                    return ConnectionManagementResult.Error("MQTT代理服务未运行");
                }

                _logger.LogInformation("准备批量断开连接，客户端数量: {Count}, 原因: {Reason}", 
                    request.ClientIds.Count, request.Reason ?? "批量管理操作");

                var results = new List<DisconnectResult>();
                var successCount = 0;
                var failureCount = 0;

                // 并行处理断开连接
                var tasks = request.ClientIds.Select(async clientId =>
                {
                    try
                    {
                        if (_connectionManager.IsConnected(clientId))
                        {
                            var reason = string.IsNullOrEmpty(request.Reason) ?
                                DisconnectReasonEnum.ServerDisconnect :
                                DisconnectReasonEnum.ServerDisconnect;

                            await _connectionManager.RemoveConnectionAsync(clientId, reason, request.SendDisconnectMessage);

                            Interlocked.Increment(ref successCount);
                            return new DisconnectResult
                            {
                                ClientId = clientId,
                                Success = true,
                                DisconnectTime = DateTime.UtcNow
                            };
                        }
                        else
                        {
                            Interlocked.Increment(ref failureCount);
                            return new DisconnectResult
                            {
                                ClientId = clientId,
                                Success = false,
                                Error = "客户端未连接"
                            };
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "断开客户端 {ClientId} 连接时发生错误", clientId);
                        Interlocked.Increment(ref failureCount);
                        return new DisconnectResult
                        {
                            ClientId = clientId,
                            Success = false,
                            Error = ex.Message
                        };
                    }
                });

                results.AddRange(await Task.WhenAll(tasks));

                var response = new BatchDisconnectResponse
                {
                    TotalRequested = request.ClientIds.Count,
                    SuccessfulDisconnects = successCount,
                    FailedDisconnects = failureCount,
                    Results = results
                };

                _logger.LogInformation("批量断开连接完成，成功: {Success}, 失败: {Failed}", 
                    successCount, failureCount);

                return ConnectionManagementResult.Success($"批量断开连接完成，成功: {successCount}, 失败: {failureCount}", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量断开连接时发生错误");
                return ConnectionManagementResult.Error($"批量断开连接失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 将MqttClientInfo映射为ConnectionInfoDto
        /// </summary>
        private ConnectionInfoDto MapToConnectionInfo(MqttClientInfo clientInfo)
        {
            return new ConnectionInfoDto
            {
                ClientId = clientInfo.ClientId,
                Username = clientInfo.Username,
                IpAddress = clientInfo.IpAddress,
                ConnectedTime = clientInfo.ConnectedTime,
                LastActivityTime = clientInfo.ConnectedTime, // 这里可以从连接管理器获取更准确的活动时间
                KeepAlive = clientInfo.KeepAlive,
                CleanSession = clientInfo.CleanSession,
                ProtocolVersion = "3.1.1", // 这里可以从连接对象获取实际版本
                Status = "Connected",
                SubscribedTopics = clientInfo.SubscribedTopics?.ToList() ?? new List<string>()
            };
        }

        /// <summary>
        /// 将客户端信息映射为连接详情
        /// </summary>
        private ConnectionDetailDto MapToConnectionDetail(MqttClientInfo clientInfo, MqttClientConnection connection)
        {
            var connectionDuration = DateTime.UtcNow - clientInfo.ConnectedTime;

            var detail = new ConnectionDetailDto
            {
                ClientId = clientInfo.ClientId,
                Username = clientInfo.Username,
                IpAddress = clientInfo.IpAddress,
                ConnectedTime = clientInfo.ConnectedTime,
                LastActivityTime = clientInfo.ConnectedTime, // 可以从连接对象获取更准确的时间
                KeepAlive = clientInfo.KeepAlive,
                CleanSession = clientInfo.CleanSession,
                ProtocolVersion = "3.1.1", // 可以从连接对象获取实际版本
                Status = "Connected",
                SubscribedTopics = clientInfo.SubscribedTopics?.ToList() ?? new List<string>(),
                ConnectionDuration = FormatDuration(connectionDuration),
                SubscriptionDetails = new List<SubscriptionDetailDto>(),
                Statistics = new ClientConnectionStatisticsDto
                {
                    MessagesSent = 0, // 这些统计信息需要从连接管理器或统计服务获取
                    MessagesReceived = 0,
                    BytesTransferred = 0,
                    LastMessageTime = null
                }
            };

            // 构建订阅详情
            if (clientInfo.SubscribedTopics != null)
            {
                detail.SubscriptionDetails = clientInfo.SubscribedTopics.Select(topic => new SubscriptionDetailDto
                {
                    TopicFilter = topic,
                    Qos = 0, // 这里需要从会话或订阅管理器获取实际QoS
                    SubscribedTime = clientInfo.ConnectedTime // 这里需要获取实际订阅时间
                }).ToList();
            }

            return detail;
        }

        /// <summary>
        /// 格式化持续时间
        /// </summary>
        private string FormatDuration(TimeSpan duration)
        {
            if (duration.TotalDays >= 1)
            {
                return $"{(int)duration.TotalDays}天{duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}";
            }
            else
            {
                return $"{duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}";
            }
        }

        #endregion
    }
} 