// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Control.Models;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Communication.Control.Interfaces;

/// <summary>
/// 指令执行器接口
/// </summary>
public interface ICommandExecutor
{
    /// <summary>
    /// 执行单个指令
    /// </summary>
    /// <param name="request">执行请求</param>
    /// <returns>执行结果</returns>
    Task<CommandExecuteResult> ExecuteAsync(ExecuteCommandRequest request);

    /// <summary>
    /// 批量执行指令
    /// </summary>
    /// <param name="request">批量执行请求</param>
    /// <returns>批量执行结果</returns>
    Task<BatchExecuteResult> BatchExecuteAsync(BatchExecuteCommandRequest request);
}

/// <summary>
/// 指令介质执行器接口
/// </summary>
public interface ICommandMediumExecutor
{
    /// <summary>
    /// 执行指令
    /// </summary>
    /// <param name="command">指令实体</param>
    /// <param name="request">执行请求</param>
    /// <returns>执行结果</returns>
    Task<CommandExecuteResult> ExecuteAsync(ControlCmdEntity command, ExecuteCommandRequest request);

    /// <summary>
    /// 是否支持指定的通讯介质
    /// </summary>
    /// <param name="communicationMedium">通讯介质类型</param>
    /// <returns>是否支持</returns>
    bool CanHandle(int communicationMedium);
}
