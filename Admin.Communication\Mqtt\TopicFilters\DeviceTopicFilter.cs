using System;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Models;
using Admin.SqlSugar;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Admin.Communication.Mqtt.TopicFilters
{
    /// <summary>
    /// 设备主题过滤器
    /// </summary>
    public class DeviceTopicFilter : ITopicFilter
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<DeviceTopicFilter> _logger;
        private readonly IConfiguration _configuration;
        private readonly PresetTopicManager _presetTopicManager;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="db">数据库客户端</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="configuration">配置</param>
        /// <param name="presetTopicManager">预置主题管理器</param>
        public DeviceTopicFilter(
            ISqlSugarClient db,
            ILogger<DeviceTopicFilter> logger,
            IConfiguration configuration,
            PresetTopicManager presetTopicManager)
        {
            _db = db;
            _logger = logger;
            _configuration = configuration;
            _presetTopicManager = presetTopicManager;
        }

        /// <summary>
        /// 判断主题是否需要进行数据处理
        /// </summary>
        /// <param name="topic">MQTT主题</param>
        /// <param name="deviceId">设备ID</param>
        /// <returns>过滤结果</returns>
        public async Task<TopicFilterResult> ShouldProcessAsync(string topic, string deviceId)
        {
            try
            {
                _logger.LogDebug("开始过滤主题: {Topic}, 设备ID: {DeviceId}", topic, deviceId);

                // 1. 检查是否为预置主题
                var presetTemplate = _presetTopicManager.MatchPresetTopic(topic);
                if (presetTemplate != null)
                {
                    return await HandlePresetTopicAsync(topic, deviceId, presetTemplate);
                }

                // 2. 检查是否为自定义主题
                var customTopicConfig = await GetCustomTopicConfigAsync(deviceId, topic);
                if (customTopicConfig != null)
                {
                    return await HandleCustomTopicAsync(topic, deviceId, customTopicConfig);
                }

                // 3. 未知主题，默认转发但不处理
                _logger.LogDebug("未知主题，转发但不处理: {Topic}", topic);
                return TopicFilterResult.CreateNoProcessResult(shouldForward: true, reason: "未知主题");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "主题过滤时发生异常: {Topic}, 设备ID: {DeviceId}", topic, deviceId);
                return TopicFilterResult.CreateNoProcessResult(shouldForward: true, reason: $"过滤异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理预置主题
        /// </summary>
        private async Task<TopicFilterResult> HandlePresetTopicAsync(string topic, string deviceId, PresetTopicTemplate template)
        {
            _logger.LogInformation("匹配到预置主题: {TemplateName}, 设备ID: {DeviceId}", template.Name, deviceId);

            // 从主题中提取设备ID并验证
            var extractedDeviceId = _presetTopicManager.ExtractDeviceId(topic, template);
            if (string.IsNullOrEmpty(extractedDeviceId))
            {
                _logger.LogWarning("无法从预置主题中提取设备ID: {Topic}", topic);
                return TopicFilterResult.CreateNoProcessResult(shouldForward: true, reason: "无法提取设备ID");
            }

            // 验证设备ID是否一致
            if (extractedDeviceId != deviceId)
            {
                _logger.LogWarning("主题中的设备ID与消息设备ID不一致: 主题={ExtractedId}, 消息={DeviceId}", 
                    extractedDeviceId, deviceId);
                return TopicFilterResult.CreateNoProcessResult(shouldForward: true, reason: "设备ID不一致");
            }

            // 检查设备是否存在
            var deviceExists = await CheckDeviceExistsAsync(deviceId);
            if (!deviceExists)
            {
                _logger.LogWarning("设备不存在: {DeviceId}", deviceId);
                return TopicFilterResult.CreateNoProcessResult(shouldForward: false, reason: "设备不存在");
            }

            // 根据环境配置决定是否转发
            var shouldForward = GetShouldForwardForPresetTopic(template);

            _logger.LogInformation("预置主题处理决策: 处理=是, 转发={ShouldForward}, 原因={Reason}", 
                shouldForward, template.Description);

            return TopicFilterResult.CreateProcessResult(TopicType.Preset, shouldForward, template.Description);

            await Task.CompletedTask;
        }

        /// <summary>
        /// 处理自定义主题
        /// </summary>
        private async Task<TopicFilterResult> HandleCustomTopicAsync(string topic, string deviceId, DeviceTopicConfigEntity config)
        {
            _logger.LogInformation("匹配到自定义主题配置: 设备ID={DeviceId}, 主题={Topic}", deviceId, topic);

            // 检查设备是否存在
            var deviceExists = await CheckDeviceExistsAsync(deviceId);
            if (!deviceExists)
            {
                _logger.LogWarning("设备不存在: {DeviceId}", deviceId);
                return TopicFilterResult.CreateNoProcessResult(shouldForward: false, reason: "设备不存在");
            }

            // 根据环境配置决定是否转发
            var shouldForward = GetShouldForwardForCustomTopic();

            _logger.LogInformation("自定义主题处理决策: 处理=是, 转发={ShouldForward}, 配置ID={ConfigId}",
                shouldForward, config.Id);

            return TopicFilterResult.CreateProcessResult(TopicType.Custom, shouldForward, "自定义主题配置");
        }

        /// <summary>
        /// 检查设备是否存在
        /// </summary>
        private async Task<bool> CheckDeviceExistsAsync(string deviceId)
        {
            try
            {
                var count = await _db.Queryable<DeviceEntity>()
                    .Where(d => d.DeviceId == deviceId)
                    .CountAsync();

                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查设备是否存在时发生异常: {DeviceId}", deviceId);
                return false;
            }
        }

        /// <summary>
        /// 获取自定义主题配置
        /// </summary>
        private async Task<DeviceTopicConfigEntity?> GetCustomTopicConfigAsync(string deviceId, string topic)
        {
            try
            {
                // 首先检查是否以自定义主题前缀开头
                var customTopicPrefix = _configuration.GetValue<string>("Mqtt:DataProcessing:CustomTopicPrefix", "/custom/");
                if (!topic.StartsWith(customTopicPrefix))
                {
                    return null;
                }

                // 查询设备主题配置表
                var config = await _db.Queryable<DeviceTopicConfigEntity>()
                    .Where(c => c.DeviceId == deviceId && c.Topic == topic)
                    .FirstAsync();

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "查询自定义主题配置失败: DeviceId={DeviceId}, Topic={Topic}", deviceId, topic);
                return null;
            }
        }

        /// <summary>
        /// 获取预置主题是否应该转发的配置
        /// </summary>
        private bool GetShouldForwardForPresetTopic(PresetTopicTemplate template)
        {
            // 开发环境：根据配置决定是否转发所有消息
            var environment = _configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT", "Development");
            var forwardAllInDev = _configuration.GetValue<bool>("Mqtt:DataProcessing:ForwardAllTopicsInDevelopment", true);
            var forwardDataInProd = _configuration.GetValue<bool>("Mqtt:DataProcessing:ForwardDataTopicsInProduction", false);

            if (environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                return forwardAllInDev; // 开发环境根据配置决定
            }

            // 正式环境：数据上报主题根据配置决定，其他主题默认转发
            var dataReportTopics = new[] { "设备上报属性数据", "网关批量上报属性数据" };
            if (Array.Exists(dataReportTopics, name => name == template.Name))
            {
                return forwardDataInProd; // 数据主题根据配置
            }

            return true; // 其他主题默认转发
        }

        /// <summary>
        /// 获取自定义主题是否应该转发的配置
        /// </summary>
        private bool GetShouldForwardForCustomTopic()
        {
            // 自定义主题的转发策略：开发环境根据配置，正式环境默认不转发
            var environment = _configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT", "Development");
            var forwardAllInDev = _configuration.GetValue<bool>("Mqtt:DataProcessing:ForwardAllTopicsInDevelopment", true);

            if (environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                return forwardAllInDev;
            }

            return false; // 正式环境自定义主题默认不转发
        }
    }
}
