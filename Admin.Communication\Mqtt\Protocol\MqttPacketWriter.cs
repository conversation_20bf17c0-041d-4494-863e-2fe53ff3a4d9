using Admin.Communication.Mqtt.Models;
using System;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Protocol
{
    /// <summary>
    /// MQTT包构建器
    /// </summary>
    public class MqttPacketWriter
    {
        /// <summary>
        /// 异步写入MQTT数据包到流
        /// </summary>
        /// <param name="stream">目标流</param>
        /// <param name="message">MQTT消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>写入任务</returns>
        public async Task WritePacketAsync(Stream stream, MqttMessage message, CancellationToken cancellationToken = default)
        {
            try
            {
                // 检查流状态
                if (stream == null || !stream.CanWrite)
                {
                    throw new InvalidOperationException("流不可用或不支持写入操作");
                }

                using (var memoryStream = new MemoryStream())
                {
                    // 构建可变头部和负载
                    byte[] variableHeaderAndPayload = BuildVariableHeaderAndPayload(message);

                    // 构建固定头部
                    byte[] fixedHeader = BuildFixedHeader(message, variableHeaderAndPayload.Length);

                    // 检查流是否仍然可写
                    if (!stream.CanWrite)
                    {
                        throw new InvalidOperationException("流在写入过程中变为不可写状态");
                    }

                    // 写入固定头部
                    await stream.WriteAsync(fixedHeader, 0, fixedHeader.Length, cancellationToken);

                    // 写入可变头部和负载
                    if (variableHeaderAndPayload.Length > 0)
                    {
                        await stream.WriteAsync(variableHeaderAndPayload, 0, variableHeaderAndPayload.Length, cancellationToken);
                    }

                    // 刷新流
                    await stream.FlushAsync(cancellationToken);
                }
            }
            catch (ObjectDisposedException)
            {
                // 流已被释放，重新抛出异常以便上层处理
                throw;
            }
            catch (IOException)
            {
                // IO异常，重新抛出异常以便上层处理
                throw;
            }
            catch (SocketException)
            {
                // Socket异常，重新抛出异常以便上层处理
                throw;
            }
            catch (InvalidOperationException)
            {
                // 无效操作异常，重新抛出异常以便上层处理
                throw;
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，重新抛出异常以便上层处理
                throw;
            }
        }

        /// <summary>
        /// 构建固定头部
        /// </summary>
        private byte[] BuildFixedHeader(MqttMessage message, int remainingLength)
        {
            using (var memoryStream = new MemoryStream())
            {
                // 第一个字节：消息类型和标志
                byte firstByte = (byte)(message.MessageType << 4);

                // 设置标志
                if (message is MqttPublishMessage publishMessage)
                {
                    // PUBLISH消息的标志
                    if (publishMessage.IsDuplicate)
                    {
                        firstByte |= MqttProtocol.FixedHeaderFlags.DupFlag;
                    }

                    firstByte |= (byte)(publishMessage.QualityOfService << 1);

                    if (publishMessage.Retain)
                    {
                        firstByte |= MqttProtocol.FixedHeaderFlags.RetainFlag;
                    }
                }
                else if (message is MqttPubRelMessage || message is MqttSubscribeMessage || message is MqttUnsubscribeMessage)
                {
                    // PUBREL, SUBSCRIBE, UNSUBSCRIBE消息的标志固定为0x02
                    firstByte |= 0x02;
                }

                memoryStream.WriteByte(firstByte);

                // 写入剩余长度
                WriteRemainingLength(memoryStream, remainingLength);

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// 构建可变头部和负载
        /// </summary>
        protected virtual byte[] BuildVariableHeaderAndPayload(MqttMessage message)
        {
            using (var memoryStream = new MemoryStream())
            {
                switch ((MqttProtocol.MessageType)message.MessageType)
                {
                    case MqttProtocol.MessageType.Connect:
                        WriteConnectVariableHeaderAndPayload(memoryStream, (MqttConnectMessage)message);
                        break;
                    case MqttProtocol.MessageType.ConnAck:
                        WriteConnAckVariableHeaderAndPayload(memoryStream, (MqttConnAckMessage)message);
                        break;
                    case MqttProtocol.MessageType.Publish:
                        WritePublishVariableHeaderAndPayload(memoryStream, (MqttPublishMessage)message);
                        break;
                    case MqttProtocol.MessageType.PubAck:
                    case MqttProtocol.MessageType.PubRec:
                    case MqttProtocol.MessageType.PubRel:
                    case MqttProtocol.MessageType.PubComp:
                    case MqttProtocol.MessageType.UnsubAck:
                        WriteMessageIdVariableHeaderAndPayload(memoryStream, message);
                        break;
                    case MqttProtocol.MessageType.Subscribe:
                        WriteSubscribeVariableHeaderAndPayload(memoryStream, (MqttSubscribeMessage)message);
                        break;
                    case MqttProtocol.MessageType.SubAck:
                        WriteSubAckVariableHeaderAndPayload(memoryStream, (MqttSubAckMessage)message);
                        break;
                    case MqttProtocol.MessageType.Unsubscribe:
                        WriteUnsubscribeVariableHeaderAndPayload(memoryStream, (MqttUnsubscribeMessage)message);
                        break;
                    case MqttProtocol.MessageType.PingReq:
                    case MqttProtocol.MessageType.PingResp:
                    case MqttProtocol.MessageType.Disconnect:
                        // 这些消息没有可变头部和负载
                        break;
                    default:
                        throw new MqttProtocolException($"不支持的MQTT消息类型: {message.MessageType}");
                }

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// 写入CONNECT消息的可变头部和负载
        /// </summary>
        private void WriteConnectVariableHeaderAndPayload(MemoryStream stream, MqttConnectMessage message)
        {
            // 协议名称
            WriteString(stream, message.ProtocolName);

            // 协议版本
            stream.WriteByte(message.ProtocolVersion);

            // 连接标志
            byte connectFlags = 0;
            if (message.CleanSession)
            {
                connectFlags |= MqttProtocol.ConnectFlags.CleanSession;
            }

            if (message.HasWill)
            {
                connectFlags |= MqttProtocol.ConnectFlags.Will;
                connectFlags |= (byte)(message.WillQoS << 3);
                if (message.WillRetain)
                {
                    connectFlags |= MqttProtocol.ConnectFlags.WillRetain;
                }
            }

            if (!string.IsNullOrEmpty(message.Username))
            {
                connectFlags |= MqttProtocol.ConnectFlags.Username;
            }

            if (!string.IsNullOrEmpty(message.Password))
            {
                connectFlags |= MqttProtocol.ConnectFlags.Password;
            }

            stream.WriteByte(connectFlags);

            // 保持连接时间
            stream.WriteByte((byte)(message.KeepAlive >> 8));
            stream.WriteByte((byte)(message.KeepAlive & 0xFF));

            // 客户端ID
            WriteString(stream, message.ClientId);

            // 遗嘱消息
            if (message.HasWill)
            {
                WriteString(stream, message.WillTopic);
                WriteBinaryData(stream, message.WillMessage);
            }

            // 用户名
            if (!string.IsNullOrEmpty(message.Username))
            {
                WriteString(stream, message.Username);
            }

            // 密码
            if (!string.IsNullOrEmpty(message.Password))
            {
                WriteString(stream, message.Password);
            }
        }

        /// <summary>
        /// 写入CONNACK消息的可变头部和负载
        /// </summary>
        private void WriteConnAckVariableHeaderAndPayload(MemoryStream stream, MqttConnAckMessage message)
        {
            // 会话存在标志
            stream.WriteByte((byte)(message.SessionPresent ? 0x01 : 0x00));

            // 返回码
            stream.WriteByte((byte)message.ReturnCode);
        }

        /// <summary>
        /// 写入PUBLISH消息的可变头部和负载
        /// </summary>
        private void WritePublishVariableHeaderAndPayload(MemoryStream stream, MqttPublishMessage message)
        {
            // 主题
            WriteString(stream, message.Topic);

            // 消息ID (仅当QoS > 0时)
            if (message.QualityOfService > 0)
            {
                stream.WriteByte((byte)(message.MessageId >> 8));
                stream.WriteByte((byte)(message.MessageId & 0xFF));
            }

            // 负载
            if (message.Payload != null && message.Payload.Length > 0)
            {
                stream.Write(message.Payload, 0, message.Payload.Length);
            }
        }

        /// <summary>
        /// 写入只包含消息ID的可变头部和负载
        /// </summary>
        private void WriteMessageIdVariableHeaderAndPayload(MemoryStream stream, MqttMessage message)
        {
            stream.WriteByte((byte)(message.MessageId >> 8));
            stream.WriteByte((byte)(message.MessageId & 0xFF));
        }

        /// <summary>
        /// 写入SUBSCRIBE消息的可变头部和负载
        /// </summary>
        private void WriteSubscribeVariableHeaderAndPayload(MemoryStream stream, MqttSubscribeMessage message)
        {
            // 消息ID
            stream.WriteByte((byte)(message.MessageId >> 8));
            stream.WriteByte((byte)(message.MessageId & 0xFF));

            // 主题过滤器
            foreach (var subscription in message.Subscriptions)
            {
                WriteString(stream, subscription.TopicFilter);
                stream.WriteByte(subscription.QualityOfService);
            }
        }

        /// <summary>
        /// 写入SUBACK消息的可变头部和负载
        /// </summary>
        private void WriteSubAckVariableHeaderAndPayload(MemoryStream stream, MqttSubAckMessage message)
        {
            // 消息ID
            stream.WriteByte((byte)(message.MessageId >> 8));
            stream.WriteByte((byte)(message.MessageId & 0xFF));

            // 返回码
            foreach (byte returnCode in message.ReturnCodes)
            {
                stream.WriteByte(returnCode);
            }
        }

        /// <summary>
        /// 写入UNSUBSCRIBE消息的可变头部和负载
        /// </summary>
        private void WriteUnsubscribeVariableHeaderAndPayload(MemoryStream stream, MqttUnsubscribeMessage message)
        {
            // 消息ID
            stream.WriteByte((byte)(message.MessageId >> 8));
            stream.WriteByte((byte)(message.MessageId & 0xFF));

            // 主题过滤器
            foreach (string topic in message.TopicFilters)
            {
                WriteString(stream, topic);
            }
        }

        /// <summary>
        /// 写入剩余长度
        /// </summary>
        private void WriteRemainingLength(MemoryStream stream, int length)
        {
            do
            {
                byte digit = (byte)(length % 128);
                length /= 128;
                if (length > 0)
                {
                    digit |= 0x80;
                }
                stream.WriteByte(digit);
            }
            while (length > 0);
        }

        /// <summary>
        /// 写入字符串
        /// </summary>
        protected void WriteString(MemoryStream stream, string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                // 长度为0的字符串
                stream.WriteByte(0);
                stream.WriteByte(0);
                return;
            }

            byte[] data = Encoding.UTF8.GetBytes(value);
            stream.WriteByte((byte)(data.Length >> 8));
            stream.WriteByte((byte)(data.Length & 0xFF));
            stream.Write(data, 0, data.Length);
        }

        /// <summary>
        /// 写入二进制数据
        /// </summary>
        protected void WriteBinaryData(MemoryStream stream, byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                // 长度为0的数据
                stream.WriteByte(0);
                stream.WriteByte(0);
                return;
            }

            stream.WriteByte((byte)(data.Length >> 8));
            stream.WriteByte((byte)(data.Length & 0xFF));
            stream.Write(data, 0, data.Length);
        }

        /// <summary>
        /// 写入CONNACK消息
        /// </summary>
        /// <param name="message">CONNACK消息</param>
        /// <returns>消息字节数组</returns>
        public byte[] WriteConnAck(MqttConnAckMessage message)
        {
            using (var memoryStream = new MemoryStream())
            {
                // 构建可变头部和负载
                byte[] variableHeaderAndPayload;
                using (var payloadStream = new MemoryStream())
                {
                    WriteConnAckVariableHeaderAndPayload(payloadStream, message);
                    variableHeaderAndPayload = payloadStream.ToArray();
                }

                // 构建固定头部
                byte[] fixedHeader = BuildFixedHeader(message, variableHeaderAndPayload.Length);

                // 写入固定头部
                memoryStream.Write(fixedHeader, 0, fixedHeader.Length);

                // 写入可变头部和负载
                if (variableHeaderAndPayload.Length > 0)
                {
                    memoryStream.Write(variableHeaderAndPayload, 0, variableHeaderAndPayload.Length);
                }

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// 写入DISCONNECT消息
        /// </summary>
        /// <param name="message">DISCONNECT消息</param>
        /// <returns>消息字节数组</returns>
        public byte[] WriteDisconnect(MqttDisconnectMessage message)
        {
            using (var memoryStream = new MemoryStream())
            {
                // 构建可变头部和负载
                byte[] variableHeaderAndPayload;
                using (var payloadStream = new MemoryStream())
                {
                    // MQTT 3.1/3.1.1 DISCONNECT消息没有可变头部和负载
                    variableHeaderAndPayload = payloadStream.ToArray();
                }

                // 构建固定头部
                byte[] fixedHeader = BuildFixedHeader(message, variableHeaderAndPayload.Length);

                // 写入固定头部
                memoryStream.Write(fixedHeader, 0, fixedHeader.Length);

                // 写入可变头部和负载
                if (variableHeaderAndPayload.Length > 0)
                {
                    memoryStream.Write(variableHeaderAndPayload, 0, variableHeaderAndPayload.Length);
                }

                return memoryStream.ToArray();
            }
        }
    }
} 