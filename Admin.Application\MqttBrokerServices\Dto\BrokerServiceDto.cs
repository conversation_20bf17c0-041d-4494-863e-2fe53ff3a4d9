using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Admin.Application.MqttBrokerServices.Dto
{
    #region 输入模型

    /// <summary>
    /// 启动代理服务输入
    /// </summary>
    public class StartBrokerInput
    {
        /// <summary>
        /// 监听端口，默认为1883
        /// </summary>
        [Range(1, 65535, ErrorMessage = "端口号必须在1-65535之间")]
        public int Port { get; set; } = 1883;

        /// <summary>
        /// 启动时的配置更新
        /// </summary>
        public BrokerStartConfigurationInput? Configuration { get; set; }
    }

    /// <summary>
    /// 重启代理服务输入
    /// </summary>
    public class RestartBrokerInput
    {
        /// <summary>
        /// 是否优雅停机，默认为true
        /// </summary>
        public bool GracefulShutdown { get; set; } = true;

        /// <summary>
        /// 停机超时时间(秒)，默认为30秒
        /// </summary>
        [Range(1, 300, ErrorMessage = "停机超时时间必须在1-300秒之间")]
        public int ShutdownTimeout { get; set; } = 30;
    }

    /// <summary>
    /// 代理启动配置输入
    /// </summary>
    public class BrokerStartConfigurationInput
    {
        /// <summary>
        /// 最大连接数
        /// </summary>
        [Range(1, 100000, ErrorMessage = "最大连接数必须在1-100000之间")]
        public int? MaxConnections { get; set; }

        /// <summary>
        /// 单个IP最大连接数
        /// </summary>
        [Range(1, 1000, ErrorMessage = "单个IP最大连接数必须在1-1000之间")]
        public int? MaxConnectionsPerIp { get; set; }

        /// <summary>
        /// 连接超时时间(秒)
        /// </summary>
        [Range(1, 3600, ErrorMessage = "连接超时时间必须在1-3600秒之间")]
        public int? ConnectionTimeout { get; set; }

        /// <summary>
        /// 是否启用TLS
        /// </summary>
        public bool? EnableTls { get; set; }

        /// <summary>
        /// 是否允许匿名访问
        /// </summary>
        public bool? AllowAnonymousAccess { get; set; }
    }

    #endregion

    #region 输出模型

    /// <summary>
    /// 代理服务操作输出
    /// </summary>
    public class BrokerServiceOutput
    {
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 端口
        /// </summary>
        public int? Port { get; set; }

        /// <summary>
        /// 是否运行中
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 停止时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? StopTime { get; set; }

        /// <summary>
        /// 重启时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? RestartTime { get; set; }

        /// <summary>
        /// 之前运行时长
        /// </summary>
        public string? PreviousUptime { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 代理状态输出
    /// </summary>
    public class BrokerStatusOutput
    {
        /// <summary>
        /// 是否运行中
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 端口
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 运行时长
        /// </summary>
        public string Uptime { get; set; } = string.Empty;

        /// <summary>
        /// 版本
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 已连接客户端数量
        /// </summary>
        public int ConnectedClientCount { get; set; }

        /// <summary>
        /// 配置信息
        /// </summary>
        public BrokerStatusConfigurationOutput Configuration { get; set; } = new();
    }

    /// <summary>
    /// 代理状态配置输出
    /// </summary>
    public class BrokerStatusConfigurationOutput
    {
        /// <summary>
        /// 最大连接数
        /// </summary>
        public int MaxConnections { get; set; }

        /// <summary>
        /// 单个IP最大连接数
        /// </summary>
        public int MaxConnectionsPerIp { get; set; }

        /// <summary>
        /// 连接超时时间
        /// </summary>
        public int ConnectionTimeout { get; set; }

        /// <summary>
        /// 是否启用保留消息
        /// </summary>
        public bool EnableRetainedMessages { get; set; }

        /// <summary>
        /// 是否启用统计
        /// </summary>
        public bool EnableStatistics { get; set; }

        /// <summary>
        /// 是否允许匿名访问
        /// </summary>
        public bool AllowAnonymousAccess { get; set; }

        /// <summary>
        /// 是否使用TLS
        /// </summary>
        public bool UseTls { get; set; }

        /// <summary>
        /// 最大消息大小
        /// </summary>
        public int MaxMessageSize { get; set; }

        /// <summary>
        /// 最大主题长度
        /// </summary>
        public int MaxTopicLength { get; set; }
    }

    /// <summary>
    /// 代理配置输出
    /// </summary>
    public class BrokerConfigurationOutput
    {
        /// <summary>
        /// 代理配置
        /// </summary>
        public BrokerConfigurationSectionOutput Broker { get; set; } = new();

        /// <summary>
        /// 认证配置
        /// </summary>
        public AuthenticationConfigurationSectionOutput Authentication { get; set; } = new();

        /// <summary>
        /// ACL配置
        /// </summary>
        public AclConfigurationSectionOutput Acl { get; set; } = new();
    }

    /// <summary>
    /// 代理配置节输出
    /// </summary>
    public class BrokerConfigurationSectionOutput
    {
        /// <summary>
        /// 端口
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 最大连接数
        /// </summary>
        public int MaxConnections { get; set; }

        /// <summary>
        /// 单个IP最大连接数
        /// </summary>
        public int MaxConnectionsPerIp { get; set; }

        /// <summary>
        /// 连接超时时间
        /// </summary>
        public int ConnectionTimeout { get; set; }

        /// <summary>
        /// 是否使用TLS
        /// </summary>
        public bool UseTls { get; set; }

        /// <summary>
        /// 证书路径
        /// </summary>
        public string? CertificatePath { get; set; }

        /// <summary>
        /// 是否允许匿名访问
        /// </summary>
        public bool AllowAnonymousAccess { get; set; }

        /// <summary>
        /// 是否启用保留消息
        /// </summary>
        public bool EnableRetainedMessages { get; set; }

        /// <summary>
        /// 是否启用统计
        /// </summary>
        public bool EnableStatistics { get; set; }

        /// <summary>
        /// 最大主题长度
        /// </summary>
        public int MaxTopicLength { get; set; }

        /// <summary>
        /// 最大消息大小
        /// </summary>
        public int MaxMessageSize { get; set; }

        /// <summary>
        /// 绑定地址
        /// </summary>
        public string BindAddress { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用WebSocket
        /// </summary>
        public bool EnableWebSockets { get; set; }

        /// <summary>
        /// WebSocket端口
        /// </summary>
        public int WebSocketPort { get; set; }

        /// <summary>
        /// WebSocket路径
        /// </summary>
        public string? WebSocketPath { get; set; }
    }

    /// <summary>
    /// 认证配置节输出
    /// </summary>
    public class AuthenticationConfigurationSectionOutput
    {
        /// <summary>
        /// 默认用户名
        /// </summary>
        public string? DefaultUsername { get; set; }

        /// <summary>
        /// 默认密码（已屏蔽）
        /// </summary>
        public string? DefaultPassword { get; set; }

        /// <summary>
        /// 是否需要认证
        /// </summary>
        public bool RequireAuthentication { get; set; }

        /// <summary>
        /// 用户数量
        /// </summary>
        public int UserCount { get; set; }
    }

    /// <summary>
    /// ACL配置节输出
    /// </summary>
    public class AclConfigurationSectionOutput
    {
        /// <summary>
        /// 是否启用ACL
        /// </summary>
        public bool EnableAcl { get; set; }

        /// <summary>
        /// 规则数量
        /// </summary>
        public int RuleCount { get; set; }
    }

    #endregion

    #region 配置管理相关DTO

    /// <summary>
    /// 更新配置请求
    /// </summary>
    public class UpdateConfigurationRequest
    {
        /// <summary>
        /// 代理配置更新
        /// </summary>
        public BrokerConfigurationUpdateDto? Broker { get; set; }

        /// <summary>
        /// 认证配置更新
        /// </summary>
        public AuthenticationConfigurationUpdateDto? Authentication { get; set; }

        /// <summary>
        /// ACL配置更新
        /// </summary>
        public AclConfigurationUpdateDto? Acl { get; set; }
    }

    /// <summary>
    /// 代理配置更新DTO
    /// </summary>
    public class BrokerConfigurationUpdateDto
    {
        /// <summary>
        /// 监听端口
        /// </summary>
        [Range(1, 65535, ErrorMessage = "端口号必须在1-65535之间")]
        public int? Port { get; set; }

        /// <summary>
        /// 最大连接数
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "最大连接数必须大于0")]
        public int? MaxConnections { get; set; }

        /// <summary>
        /// 单个IP最大连接数
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "单个IP最大连接数必须大于0")]
        public int? MaxConnectionsPerIp { get; set; }

        /// <summary>
        /// 连接超时时间(秒)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "连接超时时间必须大于0")]
        public int? ConnectionTimeout { get; set; }

        /// <summary>
        /// 是否启用TLS
        /// </summary>
        public bool? EnableTls { get; set; }

        /// <summary>
        /// 证书路径
        /// </summary>
        public string? CertificatePath { get; set; }

        /// <summary>
        /// 是否允许匿名访问
        /// </summary>
        public bool? AllowAnonymousAccess { get; set; }

        /// <summary>
        /// 是否启用保留消息
        /// </summary>
        public bool? EnableRetainedMessages { get; set; }

        /// <summary>
        /// 是否启用统计
        /// </summary>
        public bool? EnableStatistics { get; set; }

        /// <summary>
        /// 最大主题长度
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "最大主题长度必须大于0")]
        public int? MaxTopicLength { get; set; }

        /// <summary>
        /// 最大消息大小
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "最大消息大小必须大于0")]
        public int? MaxMessageSize { get; set; }

        /// <summary>
        /// 绑定地址
        /// </summary>
        public string? BindAddress { get; set; }
    }

    /// <summary>
    /// 认证配置更新DTO
    /// </summary>
    public class AuthenticationConfigurationUpdateDto
    {
        /// <summary>
        /// 默认用户名
        /// </summary>
        public string? DefaultUsername { get; set; }

        /// <summary>
        /// 默认密码
        /// </summary>
        public string? DefaultPassword { get; set; }

        /// <summary>
        /// 是否需要认证
        /// </summary>
        public bool? RequireAuthentication { get; set; }
    }

    /// <summary>
    /// ACL配置更新DTO
    /// </summary>
    public class AclConfigurationUpdateDto
    {
        /// <summary>
        /// 是否启用ACL
        /// </summary>
        public bool? EnableAcl { get; set; }

        /// <summary>
        /// 默认策略
        /// </summary>
        public string? DefaultPolicy { get; set; }
    }

    /// <summary>
    /// 更新配置响应
    /// </summary>
    public class UpdateConfigurationResponse
    {
        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 是否需要重启
        /// </summary>
        public bool RequiresRestart { get; set; }

        /// <summary>
        /// 已更改的设置列表
        /// </summary>
        public List<string> ChangedSettings { get; set; } = new();
    }

    #endregion

    #region JSON转换器

    /// <summary>
    /// 自定义日期时间JSON转换器
    /// </summary>
    public class JsonDateTimeConverter : JsonConverter<DateTime?>
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ssZ";

        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                var dateString = reader.GetString();
                if (DateTime.TryParseExact(dateString, DateTimeFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var date))
                {
                    return date.ToUniversalTime();
                }
            }
            return null;
        }

        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
            {
                writer.WriteStringValue(value.Value.ToUniversalTime().ToString(DateTimeFormat, CultureInfo.InvariantCulture));
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }

    #endregion
} 