// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者



namespace Admin.Application.AlarmServices.Dto;

/// <summary>
/// 告警历史分页查询输入
/// </summary>
public class AlarmHistoryPagedInput : PaginationParams
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long? DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 告警类型 (1:通讯失败, 2:参数超限)
    /// </summary>
    public int? AlarmType { get; set; }

    /// <summary>
    /// 告警级别 (1：紧急 2：严重 3：一般 4：预警)
    /// </summary>
    public int? AlarmLevel { get; set; }

    /// <summary>
    /// 告警状态 (1:待确认, 2:已确认, 3:待处理, 4:已处理)
    /// </summary>
    public int? AlarmStatus { get; set; }

    /// <summary>
    /// 是否已解除
    /// </summary>
    public bool? IsReleased { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 告警历史输出
/// </summary>
public class AlarmHistoryOutput
{
    /// <summary>
    /// 告警ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 事件ID
    /// </summary>
    public string EventId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 告警描述
    /// </summary>
    public string AlarmDescription { get; set; }

    /// <summary>
    /// 告警值
    /// </summary>
    public decimal AlarmValue { get; set; }

    /// <summary>
    /// 告警级别
    /// </summary>
    public int AlarmLevel { get; set; }

    /// <summary>
    /// 告警级别描述
    /// </summary>
    public string AlarmLevelDescription { get; set; }

    /// <summary>
    /// 告警状态
    /// </summary>
    public int AlarmStatus { get; set; }

    /// <summary>
    /// 告警状态描述
    /// </summary>
    public string AlarmStatusDescription { get; set; }

    /// <summary>
    /// 告警时间
    /// </summary>
    public DateTime AlarmTime { get; set; }

    /// <summary>
    /// 告警确认人
    /// </summary>
    public string? ConfirmedPeople { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmedTime { get; set; }

    /// <summary>
    /// 解除时间
    /// </summary>
    public DateTime? ReleaseTime { get; set; }

    /// <summary>
    /// 解除数值
    /// </summary>
    public decimal? ReleaseValue { get; set; }

    /// <summary>
    /// 解除描述
    /// </summary>
    public string? ReleaseDescription { get; set; }

    /// <summary>
    /// 是否已解除
    /// </summary>
    public bool IsReleased => ReleaseTime.HasValue;

    /// <summary>
    /// 告警持续时间（分钟）
    /// </summary>
    public double? DurationMinutes => ReleaseTime.HasValue
        ? (ReleaseTime.Value - AlarmTime).TotalMinutes
        : (DateTime.Now - AlarmTime).TotalMinutes;
}

/// <summary>
/// 确认告警输入
/// </summary>
public class ConfirmAlarmInput
{
    /// <summary>
    /// 告警ID列表
    /// </summary>
    public List<long> AlarmIds { get; set; } = new();

    /// <summary>
    /// 确认人
    /// </summary>
    public string ConfirmedPeople { get; set; } = string.Empty;
}

/// <summary>
/// 告警统计输出
/// </summary>
public class AlarmStatisticsOutput
{
    /// <summary>
    /// 总告警数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 待确认数量
    /// </summary>
    public int PendingConfirmationCount { get; set; }

    /// <summary>
    /// 已确认数量
    /// </summary>
    public int ConfirmedCount { get; set; }

    /// <summary>
    /// 待处理数量
    /// </summary>
    public int PendingProcessingCount { get; set; }

    /// <summary>
    /// 已处理数量
    /// </summary>
    public int ProcessedCount { get; set; }

    /// <summary>
    /// 未解除数量
    /// </summary>
    public int UnreleasedCount { get; set; }

    /// <summary>
    /// 已解除数量
    /// </summary>
    public int ReleasedCount { get; set; }

    /// <summary>
    /// 通讯失败数量
    /// </summary>
    public int CommunicationFailureCount { get; set; }

    /// <summary>
    /// 参数超限数量
    /// </summary>
    public int ParameterOutOfRangeCount { get; set; }

    /// <summary>
    /// 紧急级别数量
    /// </summary>
    public int EmergencyCount { get; set; }

    /// <summary>
    /// 严重级别数量
    /// </summary>
    public int CriticalCount { get; set; }

    /// <summary>
    /// 一般级别数量
    /// </summary>
    public int NormalCount { get; set; }

    /// <summary>
    /// 预警级别数量
    /// </summary>
    public int WarningCount { get; set; }
}
