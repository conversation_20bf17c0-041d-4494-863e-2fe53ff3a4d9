// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Communication.Mqtt.Abstractions;

/// <summary>
/// MQTT用户服务接口
/// </summary>
public interface IMqttUserService
{
    /// <summary>
    /// 验证用户凭据
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>验证结果</returns>
    Task<(bool IsAuthenticated, string Role, string Message)> ValidateCredentialsAsync(string username, string password, string clientId);

    /// <summary>
    /// 获取用户信息
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户信息</returns>
    Task<MqttUserEntity> GetUserAsync(string username);

    /// <summary>
    /// 获取匹配的ACL规则
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topic">主题</param>
    /// <param name="accessType">访问类型</param>
    /// <returns>ACL规则列表</returns>
    Task<List<MqttAclRuleEntity>> GetMatchingAclRulesAsync(string username, string clientId, string topic, int accessType);

    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="ip">IP地址</param>
    /// <returns>异步任务</returns>
    Task UpdateUserLastLoginAsync(string username, string ip);

    /// <summary>
    /// 添加MQTT用户
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <returns>是否成功</returns>
    Task<bool> AddUserAsync(MqttUserEntity user);

    /// <summary>
    /// 更新MQTT用户
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <returns>是否成功</returns>
    Task<bool> UpdateUserAsync(MqttUserEntity user);

    /// <summary>
    /// 删除MQTT用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>是否成功</returns>
    Task<bool> DeleteUserAsync(string username);

    /// <summary>
    /// 检查用户是否存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>是否存在</returns>
    Task<bool> UserExistsAsync(string username);

    /// <summary>
    /// 为设备创建MQTT用户
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="deviceSecret">设备密钥</param>
    /// <param name="deviceName">设备名称</param>
    /// <param name="expireTime">过期时间</param>
    /// <returns>是否成功</returns>
    Task<bool> CreateDeviceUserAsync(string deviceId, string deviceSecret, string? deviceName = null, DateTime? expireTime = null);

    /// <summary>
    /// 启用/禁用用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>是否成功</returns>
    Task<bool> ToggleUserAsync(string username, bool isEnabled);

    /// <summary>
    /// 重置用户密码
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="newPassword">新密码</param>
    /// <returns>是否成功</returns>
    Task<bool> ResetPasswordAsync(string username, string newPassword);

    /// <summary>
    /// 获取所有用户列表
    /// </summary>
    /// <returns>用户列表</returns>
    Task<List<MqttUserEntity>> GetAllUsersAsync();

    /// <summary>
    /// 分页查询用户
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="username">用户名过滤</param>
    /// <param name="isEnabled">是否启用过滤</param>
    /// <returns>用户列表和总数</returns>
    Task<(List<MqttUserEntity> Users, int TotalCount)> QueryUsersAsync(int page = 1, int pageSize = 20, string? username = null, bool? isEnabled = null);
}