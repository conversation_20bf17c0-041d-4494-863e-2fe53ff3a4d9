// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Multiplex.Contracts.Enums;

/// <summary>
/// 协议类型枚举 (基于ProductEntity定义)
/// </summary>
public enum ProtocolTypeEnum
{
    /// <summary>
    /// MQTT协议
    /// </summary>
    MQTT = 1,

    /// <summary>
    /// Modbus协议
    /// </summary>
    Modbus = 2
}

/// <summary>
/// 数据格式枚举 (基于ProductEntity定义)
/// </summary>
public enum DataFormatEnum
{
    /// <summary>
    /// JSON格式
    /// </summary>
    Json = 1,

    /// <summary>
    /// HEX格式
    /// </summary>
    Hex = 2
}

/// <summary>
/// 设备属性类型枚举
/// </summary>
public enum DevicePropertyTypeEnum
{
    /// <summary>
    /// 直连设备
    /// </summary>
    DirectDevice = 1,

    /// <summary>
    /// 网关设备
    /// </summary>
    GatewayDevice = 2,

    /// <summary>
    /// 网关子设备
    /// </summary>
    GatewaySubDevice = 3
}

/// <summary>
/// 设备状态枚举
/// </summary>
public enum DeviceStatusEnum
{
    /// <summary>
    /// 离线
    /// </summary>
    Offline = 0,

    /// <summary>
    /// 在线
    /// </summary>
    Online = 1
}

/// <summary>
/// 告警类型枚举
/// </summary>
public enum AlarmTypeEnum
{
    /// <summary>
    /// 通讯失败
    /// </summary>
    CommunicationFailure = 1,

    /// <summary>
    /// 数值超上限
    /// </summary>
    ValueOverUpperLimit = 2,

    /// <summary>
    /// 数值超下限
    /// </summary>
    ValueOverLowerLimit = 3,

    /// <summary>
    /// 状态异常
    /// </summary>
    StatusAbnormal = 4
}

/// <summary>
/// 告警级别枚举
/// </summary>
public enum AlarmLevelEnum
{
    /// <summary>
    /// 紧急
    /// </summary>
    Emergency = 1,

    /// <summary>
    /// 严重
    /// </summary>
    Critical = 2,

    /// <summary>
    /// 一般
    /// </summary>
    Normal = 3,

    /// <summary>
    /// 预警
    /// </summary>
    Warning = 4
}

/// <summary>
/// 告警状态枚举
/// </summary>
public enum AlarmStatusEnum
{
    /// <summary>
    /// 待确认
    /// </summary>
    PendingConfirmation = 1,

    /// <summary>
    /// 已确认
    /// </summary>
    Confirmed = 2,

    /// <summary>
    /// 待处理
    /// </summary>
    PendingProcessing = 3,

    /// <summary>
    /// 已处理
    /// </summary>
    Processed = 4
}

