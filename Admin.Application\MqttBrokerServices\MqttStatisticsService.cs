using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using Admin.Application.MqttBrokerServices.Dto;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.Text.Json;
using Admin.Communication.Mqtt.Events;

namespace Admin.Application.MqttBrokerServices
{
    /// <summary>
    /// MQTT统计监控服务接口
    /// </summary>
    public interface IMqttStatisticsService
    {
        /// <summary>
        /// 获取总体统计概览
        /// </summary>
        Task<StatisticsResult> GetOverviewAsync();

        /// <summary>
        /// 获取实时性能指标
        /// </summary>
        Task<StatisticsResult> GetPerformanceMetricsAsync(GetPerformanceMetricsRequest request);

        /// <summary>
        /// 获取连接统计
        /// </summary>
        Task<StatisticsResult> GetConnectionStatisticsAsync(GetConnectionStatisticsRequest request);

        /// <summary>
        /// 获取消息统计
        /// </summary>
        Task<StatisticsResult> GetMessageStatisticsAsync(GetMessageStatisticsRequest request);

        /// <summary>
        /// 获取主题分析
        /// </summary>
        Task<StatisticsResult> GetTopicAnalysisAsync(GetTopicAnalysisRequest request);

        /// <summary>
        /// 获取会话统计
        /// </summary>
        Task<StatisticsResult> GetSessionStatisticsAsync(GetSessionStatisticsRequest request);

        /// <summary>
        /// 获取历史统计数据
        /// </summary>
        Task<StatisticsResult> GetHistoricalStatisticsAsync(GetHistoricalStatisticsRequest request);

        /// <summary>
        /// 导出统计报告
        /// </summary>
        Task<StatisticsResult> ExportStatisticsReportAsync(ExportStatisticsRequest request);
    }

    /// <summary>
    /// MQTT统计监控服务实现
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.MQTT)]
    public class MqttStatisticsService(
        IMqttBroker mqttBroker,
        IMqttConnectionManager connectionManager,
        ILogger<MqttStatisticsService> logger) : ApplicationService, IMqttStatisticsService
    {
        private readonly IMqttBroker _mqttBroker = mqttBroker ?? throw new ArgumentNullException(nameof(mqttBroker));
        private readonly IMqttConnectionManager _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        private readonly ILogger<MqttStatisticsService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly DateTime _serviceStartTime = DateTime.UtcNow;

        // 统计计数器
        private long _totalMessagesPublished = 0;
        private long _totalMessagesReceived = 0;
        private long _totalBytesTransferred = 0;

        // 性能计数器
        private readonly Dictionary<string, long> _topicMessageCounts = new();
        private readonly Dictionary<string, DateTime> _topicLastActivity = new();

        // 延迟初始化标志
        private bool _isInitialized = false;
        private readonly object _initLock = new();

        /// <summary>
        /// 确保事件订阅已初始化
        /// </summary>
        private void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                lock (_initLock)
                {
                    if (!_isInitialized)
                    {
                        SubscribeToMqttEvents();
                        _isInitialized = true;
                    }
                }
            }
        }

        /// <summary>
        /// 获取总体统计概览
        /// </summary>
        public async Task<StatisticsResult> GetOverviewAsync()
        {
            EnsureInitialized();

            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return StatisticsResult.Error("MQTT代理服务未运行");
                }

                var connectionStats = _connectionManager.GetConnectionStatistics();
                var connectedClients = _mqttBroker.GetConnectedClients();

                var overview = new StatisticsOverviewDto
                {
                    Broker = new BrokerStatisticsDto
                    {
                        IsRunning = _mqttBroker.IsRunning,
                        Port = _mqttBroker.Port,
                        StartTime = _serviceStartTime,
                        Uptime = FormatUptime(DateTime.UtcNow - _serviceStartTime),
                        Version = "1.0.0"
                    },
                    Connections = new ConnectionStatisticsDto
                    {
                        Current = connectionStats.CurrentConnections,
                        Total = connectionStats.TotalConnections,
                        TotalDisconnections = connectionStats.TotalDisconnections,
                        AbnormalDisconnections = connectionStats.AbnormalDisconnections,
                        MaxConcurrent = connectionStats.MaxConcurrentConnections,
                        AverageDuration = FormatDuration(TimeSpan.FromSeconds(connectionStats.AverageConnectionDuration)),
                        ConnectionsByIp = connectionStats.ConnectionsByIp,
                        TopClientsByConnections = GetTopClientsByConnections(connectedClients)
                    },
                    Sessions = await GetSessionStatisticsOverviewAsync(),
                    Messages = new MessageStatisticsDto
                    {
                        TotalPublished = _totalMessagesPublished,
                        TotalReceived = _totalMessagesReceived,
                        TotalBytesTransferred = _totalBytesTransferred,
                        PublishRate = CalculateMessageRate(_totalMessagesPublished),
                        ReceiveRate = CalculateMessageRate(_totalMessagesReceived),
                        AverageMessageSize = _totalMessagesPublished > 0 ? _totalBytesTransferred / _totalMessagesPublished : 0,
                        TopTopicsByMessages = GetTopTopicsByMessages(),
                        MessagesByQos = GetMessagesByQos(),
                        RetainedMessageCount = GetRetainedMessageCount()
                    },
                    Subscriptions = await GetSubscriptionStatisticsAsync(),
                    Authentication = new AuthenticationStatisticsDto
                    {
                        TotalAttempts = connectionStats.TotalConnections,
                        SuccessfulAttempts = connectionStats.TotalConnections - connectionStats.AuthenticationFailures,
                        FailedAttempts = connectionStats.AuthenticationFailures,
                        SuccessRate = connectionStats.TotalConnections > 0 
                            ? (double)(connectionStats.TotalConnections - connectionStats.AuthenticationFailures) / connectionStats.TotalConnections * 100 
                            : 0,
                        RecentFailures = GetRecentAuthenticationFailures()
                    }
                };

                _logger.LogDebug("获取统计概览成功");
                return StatisticsResult.Success("获取统计概览成功", overview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计概览时发生错误");
                return StatisticsResult.Error($"获取统计概览失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取实时性能指标
        /// </summary>
        public async Task<StatisticsResult> GetPerformanceMetricsAsync(GetPerformanceMetricsRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return StatisticsResult.Error("MQTT代理服务未运行");
                }

                var currentProcess = Process.GetCurrentProcess();
                var connectionStats = _connectionManager.GetConnectionStatistics();

                var metrics = new PerformanceMetricsDto
                {
                    Timestamp = DateTime.UtcNow,
                    CpuUsage = await GetCpuUsageAsync(),
                    MemoryUsage = new MemoryUsageDto
                    {
                        WorkingSet = currentProcess.WorkingSet64,
                        PrivateMemory = currentProcess.PrivateMemorySize64,
                        VirtualMemory = currentProcess.VirtualMemorySize64,
                        GcTotalMemory = GC.GetTotalMemory(false)
                    },
                    NetworkMetrics = new NetworkMetricsDto
                    {
                        ConnectionsPerSecond = CalculateConnectionRate(),
                        MessagesPerSecond = CalculateMessageRate(_totalMessagesPublished + _totalMessagesReceived),
                        BytesPerSecond = CalculateBytesRate(),
                        ActiveConnections = connectionStats.CurrentConnections,
                        PacketLoss = 0 // 需要实际实现
                    },
                    SystemMetrics = new SystemMetricsDto
                    {
                        ThreadCount = currentProcess.Threads.Count,
                        HandleCount = currentProcess.HandleCount,
                        UptimeSeconds = (int)(DateTime.UtcNow - _serviceStartTime).TotalSeconds,
                        GcCollections = new Dictionary<int, int>
                        {
                            { 0, GC.CollectionCount(0) },
                            { 1, GC.CollectionCount(1) },
                            { 2, GC.CollectionCount(2) }
                        }
                    }
                };

                // 如果请求指定了时间范围，获取历史数据
                if (request.StartTime.HasValue && request.EndTime.HasValue)
                {
                    metrics.HistoricalData = await GetHistoricalPerformanceDataAsync(
                        request.StartTime.Value, 
                        request.EndTime.Value,
                        request.Interval ?? TimeSpan.FromMinutes(5));
                }

                _logger.LogDebug("获取性能指标成功");
                return StatisticsResult.Success("获取性能指标成功", metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能指标时发生错误");
                return StatisticsResult.Error($"获取性能指标失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取连接统计
        /// </summary>
        public async Task<StatisticsResult> GetConnectionStatisticsAsync(GetConnectionStatisticsRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return StatisticsResult.Error("MQTT代理服务未运行");
                }

                var connectionStats = _connectionManager.GetConnectionStatistics();
                var connectedClients = _mqttBroker.GetConnectedClients();

                var response = new GetConnectionStatisticsResponse
                {
                    Summary = new ConnectionStatisticsSummaryDto
                    {
                        CurrentConnections = connectionStats.CurrentConnections,
                        TotalConnections = connectionStats.TotalConnections,
                        TotalDisconnections = connectionStats.TotalDisconnections,
                        AbnormalDisconnections = connectionStats.AbnormalDisconnections,
                        MaxConcurrentConnections = connectionStats.MaxConcurrentConnections,
                        AverageConnectionDuration = connectionStats.AverageConnectionDuration,
                        ConnectionSuccessRate = connectionStats.TotalConnections > 0 
                            ? (double)(connectionStats.TotalConnections - connectionStats.ConnectionRejections) / connectionStats.TotalConnections * 100 
                            : 0
                    },
                    ConnectionsByIp = connectionStats.ConnectionsByIp.Select(kvp => new IpConnectionStatsDto
                    {
                        IpAddress = kvp.Key,
                        ConnectionCount = kvp.Value,
                        Percentage = connectionStats.CurrentConnections > 0 
                            ? (double)kvp.Value / connectionStats.CurrentConnections * 100 
                            : 0
                    }).OrderByDescending(x => x.ConnectionCount).ToList(),
                    ConnectionsByTime = await GetConnectionsByTimeAsync(
                        request.StartTime ?? DateTime.UtcNow.AddHours(-24),
                        request.EndTime ?? DateTime.UtcNow,
                        request.GroupBy ?? TimeGroupBy.Hour),
                    TopClients = GetTopClientsByConnections(connectedClients),
                    ConnectionDurationDistribution = GetConnectionDurationDistribution(connectedClients)
                };

                _logger.LogDebug("获取连接统计成功，当前连接数: {CurrentConnections}", connectionStats.CurrentConnections);
                return StatisticsResult.Success("获取连接统计成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取连接统计时发生错误");
                return StatisticsResult.Error($"获取连接统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取消息统计
        /// </summary>
        public async Task<StatisticsResult> GetMessageStatisticsAsync(GetMessageStatisticsRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return StatisticsResult.Error("MQTT代理服务未运行");
                }

                var response = new GetMessageStatisticsResponse
                {
                    Summary = new MessageStatisticsSummaryDto
                    {
                        TotalMessages = _totalMessagesPublished + _totalMessagesReceived,
                        TotalPublished = _totalMessagesPublished,
                        TotalReceived = _totalMessagesReceived,
                        TotalBytesTransferred = _totalBytesTransferred,
                        AverageMessageSize = (_totalMessagesPublished + _totalMessagesReceived) > 0 
                            ? _totalBytesTransferred / (_totalMessagesPublished + _totalMessagesReceived) 
                            : 0,
                        MessagesPerSecond = CalculateMessageRate(_totalMessagesPublished + _totalMessagesReceived),
                        PublishSuccessRate = 100.0 // 需要实际实现
                    },
                    MessagesByQos = GetMessagesByQos(),
                    MessagesByTopic = GetTopTopicsByMessages().Take(request.TopTopicsCount ?? 10).ToList(),
                    MessagesByTime = await GetMessagesByTimeAsync(
                        request.StartTime ?? DateTime.UtcNow.AddHours(-24),
                        request.EndTime ?? DateTime.UtcNow,
                        request.GroupBy ?? TimeGroupBy.Hour),
                    RetainedMessages = new RetainedMessageStatsDto
                    {
                        Count = GetRetainedMessageCount(),
                        TotalSize = GetRetainedMessagesTotalSize(),
                        TopTopics = GetTopRetainedMessageTopics()
                    }
                };

                _logger.LogDebug("获取消息统计成功，总消息数: {TotalMessages}", response.Summary.TotalMessages);
                return StatisticsResult.Success("获取消息统计成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取消息统计时发生错误");
                return StatisticsResult.Error($"获取消息统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取主题分析
        /// </summary>
        public async Task<StatisticsResult> GetTopicAnalysisAsync(GetTopicAnalysisRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return StatisticsResult.Error("MQTT代理服务未运行");
                }

                var connectedClients = _mqttBroker.GetConnectedClients();
                var allTopics = GetAllTopicsFromClients(connectedClients);

                var response = new GetTopicAnalysisResponse
                {
                    Summary = new TopicAnalysisSummaryDto
                    {
                        TotalTopics = allTopics.Count,
                        ActiveTopics = GetActiveTopicsCount(),
                        SubscribedTopics = allTopics.Count(t => t.SubscriberCount > 0),
                        AverageSubscribersPerTopic = allTopics.Count > 0 ? allTopics.Average(t => t.SubscriberCount) : 0
                    },
                    TopTopicsBySubscribers = allTopics
                        .OrderByDescending(t => t.SubscriberCount)
                        .Take(request.TopTopicsCount ?? 10)
                        .ToList(),
                    TopTopicsByMessages = GetTopTopicsByMessages()
                        .Take(request.TopTopicsCount ?? 10)
                        .ToList(),
                    TopicHierarchy = await AnalyzeTopicHierarchyAsync(allTopics, request.MaxDepth ?? 3),
                    WildcardUsage = AnalyzeWildcardUsage(allTopics)
                };

                // 如果请求了特定主题的详细信息
                if (!string.IsNullOrEmpty(request.TopicFilter))
                {
                    response.TopicDetails = await GetTopicDetailsAsync(request.TopicFilter);
                }

                _logger.LogDebug("获取主题分析成功，总主题数: {TotalTopics}", response.Summary.TotalTopics);
                return StatisticsResult.Success("获取主题分析成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主题分析时发生错误");
                return StatisticsResult.Error($"获取主题分析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取会话统计
        /// </summary>
        public async Task<StatisticsResult> GetSessionStatisticsAsync(GetSessionStatisticsRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return StatisticsResult.Error("MQTT代理服务未运行");
                }

                var connectedClients = _mqttBroker.GetConnectedClients();
                var sessions = await GetAllSessionsFromConnectionManagerAsync();

                var response = new GetSessionStatisticsResponse
                {
                    Summary = new SessionStatisticsSummaryDto
                    {
                        TotalSessions = sessions.Count,
                        ActiveSessions = sessions.Count(s => s.State == SessionState.Active),
                        SuspendedSessions = sessions.Count(s => s.State == SessionState.Suspended),
                        PersistentSessions = sessions.Count(s => s.IsPersistent),
                        CleanSessions = sessions.Count(s => !s.IsPersistent),
                        AverageSessionDuration = CalculateAverageSessionDuration(sessions),
                        SessionsWithPendingMessages = sessions.Count(s => s.GetPendingMessageCount() > 0)
                    },
                    SessionsByState = sessions.GroupBy(s => s.State)
                        .Select(g => new SessionStateStatsDto
                        {
                            State = g.Key.ToString(),
                            Count = g.Count(),
                            Percentage = sessions.Count > 0 ? (double)g.Count() / sessions.Count * 100 : 0
                        }).ToList(),
                    SessionsByDuration = GetSessionsByDuration(sessions),
                    PendingMessagesStats = new PendingMessagesStatsDto
                    {
                        TotalPendingMessages = sessions.Sum(s => s.GetPendingMessageCount()),
                        SessionsWithPendingMessages = sessions.Count(s => s.GetPendingMessageCount() > 0),
                        AveragePendingMessagesPerSession = sessions.Count > 0 
                            ? sessions.Average(s => s.GetPendingMessageCount()) 
                            : 0,
                        MaxPendingMessagesInSession = sessions.Count > 0 
                            ? sessions.Max(s => s.GetPendingMessageCount()) 
                            : 0
                    }
                };

                _logger.LogDebug("获取会话统计成功，总会话数: {TotalSessions}", response.Summary.TotalSessions);
                return StatisticsResult.Success("获取会话统计成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话统计时发生错误");
                return StatisticsResult.Error($"获取会话统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取历史统计数据
        /// </summary>
        public async Task<StatisticsResult> GetHistoricalStatisticsAsync(GetHistoricalStatisticsRequest request)
        {
            try
            {
                var response = new GetHistoricalStatisticsResponse
                {
                    TimeRange = new TimeRangeDto
                    {
                        StartTime = request.StartTime,
                        EndTime = request.EndTime,
                        Interval = request.Interval
                    },
                    ConnectionHistory = await GetHistoricalConnectionDataAsync(request.StartTime, request.EndTime, request.Interval),
                    MessageHistory = await GetHistoricalMessageDataAsync(request.StartTime, request.EndTime, request.Interval),
                    PerformanceHistory = await GetHistoricalPerformanceDataAsync(request.StartTime, request.EndTime, request.Interval),
                    Summary = new HistoricalSummaryDto
                    {
                        PeakConnections = await GetPeakConnectionsAsync(request.StartTime, request.EndTime),
                        PeakMessageRate = await GetPeakMessageRateAsync(request.StartTime, request.EndTime),
                        AverageUptime = await GetAverageUptimeAsync(request.StartTime, request.EndTime),
                        TotalDowntime = await GetTotalDowntimeAsync(request.StartTime, request.EndTime)
                    }
                };

                _logger.LogDebug("获取历史统计数据成功，时间范围: {StartTime} - {EndTime}", 
                    request.StartTime, request.EndTime);
                return StatisticsResult.Success("获取历史统计数据成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取历史统计数据时发生错误");
                return StatisticsResult.Error($"获取历史统计数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出统计报告
        /// </summary>
        public async Task<StatisticsResult> ExportStatisticsReportAsync(ExportStatisticsRequest request)
        {
            try
            {
                var reportData = await GenerateReportDataAsync(request);
                
                byte[] reportBytes;
                string contentType;
                string fileName;

                switch (request.Format)
                {
                    case ExportFormat.Json:
                        reportBytes = System.Text.Json.JsonSerializer.SerializeToUtf8Bytes(reportData, new JsonSerializerOptions { WriteIndented = true });
                        contentType = "application/json";
                        fileName = $"mqtt-statistics-{DateTime.UtcNow:yyyyMMdd-HHmmss}.json";
                        break;
                    case ExportFormat.Csv:
                        reportBytes = await GenerateCsvReportAsync(reportData);
                        contentType = "text/csv";
                        fileName = $"mqtt-statistics-{DateTime.UtcNow:yyyyMMdd-HHmmss}.csv";
                        break;
                    case ExportFormat.Excel:
                        reportBytes = await GenerateExcelReportAsync(reportData);
                        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                        fileName = $"mqtt-statistics-{DateTime.UtcNow:yyyyMMdd-HHmmss}.xlsx";
                        break;
                    default:
                        return StatisticsResult.Error("不支持的导出格式");
                }

                var response = new ExportStatisticsResponse
                {
                    FileName = fileName,
                    ContentType = contentType,
                    FileSize = reportBytes.Length,
                    Data = Convert.ToBase64String(reportBytes),
                    GeneratedAt = DateTime.UtcNow
                };

                _logger.LogInformation("导出统计报告成功，格式: {Format}, 文件大小: {FileSize} bytes", 
                    request.Format, reportBytes.Length);
                return StatisticsResult.Success("导出统计报告成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出统计报告时发生错误");
                return StatisticsResult.Error($"导出统计报告失败: {ex.Message}");
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 订阅MQTT事件以收集统计信息
        /// </summary>
        private void SubscribeToMqttEvents()
        {
            _mqttBroker.MessageReceived += OnMessageReceived;
            _mqttBroker.ClientConnected += OnClientConnected;
            _mqttBroker.ClientDisconnected += OnClientDisconnected;
            _mqttBroker.ClientSubscribed += OnClientSubscribed;
            _mqttBroker.ClientUnsubscribed += OnClientUnsubscribed;
        }

        private void OnMessageReceived(object sender, MqttMessageReceivedEventArgs e)
        {
            Interlocked.Increment(ref _totalMessagesReceived);
            Interlocked.Add(ref _totalBytesTransferred, e.Payload?.Length ?? 0);
            
            // 更新主题统计
            lock (_topicMessageCounts)
            {
                _topicMessageCounts[e.Topic] = _topicMessageCounts.GetValueOrDefault(e.Topic) + 1;
                _topicLastActivity[e.Topic] = DateTime.UtcNow;
            }
        }

        private void OnClientConnected(object sender, MqttClientConnectedEventArgs e)
        {
            _logger.LogDebug("客户端连接统计更新: {ClientId}", e.ClientInfo.ClientId);
        }

        private void OnClientDisconnected(object sender, MqttClientDisconnectedEventArgs e)
        {
            _logger.LogDebug("客户端断开连接统计更新: {ClientId}", e.ClientId);
        }

        private void OnClientSubscribed(object sender, MqttSubscriptionEventArgs e)
        {
            _logger.LogDebug("客户端订阅统计更新: {ClientId} - {Topic}", e.ClientId, e.Topic);
        }

        private void OnClientUnsubscribed(object sender, MqttSubscriptionEventArgs e)
        {
            _logger.LogDebug("客户端取消订阅统计更新: {ClientId} - {Topic}", e.ClientId, e.Topic);
        }

        private async Task<SessionStatisticsOverviewDto> GetSessionStatisticsOverviewAsync()
        {
            var sessions = await GetAllSessionsFromConnectionManagerAsync();
            
            return new SessionStatisticsOverviewDto
            {
                Active = sessions.Count(s => s.State == SessionState.Active),
                Suspended = sessions.Count(s => s.State == SessionState.Suspended),
                Persistent = sessions.Count(s => s.IsPersistent),
                TotalCreated = sessions.Count
            };
        }

        private async Task<SubscriptionStatisticsDto> GetSubscriptionStatisticsAsync()
        {
            var connectedClients = _mqttBroker.GetConnectedClients();
            var allSubscriptions = connectedClients.SelectMany(c => c.SubscribedTopics ?? new List<string>()).ToList();
            
            return new SubscriptionStatisticsDto
            {
                TotalSubscriptions = allSubscriptions.Count,
                UniqueTopics = allSubscriptions.Distinct().Count(),
                AverageSubscriptionsPerClient = connectedClients.Count > 0 
                    ? (double)allSubscriptions.Count / connectedClients.Count 
                    : 0,
                TopSubscribedTopics = GetTopSubscribedTopics(allSubscriptions),
                WildcardSubscriptions = allSubscriptions.Count(t => t.Contains('+') || t.Contains('#'))
            };
        }

        private List<TopClientDto> GetTopClientsByConnections(IReadOnlyList<MqttClientInfo> clients)
        {
            return clients
                .GroupBy(c => c.ClientId)
                .Select(g => new TopClientDto
                {
                    ClientId = g.Key,
                    Count = g.Count(),
                    LastSeen = g.Max(c => c.ConnectedTime)
                })
                .OrderByDescending(c => c.Count)
                .Take(10)
                .ToList();
        }

        private List<TopTopicDto> GetTopTopicsByMessages()
        {
            lock (_topicMessageCounts)
            {
                return _topicMessageCounts
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10)
                    .Select(kvp => new TopTopicDto
                    {
                        Topic = kvp.Key,
                        MessageCount = kvp.Value,
                        LastActivity = _topicLastActivity.GetValueOrDefault(kvp.Key, DateTime.MinValue)
                    })
                    .ToList();
            }
        }

        private List<MessageQosStatsDto> GetMessagesByQos()
        {
            // 这里需要实际实现QoS统计，目前返回模拟数据
            return new List<MessageQosStatsDto>
            {
                new() { QosLevel = 0, Count = _totalMessagesReceived * 70 / 100, Percentage = 70 },
                new() { QosLevel = 1, Count = _totalMessagesReceived * 25 / 100, Percentage = 25 },
                new() { QosLevel = 2, Count = _totalMessagesReceived * 5 / 100, Percentage = 5 }
            };
        }

        private long GetRetainedMessageCount()
        {
            // 需要从MQTT代理获取实际的保留消息数量
            return 0; // 暂时返回0
        }

        private List<AuthenticationFailureDto> GetRecentAuthenticationFailures()
        {
            // 需要实际实现认证失败记录
            return new List<AuthenticationFailureDto>();
        }

        private async Task<double> GetCpuUsageAsync()
        {
            // 简单的CPU使用率计算
            var startTime = DateTime.UtcNow;
            var startCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
            await Task.Delay(500);
            var endTime = DateTime.UtcNow;
            var endCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
            
            var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
            var totalMsPassed = (endTime - startTime).TotalMilliseconds;
            var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
            
            return cpuUsageTotal * 100;
        }

        private double CalculateConnectionRate()
        {
            var uptime = (DateTime.UtcNow - _serviceStartTime).TotalSeconds;
            return uptime > 0 ? _connectionManager.GetConnectionStatistics().TotalConnections / uptime : 0;
        }

        private double CalculateMessageRate(long totalMessages)
        {
            var uptime = (DateTime.UtcNow - _serviceStartTime).TotalSeconds;
            return uptime > 0 ? totalMessages / uptime : 0;
        }

        private double CalculateBytesRate()
        {
            var uptime = (DateTime.UtcNow - _serviceStartTime).TotalSeconds;
            return uptime > 0 ? _totalBytesTransferred / uptime : 0;
        }

        private async Task<List<MqttSession>> GetAllSessionsFromConnectionManagerAsync()
        {
            // 需要从连接管理器获取所有会话
            // 这里返回空列表，需要实际实现
            return new List<MqttSession>();
        }

        private List<TopicInfoDto> GetAllTopicsFromClients(IReadOnlyList<MqttClientInfo> clients)
        {
            var topicGroups = clients
                .SelectMany(c => c.SubscribedTopics ?? new List<string>())
                .GroupBy(t => t)
                .Select(g => new TopicInfoDto
                {
                    Topic = g.Key,
                    SubscriberCount = g.Count(),
                    MessageCount = _topicMessageCounts.GetValueOrDefault(g.Key, 0),
                    LastActivity = _topicLastActivity.GetValueOrDefault(g.Key, DateTime.MinValue)
                })
                .ToList();

            return topicGroups;
        }

        private int GetActiveTopicsCount()
        {
            lock (_topicLastActivity)
            {
                var activeThreshold = DateTime.UtcNow.AddMinutes(-5);
                return _topicLastActivity.Count(kvp => kvp.Value > activeThreshold);
            }
        }

        private async Task<List<TimeSeriesDataDto>> GetConnectionsByTimeAsync(DateTime startTime, DateTime endTime, TimeGroupBy groupBy)
        {
            // 需要实际实现基于时间的连接统计
            return new List<TimeSeriesDataDto>();
        }

        private List<ConnectionDurationStatsDto> GetConnectionDurationDistribution(IReadOnlyList<MqttClientInfo> clients)
        {
            var now = DateTime.UtcNow;
            var durations = clients.Select(c => (now - c.ConnectedTime).TotalMinutes).ToList();
            
            return new List<ConnectionDurationStatsDto>
            {
                new() { Range = "0-1 分钟", Count = durations.Count(d => d <= 1) },
                new() { Range = "1-5 分钟", Count = durations.Count(d => d > 1 && d <= 5) },
                new() { Range = "5-30 分钟", Count = durations.Count(d => d > 5 && d <= 30) },
                new() { Range = "30分钟-1小时", Count = durations.Count(d => d > 30 && d <= 60) },
                new() { Range = "1小时以上", Count = durations.Count(d => d > 60) }
            };
        }

        private async Task<List<TimeSeriesDataDto>> GetMessagesByTimeAsync(DateTime startTime, DateTime endTime, TimeGroupBy groupBy)
        {
            // 需要实际实现基于时间的消息统计
            return new List<TimeSeriesDataDto>();
        }

        private long GetRetainedMessagesTotalSize()
        {
            // 需要实际实现
            return 0;
        }

        private List<TopTopicDto> GetTopRetainedMessageTopics()
        {
            // 需要实际实现
            return new List<TopTopicDto>();
        }

        private async Task<TopicHierarchyDto> AnalyzeTopicHierarchyAsync(List<TopicInfoDto> topics, int maxDepth)
        {
            // 需要实际实现主题层次分析
            return new TopicHierarchyDto
            {
                MaxDepth = maxDepth,
                TotalLevels = 0,
                Hierarchy = new List<TopicLevelDto>()
            };
        }

        private WildcardUsageDto AnalyzeWildcardUsage(List<TopicInfoDto> topics)
        {
            var totalTopics = topics.Count;
            var singleLevelWildcards = topics.Count(t => t.Topic.Contains('+'));
            var multiLevelWildcards = topics.Count(t => t.Topic.Contains('#'));
            
            return new WildcardUsageDto
            {
                TotalWildcardTopics = singleLevelWildcards + multiLevelWildcards,
                SingleLevelWildcards = singleLevelWildcards,
                MultiLevelWildcards = multiLevelWildcards,
                WildcardPercentage = totalTopics > 0 
                    ? (double)(singleLevelWildcards + multiLevelWildcards) / totalTopics * 100 
                    : 0
            };
        }

        private async Task<TopicDetailDto> GetTopicDetailsAsync(string topicFilter)
        {
            // 需要实际实现主题详细信息获取
            return new TopicDetailDto
            {
                Topic = topicFilter,
                SubscriberCount = 0,
                MessageCount = 0,
                TotalBytes = 0,
                LastActivity = DateTime.MinValue,
                Subscribers = new List<string>()
            };
        }

        private double CalculateAverageSessionDuration(List<MqttSession> sessions)
        {
            if (!sessions.Any()) return 0;
            
            var now = DateTime.UtcNow;
            var durations = sessions.Select(s => (now - s.CreatedTime).TotalSeconds);
            return durations.Average();
        }

        private List<SessionDurationStatsDto> GetSessionsByDuration(List<MqttSession> sessions)
        {
            var now = DateTime.UtcNow;
            var durations = sessions.Select(s => (now - s.CreatedTime).TotalMinutes).ToList();
            
            return new List<SessionDurationStatsDto>
            {
                new() { Range = "0-1 分钟", Count = durations.Count(d => d <= 1) },
                new() { Range = "1-5 分钟", Count = durations.Count(d => d > 1 && d <= 5) },
                new() { Range = "5-30 分钟", Count = durations.Count(d => d > 5 && d <= 30) },
                new() { Range = "30分钟-1小时", Count = durations.Count(d => d > 30 && d <= 60) },
                new() { Range = "1小时以上", Count = durations.Count(d => d > 60) }
            };
        }

        private async Task<List<TimeSeriesDataDto>> GetHistoricalConnectionDataAsync(DateTime startTime, DateTime endTime, TimeSpan interval)
        {
            // 需要实际实现历史连接数据
            return new List<TimeSeriesDataDto>();
        }

        private async Task<List<TimeSeriesDataDto>> GetHistoricalMessageDataAsync(DateTime startTime, DateTime endTime, TimeSpan interval)
        {
            // 需要实际实现历史消息数据
            return new List<TimeSeriesDataDto>();
        }

        private async Task<List<TimeSeriesDataDto>> GetHistoricalPerformanceDataAsync(DateTime startTime, DateTime endTime, TimeSpan interval)
        {
            // 需要实际实现历史性能数据
            return new List<TimeSeriesDataDto>();
        }

        private async Task<int> GetPeakConnectionsAsync(DateTime startTime, DateTime endTime)
        {
            // 需要实际实现峰值连接数统计
            return _connectionManager.GetConnectionStatistics().MaxConcurrentConnections;
        }

        private async Task<double> GetPeakMessageRateAsync(DateTime startTime, DateTime endTime)
        {
            // 需要实际实现峰值消息速率统计
            return CalculateMessageRate(_totalMessagesPublished + _totalMessagesReceived);
        }

        private async Task<double> GetAverageUptimeAsync(DateTime startTime, DateTime endTime)
        {
            // 需要实际实现平均运行时间统计
            return (DateTime.UtcNow - _serviceStartTime).TotalHours;
        }

        private async Task<TimeSpan> GetTotalDowntimeAsync(DateTime startTime, DateTime endTime)
        {
            // 需要实际实现总停机时间统计
            return TimeSpan.Zero;
        }

        private async Task<object> GenerateReportDataAsync(ExportStatisticsRequest request)
        {
            var overview = await GetOverviewAsync();
            return overview.Data;
        }

        private async Task<byte[]> GenerateCsvReportAsync(object reportData)
        {
            // 需要实际实现CSV导出
            var csv = "统计项,值\n";
            csv += "服务状态,运行中\n";
            // ... 更多CSV数据
            return System.Text.Encoding.UTF8.GetBytes(csv);
        }

        private async Task<byte[]> GenerateExcelReportAsync(object reportData)
        {
            // 需要实际实现Excel导出，这里返回空数据
            return new byte[0];
        }

        private List<TopSubscribedTopicDto> GetTopSubscribedTopics(List<string> allSubscriptions)
        {
            return allSubscriptions
                .GroupBy(t => t)
                .Select(g => new TopSubscribedTopicDto
                {
                    Topic = g.Key,
                    SubscriberCount = g.Count(),
                    Percentage = (double)g.Count() / allSubscriptions.Count * 100
                })
                .OrderByDescending(t => t.SubscriberCount)
                .Take(10)
                .ToList();
        }

        private string FormatUptime(TimeSpan uptime)
        {
            if (uptime.TotalDays >= 1)
                return $"{(int)uptime.TotalDays}天 {uptime.Hours}小时 {uptime.Minutes}分钟";
            else if (uptime.TotalHours >= 1)
                return $"{uptime.Hours}小时 {uptime.Minutes}分钟";
            else
                return $"{uptime.Minutes}分钟 {uptime.Seconds}秒";
        }

        private string FormatDuration(TimeSpan duration)
        {
            if (duration.TotalDays >= 1)
                return $"{duration.TotalDays:F1}天";
            else if (duration.TotalHours >= 1)
                return $"{duration.TotalHours:F1}小时";
            else if (duration.TotalMinutes >= 1)
                return $"{duration.TotalMinutes:F1}分钟";
            else
                return $"{duration.TotalSeconds:F1}秒";
        }

        #endregion
    }
} 