// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 组合控制
/// </summary>
[SugarTable("COMBINE_CONTROL")]
public partial class CombineControlEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 组合名称
    /// </summary>
    [SugarColumn(ColumnName = "NAME")]
    public string Name { get; set; }


    /// <summary>
    /// 指令id集
    /// </summary>
    [SugarColumn(ColumnName = "CMD_IDS")]
    public string CmdIds { get; set; }

    /// <summary>
    /// 区域id
    /// </summary>
    [SugarColumn(ColumnName = "AREA_ID")]
    public int? AreaId { get; set; }

    /// <summary>
    /// 指令间隔
    /// 单位：毫秒
    /// </summary>
    [SugarColumn(ColumnName = "CMD_INTERVAL")]
    public int CmdInterval { get; set; } = 1000;

    /// <summary>
    /// 是否启用APP控制
    /// 0:否 1:是
    /// </summary>
    [SugarColumn(ColumnName = "IS_APP_CONTROL")]
    public int IsAppControl { get; set; } = 1;
}
