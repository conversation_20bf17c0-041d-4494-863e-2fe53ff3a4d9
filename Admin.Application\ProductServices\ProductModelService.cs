// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ProductServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ProductServices;

/// <summary>
/// 产品模型服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class ProductModelService(ISqlSugarClient db, Repository<ProductModelEntity> productModelRepository) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<ProductModelEntity> _productModelRepository = productModelRepository;

    /// <summary>
    /// 获取产品模型分页列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页列表</returns>
    public async Task<PagedList<ProductModelOutput>> GetPagedListAsync(ProductModelQueryInput input)
    {
        var query = _db.Queryable<ProductModelEntity>()
            .LeftJoin<ProductEntity>((pm, p) => pm.ProductId == p.Id)
            .WhereIF(input.ProductId.HasValue, (pm, p) => pm.ProductId == input.ProductId.Value)
            .WhereIF(!string.IsNullOrEmpty(input.ModelName), (pm, p) => pm.ModelName.Contains(input.ModelName))
            .WhereIF(input.DeviceGroup.HasValue, (pm, p) => pm.DeviceGroup == input.DeviceGroup.Value)

            .WhereIF(input.IsEnabled.HasValue, (pm, p) => pm.IsEnabled == input.IsEnabled.Value)
            .OrderByDescending((pm, p) => pm.Id)
            .Select((pm, p) => new ProductModelOutput
            {
                Id = pm.Id,
                ProductId = pm.ProductId,
                ProductName = p.ProductName,
                ModelName = pm.ModelName,
                DeviceGroup = pm.DeviceGroup,
                Description = pm.Description,
                IsEnabled = pm.IsEnabled
            });

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        var result = pagedList.Adapt<PagedList<ProductModelOutput>>();

        // 映射设备组名称
        foreach (var item in result.Items)
        {
            item.DeviceGroupName = GetDeviceGroupName(item.DeviceGroup);
        }

        return result;
    }

    /// <summary>
    /// 根据产品ID获取产品模型详情
    /// </summary>
    /// <param name="id">模型ID</param>
    /// <returns>模型详情</returns>
    public async Task<ProductModelOutput> GetByIdAsync(long id)
    {
        var entity = await _db.Queryable<ProductModelEntity>()
            .LeftJoin<ProductEntity>((pm, p) => pm.ProductId == p.Id)
            .Where((pm, p) => pm.Id == id)
            .Select((pm, p) => new ProductModelOutput
            {
                Id = pm.Id,
                ProductId = pm.ProductId,
                ProductName = p.ProductName,
                ModelName = pm.ModelName,
                DeviceGroup = pm.DeviceGroup,
                Description = pm.Description,
                IsEnabled = pm.IsEnabled
            })
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        var output = entity.Adapt<ProductModelOutput>();

        // 映射设备组名称
        output.DeviceGroupName = GetDeviceGroupName(output.DeviceGroup);

        return output;
    }

    /// <summary>
    /// 获取产品模型详情（包含属性信息）
    /// </summary>
    /// <param name="id">模型ID</param>
    /// <returns>模型详情</returns>
    public async Task<ProductModelDetailOutput> GetDetailByIdAsync(long id)
    {
        var entity = await _db.Queryable<ProductModelEntity>()
            .LeftJoin<ProductEntity>((pm, p) => pm.ProductId == p.Id)
            .Where((pm, p) => pm.Id == id)
            .Select((pm, p) => new ProductModelOutput
            {
                Id = pm.Id,
                ProductId = pm.ProductId,
                ProductName = p.ProductName,
                ModelName = pm.ModelName,
                DeviceGroup = pm.DeviceGroup,
                Description = pm.Description,
                IsEnabled = pm.IsEnabled
            })
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        var detail = entity.Adapt<ProductModelDetailOutput>();

        // 映射设备组名称
        detail.DeviceGroupName = GetDeviceGroupName(detail.DeviceGroup);

        // 获取属性列表
        var properties = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == id)
            .OrderBy(mp => mp.Sort)
            .OrderBy(mp => mp.Id)
            .ToListAsync();

        detail.Properties = properties.Adapt<List<ProductPropertyOutput>>();

        return detail;
    }

    /// <summary>
    /// 添加产品模型
    /// </summary>
    /// <param name="input">模型信息</param>
    /// <returns>模型ID</returns>
    public async Task<long> AddAsync(ProductModelInput input)
    {
        // 验证产品是否存在
        var product = await _db.Queryable<ProductEntity>()
            .Where(p => p.Id == input.ProductId)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品不存在");

        // 验证模型名称是否重复（同一产品下）
        var exists = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.ProductId == input.ProductId && pm.ModelName == input.ModelName)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("该产品下已存在同名模型");



        var entity = input.Adapt<ProductModelEntity>();
        return await _productModelRepository.InsertReturnSnowflakeIdAsync(entity);
    }

    /// <summary>
    /// 更新产品模型
    /// 只能修改ModelName、Description、IsEnabled
    /// </summary>
    /// <param name="input">模型信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateAsync(UpdateProductModelInput input)
    {
        var entity = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.Id == input.Id)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        // 验证模型名称是否重复（同一产品下，排除自己）
        var exists = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.ProductId == entity.ProductId && pm.ModelName == input.ModelName && pm.Id != input.Id)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("该产品下已存在同名模型");

        // 只更新允许修改的字段：ModelName、Description、IsEnabled
        entity.ModelName = input.ModelName;
        entity.Description = input.Description;
        entity.IsEnabled = input.IsEnabled;

        return await _productModelRepository.UpdateAsync(entity);
    }

    /// <summary>
    /// 删除产品模型
    /// </summary>
    /// <param name="id">模型ID</param>
    /// <returns>是否成功</returns>
    [UnitOfWork]
    public async Task<bool> DeleteAsync(long id)
    {
        var entity = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.Id == id)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        // 检查是否有关联的设备
        var hasDevices = await _db.Queryable<DeviceEntity>()
            .Where(d => d.ModelId == id)
            .AnyAsync();

        if (hasDevices)
            throw PersistdValidateException.Message("该模型下存在关联的设备，无法删除");

        // 删除关联的属性
        await _db.Deleteable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == id)
            .ExecuteCommandAsync();

        return await _productModelRepository.DeleteAsync(entity);
    }

    /// <summary>
    /// 批量删除产品模型
    /// </summary>
    /// <param name="ids">模型ID集合</param>
    /// <returns>是否成功</returns>
    [UnitOfWork]
    public async Task<bool> BatchDeleteAsync(List<long> ids)
    {
        if (ids == null || ids.Count == 0)
            throw PersistdValidateException.Message("请选择要删除的模型");

        // 检查是否有关联的设备
        var hasDevices = await _db.Queryable<DeviceEntity>()
            .Where(d => ids.Contains(d.ModelId))
            .AnyAsync();

        if (hasDevices)
            throw PersistdValidateException.Message("选中的模型中存在关联的设备，无法删除");

        // 删除关联的属性
        await _db.Deleteable<ModelPropertyEntity>()
            .Where(mp => ids.Contains(mp.ModelId))
            .ExecuteCommandAsync();

        return await _productModelRepository.DeleteAsync(pm => ids.Contains(pm.Id));
    }

    /// <summary>
    /// 根据产品ID获取模型列表
    /// </summary>
    /// <param name="productId">产品ID</param>
    /// <returns>模型列表</returns>
    public async Task<List<ProductModelSimpleOutput>> GetByProductIdAsync(long productId)
    {
        var models = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.ProductId == productId && pm.IsEnabled)
            .Select(pm => new ProductModelSimpleOutput
            {
                Id = pm.Id,
                ProductId = pm.ProductId,
                ModelName = pm.ModelName,
                DeviceGroup = pm.DeviceGroup
            })
            .OrderBy(pm => pm.ModelName)
            .ToListAsync();

        var result = models.Adapt<List<ProductModelSimpleOutput>>();

        // 映射设备组名称
        foreach (var item in result)
        {
            item.DeviceGroupName = GetDeviceGroupName(item.DeviceGroup);
        }

        return result;
    }

    /// <summary>
    /// 根据设备组获取模型列表
    /// </summary>
    /// <param name="deviceGroup">设备组</param>
    /// <returns>模型列表</returns>
    public async Task<List<ProductModelSimpleOutput>> GetByDeviceGroupAsync(int deviceGroup)
    {
        var models = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.DeviceGroup == deviceGroup && pm.IsEnabled)
            .Select(pm => new ProductModelSimpleOutput
            {
                Id = pm.Id,
                ProductId = pm.ProductId,
                ModelName = pm.ModelName,
                DeviceGroup = pm.DeviceGroup
            })
            .OrderBy(pm => pm.ModelName)
            .ToListAsync();

        var result = models.Adapt<List<ProductModelSimpleOutput>>();

        // 映射设备组名称
        foreach (var item in result)
        {
            item.DeviceGroupName = GetDeviceGroupName(item.DeviceGroup);
        }

        return result;
    }

    /// <summary>
    /// 启用/禁用模型
    /// </summary>
    /// <param name="id">模型ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>是否成功</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        var entity = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.Id == id)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        entity.IsEnabled = isEnabled;
        return await _productModelRepository.UpdateAsync(entity);
    }

    /// <summary>
    /// 获取模型简单列表 (用于下拉选择)
    /// </summary>
    /// <returns>模型简单列表</returns>
    public async Task<List<ProductModelSimpleOutput>> GetSimpleListAsync()
    {
        var models = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.IsEnabled)
            .Select(pm => new ProductModelSimpleOutput
            {
                Id = pm.Id,
                ProductId = pm.ProductId,
                ModelName = pm.ModelName,
                DeviceGroup = pm.DeviceGroup
            })
            .OrderBy(pm => pm.ModelName)
            .ToListAsync();

        var result = models.Adapt<List<ProductModelSimpleOutput>>();

        // 映射设备组名称
        foreach (var item in result)
        {
            item.DeviceGroupName = GetDeviceGroupName(item.DeviceGroup);
        }

        return result;
    }

    /// <summary>
    /// 获取设备组名称
    /// </summary>
    /// <param name="deviceGroup">设备组</param>
    /// <returns>设备组名称</returns>
    private static string GetDeviceGroupName(int deviceGroup)
    {
        return deviceGroup switch
        {
            1 => "温湿度",
            2 => "漏水检测",
            3 => "空调",
            4 => "UPS",
            5 => "配电",
            6 => "开关",
            7 => "发电机",
            8 => "红外",
            9 => "门禁",
            10 => "传感器",
            11 => "冷通道",
            _ => "未知设备组"
        };
    }
}