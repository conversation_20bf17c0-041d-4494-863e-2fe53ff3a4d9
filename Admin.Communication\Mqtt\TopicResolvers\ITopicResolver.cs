using System.Threading.Tasks;
using Admin.Communication.Mqtt.DataParsers;
using Admin.Communication.Mqtt.TopicFilters;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Communication.Mqtt.TopicResolvers
{
    /// <summary>
    /// 主题解析器接口
    /// </summary>
    public interface ITopicResolver
    {
        /// <summary>
        /// 解析主题并获取设备解析上下文
        /// </summary>
        /// <param name="topic">MQTT主题</param>
        /// <param name="deviceId">设备ID</param>
        /// <returns>设备解析上下文</returns>
        Task<TopicResolveResult> ResolveAsync(string topic, string deviceId);
    }

    /// <summary>
    /// 主题解析结果
    /// </summary>
    public class TopicResolveResult
    {
        /// <summary>
        /// 是否解析成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 主题类型
        /// </summary>
        public TopicType TopicType { get; set; }

        /// <summary>
        /// 数据格式
        /// </summary>
        public DataFormatEnum DataFormat { get; set; }

        /// <summary>
        /// 设备解析上下文
        /// </summary>
        public DeviceParseContext? ParseContext { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="topicType">主题类型</param>
        /// <param name="dataFormat">数据格式</param>
        /// <param name="parseContext">解析上下文</param>
        /// <returns>成功结果</returns>
        public static TopicResolveResult Success(TopicType topicType, DataFormatEnum dataFormat, DeviceParseContext parseContext)
        {
            return new TopicResolveResult
            {
                IsSuccess = true,
                TopicType = topicType,
                DataFormat = dataFormat,
                ParseContext = parseContext
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>失败结果</returns>
        public static TopicResolveResult Failure(string errorMessage)
        {
            return new TopicResolveResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }


}
