// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Application.DeviceServices.Dto;

/// <summary>
/// 设备最新数据输出
/// </summary>
public class DeviceDataLatestOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备编码
    /// </summary>
    public string DeviceCode { get; set; }

    /// <summary>
    /// 产品ID
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 属性ID
    /// </summary>
    public long PropertyId { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; }

    /// <summary>
    /// 数据类型（1:int整形 2:long长整型 3:decimal小数 4:string字符串 5:datetime日期时间 6:JSON结构体 7:enum枚举 8:boolean布尔 9:stringList数组）
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 数据值
    /// </summary>
    public string DataValue { get; set; }

    /// <summary>
    /// 属性单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 数据状态 (1:正常 2:异常)
    /// </summary>
    public int DataStatus { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 设备状态 (0:离线 1:在线)
    /// </summary>
    public int DeviceStatus { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 离线时长（分钟）
    /// </summary>
    public int OfflineMinutes { get; set; }
}

/// <summary>
/// 设备历史数据输出
/// </summary>
public class DeviceDataHistoryOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; }

    /// <summary>
    /// 属性值
    /// </summary>
    public string PropertyValue { get; set; }

    /// <summary>
    /// 数据时间戳
    /// </summary>
    public DateTime DataTime { get; set; }
}

/// <summary>
/// 获取设备历史数据输入
/// </summary>
public class GetDeviceHistoryInput : PaginationParams
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 设备数据统计输出
/// </summary>
public class DeviceDataStatisticsOutput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 总数据量
    /// </summary>
    public long TotalCount { get; set; }

    /// <summary>
    /// 最早数据时间
    /// </summary>
    public DateTime? EarliestTime { get; set; }

    /// <summary>
    /// 最新数据时间
    /// </summary>
    public DateTime? LatestTime { get; set; }

    /// <summary>
    /// 统计时间范围
    /// </summary>
    public string TimeRange { get; set; }
}

/// <summary>
/// 获取设备数据统计输入
/// </summary>
public class GetDataStatisticsInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 设备状态概览输出
/// </summary>
public class DeviceStatusOverviewOutput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备编码
    /// </summary>
    public string DeviceCode { get; set; }

    /// <summary>
    /// 设备状态 (0:离线 1:在线)
    /// </summary>
    public int DeviceStatus { get; set; }

    /// <summary>
    /// 最后数据时间
    /// </summary>
    public DateTime? LastDataTime { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 离线时长（分钟）
    /// </summary>
    public int OfflineMinutes { get; set; }

    /// <summary>
    /// 设备数据JSON
    /// </summary>
    public string DataJson { get; set; }
}

/// <summary>
/// 批量获取设备最新数据输入
/// </summary>
public class BatchGetLatestDataInput
{
    /// <summary>
    /// 设备ID列表
    /// </summary>
    public List<long> DeviceIds { get; set; } = new();

    /// <summary>
    /// 是否包含离线设备
    /// </summary>
    public bool IncludeOffline { get; set; } = true;
}

/// <summary>
/// 设备数据趋势输出
/// </summary>
public class DeviceDataTrendOutput
{
    /// <summary>
    /// 时间分组
    /// </summary>
    public string TimeGroup { get; set; }

    /// <summary>
    /// 数据数量
    /// </summary>
    public int DataCount { get; set; }

    /// <summary>
    /// 平均值
    /// </summary>
    public decimal? AvgValue { get; set; }

    /// <summary>
    /// 最大值
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// 最小值
    /// </summary>
    public decimal? MinValue { get; set; }
}

/// <summary>
/// 保存设备最新数据输入
/// </summary>
public class SaveDeviceLatestDataInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 属性ID
    /// </summary>
    public long PropertyId { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; }

    /// <summary>
    /// 数据类型（1:int整形 2:long长整型 3:decimal小数 4:string字符串 5:datetime日期时间 6:JSON结构体 7:enum枚举 8:boolean布尔 9:stringList数组）
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 数据值
    /// </summary>
    public string DataValue { get; set; }

    /// <summary>
    /// 属性单位
    /// </summary>
    public string Unit { get; set; } = string.Empty;

    /// <summary>
    /// 数据状态 (1:正常 2:异常)
    /// </summary>
    public int DataStatus { get; set; } = 1;
}

/// <summary>
/// 获取设备属性最新数据输入
/// </summary>
public class GetLatestPropertyDataInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 属性ID
    /// </summary>
    public long PropertyId { get; set; }
}

/// <summary>
/// 获取设备数据趋势输入
/// </summary>
public class GetDataTrendInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 时间格式化字符串
    /// </summary>
    public string TimeFormat { get; set; } = "%Y-%m-%d %H";

    /// <summary>
    /// 属性路径（JSON路径）
    /// </summary>
    public string PropertyPath { get; set; }
} 