using System;
using System.Text;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.DataParsers;
using Admin.Communication.Mqtt.TopicResolvers;
using Admin.Multiplex.Contracts.Enums;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.Examples
{
    /// <summary>
    /// 数据解析器使用示例
    /// </summary>
    public class DataParserExample
    {
        private readonly ITopicResolver _topicResolver;
        private readonly IDataParserFactory _dataParserFactory;
        private readonly ILogger<DataParserExample> _logger;

        public DataParserExample(
            ITopicResolver topicResolver,
            IDataParserFactory dataParserFactory,
            ILogger<DataParserExample> logger)
        {
            _topicResolver = topicResolver;
            _dataParserFactory = dataParserFactory;
            _logger = logger;
        }

        /// <summary>
        /// 演示JSON数据解析
        /// </summary>
        public async Task DemoJsonParsingAsync()
        {
            _logger.LogInformation("=== JSON数据解析示例 ===");

            // 模拟设备数据
            var deviceId = "device001";
            var topic = "/devices/device001/sys/properties/report";
            var jsonPayload = """
                {
                    "temp": 25.5,
                    "humi": 60.2,
                    "pressure": 1013.25
                }
                """;

            await ProcessDeviceDataAsync(deviceId, topic, Encoding.UTF8.GetBytes(jsonPayload));
        }

        /// <summary>
        /// 演示网关设备JSON数据解析
        /// </summary>
        public async Task DemoGatewayJsonParsingAsync()
        {
            _logger.LogInformation("=== 网关设备JSON数据解析示例 ===");

            var deviceId = "gateway001";
            var topic = "/devices/gateway001/sys/gateway/sub_devices/properties/report";
            var jsonPayload = """
                {
                    "sensors": {
                        "temp": 25.5,
                        "humi": 60.2
                    },
                    "IO": {
                        "IN1": 1,
                        "OUT1": 0
                    }
                }
                """;

            await ProcessDeviceDataAsync(deviceId, topic, Encoding.UTF8.GetBytes(jsonPayload));
        }

        /// <summary>
        /// 演示HEX数据解析
        /// </summary>
        public async Task DemoHexParsingAsync()
        {
            _logger.LogInformation("=== HEX数据解析示例 ===");

            var deviceId = "modbus001";
            var topic = "/devices/modbus001/sys/properties/report";
            
            // 模拟Modbus RTU响应数据: 01 03 04 02 15 12 54 CRC16
            var hexPayload = new byte[] { 0x01, 0x03, 0x04, 0x02, 0x15, 0x12, 0x54, 0x85, 0xCA };

            await ProcessDeviceDataAsync(deviceId, topic, hexPayload);
        }

        /// <summary>
        /// 演示配置主题数据解析
        /// </summary>
        public async Task DemoConfiguredTopicParsingAsync()
        {
            _logger.LogInformation("=== 配置主题数据解析示例 ===");

            var deviceId = "custom001";
            var topic = "/custom/device/custom001/data";
            var jsonPayload = """
                {
                    "status": "online",
                    "battery": 85,
                    "signal": -65
                }
                """;

            await ProcessDeviceDataAsync(deviceId, topic, Encoding.UTF8.GetBytes(jsonPayload));
        }

        /// <summary>
        /// 处理设备数据的通用方法
        /// </summary>
        private async Task ProcessDeviceDataAsync(string deviceId, string topic, byte[] payload)
        {
            try
            {
                _logger.LogInformation("处理设备数据: 设备={DeviceId}, 主题={Topic}, 载荷大小={Size}字节",
                    deviceId, topic, payload.Length);

                // 1. 解析主题
                var resolveResult = await _topicResolver.ResolveAsync(topic, deviceId);
                if (!resolveResult.IsSuccess)
                {
                    _logger.LogWarning("主题解析失败: {ErrorMessage}", resolveResult.ErrorMessage);
                    return;
                }

                _logger.LogInformation("主题解析成功: 类型={TopicType}, 数据格式={DataFormat}",
                    resolveResult.TopicType, resolveResult.DataFormat);

                // 2. 获取解析器
                var parser = _dataParserFactory.GetParser(resolveResult.DataFormat);
                if (parser == null)
                {
                    _logger.LogWarning("未找到支持的解析器: 数据格式={DataFormat}", resolveResult.DataFormat);
                    return;
                }

                _logger.LogInformation("使用解析器: {ParserName}", parser.Name);

                // 3. 检查解析能力
                if (!parser.CanParse(payload, resolveResult.ParseContext!))
                {
                    _logger.LogWarning("解析器无法处理当前载荷");
                    return;
                }

                // 4. 执行解析
                var parseResult = await parser.ParseAsync(payload, resolveResult.ParseContext!);
                if (!parseResult.IsSuccess)
                {
                    _logger.LogError("数据解析失败: {ErrorMessage}", parseResult.ErrorMessage);
                    return;
                }

                // 5. 显示解析结果
                _logger.LogInformation("解析成功: 解析出{Count}个属性, 耗时={ElapsedMs}ms",
                    parseResult.DataItems.Count, parseResult.ElapsedMilliseconds);

                foreach (var dataItem in parseResult.DataItems)
                {
                    if (dataItem.IsDefined)
                    {
                        _logger.LogInformation("  ✓ {PropertyName}({PropertyKey}) = {Value} [{DataType}] {Unit}",
                            dataItem.PropertyName, dataItem.PropertyKey, dataItem.Value,
                            GetDataTypeName(dataItem.DataType), dataItem.Unit);
                    }
                    else
                    {
                        _logger.LogInformation("  ⚠ 未定义属性: {PropertyKey} = {Value}",
                            dataItem.PropertyKey, dataItem.Value);
                    }
                }

                _logger.LogInformation("数据处理完成\n");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备数据时发生异常");
            }
        }

        /// <summary>
        /// 获取数据类型名称
        /// </summary>
        private static string GetDataTypeName(int dataType)
        {
            return dataType switch
            {
                1 => "int",
                2 => "long",
                3 => "decimal",
                4 => "string",
                5 => "datetime",
                6 => "json",
                7 => "enum",
                8 => "boolean",
                9 => "array",
                _ => "unknown"
            };
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public async Task RunAllExamplesAsync()
        {
            _logger.LogInformation("开始运行数据解析器示例...\n");

            await DemoJsonParsingAsync();
            await DemoGatewayJsonParsingAsync();
            await DemoHexParsingAsync();
            await DemoConfiguredTopicParsingAsync();

            _logger.LogInformation("所有示例运行完成!");
        }
    }
}
