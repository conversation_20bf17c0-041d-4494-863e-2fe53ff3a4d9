[00:26:47] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误
System.IO.EndOfStreamException: Unable to read beyond the end of the stream.
   at System.IO.BinaryReader.ReadByte()
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadString(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 385
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseConnectMessage(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 200
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseMessage(MessageType messageType, Byte flags, Byte[] data) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 129
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 51
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadConnectMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 786
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 374

[00:26:52] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误
System.IO.EndOfStreamException: Unable to read beyond the end of the stream.
   at System.IO.BinaryReader.ReadByte()
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadString(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 385
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseConnectMessage(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 200
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseMessage(MessageType messageType, Byte flags, Byte[] data) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 129
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 51
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadConnectMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 786
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 374

[00:26:56] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误
System.IO.EndOfStreamException: Unable to read beyond the end of the stream.
   at System.IO.BinaryReader.ReadByte()
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadString(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 385
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseConnectMessage(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 200
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseMessage(MessageType messageType, Byte flags, Byte[] data) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 129
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 51
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadConnectMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 786
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 374

[00:27:00] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误
System.IO.EndOfStreamException: Unable to read beyond the end of the stream.
   at System.IO.BinaryReader.ReadByte()
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadString(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 385
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseConnectMessage(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 200
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseMessage(MessageType messageType, Byte flags, Byte[] data) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 129
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 51
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadConnectMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 786
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 374

[00:27:08] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误
System.IO.EndOfStreamException: Unable to read beyond the end of the stream.
   at System.IO.BinaryReader.ReadByte()
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadString(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 385
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseConnectMessage(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 200
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseMessage(MessageType messageType, Byte flags, Byte[] data) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 129
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 51
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadConnectMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 786
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 374

[00:27:58] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误
System.IO.EndOfStreamException: Unable to read beyond the end of the stream.
   at System.IO.BinaryReader.ReadByte()
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadString(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 385
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseConnectMessage(BinaryReader reader) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 200
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParseMessage(MessageType messageType, Byte flags, Byte[] data) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 129
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 51
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadConnectMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 786
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 374

[00:35:14] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[10:09:15] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[11:46:04] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[23:32:48] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

