using Admin.Application.DeviceServices.Dto;
using Admin.Application.MqttBrokerServices;
using Admin.Application.MqttBrokerServices.Dto;
using Admin.Communication.Alarm.Services;
using Admin.Communication.Modbus;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Core.DataEncryption.Encryptions;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;

namespace Admin.Application.DeviceServices;

/// <summary>
/// 设备管理服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class DeviceService(ISqlSugarClient db, Repository<DeviceEntity> repository, IMqttUserService mqttUserService, IMqttAclManagementService mqttAclService, AlarmEventGeneratorService alarmEventGenerator, ILogger<DeviceService> logger) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<DeviceEntity> _repository = repository;
    private readonly IMqttUserService _mqttUserService = mqttUserService;
    private readonly IMqttAclManagementService _mqttAclService = mqttAclService;
    private readonly AlarmEventGeneratorService _alarmEventGenerator = alarmEventGenerator;
    private readonly ILogger<DeviceService> _logger = logger;

    /// <summary>
    /// 添加设备
    /// </summary>
    /// 设备添加流程：
    /// 选择模型 -> 创建设备 -> 如果协议类型为modbus、数据格式为HEX时同步模型指令至设备指令 -> 同步模型参数到设备参数表
    /// 直连设备和网关设备创建ACL规则并创建创建mqtt用户为DeviceId，密码为主键Id
    /// <param name="input">设备信息</param>
    /// <returns>设备实体信息</returns>
    public async Task<DeviceOutput> AddAsync(AddDeviceInput input)
    {
        // 1. 通过ModelId获取产品信息并验证
        var product = await GetProductByModelIdAsync(input.ModelId);

        // 2. 验证输入数据
        await ValidateDeviceInputAsync(input, product);

        // 3. 创建设备实体
        var entity = CreateDeviceEntity(input, product);

        // 4. 保存设备到数据库
        var deviceId = await _repository.InsertReturnSnowflakeIdAsync(entity);

        // 5. 根据产品协议类型同步模型参数到设备参数表
        await SyncModelParametersToDeviceAsync(deviceId, input.ModelId, product);

        // 6. 为直连设备和网关设备创建MQTT用户和ACL规则
        // 注意：根据注释要求，MQTT用户为DeviceId，密码为主键Id
        if ((product.ProductType == (int)DevicePropertyTypeEnum.DirectDevice ||
             product.ProductType == (int)DevicePropertyTypeEnum.GatewayDevice) &&
            !string.IsNullOrEmpty(entity.DeviceId))
        {
            // 使用设备主键ID作为MQTT用户密码
            var deviceSecret = deviceId.ToString();

            // 创建MQTT用户
            await _mqttUserService.CreateDeviceUserAsync(entity.DeviceId, deviceSecret, entity.DeviceName);

            // 创建ACL规则
            await CreateMqttAclRulesAsync(entity);
        }

        // 7. 生成告警事件
        try
        {
            var eventCount = await _alarmEventGenerator.GenerateAlarmEventsAsync(deviceId);
            _logger.LogInformation("设备添加完成，生成告警事件: DeviceId={DeviceId}, EventCount={EventCount}",
                deviceId, eventCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成告警事件失败: DeviceId={DeviceId}", deviceId);
            // 不抛出异常，避免影响设备创建流程
        }

        // 8. 返回完整的设备信息
        entity.Id = deviceId;

        // 构建输出DTO，使用已获取的产品信息
        var deviceOutput = BuildDeviceOutput(entity, input, product);
        return deviceOutput;
    }
    
    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>设备信息</returns>
    public async Task<DeviceOutput> GetByIdAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ?? 
            throw PersistdValidateException.Message("设备不存在");
        
        return entity.Adapt<DeviceOutput>();
    }
    
    /// <summary>
    /// 分页查询设备
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>设备分页列表</returns>
    public async Task<PagedList<DeviceOutput>> QueryAsync(DeviceQueryInput input)
    {
        var query = _db.Queryable<DeviceEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.DeviceName), d => d.DeviceName.Contains(input.DeviceName!))
            .WhereIF(input.Status.HasValue, d => d.Status == input.Status!.Value)
            .WhereIF(input.ProductId.HasValue, d => SqlFunc.Subqueryable<ProductModelEntity>().Where(pm => pm.Id == d.ModelId && pm.ProductId == input.ProductId!.Value).Any())
            .WhereIF(input.ParentId.HasValue, d => d.ParentId == input.ParentId!.Value)
            .WhereIF(!string.IsNullOrEmpty(input.DeviceIdentityCode), d => d.DeviceIdentityCode!.Contains(input.DeviceIdentityCode!))
            .OrderByDescending(d => d.CreateTime);
        
        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList.Adapt<PagedList<DeviceOutput>>();
    }
    
    /// <summary>
    /// 更新设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <param name="input">更新信息</param>
    /// <returns>是否成功</returns>
    /// 只能修改DeviceName、DeviceDescription、IpAddress
    public async Task<bool> UpdateAsync(long id, UpdateDeviceInput input)
    {
        // 获取设备
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("设备不存在");
        entity.DeviceName = input.DeviceName;
        entity.DeviceDescription = input.DeviceDescription ?? string.Empty;
        entity.IpAddress = input.IpAddress;
        // 保存更新
        var result = await _repository.UpdateAsync(entity);
        return result;
    }
    
    /// <summary>
    /// 删除设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>是否成功</returns>
    /// 删除设备包含：设备参数表 - 设备指令表(如有) - 设备MQTT用户 - ACL规则 ->设备主题配置表
    public async Task<bool> DeleteAsync(long id)
    {
        // 获取设备
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("设备不存在");

        // 检查是否有子设备
        var hasChildren = await _db.Queryable<DeviceEntity>()
            .AnyAsync(d => d.ParentId == id);

        bool deleteResult;
        if (hasChildren)
        {
            try
            {
                await _db.Ado.BeginTranAsync();

                // 1. 获取所有子设备
                var childDevices = await _db.Queryable<DeviceEntity>()
                    .Where(d => d.ParentId == id)
                    .ToListAsync();

                // 2. 删除所有子设备的相关数据
                foreach (var childDevice in childDevices)
                {
                    await DeleteDeviceRelatedDataAsync(childDevice.Id);
                }

                // 3. 删除子设备
                await _db.Deleteable<DeviceEntity>().Where(d => d.ParentId == id).ExecuteCommandAsync();

                // 4. 删除主设备的相关数据
                await DeleteDeviceRelatedDataAsync(entity.Id);

                // 5. 删除主设备
                await _db.Deleteable<DeviceEntity>().In(entity.Id).ExecuteCommandAsync();

                await _db.Ado.CommitTranAsync();

                // 删除非网关子设备的MQTT用户和ACL规则
                await DeleteDeviceMqttUsersAsync(entity);

                deleteResult = true;
            }
            catch (Exception ex)
            {
                await _db.Ado.RollbackTranAsync();
                throw PersistdValidateException.Message("删除事务失败: " + ex.Message);
            }
        }
        else
        {
            try
            {
                await _db.Ado.BeginTranAsync();

                // 1. 删除设备相关数据
                await DeleteDeviceRelatedDataAsync(entity.Id);

                // 2. 删除设备
                await _db.Deleteable<DeviceEntity>().In(entity.Id).ExecuteCommandAsync();

                await _db.Ado.CommitTranAsync();

                // 3. 删除对应的MQTT用户和ACL规则
                await DeleteDeviceMqttUsersAsync(entity);

                deleteResult = true;
            }
            catch (Exception ex)
            {
                await _db.Ado.RollbackTranAsync();
                throw PersistdValidateException.Message("删除设备失败: " + ex.Message);
            }
        }

        return deleteResult;
    }
    
    /// <summary>
    /// 启用设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> EnableAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ?? 
            throw PersistdValidateException.Message("设备不存在");
        
        if (entity.IsEnabled)
            throw PersistdValidateException.Message("设备已启用，无需重复操作");
        
        entity.IsEnabled = true;        
        return await _repository.UpdateAsync(entity);
    }
    
    /// <summary>
    /// 禁用设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DisableAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ?? 
            throw PersistdValidateException.Message("设备不存在");
        
        if (!entity.IsEnabled)
            throw PersistdValidateException.Message("设备已禁用，无需重复操作");
        
        entity.IsEnabled = false;
        return await _repository.UpdateAsync(entity);
    }
    
    /// <summary>
    /// 重置设备密钥
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>新的设备密钥</returns>
    public async Task<string> ResetSecretAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("设备不存在");

        // 重置密钥时使用UUID作为新密码
        var newSecret = SecretKeyGeneration.GenerateUuidKey("N"); // 32位guid，不包含连字符

        // 获取设备类型（通过模型关联的产品信息）
        var deviceType = await GetDeviceTypeAsync(entity.ModelId);

        // 如果是直连设备或网关设备，更新MQTT用户密码
        if ((deviceType == (int)DevicePropertyTypeEnum.DirectDevice ||
             deviceType == (int)DevicePropertyTypeEnum.GatewayDevice) &&
            !string.IsNullOrEmpty(entity.DeviceId))
        {
            try
            {
                await _mqttUserService.ResetPasswordAsync(entity.DeviceId, newSecret);
                _logger.LogInformation("设备密钥重置成功: DeviceId={DeviceId}", entity.DeviceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设备密钥重置失败: DeviceId={DeviceId}, Error={Error}",
                    entity.DeviceId, ex.Message);
                throw PersistdValidateException.Message("设备密钥重置失败");
            }
        }

        return newSecret;
    }

    /// <summary>
    /// 创建MQTT ACL规则
    /// </summary>
    /// <param name="entity">设备实体</param>
    private async Task CreateMqttAclRulesAsync(DeviceEntity entity)
    {
        try
        {
            var aclInput = new CreateDeviceAclRulesInput
            {
                DeviceId = entity.DeviceId,
                DeviceName = entity.DeviceName,
                AllowSystemTopics = false, // 默认不允许系统主题
                CustomAllowedTopics = null,
                CustomDeniedTopics = null
            };

            var aclResult = await _mqttAclService.CreateDeviceAclRulesAsync(aclInput);

            _logger.LogInformation("设备注册成功，已自动创建ACL规则: DeviceId={DeviceId}, 成功={SuccessCount}, 失败={FailCount}",
                entity.DeviceId, aclResult.SuccessCount, aclResult.FailCount);
        }
        catch (Exception ex)
        {
            // ACL规则创建失败不影响设备注册，只记录警告日志
            _logger.LogWarning(ex, "设备注册成功，但创建ACL规则失败: DeviceId={DeviceId}, Error={Error}",
                entity.DeviceId, ex.Message);
        }
    }



    /// <summary>
    /// 删除设备相关数据（设备参数表、设备指令表、设备主题配置表）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    private async Task DeleteDeviceRelatedDataAsync(long deviceId)
    {
        try
        {
            _logger.LogInformation("开始删除设备相关数据: DeviceId={DeviceId}", deviceId);

            // 1. 删除设备参数表数据
            var deviceParaCount = await _db.Deleteable<DeviceParaEntity>()
                .Where(dp => dp.DeviceId == deviceId)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除设备参数表数据: DeviceId={DeviceId}, 删除数量={Count}", deviceId, deviceParaCount);

            // 2. 删除设备指令表数据（如有）
            var deviceInstructionCount = await _db.Deleteable<DeviceInstructionEntity>()
                .Where(di => di.DeviceId == deviceId)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除设备指令表数据: DeviceId={DeviceId}, 删除数量={Count}", deviceId, deviceInstructionCount);

            // 3. 删除设备主题配置表数据（如有）
            var deviceTopicCount = await _db.Deleteable<DeviceTopicConfigEntity>()
                .Where(dt => dt.DeviceId == deviceId.ToString())
                .ExecuteCommandAsync();

            _logger.LogInformation("删除设备主题配置表数据: DeviceId={DeviceId}, 删除数量={Count}", deviceId, deviceTopicCount);

            _logger.LogInformation("完成删除设备相关数据: DeviceId={DeviceId}", deviceId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除设备相关数据失败: DeviceId={DeviceId}, Error={Error}", deviceId, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// 删除设备对应的MQTT用户和ACL规则
    /// </summary>
    /// <param name="device">设备实体</param>
    private async Task DeleteDeviceMqttUsersAsync(DeviceEntity device)
    {
        try
        {
            // 获取设备类型（通过模型关联的产品信息）
            var deviceType = await GetDeviceTypeAsync(device.ModelId);

            // 只有直连设备和网关设备才有MQTT用户和ACL规则需要删除
            if ((deviceType == (int)DevicePropertyTypeEnum.DirectDevice ||
                 deviceType == (int)DevicePropertyTypeEnum.GatewayDevice) &&
                !string.IsNullOrEmpty(device.DeviceId))
            {
                await _mqttUserService.DeleteUserAsync(device.DeviceId);
                await _mqttAclService.DeleteDeviceAclRulesAsync(new DeleteDeviceAclRulesInput
                {
                    DeviceId = device.DeviceId,
                    DeleteAllRelated = true
                });
                _logger.LogInformation("设备删除，已删除对应的MQTT用户和ACL规则: DeviceId={DeviceId}", device.DeviceId);
            }
            else
            {
                _logger.LogDebug("设备删除，无需删除MQTT用户（子设备无MQTT用户）: DeviceId={DeviceId}, DeviceType={DeviceType}",
                    device.DeviceId, deviceType);
            }
        }
        catch (Exception ex)
        {
            // MQTT用户删除失败不影响设备删除，只记录警告日志
            _logger.LogWarning(ex, "删除设备MQTT用户时发生错误: DeviceId={DeviceId}, Error={Error}",
                device.DeviceId, ex.Message);
        }
    }

    #region 私有辅助方法

    /// <summary>
    /// 验证设备输入数据
    /// </summary>
    /// <param name="input">设备输入数据</param>
    /// <param name="product">产品信息</param>
    private async Task ValidateDeviceInputAsync(AddDeviceInput input, ProductEntity product)
    {
        // 验证设备标识码唯一性（子设备可以为空字符串，但直连设备必须唯一）
        if (!string.IsNullOrEmpty(input.DeviceIdentityCode))
        {
            var exists = await _db.Queryable<DeviceEntity>()
                .AnyAsync(d => d.DeviceIdentityCode == input.DeviceIdentityCode);

            if (exists)
                throw PersistdValidateException.Message("设备标识码已存在");
        }

        // 验证设备属性类型与父设备ID的一致性
        bool isSubDevice = input.ParentId > 0;
        bool isSubDeviceByType = product.ProductType == (int)DevicePropertyTypeEnum.GatewaySubDevice;

        if (isSubDevice != isSubDeviceByType)
        {
            throw PersistdValidateException.Message("父设备ID和产品类型不匹配");
        }

        // 验证父设备
        if (input.ParentId > 0)
        {
            var parentExists = await _repository.GetByIdAsync(input.ParentId);
            if (parentExists is null)
                throw PersistdValidateException.Message("父设备不存在");

            // 验证父设备必须是网关设备（通过模型关联的产品信息获取设备类型）
            var parentDeviceType = await GetDeviceTypeAsync(parentExists.ModelId);
            if (parentDeviceType != (int)DevicePropertyTypeEnum.GatewayDevice)
                throw PersistdValidateException.Message("父设备必须是网关设备");
        }

        // 验证直连设备和网关设备必须有设备标识码
        if (product.ProductType != (int)DevicePropertyTypeEnum.GatewaySubDevice)
        {
            if (string.IsNullOrEmpty(input.DeviceIdentityCode))
                throw PersistdValidateException.Message("直连设备和网关设备的设备标识码不能为空");
        }

        // 验证DeviceId唯一性（如果提供了DeviceId）
        if (!string.IsNullOrEmpty(input.DeviceId))
        {
            var deviceIdExists = await _db.Queryable<DeviceEntity>()
                .AnyAsync(d => d.DeviceId == input.DeviceId);

            if (deviceIdExists)
                throw PersistdValidateException.Message("设备ID已存在");
        }
    }

    /// <summary>
    /// 创建设备实体
    /// </summary>
    /// <param name="input">设备输入数据</param>
    /// <param name="product">产品信息</param>
    /// <returns>设备实体</returns>
    private static DeviceEntity CreateDeviceEntity(AddDeviceInput input, ProductEntity product)
    {
        var entity = input.Adapt<DeviceEntity>();
        bool isSubDevice = input.ParentId > 0;

        // 根据产品类型判断是否为子设备
        bool isSubDeviceByType = product.ProductType == (int)DevicePropertyTypeEnum.GatewaySubDevice;

        // 确保ParentId和产品类型的一致性
        if (isSubDevice != isSubDeviceByType)
        {
            throw PersistdValidateException.Message("父设备ID和产品类型不匹配");
        }

        if (isSubDevice)
        {
            // 子设备：DeviceId为空字符串，DeviceIdentityCode为空字符串
            entity.DeviceId = string.Empty;
            entity.DeviceIdentityCode = string.Empty;
        }
        else
        {
            // 直连设备：验证DeviceIdentityCode不能为空
            if (string.IsNullOrEmpty(input.DeviceIdentityCode))
                throw PersistdValidateException.Message("直连设备的设备标识码不能为空");

            // 直连设备：生成DeviceId
            entity.DeviceId = GenerateDeviceId(input.DeviceId, input.DeviceIdentityCode);
        }

        // 设置初始状态
        entity.Status = (int)DeviceStatusEnum.Offline;
        entity.IsEnabled = true;

        return entity;
    }

    /// <summary>
    /// 生成设备ID
    /// </summary>
    /// <param name="providedDeviceId">用户提供的设备ID</param>
    /// <param name="deviceIdentityCode">设备标识码</param>
    /// <returns>生成的设备ID</returns>
    private static string GenerateDeviceId(string? providedDeviceId, string deviceIdentityCode)
    {
        if (!string.IsNullOrEmpty(providedDeviceId))
        {
            return providedDeviceId;
        }

        // 如果未提供DeviceId，按照规则生成：guid + _ + IdentityCode
        return $"{Guid.NewGuid():N}_{deviceIdentityCode}";
    }

    /// <summary>
    /// 同步模型参数到设备参数表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="modelId">模型ID</param>
    /// <param name="product">产品信息</param>
    private async Task SyncModelParametersToDeviceAsync(long deviceId, long modelId, ProductEntity product)
    {
        try
        {
            _logger.LogInformation("开始同步模型参数：DeviceId={DeviceId}, ProductId={ProductId}, ModelId={ModelId}, 协议类型={ProtocolType}, 数据格式={DataFormat}",
                deviceId, product.Id, modelId, product.ProtocolType, product.DataFormat);

            // 根据协议类型和数据格式执行不同的同步逻辑
            if (product.ProtocolType == (int)ProtocolTypeEnum.MQTT &&
                product.DataFormat == (int)DataFormatEnum.Json) // MQTT协议
            {
                await SyncMqttModelPropertiesToDeviceAsync(deviceId, modelId);
            }
            else if (product.ProtocolType == (int)ProtocolTypeEnum.Modbus &&
                     product.DataFormat == (int)DataFormatEnum.Hex) // Modbus协议且数据格式为HEX
            {
                // 按照注释要求：协议类型为modbus、数据格式为HEX时同步模型指令至设备指令
                await SyncModbusModelInstructionsToDeviceAsync(deviceId, modelId);
            }
            else
            {
                _logger.LogInformation("协议类型 {ProtocolType} 和数据格式 {DataFormat} 不需要同步模型参数",
                    product.ProtocolType, product.DataFormat);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步模型参数失败：DeviceId={DeviceId}, ModelId={ModelId}",
                deviceId, modelId);
            // 不抛出异常，避免影响设备创建流程
        }
    }

    /// <summary>
    /// 同步MQTT模型属性到设备参数表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="modelId">模型ID</param>
    private async Task SyncMqttModelPropertiesToDeviceAsync(long deviceId, long modelId)
    {
        try
        {
            // 查询模型属性
            var modelProperties = await _db.Queryable<ModelPropertyEntity>()
                .Where(mp => mp.ModelId == modelId)
                .ToListAsync();

            if (!modelProperties.Any())
            {
                _logger.LogInformation("模型 {ModelId} 没有属性需要同步", modelId);
                return;
            }

            var deviceParaList = new List<DeviceParaEntity>();

            foreach (var property in modelProperties)
            {
                var devicePara = new DeviceParaEntity
                {
                    DeviceId = deviceId,
                    InstructionId = 0, // MQTT JSON模型属性，指令ID为0
                    Name = property.Name,
                    Sort = 0, // MQTT JSON模型属性，参数序号为0
                    JsonKey = property.JsonKey ?? string.Empty,
                    DataType = property.DataType,
                    EnumDescription = property.EnumDescription ?? string.Empty,
                    Unit = property.Unit ?? string.Empty,
                    DivisionFactor = property.DivisionFactor,
                    DecimalPlaces = property.DecimalPlaces,
                    CorrectionScale = property.CorrectionScale,
                    CorrectionAmplitude = property.CorrectionAmplitude,
                    AlarmUpperLimit = property.AlarmUpperLimit,
                    AlarmUpperLimitClearValue = property.AlarmUpperLimitClearValue,
                    AlarmLowerLimit = property.AlarmLowerLimit,
                    AlarmLowerLimitClearValue = property.AlarmLowerLimitClearValue,
                    WarningUpperLimit = property.WarningUpperLimit,
                    WarningUpperLimitClearValue = property.WarningUpperLimitClearValue,
                    WarningLowerLimit = property.WarningLowerLimit,
                    WarningLowerLimitClearValue = property.WarningLowerLimitClearValue,
                    IsSave = property.IsSave,
                    SaveAmplitude = property.SaveAmplitude,
                    SaveAmplitudeType = property.SaveAmplitudeType,
                    SaveInterval = property.SaveInterval,
                    MonitorStatus = property.MonitorStatus,
                    Description = property.Description ?? string.Empty
                };

                deviceParaList.Add(devicePara);
            }

            // 批量插入设备参数
            await _db.Insertable(deviceParaList).ExecuteCommandAsync();

            _logger.LogInformation("成功同步MQTT模型属性到设备参数表：DeviceId={DeviceId}, ModelId={ModelId}, 属性数量={Count}",
                deviceId, modelId, deviceParaList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步MQTT模型属性失败：DeviceId={DeviceId}, ModelId={ModelId}",
                deviceId, modelId);
            throw;
        }
    }

    /// <summary>
    /// 同步Modbus模型指令到设备指令表和设备参数表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="modelId">模型ID</param>
    private async Task SyncModbusModelInstructionsToDeviceAsync(long deviceId, long modelId)
    {
        try
        {
            // 1. 查询模型指令
            var modelInstructions = await _db.Queryable<ModelInstructionEntity>()
                .Where(mi => mi.ModelId == modelId)
                .ToListAsync();

            if (!modelInstructions.Any())
            {
                _logger.LogInformation("模型 {ModelId} 没有指令需要同步", modelId);
                return;
            }

            var deviceInstructionList = new List<DeviceInstructionEntity>();
            var deviceParaList = new List<DeviceParaEntity>();

            foreach (var instruction in modelInstructions)
            {
                // 2. 生成Modbus RTU指令并同步到设备指令表
                var modbusCommand = await GenerateModbusRtuCommandAsync(deviceId, instruction);

                var deviceInstruction = new DeviceInstructionEntity
                {
                    DeviceId = deviceId,
                    InstructionName = instruction.InstructionName,
                    send_str = modbusCommand,
                    Encode = instruction.Encode,
                    ResponseTime = instruction.ResponseTime,
                    RetryCount = instruction.RetryCount,
                    IsEnabled = true // 默认启用
                };

                deviceInstructionList.Add(deviceInstruction);

                // 3. 查询指令相关的模型属性并同步到设备参数表
                var instructionProperties = await _db.Queryable<ModelPropertyEntity>()
                    .Where(mp => mp.InstructionId == instruction.Id)
                    .ToListAsync();

                foreach (var property in instructionProperties)
                {
                    var devicePara = new DeviceParaEntity
                    {
                        DeviceId = deviceId,
                        InstructionId = instruction.Id,
                        Name = property.Name,
                        Sort = property.Sort,
                        JsonKey = string.Empty, // Modbus HEX设备，参数标识符为空
                        DataType = property.DataType,
                        EnumDescription = property.EnumDescription ?? string.Empty,
                        Unit = property.Unit ?? string.Empty,
                        DivisionFactor = property.DivisionFactor,
                        DecimalPlaces = property.DecimalPlaces,
                        CorrectionScale = property.CorrectionScale,
                        CorrectionAmplitude = property.CorrectionAmplitude,
                        AlarmUpperLimit = property.AlarmUpperLimit,
                        AlarmUpperLimitClearValue = property.AlarmUpperLimitClearValue,
                        AlarmLowerLimit = property.AlarmLowerLimit,
                        AlarmLowerLimitClearValue = property.AlarmLowerLimitClearValue,
                        WarningUpperLimit = property.WarningUpperLimit,
                        WarningUpperLimitClearValue = property.WarningUpperLimitClearValue,
                        WarningLowerLimit = property.WarningLowerLimit,
                        WarningLowerLimitClearValue = property.WarningLowerLimitClearValue,
                        IsSave = property.IsSave,
                        SaveAmplitude = property.SaveAmplitude,
                        SaveAmplitudeType = property.SaveAmplitudeType,
                        SaveInterval = property.SaveInterval,
                        MonitorStatus = property.MonitorStatus,
                        Description = property.Description ?? string.Empty
                    };

                    deviceParaList.Add(devicePara);
                }
            }

            // 4. 批量插入设备指令
            if (deviceInstructionList.Any())
            {
                await _db.Insertable(deviceInstructionList).ExecuteCommandAsync();
            }

            // 5. 批量插入设备参数
            if (deviceParaList.Any())
            {
                await _db.Insertable(deviceParaList).ExecuteCommandAsync();
            }

            _logger.LogInformation("成功同步Modbus模型指令到设备：DeviceId={DeviceId}, ModelId={ModelId}, 指令数量={InstructionCount}, 参数数量={ParameterCount}",
                deviceId, modelId, deviceInstructionList.Count, deviceParaList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步Modbus模型指令失败：DeviceId={DeviceId}, ModelId={ModelId}",
                deviceId, modelId);
            throw;
        }
    }

    /// <summary>
    /// 生成Modbus RTU指令
    /// 指令组成：ModbusAddr + FunctionCode + StartAddress + ReadCount + CRC16
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="instruction">模型指令</param>
    /// <returns>16进制格式的Modbus RTU指令</returns>
    private async Task<string> GenerateModbusRtuCommandAsync(long deviceId, ModelInstructionEntity instruction)
    {
        try
        {
            // 1. 获取设备的Modbus地址
            var device = await _repository.GetByIdAsync(deviceId);
            if (device == null)
            {
                _logger.LogWarning("生成Modbus指令失败：设备不存在 DeviceId={DeviceId}", deviceId);
                return string.Empty;
            }

            // 2. 使用通信层的Modbus RTU指令生成器
            var hexCommand = ModbusRtuCommandGenerator.GenerateReadCommand(
                device.ModbusAddr,
                instruction.FunctionCode,
                instruction.StartAddress,
                instruction.ReadCount
            );

            _logger.LogInformation("生成Modbus RTU指令：设备地址={ModbusAddr}, 功能码={FunctionCode}, 起始地址={StartAddress}, 读取数量={ReadCount}, 完整指令={Command}",
                device.ModbusAddr, instruction.FunctionCode, instruction.StartAddress, instruction.ReadCount, hexCommand);

            return hexCommand;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成Modbus RTU指令失败：DeviceId={DeviceId}, InstructionId={InstructionId}",
                deviceId, instruction.Id);
            return string.Empty;
        }
    }

    /// <summary>
    /// 通过ModelId获取产品信息
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>产品信息</returns>
    private async Task<ProductEntity> GetProductByModelIdAsync(long modelId)
    {
        // 获取模型信息
        var model = await _db.Queryable<ProductModelEntity>()
            .Where(m => m.Id == modelId)
            .FirstAsync();

        if (model == null)
        {
            throw PersistdValidateException.Message("产品模型不存在");
        }

        // 获取产品信息
        var product = await _db.Queryable<ProductEntity>()
            .Where(p => p.Id == model.ProductId)
            .FirstAsync();

        if (product == null)
        {
            throw PersistdValidateException.Message("产品不存在");
        }

        return product;
    }

    /// <summary>
    /// 获取设备类型（通过模型关联的产品信息）
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>设备类型</returns>
    private async Task<int> GetDeviceTypeAsync(long modelId)
    {
        var model = await _db.Queryable<ProductModelEntity>()
            .Where(m => m.Id == modelId)
            .FirstAsync();

        if (model == null)
        {
            _logger.LogWarning("获取设备类型失败：模型不存在 ModelId={ModelId}", modelId);
            return (int)DevicePropertyTypeEnum.DirectDevice; // 默认为直连设备
        }

        var product = await _db.Queryable<ProductEntity>()
            .Where(p => p.Id == model.ProductId)
            .FirstAsync();

        if (product == null)
        {
            _logger.LogWarning("获取设备类型失败：产品不存在 ProductId={ProductId}", model.ProductId);
            return (int)DevicePropertyTypeEnum.DirectDevice; // 默认为直连设备
        }

        return product.ProductType; // 产品类型即设备类型
    }



    /// <summary>
    /// 构建设备输出DTO
    /// </summary>
    /// <param name="entity">设备实体</param>
    /// <param name="input">输入参数</param>
    /// <param name="product">产品信息</param>
    /// <returns>设备输出DTO</returns>
    private static DeviceOutput BuildDeviceOutput(DeviceEntity entity, AddDeviceInput input, ProductEntity product)
    {
        // 构建输出DTO
        var output = entity.Adapt<DeviceOutput>();

        // 填充产品相关信息
        output.ProductId = product.Id;
        output.ProtocolType = product.ProtocolType;
        output.DataFormat = product.DataFormat;
        output.DeviceType = product.ProductType; // 使用产品类型
        output.GroupKey = input.GroupKey;

        // 设备密钥只有直连设备和网关设备才有，子设备为null
        if (product.ProductType == (int)DevicePropertyTypeEnum.GatewaySubDevice)
        {
            output.DeviceSecret = null;
        }
        else
        {
            // 注意：这里无法获取到实际的密钥，因为密钥不存储在设备实体中
            // 在实际应用中，可能需要从其他地方获取或者不返回密钥信息
            output.DeviceSecret = null; // 出于安全考虑，不返回密钥
        }

        return output;
    }

    #endregion
}