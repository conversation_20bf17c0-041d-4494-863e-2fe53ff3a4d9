namespace Admin.Multiplex.Contracts.Enums.Mqtt
{
    /// <summary>
    /// MQTT消息状态
    /// </summary>
    public enum MqttMessageStatusEnum
    {
        /// <summary>
        /// 等待发送
        /// </summary>
        Pending = 1,

        /// <summary>
        /// 发送中
        /// </summary>
        Sending = 2,

        /// <summary>
        /// 已发送
        /// </summary>
        Sent = 3,

        /// <summary>
        /// 已确认
        /// </summary>
        Acknowledged = 4,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 5,

        /// <summary>
        /// 已失败
        /// </summary>
        Failed = 6,

        /// <summary>
        /// 发送失败
        /// </summary>
        SendFailed = 7,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired = 8,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 9
    }
} 