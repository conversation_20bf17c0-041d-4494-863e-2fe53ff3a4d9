using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Serialization
{
    /// <summary>
    /// 消息序列化器接口
    /// </summary>
    public interface IMessageSerializer
    {
        /// <summary>
        /// 序列化器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 序列化器描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 支持的内容类型
        /// </summary>
        string ContentType { get; }

        /// <summary>
        /// 序列化器优先级（数值越小优先级越高）
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 是否支持指定的类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否支持</returns>
        bool CanSerialize(Type type);

        /// <summary>
        /// 序列化对象到字节数组
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>序列化后的字节数组</returns>
        byte[] Serialize<T>(T obj);

        /// <summary>
        /// 异步序列化对象到字节数组
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>序列化后的字节数组</returns>
        Task<byte[]> SerializeAsync<T>(T obj);

        /// <summary>
        /// 反序列化字节数组到对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="data">要反序列化的字节数组</param>
        /// <returns>反序列化后的对象</returns>
        T Deserialize<T>(byte[] data);

        /// <summary>
        /// 异步反序列化字节数组到对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="data">要反序列化的字节数组</param>
        /// <returns>反序列化后的对象</returns>
        Task<T> DeserializeAsync<T>(byte[] data);

        /// <summary>
        /// 反序列化字节数组到指定类型的对象
        /// </summary>
        /// <param name="data">要反序列化的字节数组</param>
        /// <param name="type">目标类型</param>
        /// <returns>反序列化后的对象</returns>
        object Deserialize(byte[] data, Type type);

        /// <summary>
        /// 异步反序列化字节数组到指定类型的对象
        /// </summary>
        /// <param name="data">要反序列化的字节数组</param>
        /// <param name="type">目标类型</param>
        /// <returns>反序列化后的对象</returns>
        Task<object> DeserializeAsync(byte[] data, Type type);

        /// <summary>
        /// 序列化对象到字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>序列化后的字符串</returns>
        string SerializeToString<T>(T obj);

        /// <summary>
        /// 从字符串反序列化对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="data">要反序列化的字符串</param>
        /// <returns>反序列化后的对象</returns>
        T DeserializeFromString<T>(string data);

        /// <summary>
        /// 验证数据是否可以被此序列化器反序列化
        /// </summary>
        /// <param name="data">要验证的数据</param>
        /// <returns>是否可以反序列化</returns>
        bool CanDeserialize(byte[] data);

        /// <summary>
        /// 获取序列化器的配置选项
        /// </summary>
        /// <returns>配置选项</returns>
        SerializerOptions GetOptions();

        /// <summary>
        /// 设置序列化器的配置选项
        /// </summary>
        /// <param name="options">配置选项</param>
        void SetOptions(SerializerOptions options);
    }

    /// <summary>
    /// 序列化器配置选项
    /// </summary>
    public class SerializerOptions
    {
        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool EnableCompression { get; set; } = false;

        /// <summary>
        /// 是否忽略空值
        /// </summary>
        public bool IgnoreNullValues { get; set; } = true;

        /// <summary>
        /// 是否使用驼峰命名
        /// </summary>
        public bool UseCamelCase { get; set; } = true;

        /// <summary>
        /// 是否缩进格式化
        /// </summary>
        public bool Indented { get; set; } = false;

        /// <summary>
        /// 编码格式
        /// </summary>
        public string Encoding { get; set; } = "UTF-8";

        /// <summary>
        /// 最大序列化深度
        /// </summary>
        public int MaxDepth { get; set; } = 64;

        /// <summary>
        /// 自定义属性
        /// </summary>
        public Dictionary<string, object> CustomProperties { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 序列化结果
    /// </summary>
    public class SerializationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 序列化后的数据
        /// </summary>
        public byte[] Data { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 序列化耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public int DataSize => Data?.Length ?? 0;

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="data">序列化数据</param>
        /// <param name="elapsedMs">耗时</param>
        /// <returns>成功结果</returns>
        public static SerializationResult Success(byte[] data, long elapsedMs = 0)
        {
            return new SerializationResult
            {
                IsSuccess = true,
                Data = data,
                ElapsedMilliseconds = elapsedMs
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="exception">异常</param>
        /// <returns>失败结果</returns>
        public static SerializationResult Failure(string errorMessage, Exception exception = null)
        {
            return new SerializationResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Exception = exception
            };
        }
    }

    /// <summary>
    /// 反序列化结果
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    public class DeserializationResult<T>
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 反序列化后的对象
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 反序列化耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="data">反序列化对象</param>
        /// <param name="elapsedMs">耗时</param>
        /// <returns>成功结果</returns>
        public static DeserializationResult<T> Success(T data, long elapsedMs = 0)
        {
            return new DeserializationResult<T>
            {
                IsSuccess = true,
                Data = data,
                ElapsedMilliseconds = elapsedMs
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="exception">异常</param>
        /// <returns>失败结果</returns>
        public static DeserializationResult<T> Failure(string errorMessage, Exception exception = null)
        {
            return new DeserializationResult<T>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Exception = exception
            };
        }
    }
} 