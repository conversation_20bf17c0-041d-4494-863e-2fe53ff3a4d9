[{"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController", "Method": "Get", "RelativePath": "api/abp/api-definition", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "IncludeTypes", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController", "Method": "GetAsync", "RelativePath": "api/abp/application-configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "IncludeLocalizationResources", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController", "Method": "GetAsync", "RelativePath": "api/abp/application-localization", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CultureName", "Type": "System.String", "IsRequired": false}, {"Name": "OnlyDynamics", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AlarmServices.AlarmEventService", "Method": "SyncSystemAlarmEventsAsync", "RelativePath": "api/v1/alarm-event/sync-system-alarm-events", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.AlarmServices.AlarmEventSyncResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AlarmServices.AlarmHistoryService", "Method": "ConfirmAlarmsAsync", "RelativePath": "api/v1/alarm-history/confirm-alarms", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.AlarmServices.Dto.ConfirmAlarmInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AlarmServices.AlarmHistoryService", "Method": "GetConfirmedAlarmsAsync", "RelativePath": "api/v1/alarm-history/confirmed-alarms", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeviceName", "Type": "System.String", "IsRequired": false}, {"Name": "AlarmType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AlarmLevel", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AlarmStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsReleased", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AlarmServices.AlarmHistoryService", "Method": "GetPendingConfirmationAlarmsAsync", "RelativePath": "api/v1/alarm-history/pending-confirmation-alarms", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeviceName", "Type": "System.String", "IsRequired": false}, {"Name": "AlarmType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AlarmLevel", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AlarmStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsReleased", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AlarmServices.AlarmHistoryService", "Method": "GetUnconfirmedButReleasedAlarmsAsync", "RelativePath": "api/v1/alarm-history/unconfirmed-but-released-alarms", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeviceName", "Type": "System.String", "IsRequired": false}, {"Name": "AlarmType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AlarmLevel", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AlarmStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsReleased", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.AlarmServices.Dto.AlarmHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "BindUserAsync", "RelativePath": "api/v1/auth/bind-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.AuthServices.Dtos.BindUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "GetCallbackAsync", "RelativePath": "api/v1/auth/callback", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "State", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "GetFunctionsAsync", "RelativePath": "api/v1/auth/functions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "LoginAsync", "RelativePath": "api/v1/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.AuthServices.Dtos.LoginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.AuthServices.Dtos.LoginOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "GetOrganizationTreeAsync", "RelativePath": "api/v1/auth/organization-tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "RegisterUserAsync", "RelativePath": "api/v1/auth/register-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.AuthServices.Dtos.RegisterUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "GetSystemPlatformInfoAsync", "RelativePath": "api/v1/auth/system-platform-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.AuthServices.Dtos.GetSystemPlatformInfoOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "GetUnreadNoticeAsync", "RelativePath": "api/v1/auth/unread-notice", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Multiplex.Contracts.IAdminUser.Models.NoticeItemModel, Admin.Multiplex.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "GetUserInfoAsync", "RelativePath": "api/v1/auth/user-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "password", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.AuthServices.Dtos.GetUserInfoOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "PutUserInfoAsync", "RelativePath": "api/v1/auth/user-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.AuthServices.Dtos.PutUserInfoInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.AuthServices.AuthService", "Method": "GetVbenUserInfoAsync", "RelativePath": "api/v1/auth/vben-user-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "AddAsync", "RelativePath": "api/v1/control-cmd", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ControlCmdServices.Dto.AddControlCmdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ControlCmdServices.Dto.ControlCmdOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "UpdateAsync", "RelativePath": "api/v1/control-cmd", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ControlCmdServices.Dto.UpdateControlCmdInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ControlCmdServices.Dto.ControlCmdOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "ExecuteCommandByIdAsync", "RelativePath": "api/v1/control-cmd/{commandId}/execute-command-by-id", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandId", "Type": "System.Int64", "IsRequired": true}, {"Name": "parameters", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}, {"Name": "source", "Type": "System.String", "IsRequired": false}, {"Name": "username", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Communication.Control.Models.CommandExecuteResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "GetByDeviceIdAsync", "RelativePath": "api/v1/control-cmd/{deviceId}/by-device-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ControlCmdServices.Dto.ControlCmdOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "DeleteAsync", "RelativePath": "api/v1/control-cmd/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "GetByIdAsync", "RelativePath": "api/v1/control-cmd/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ControlCmdServices.Dto.ControlCmdOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "BatchExecuteCommandAsync", "RelativePath": "api/v1/control-cmd/batch-execute-command", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Communication.Control.Models.BatchExecuteCommandRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Communication.Control.Models.BatchExecuteResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "ExecuteCommandAsync", "RelativePath": "api/v1/control-cmd/execute-command", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Communication.Control.Models.ExecuteCommandRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Communication.Control.Models.CommandExecuteResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlCmdService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/control-cmd/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CmdName", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CommunicationMedium", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Encode", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsAppControl", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlCmdOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlPlanService", "Method": "AddAsync", "RelativePath": "api/v1/control-plan", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ControlCmdServices.Dto.AddControlPlanInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ControlCmdServices.Dto.ControlPlanOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlPlanService", "Method": "UpdateAsync", "RelativePath": "api/v1/control-plan", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ControlCmdServices.Dto.UpdateControlPlanInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ControlCmdServices.Dto.ControlPlanOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlPlanService", "Method": "DeleteAsync", "RelativePath": "api/v1/control-plan/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlPlanService", "Method": "GetByIdAsync", "RelativePath": "api/v1/control-plan/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ControlCmdServices.Dto.ControlPlanOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ControlCmdServices.ControlPlanService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/control-plan/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "TriggerType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsAppControl", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.ControlCmdServices.Dto.ControlPlanOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.DefinitionService", "Method": "AddAsync", "RelativePath": "api/v1/definition", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Workflow.Services.WfDefiniationDtos.AddWfDefinitionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.DefinitionService", "Method": "GetAsync", "RelativePath": "api/v1/definition/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.DefinitionService", "Method": "PutAsync", "RelativePath": "api/v1/definition/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Workflow.Services.WfDefiniationDtos.PutWfDefinitionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.DefinitionService", "Method": "DeleteAsync", "RelativePath": "api/v1/definition/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.DefinitionService", "Method": "LockAsync", "RelativePath": "api/v1/definition/{id}/lock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.DefinitionService", "Method": "GetDefinitionsAsync", "RelativePath": "api/v1/definition/definitions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.DefinitionService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/definition/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Version", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsLocked", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.WfDefiniationDtos.WfDefinitionOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "AddAsync", "RelativePath": "api/v1/device", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.AddDeviceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceDataService", "Method": "DeleteDeviceLatestDataAsync", "RelativePath": "api/v1/device-data/{deviceId}/device-latest-data", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceDataService", "Method": "DeleteHistoryDataAsync", "RelativePath": "api/v1/device-data/{deviceId}/history-data", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}, {"Name": "beforeDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceDataService", "Method": "IsDeviceOnlineAsync", "RelativePath": "api/v1/device-data/{deviceId}/is-device-online", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}, {"Name": "timeoutMinutes", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceDataService", "Method": "BatchDeleteHistoryDataAsync", "RelativePath": "api/v1/device-data/batch-delete-history-data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "beforeDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceDataService", "Method": "GetHistoryDataAsync", "RelativePath": "api/v1/device-data/history-data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceId", "Type": "System.Int64", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceDataService", "Method": "GetOfflineDevicesAsync", "RelativePath": "api/v1/device-data/offline-devices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeoutMinutes", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "AddAsync", "RelativePath": "api/v1/device-instruction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceInstructionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "GetByDeviceIdAsync", "RelativePath": "api/v1/device-instruction/{deviceId}/by-device-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.DeviceServices.Dto.DeviceInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "GetSimpleListAsync", "RelativePath": "api/v1/device-instruction/{deviceId}/simple-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}, {"Name": "enabledOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "UpdateAsync", "RelativePath": "api/v1/device-instruction/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceInstructionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "DeleteAsync", "RelativePath": "api/v1/device-instruction/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "GetByIdAsync", "RelativePath": "api/v1/device-instruction/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceInstructionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "SetEnabledAsync", "RelativePath": "api/v1/device-instruction/{id}/set-enabled", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "isEnabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/device-instruction/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceInstructionService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/device-instruction/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "InstructionName", "Type": "System.String", "IsRequired": false}, {"Name": "IsEnabled", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "AddAsync", "RelativePath": "api/v1/device-para", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.AddDeviceParaInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceParaOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "UpdateAsync", "RelativePath": "api/v1/device-para", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceParaOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "DeleteAsync", "RelativePath": "api/v1/device-para", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "parameterId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "GetByDeviceIdAsync", "RelativePath": "api/v1/device-para/{deviceId}/by-device-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.DeviceServices.Dto.DeviceParaOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "GetSimpleListByDeviceIdAsync", "RelativePath": "api/v1/device-para/{deviceId}/simple-list-by-device-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "GetByIdAsync", "RelativePath": "api/v1/device-para/{parameterId}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parameterId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceParaOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/device-para/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "QueryAsync", "RelativePath": "api/v1/device-para/query", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.DeviceParaQueryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceParaOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceParaService", "Method": "SetMonitorStatusAsync", "RelativePath": "api/v1/device-para/set-monitor-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceTopicConfigService", "Method": "AddAsync", "RelativePath": "api/v1/device-topic-config", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceTopicConfigService", "Method": "GetByDeviceIdAsync", "RelativePath": "api/v1/device-topic-config/{deviceId}/by-device-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceTopicConfigService", "Method": "DeleteByDeviceIdAsync", "RelativePath": "api/v1/device-topic-config/{deviceId}/by-device-id", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceTopicConfigService", "Method": "DeleteAsync", "RelativePath": "api/v1/device-topic-config/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceTopicConfigService", "Method": "GetByIdAsync", "RelativePath": "api/v1/device-topic-config/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceTopicConfigService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/device-topic-config/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceTopicConfigService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/device-topic-config/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceId", "Type": "System.String", "IsRequired": false}, {"Name": "TemplateName", "Type": "System.String", "IsRequired": false}, {"Name": "Topic", "Type": "System.String", "IsRequired": false}, {"Name": "AccessType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "UpdateAsync", "RelativePath": "api/v1/device/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.UpdateDeviceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "DeleteAsync", "RelativePath": "api/v1/device/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "GetByIdAsync", "RelativePath": "api/v1/device/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DeviceServices.Dto.DeviceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "DisableAsync", "RelativePath": "api/v1/device/{id}/disable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "EnableAsync", "RelativePath": "api/v1/device/{id}/enable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "ResetSecretAsync", "RelativePath": "api/v1/device/{id}/reset-secret", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DeviceServices.DeviceService", "Method": "QueryAsync", "RelativePath": "api/v1/device/query", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DeviceServices.Dto.DeviceQueryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.DeviceServices.Dto.DeviceOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictCategoryServices.DictCategoryService", "Method": "AddAsync", "RelativePath": "api/v1/dict-category", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictCategoryServices.DictCategoryService", "Method": "GetAsync", "RelativePath": "api/v1/dict-category/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictCategoryServices.DictCategoryService", "Method": "PutAsync", "RelativePath": "api/v1/dict-category/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictCategoryServices.DictCategoryService", "Method": "DeleteAsync", "RelativePath": "api/v1/dict-category/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictCategoryServices.DictCategoryService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/dict-category/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictDataServices.DictDataService", "Method": "GetListAsync", "RelativePath": "api/v1/dict-data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.DictDataServices.Dtos.DictDataOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictDataServices.DictDataService", "Method": "AddAsync", "RelativePath": "api/v1/dict-data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.DictDataServices.Dtos.AddDictDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictDataServices.DictDataService", "Method": "GetAsync", "RelativePath": "api/v1/dict-data/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.DictDataServices.Dtos.DictDataOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictDataServices.DictDataService", "Method": "PutAsync", "RelativePath": "api/v1/dict-data/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.DictDataServices.Dtos.AddDictDataInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictDataServices.DictDataService", "Method": "DeleteAsync", "RelativePath": "api/v1/dict-data/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.DictDataServices.DictDataService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/dict-data/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Int64", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.DictDataServices.Dtos.DictDataOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "AddAsync", "RelativePath": "api/v1/function", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.FunctionServices.Dtos.AddFunctionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "GetInterfacesAsync", "RelativePath": "api/v1/function/{functionId}/interfaces", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "functionId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "GetAsync", "RelativePath": "api/v1/function/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.FunctionServices.Dtos.FunctionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "PutAsync", "RelativePath": "api/v1/function/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.FunctionServices.Dtos.AddFunctionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "DeleteAsync", "RelativePath": "api/v1/function/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "DeleteFunctionInterfaceAsync", "RelativePath": "api/v1/function/{id}/function-interface", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "AssignInterfaceAsync", "RelativePath": "api/v1/function/assign-interface", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.FunctionServices.Dtos.AssignInterfaceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/function/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.FunctionServices.Dtos.FunctionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.FunctionServices.FunctionService", "Method": "GetTreeAsync", "RelativePath": "api/v1/function/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.FunctionServices.Dtos.FunctionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.InstanceService", "Method": "AuditingAsync", "RelativePath": "api/v1/instance/{id}/auditing", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Workflow.Services.InstanceDtos.AuditingInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.InstanceService", "Method": "GetInstanceDetailAsync", "RelativePath": "api/v1/instance/{id}/instance-detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Workflow.Services.InstanceDtos.InstanceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.InstanceService", "Method": "StartAsync", "RelativePath": "api/v1/instance/{id}/start", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "data", "Type": "Admin.Workflow.DataTypes.GeneralAuditingDefinition", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.InstanceService", "Method": "TerminateAsync", "RelativePath": "api/v1/instance/{id}/terminate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.InstanceService", "Method": "GetAuditingPagedListAsync", "RelativePath": "api/v1/instance/auditing-paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.WaitingAuditingOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Workflow.Services.InstanceService", "Method": "GetSelfPagedListAsync", "RelativePath": "api/v1/instance/self-paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "WorkflowStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Workflow.Services.InstanceDtos.InstanceOutput, Admin.Workflow, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.InterfaceServices.InterfaceService", "Method": "AsyncApi", "RelativePath": "api/v1/interface/async-api", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.InterfaceServices.InterfaceService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/interface/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "GroupName", "Type": "System.String", "IsRequired": false}, {"Name": "Path", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusInstructionManagementService", "Method": "GetDeviceInstructionsAsync", "RelativePath": "api/v1/modbus-instruction-management/{deviceId}/device-instructions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ModbusServices.ModbusInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusInstructionManagementService", "Method": "SetDeviceInstructionsEnabledAsync", "RelativePath": "api/v1/modbus-instruction-management/{deviceId}/set-device-instructions-enabled", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}, {"Name": "enabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusInstructionManagementService", "Method": "SetInstructionEnabledAsync", "RelativePath": "api/v1/modbus-instruction-management/{instructionId}/set-instruction-enabled", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "instructionId", "Type": "System.Int64", "IsRequired": true}, {"Name": "enabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusInstructionManagementService", "Method": "UpdateInstructionAsync", "RelativePath": "api/v1/modbus-instruction-management/instruction", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ModbusServices.UpdateModbusInstructionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ModbusServices.ModbusInstructionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusInstructionManagementService", "Method": "GetModbusDevicesAsync", "RelativePath": "api/v1/modbus-instruction-management/modbus-devices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ModbusServices.ModbusDeviceOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusTestService", "Method": "GetDeviceInstructionsForTestAsync", "RelativePath": "api/v1/modbus-test/{deviceId}/device-instructions-for-test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ModbusServices.DeviceInstructionTestOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusTestService", "Method": "GenerateModbusCommand", "RelativePath": "api/v1/modbus-test/generate-modbus-command", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ModbusServices.ModbusCommandInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ModbusServices.ModbusCommandResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusTestService", "Method": "SendTestCommandAsync", "RelativePath": "api/v1/modbus-test/send-test-command", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ModbusServices.ModbusTestInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ModbusServices.ModbusTestResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ModbusServices.ModbusTestService", "Method": "SimulateDeviceResponseAsync", "RelativePath": "api/v1/modbus-test/simulate-device-response", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ModbusServices.ModbusResponseInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ModbusServices.ModbusTestResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "CreateAsync", "RelativePath": "api/v1/model-instruction", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.ModelInstructionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProductServices.Dto.ModelInstructionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "UpdateAsync", "RelativePath": "api/v1/model-instruction", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.UpdateModelInstructionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "DeleteAsync", "RelativePath": "api/v1/model-instruction/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "GetByIdAsync", "RelativePath": "api/v1/model-instruction/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProductServices.Dto.ModelInstructionOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "SetEnabledAsync", "RelativePath": "api/v1/model-instruction/{id}/set-enabled", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "isEnabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "GetByModelIdAsync", "RelativePath": "api/v1/model-instruction/{modelId}/by-model-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/model-instruction/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/model-instruction/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ModelId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "InstructionName", "Type": "System.String", "IsRequired": false}, {"Name": "IsEnabled", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelInstructionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelInstructionService", "Method": "GetSimpleListAsync", "RelativePath": "api/v1/model-instruction/simple-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "AddAsync", "RelativePath": "api/v1/model-property", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.ModelPropertyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "UpdateAsync", "RelativePath": "api/v1/model-property", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.UpdateModelPropertyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "DeleteAsync", "RelativePath": "api/v1/model-property/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetByIdAsync", "RelativePath": "api/v1/model-property/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProductServices.Dto.ModelPropertyOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetProductPropertyByIdAsync", "RelativePath": "api/v1/model-property/{id}/product-property-by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProductServices.Dto.ProductPropertyOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetByInstructionIdAsync", "RelativePath": "api/v1/model-property/{instructionId}/by-instruction-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "instructionId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ModelPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "DeleteByInstructionIdAsync", "RelativePath": "api/v1/model-property/{instructionId}/by-instruction-id", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "instructionId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetByModelIdAsync", "RelativePath": "api/v1/model-property/{modelId}/by-model-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ModelPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "DeleteByModelIdAsync", "RelativePath": "api/v1/model-property/{modelId}/by-model-id", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetProductPropertiesByModelIdAsync", "RelativePath": "api/v1/model-property/{modelId}/product-properties-by-model-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetProductPropertySimpleByModelIdAsync", "RelativePath": "api/v1/model-property/{modelId}/product-property-simple-by-model-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetSimpleByModelIdAsync", "RelativePath": "api/v1/model-property/{modelId}/simple-by-model-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "modelId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "BatchAddAsync", "RelativePath": "api/v1/model-property/batch-add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/model-property/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/model-property/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ModelId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "InstructionId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Key", "Type": "System.String", "IsRequired": false}, {"Name": "DataType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MonitorStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsSave", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ModelPropertyOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetProductPropertySimpleListAsync", "RelativePath": "api/v1/model-property/product-property-simple-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ModelPropertyService", "Method": "GetSimpleListAsync", "RelativePath": "api/v1/model-property/simple-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "AddAsync", "RelativePath": "api/v1/mqtt-acl-management", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "GetAsync", "RelativePath": "api/v1/mqtt-acl-management/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.AclRuleOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "PutAsync", "RelativePath": "api/v1/mqtt-acl-management/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "DeleteAsync", "RelativePath": "api/v1/mqtt-acl-management/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "ToggleAsync", "RelativePath": "api/v1/mqtt-acl-management/{id}/toggle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "isActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "UpdateDeviceAclRulesAsync", "RelativePath": "api/v1/mqtt-acl-management/{oldDeviceId}/device-acl-rules", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "oldDeviceId", "Type": "System.String", "IsRequired": true}, {"Name": "newDeviceInfo", "Type": "Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/mqtt-acl-management/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "CreateDeviceAclRulesAsync", "RelativePath": "api/v1/mqtt-acl-management/device-acl-rules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "DeleteDeviceAclRulesAsync", "RelativePath": "api/v1/mqtt-acl-management/device-acl-rules", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceId", "Type": "System.String", "IsRequired": false}, {"Name": "DeleteAllRelated", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "ExportAsync", "RelativePath": "api/v1/mqtt-acl-management/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.MqttBrokerServices.Dto.AclRuleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "ImportAsync", "RelativePath": "api/v1/mqtt-acl-management/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "rules", "Type": "System.Collections.Generic.List`1[[Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.ImportResultOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/mqtt-acl-management/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RuleName", "Type": "System.String", "IsRequired": false}, {"Name": "AccessType", "Type": "System.String", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "ClientId", "Type": "System.String", "IsRequired": false}, {"Name": "Topic", "Type": "System.String", "IsRequired": false}, {"Name": "Permission", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.AclRuleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "QueryDeviceAclRulesAsync", "RelativePath": "api/v1/mqtt-acl-management/query-device-acl-rules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.MqttBrokerServices.Dto.AclRuleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttAclManagementService", "Method": "TestPermissionAsync", "RelativePath": "api/v1/mqtt-acl-management/test-permission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.TestPermissionInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttBrokerManagementService", "Method": "GetConfigurationAsync", "RelativePath": "api/v1/mqtt-broker-management/configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttBrokerManagementService", "Method": "UpdateConfigurationAsync", "RelativePath": "api/v1/mqtt-broker-management/configuration", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttBrokerManagementService", "Method": "RestartAsync", "RelativePath": "api/v1/mqtt-broker-management/restart", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttBrokerManagementService", "Method": "StartAsync", "RelativePath": "api/v1/mqtt-broker-management/start", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.StartBrokerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttBrokerManagementService", "Method": "GetStatusAsync", "RelativePath": "api/v1/mqtt-broker-management/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttBrokerManagementService", "Method": "StopAsync", "RelativePath": "api/v1/mqtt-broker-management/stop", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttConnectionManagementService", "Method": "GetConnectionDetailAsync", "RelativePath": "api/v1/mqtt-connection-management/{clientId}/connection-detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttConnectionManagementService", "Method": "DisconnectConnectionAsync", "RelativePath": "api/v1/mqtt-connection-management/{clientId}/disconnect-connection", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttConnectionManagementService", "Method": "BatchDisconnectAsync", "RelativePath": "api/v1/mqtt-connection-management/batch-disconnect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttConnectionManagementService", "Method": "GetConnectionsAsync", "RelativePath": "api/v1/mqtt-connection-management/connections", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ClientId", "Type": "System.String", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttMessagePublishService", "Method": "BatchPublishAsync", "RelativePath": "api/v1/mqtt-message-publish/batch-publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttMessagePublishService", "Method": "ClearRetainedMessageAsync", "RelativePath": "api/v1/mqtt-message-publish/clear-retained-message", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttMessagePublishService", "Method": "PublishMessageAsync", "RelativePath": "api/v1/mqtt-message-publish/publish-message", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.MessagePublishResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttMessagePublishService", "Method": "GetRetainedMessagesAsync", "RelativePath": "api/v1/mqtt-message-publish/retained-messages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TopicPattern", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PublisherId", "Type": "System.String", "IsRequired": false}, {"Name": "Qos", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttMessagePublishService", "Method": "GetTopicSubscriberCount", "RelativePath": "api/v1/mqtt-message-publish/topic-subscriber-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "topic", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttSessionManagementService", "Method": "GetSessionByClientIdAsync", "RelativePath": "api/v1/mqtt-session-management/{clientId}/session-by-client-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttSessionManagementService", "Method": "CleanupSessionAsync", "RelativePath": "api/v1/mqtt-session-management/{sessionId}/cleanup-session", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttSessionManagementService", "Method": "GetSessionDetailAsync", "RelativePath": "api/v1/mqtt-session-management/{sessionId}/session-detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttSessionManagementService", "Method": "UpdateSessionStateAsync", "RelativePath": "api/v1/mqtt-session-management/{sessionId}/session-state", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttSessionManagementService", "Method": "BatchSessionOperationAsync", "RelativePath": "api/v1/mqtt-session-management/batch-session-operation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttSessionManagementService", "Method": "CleanupExpiredSessionsAsync", "RelativePath": "api/v1/mqtt-session-management/cleanup-expired-sessions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttSessionManagementService", "Method": "GetSessionsAsync", "RelativePath": "api/v1/mqtt-session-management/sessions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ClientId", "Type": "System.String", "IsRequired": false}, {"Name": "State", "Type": "System.String", "IsRequired": false}, {"Name": "Persistent", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Expired", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "GetConnectionStatisticsAsync", "RelativePath": "api/v1/mqtt-statistics/connection-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "GroupBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "ExportStatisticsReportAsync", "RelativePath": "api/v1/mqtt-statistics/export-statistics-report", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "GetHistoricalStatisticsAsync", "RelativePath": "api/v1/mqtt-statistics/historical-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartTime", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndTime", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Interval", "Type": "System.TimeSpan", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "GetMessageStatisticsAsync", "RelativePath": "api/v1/mqtt-statistics/message-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "GroupBy", "Type": "System.String", "IsRequired": false}, {"Name": "TopTopicsCount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "GetOverviewAsync", "RelativePath": "api/v1/mqtt-statistics/overview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "GetPerformanceMetricsAsync", "RelativePath": "api/v1/mqtt-statistics/performance-metrics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Interval", "Type": "System.Nullable`1[[System.TimeSpan, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "GetSessionStatisticsAsync", "RelativePath": "api/v1/mqtt-statistics/session-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsRequest", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttStatisticsService", "Method": "GetTopicAnalysisAsync", "RelativePath": "api/v1/mqtt-statistics/topic-analysis", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TopTopicsCount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TopicFilter", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.StatisticsResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "AddAsync", "RelativePath": "api/v1/mqtt-user-management", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "UpdateAsync", "RelativePath": "api/v1/mqtt-user-management", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "DeleteAsync", "RelativePath": "api/v1/mqtt-user-management", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "GetAsync", "RelativePath": "api/v1/mqtt-user-management", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Application.MqttBrokerServices.Dto.MqttUserOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "DeleteDeviceUserAsync", "RelativePath": "api/v1/mqtt-user-management/{deviceId}/device-user", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "CreateDeviceUserAsync", "RelativePath": "api/v1/mqtt-user-management/device-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "QueryAsync", "RelativePath": "api/v1/mqtt-user-management/query", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.MqttUserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "ResetPasswordAsync", "RelativePath": "api/v1/mqtt-user-management/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "newPassword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.MqttBrokerServices.MqttUserManagementService", "Method": "ToggleAsync", "RelativePath": "api/v1/mqtt-user-management/toggle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "isEnabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.NoticeServices.NoticeService", "Method": "AddAsync", "RelativePath": "api/v1/notice", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.NoticeServices.Dtos.AddNoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.NoticeServices.NoticeService", "Method": "GetAsync", "RelativePath": "api/v1/notice/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.NoticeServices.Dtos.NoticeOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.NoticeServices.NoticeService", "Method": "PutAsync", "RelativePath": "api/v1/notice/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.NoticeServices.Dtos.PutNoticeInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.NoticeServices.NoticeService", "Method": "DeleteAsync", "RelativePath": "api/v1/notice/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.NoticeServices.NoticeService", "Method": "SendAsync", "RelativePath": "api/v1/notice/{id}/send", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "userIds", "Type": "System.Int64[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.NoticeServices.NoticeService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/notice/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "NoticeType", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Level", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.NoticeServices.Dtos.NoticeOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.OrganizationServices.OrganizationService", "Method": "AddAsync", "RelativePath": "api/v1/organization", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.OrganizationServices.Dtos.AddOrganizationInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.OrganizationServices.OrganizationService", "Method": "GetAsync", "RelativePath": "api/v1/organization/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.OrganizationServices.Dtos.OrganizationOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.OrganizationServices.OrganizationService", "Method": "PutAsync", "RelativePath": "api/v1/organization/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.OrganizationServices.Dtos.AddOrganizationInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.OrganizationServices.OrganizationService", "Method": "DeleteAsync", "RelativePath": "api/v1/organization/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.OrganizationServices.OrganizationService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/organization/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.OrganizationServices.Dtos.OrganizationOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "AddAsync", "RelativePath": "api/v1/product", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.ProductInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "UpdateAsync", "RelativePath": "api/v1/product", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.UpdateProductInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "AddAsync", "RelativePath": "api/v1/product-model", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.ProductModelInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "UpdateAsync", "RelativePath": "api/v1/product-model", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.ProductServices.Dto.UpdateProductModelInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "DeleteAsync", "RelativePath": "api/v1/product-model/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "GetByIdAsync", "RelativePath": "api/v1/product-model/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProductServices.Dto.ProductModelOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "GetDetailByIdAsync", "RelativePath": "api/v1/product-model/{id}/detail-by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProductServices.Dto.ProductModelDetailOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "SetEnabledAsync", "RelativePath": "api/v1/product-model/{id}/set-enabled", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "isEnabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "GetByProductIdAsync", "RelativePath": "api/v1/product-model/{productId}/by-product-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductModelSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/product-model/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "GetByDeviceGroupAsync", "RelativePath": "api/v1/product-model/by-device-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceGroup", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductModelSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/product-model/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ProductId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ModelName", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceGroup", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsEnabled", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductModelOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductModelService", "Method": "GetSimpleListAsync", "RelativePath": "api/v1/product-model/simple-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductModelSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "DeleteAsync", "RelativePath": "api/v1/product/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "GetByIdAsync", "RelativePath": "api/v1/product/{id}/by-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProductServices.Dto.ProductOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "BatchDeleteAsync", "RelativePath": "api/v1/product/batch-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "GetByProductTypeAsync", "RelativePath": "api/v1/product/by-product-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productType", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "GetByProtocolTypeAsync", "RelativePath": "api/v1/product/by-protocol-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "protocolType", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/product/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ProductName", "Type": "System.String", "IsRequired": false}, {"Name": "ProtocolType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProductType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DataFormat", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.ProductServices.Dto.ProductOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProductServices.ProductService", "Method": "GetSimpleListAsync", "RelativePath": "api/v1/product/simple-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.ProductServices.Dto.ProductSimpleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProfileSystemServices.ProfileSystemService", "Method": "AddAsync", "RelativePath": "api/v1/profile-system", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProfileSystemServices.ProfileSystemService", "Method": "DownloadAsync", "RelativePath": "api/v1/profile-system/{fileId}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Admin.Application.ProfileSystemServices.ProfileSystemService", "Method": "GetAsync", "RelativePath": "api/v1/profile-system/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProfileSystemServices.ProfileSystemService", "Method": "DeleteAsync", "RelativePath": "api/v1/profile-system/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.ProfileSystemServices.ProfileSystemService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/profile-system/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RequestLogServices.RequestLogService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/request-log/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RequestDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ControllerName", "Type": "System.String", "IsRequired": false}, {"Name": "ActionName", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.RequestLogServices.Dtos.RequestLogOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RequestLogServices.RequestLogService", "Method": "GetRequestLogChartAsync", "RelativePath": "api/v1/request-log/request-log-chart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.Core.Echarts.ChartModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "AddAsync", "RelativePath": "api/v1/role", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.RoleServices.Dtos.AddRoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "GetAsync", "RelativePath": "api/v1/role/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.RoleServices.Dtos.RoleOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "PutAsync", "RelativePath": "api/v1/role/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.RoleServices.Dtos.AddRoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "DeleteAsync", "RelativePath": "api/v1/role/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "AssignFunctionAsync", "RelativePath": "api/v1/role/{roleId}/assign-function", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "System.Int64[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "GetFunctionsAsync", "RelativePath": "api/v1/role/{roleId}/functions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.RoleServices.Dtos.FunctionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/role/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.RoleServices.Dtos.RoleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.RoleServices.RoleService", "Method": "GetRolesAsync", "RelativePath": "api/v1/role/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Admin.Application.RoleServices.Dtos.RoleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.SystemConfigServices.SystemConfigService", "Method": "AddAsync", "RelativePath": "api/v1/system-config", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.SystemConfigServices.SystemConfigService", "Method": "GetAsync", "RelativePath": "api/v1/system-config/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.SystemConfigServices.SystemConfigService", "Method": "PutAsync", "RelativePath": "api/v1/system-config/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.SystemConfigServices.SystemConfigService", "Method": "DeleteAsync", "RelativePath": "api/v1/system-config/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.SystemConfigServices.SystemConfigService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/system-config/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigCode", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "AddAsync", "RelativePath": "api/v1/user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Admin.Application.UserServices.Dtos.AddUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "GetAsync", "RelativePath": "api/v1/user/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "Admin.Application.UserServices.Dtos.UserOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "PutAsync", "RelativePath": "api/v1/user/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "input", "Type": "Admin.Application.UserServices.Dtos.PutUserInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "DeleteAsync", "RelativePath": "api/v1/user/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "NormalAsync", "RelativePath": "api/v1/user/{id}/normal", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "ResetPasswordAsync", "RelativePath": "api/v1/user/{id}/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "StopAsync", "RelativePath": "api/v1/user/{id}/stop", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Admin.Application.UserServices.UserService", "Method": "GetPagedListAsync", "RelativePath": "api/v1/user/paged-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Account", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Telephone", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Admin.SqlSugar.PagedList`1[[Admin.Application.UserServices.Dtos.UserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]