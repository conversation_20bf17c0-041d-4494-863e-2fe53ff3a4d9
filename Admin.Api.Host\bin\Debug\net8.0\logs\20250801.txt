[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[15:23:24] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[15:23:24] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[15:23:24] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[15:23:24] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[15:23:24] [INF] Admin.Communication.DeviceOnline.Workers.DeviceOnlineDetectionWorker 
设备在线检测工作器初始化完成: CheckInterval=30s

[15:23:24] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[15:23:24] [INF] Admin.Communication.AdminCommunicationModule 
MQTT连接事件处理器注册成功

[15:23:25] [INF]  
项目当前环境为：Development

[15:23:25] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[15:23:25] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[15:23:25] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[15:23:25] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[15:23:25] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[15:23:25] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[15:23:25] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[15:23:27] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[15:23:27] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:35477

[15:23:27] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=123123

[15:23:27] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[15:23:38] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[15:23:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=7b54a3385d2f4c43bf9358d89052f384

[15:23:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:57546

[15:23:38] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[15:23:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[15:23:39] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
从数据库恢复持久会话: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, SessionId=e1d0e5977dae4c4c87c63b2f5c28c306

[15:24:02] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
收到未知的PUBREC消息: ClientId=123123, MessageId=39237

[15:24:05] [ERR]  
INSERT INTO `mqtt_pending_messages`  
           (`Id`,`MessageId`,`ClientId`,`Topic`,`Payload`,`Qos`,`Retain`,`Duplicate`,`PacketId`,`MessageType`,`Priority`,`CreatedTime`,`ScheduleTime`,`ExpiryTime`,`RetryCount`,`MaxRetryCount`,`LastRetryTime`,`Status`,`ErrorMessage`,`PublisherId`,`AckStatus`,`AckTime`)
     VALUES
           (@Id,@MessageId,@ClientId,@Topic,@Payload,@Qos,@Retain,@Duplicate,@PacketId,@MessageType,@Priority,@CreatedTime,@ScheduleTime,@ExpiryTime,@RetryCount,@MaxRetryCount,@LastRetryTime,@Status,@ErrorMessage,@PublisherId,@AckStatus,@AckTime) ;

[15:24:05] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存QoS 2临时消息时发生错误: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0' for key 'PRIMARY'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveQos2ReceivedMessagesAsync(String clientId, IReadOnlyDictionary`2 qos2Messages) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 2021

[15:24:05] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
收到未知的PUBREC消息: ClientId=123123, MessageId=9092

[15:24:53] [ERR]  
INSERT INTO `mqtt_pending_messages`  
           (`Id`,`MessageId`,`ClientId`,`Topic`,`Payload`,`Qos`,`Retain`,`Duplicate`,`PacketId`,`MessageType`,`Priority`,`CreatedTime`,`ScheduleTime`,`ExpiryTime`,`RetryCount`,`MaxRetryCount`,`LastRetryTime`,`Status`,`ErrorMessage`,`PublisherId`,`AckStatus`,`AckTime`)
     VALUES
           (@Id,@MessageId,@ClientId,@Topic,@Payload,@Qos,@Retain,@Duplicate,@PacketId,@MessageType,@Priority,@CreatedTime,@ScheduleTime,@ExpiryTime,@RetryCount,@MaxRetryCount,@LastRetryTime,@Status,@ErrorMessage,@PublisherId,@AckStatus,@AckTime) ;

[15:24:53] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存QoS 2临时消息时发生错误: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0' for key 'PRIMARY'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveQos2ReceivedMessagesAsync(String clientId, IReadOnlyDictionary`2 qos2Messages) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 2021

[15:24:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
收到未知的PUBREC消息: ClientId=123123, MessageId=2621

[15:24:58] [ERR]  
INSERT INTO `mqtt_pending_messages`  
           (`Id`,`MessageId`,`ClientId`,`Topic`,`Payload`,`Qos`,`Retain`,`Duplicate`,`PacketId`,`MessageType`,`Priority`,`CreatedTime`,`ScheduleTime`,`ExpiryTime`,`RetryCount`,`MaxRetryCount`,`LastRetryTime`,`Status`,`ErrorMessage`,`PublisherId`,`AckStatus`,`AckTime`)
     VALUES
           (@Id,@MessageId,@ClientId,@Topic,@Payload,@Qos,@Retain,@Duplicate,@PacketId,@MessageType,@Priority,@CreatedTime,@ScheduleTime,@ExpiryTime,@RetryCount,@MaxRetryCount,@LastRetryTime,@Status,@ErrorMessage,@PublisherId,@AckStatus,@AckTime) ;

[15:24:58] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存QoS 2临时消息时发生错误: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0' for key 'PRIMARY'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveQos2ReceivedMessagesAsync(String clientId, IReadOnlyDictionary`2 qos2Messages) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 2021

[15:24:58] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
收到未知的PUBREC消息: ClientId=123123, MessageId=6349

