// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Collections.Concurrent;

namespace Admin.Communication.Mqtt.Services;

/// <summary>
/// 控制响应等待器
/// </summary>
public class ControlResponseWaiter
{
    /// <summary>
    /// 关联ID
    /// </summary>
    public string CorrelationId { get; set; }

    /// <summary>
    /// 控制记录ID
    /// </summary>
    public long ControlRecordId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SendTime { get; set; }

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; }

    /// <summary>
    /// 完成信号
    /// </summary>
    public TaskCompletionSource<ControlResponse> CompletionSource { get; set; } = new();

    /// <summary>
    /// 是否已超时
    /// </summary>
    public bool IsTimeout => DateTime.Now.Subtract(SendTime).TotalMilliseconds > TimeoutMs;
}

/// <summary>
/// 控制响应
/// </summary>
public class ControlResponse
{
    /// <summary>
    /// 控制结果
    /// </summary>
    public string Result { get; set; }

    /// <summary>
    /// 响应内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 是否有响应内容
    /// </summary>
    public bool HasResponse => !string.IsNullOrEmpty(Content);

    public static ControlResponse Success(string? content = null)
    {
        return new ControlResponse
        {
            Result = string.IsNullOrEmpty(content) ? "链路通讯成功" : "控制成功",
            Content = content
        };
    }

    public static ControlResponse Timeout()
    {
        return new ControlResponse
        {
            Result = "链路通讯成功",
            Content = null
        };
    }

    public static ControlResponse Failed(string error)
    {
        return new ControlResponse
        {
            Result = $"控制失败: {error}",
            Content = null
        };
    }
}

/// <summary>
/// 控制响应处理器接口
/// </summary>
public interface IControlResponseHandler
{
    /// <summary>
    /// 注册等待响应
    /// </summary>
    /// <param name="waiter">响应等待器</param>
    void RegisterWaitingResponse(ControlResponseWaiter waiter);

    /// <summary>
    /// 处理收到的响应消息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="payload">响应载荷</param>
    Task HandleControlResponseAsync(long deviceId, string payload);

    /// <summary>
    /// 清理超时的等待响应
    /// </summary>
    Task CleanupTimeoutResponsesAsync();

    /// <summary>
    /// 等待响应
    /// </summary>
    /// <param name="correlationId">关联ID</param>
    /// <param name="timeoutMs">超时时间（毫秒）</param>
    /// <returns>响应结果</returns>
    Task<ControlResponse> WaitForResponseAsync(string correlationId, int timeoutMs);
}

/// <summary>
/// 控制响应处理器
/// </summary>
public class ControlResponseHandler : IControlResponseHandler, IDisposable
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<ControlResponseHandler> _logger;
    private readonly ConcurrentDictionary<string, ControlResponseWaiter> _waitingResponses = new();
    private readonly ConcurrentDictionary<long, string> _deviceCorrelationMap = new();
    private readonly Timer _cleanupTimer;

    /// <summary>
    /// 构造函数，启动定时清理
    /// </summary>
    public ControlResponseHandler(ISqlSugarClient db, ILogger<ControlResponseHandler> logger)
    {
        _db = db;
        _logger = logger;

        // 每分钟清理一次超时响应
        _cleanupTimer = new Timer(async _ => await CleanupTimeoutResponsesAsync(),
            null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        _logger.LogInformation("控制响应处理器已启动，定时清理间隔：1分钟");
    }

    /// <summary>
    /// 注册等待响应
    /// </summary>
    /// <param name="waiter">响应等待器</param>
    public void RegisterWaitingResponse(ControlResponseWaiter waiter)
    {
        _waitingResponses.TryAdd(waiter.CorrelationId, waiter);
        _deviceCorrelationMap.TryAdd(waiter.DeviceId, waiter.CorrelationId);
        
        _logger.LogDebug("注册等待响应: CorrelationId={CorrelationId}, DeviceId={DeviceId}, Timeout={TimeoutMs}ms", 
            waiter.CorrelationId, waiter.DeviceId, waiter.TimeoutMs);
    }

    /// <summary>
    /// 处理收到的响应消息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="payload">响应载荷</param>
    public async Task HandleControlResponseAsync(long deviceId, string payload)
    {
        try
        {
            _logger.LogDebug("收到控制响应: DeviceId={DeviceId}, Payload={Payload}", deviceId, payload);

            // 查找等待该设备响应的关联ID
            if (!_deviceCorrelationMap.TryGetValue(deviceId, out var correlationId))
            {
                _logger.LogWarning("未找到设备 {DeviceId} 的等待响应", deviceId);
                return;
            }

            // 查找等待响应
            if (!_waitingResponses.TryRemove(correlationId, out var waiter))
            {
                _logger.LogWarning("未找到关联ID {CorrelationId} 的等待响应", correlationId);
                return;
            }

            // 移除设备映射
            _deviceCorrelationMap.TryRemove(deviceId, out _);

            // 更新控制记录
            await UpdateControlRecordAsync(waiter.ControlRecordId, "控制成功", payload);

            // 设置完成结果
            var response = ControlResponse.Success(payload);
            waiter.CompletionSource.TrySetResult(response);

            _logger.LogInformation("控制响应处理完成: CorrelationId={CorrelationId}, DeviceId={DeviceId}", 
                correlationId, deviceId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理控制响应时发生错误: DeviceId={DeviceId}", deviceId);
        }
    }

    /// <summary>
    /// 等待响应
    /// </summary>
    /// <param name="correlationId">关联ID</param>
    /// <param name="timeoutMs">超时时间（毫秒）</param>
    /// <returns>响应结果</returns>
    public async Task<ControlResponse> WaitForResponseAsync(string correlationId, int timeoutMs)
    {
        if (!_waitingResponses.TryGetValue(correlationId, out var waiter))
        {
            return ControlResponse.Failed("未找到等待响应");
        }

        try
        {
            // 等待响应或超时
            using var cts = new CancellationTokenSource(timeoutMs);
            var response = await waiter.CompletionSource.Task.WaitAsync(cts.Token);
            return response;
        }
        catch (OperationCanceledException)
        {
            // 超时处理
            _logger.LogDebug("控制响应超时: CorrelationId={CorrelationId}, Timeout={TimeoutMs}ms", 
                correlationId, timeoutMs);

            // 清理等待响应
            _waitingResponses.TryRemove(correlationId, out _);
            _deviceCorrelationMap.TryRemove(waiter.DeviceId, out _);

            // 更新控制记录为链路通讯成功
            await UpdateControlRecordAsync(waiter.ControlRecordId, "链路通讯成功", null);

            return ControlResponse.Timeout();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "等待控制响应时发生错误: CorrelationId={CorrelationId}", correlationId);
            return ControlResponse.Failed(ex.Message);
        }
    }

    /// <summary>
    /// 清理超时的等待响应
    /// </summary>
    public async Task CleanupTimeoutResponsesAsync()
    {
        var timeoutWaiters = _waitingResponses.Values
            .Where(w => w.IsTimeout)
            .ToList();

        foreach (var waiter in timeoutWaiters)
        {
            try
            {
                _logger.LogDebug("清理超时等待响应: CorrelationId={CorrelationId}, DeviceId={DeviceId}", 
                    waiter.CorrelationId, waiter.DeviceId);

                // 移除等待响应
                _waitingResponses.TryRemove(waiter.CorrelationId, out _);
                _deviceCorrelationMap.TryRemove(waiter.DeviceId, out _);

                // 更新控制记录
                await UpdateControlRecordAsync(waiter.ControlRecordId, "链路通讯成功", null);

                // 设置超时结果
                waiter.CompletionSource.TrySetResult(ControlResponse.Timeout());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理超时等待响应时发生错误: CorrelationId={CorrelationId}", 
                    waiter.CorrelationId);
            }
        }

        if (timeoutWaiters.Count > 0)
        {
            _logger.LogInformation("清理了 {Count} 个超时的等待响应", timeoutWaiters.Count);
        }
    }

    /// <summary>
    /// 更新控制记录
    /// </summary>
    /// <param name="recordId">记录ID</param>
    /// <param name="result">控制结果</param>
    /// <param name="responseContent">响应内容</param>
    private async Task UpdateControlRecordAsync(long recordId, string result, string? responseContent)
    {
        try
        {
            await _db.Updateable<HisControlSendEntity>()
                .SetColumns(it => new HisControlSendEntity
                {
                    ControlResult = result,
                    ResponseContent = responseContent
                })
                .Where(it => it.Id == recordId)
                .ExecuteCommandAsync();

            _logger.LogDebug("更新控制记录: RecordId={RecordId}, Result={Result}", recordId, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新控制记录时发生错误: RecordId={RecordId}", recordId);
        }
    }

    /// <summary>
    /// 获取等待响应统计信息
    /// </summary>
    /// <returns>等待响应数量</returns>
    public int GetWaitingResponseCount()
    {
        return _waitingResponses.Count;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        _logger.LogInformation("控制响应处理器已释放资源");
    }
}
