// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ProductServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ProductServices;

/// <summary>
/// 产品服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class ProductService(ISqlSugarClient db, Repository<ProductEntity> productRepository) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<ProductEntity> _productRepository = productRepository;

    /// <summary>
    /// 获取产品分页列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页列表</returns>
    public async Task<PagedList<ProductOutput>> GetPagedListAsync(ProductQueryInput input)
    {
        var query = _db.Queryable<ProductEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.ProductName), p => p.ProductName.Contains(input.ProductName))
            .WhereIF(input.ProtocolType.HasValue, p => p.ProtocolType == input.ProtocolType.Value)
            .WhereIF(input.ProductType.HasValue, p => p.ProductType == input.ProductType.Value)
            .WhereIF(input.DataFormat.HasValue, p => p.DataFormat == input.DataFormat.Value)
            .OrderByDescending(p => p.CreateTime);

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        var result = pagedList.Adapt<PagedList<ProductOutput>>();

        // 映射枚举名称
        foreach (var item in result.Items)
        {
            item.ProtocolTypeName = GetProtocolTypeName(item.ProtocolType);
            item.ProductTypeName = GetProductTypeName(item.ProductType);
            item.DataFormatName = GetDataFormatName(item.DataFormat);
        }

        return result;
    }

    /// <summary>
    /// 根据ID获取产品详情
    /// </summary>
    /// <param name="id">产品ID</param>
    /// <returns>产品详情</returns>
    public async Task<ProductOutput> GetByIdAsync(long id)
    {
        var entity = await _productRepository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("产品不存在");

        var output = entity.Adapt<ProductOutput>();

        // 映射枚举名称
        output.ProtocolTypeName = GetProtocolTypeName(output.ProtocolType);
        output.ProductTypeName = GetProductTypeName(output.ProductType);
        output.DataFormatName = GetDataFormatName(output.DataFormat);

        return output;
    }

    /// <summary>
    /// 添加产品
    /// </summary>
    /// <param name="input">产品信息</param>
    /// <returns>产品ID</returns>
    public async Task<long> AddAsync(ProductInput input)
    {
        // 验证产品名称是否重复
        var exists = await _db.Queryable<ProductEntity>()
            .Where(p => p.ProductName == input.ProductName)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("产品名称已存在");

        var entity = input.Adapt<ProductEntity>();
        return await _productRepository.InsertReturnSnowflakeIdAsync(entity);
    }

    /// <summary>
    /// 更新产品
    /// 允许修改ProductName、Description
    /// </summary>
    /// <param name="input">产品信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateAsync(UpdateProductInput input)
    {
        var entity = await _productRepository.GetByIdAsync(input.Id) ??
            throw PersistdValidateException.Message("产品不存在");

        // 验证产品名称是否重复
        var exists = await _db.Queryable<ProductEntity>()
            .Where(p => p.ProductName == input.ProductName && p.Id != input.Id)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("产品名称已存在");

        entity.ProductName = input.ProductName;
        entity.Description = input.Description;

        return await _productRepository.UpdateAsync(entity);
    }

    /// <summary>
    /// 删除产品
    /// </summary>
    /// <param name="id">产品ID</param>
    /// <returns>是否成功</returns>
    [UnitOfWork]
    public async Task<bool> DeleteAsync(long id)
    {
        var entity = await _productRepository.GetByIdAsync(id) ?? 
            throw PersistdValidateException.Message("产品不存在");

        // 检查是否有关联的产品模型
        var hasModels = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.ProductId == id)
            .AnyAsync();
        
        if (hasModels)
            throw PersistdValidateException.Message("该产品下存在产品模型，无法删除");

        return await _productRepository.DeleteAsync(entity);
    }

    /// <summary>
    /// 批量删除产品
    /// </summary>
    /// <param name="ids">产品ID集合</param>
    /// <returns>是否成功</returns>
    [UnitOfWork]
    public async Task<bool> BatchDeleteAsync(List<long> ids)
    {
        if (ids == null || ids.Count == 0)
            throw PersistdValidateException.Message("请选择要删除的产品");

        // 检查是否有关联的产品模型
        var hasModels = await _db.Queryable<ProductModelEntity>()
            .Where(pm => ids.Contains(pm.ProductId))
            .AnyAsync();
        
        if (hasModels)
            throw PersistdValidateException.Message("选中的产品中存在关联的产品模型，无法删除");

        return await _productRepository.DeleteAsync(p => ids.Contains(p.Id));
    }

    /// <summary>
    /// 获取产品简单列表 (用于下拉选择)
    /// </summary>
    /// <returns>产品简单列表</returns>
    public async Task<List<ProductSimpleOutput>> GetSimpleListAsync()
    {
        var products = await _db.Queryable<ProductEntity>()
            .Select(p => new ProductSimpleOutput
            {
                Id = p.Id,
                ProductName = p.ProductName,
                ProtocolType = p.ProtocolType,
                ProductType = p.ProductType,
                DataFormat = p.DataFormat
            })
            .OrderBy(p => p.ProductName)
            .ToListAsync();

        return products;
    }

    /// <summary>
    /// 根据协议类型获取产品列表
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <returns>产品列表</returns>
    public async Task<List<ProductSimpleOutput>> GetByProtocolTypeAsync(int protocolType)
    {
        var products = await _db.Queryable<ProductEntity>()
            .Where(p => p.ProtocolType == protocolType)
            .Select(p => new ProductSimpleOutput
            {
                Id = p.Id,
                ProductName = p.ProductName,
                ProtocolType = p.ProtocolType,
                ProductType = p.ProductType,
                DataFormat = p.DataFormat
            })
            .OrderBy(p => p.ProductName)
            .ToListAsync();

        return products;
    }

    /// <summary>
    /// 根据产品类型获取产品列表
    /// </summary>
    /// <param name="productType">产品类型</param>
    /// <returns>产品列表</returns>
    public async Task<List<ProductSimpleOutput>> GetByProductTypeAsync(int productType)
    {
        var products = await _db.Queryable<ProductEntity>()
            .Where(p => p.ProductType == productType)
            .Select(p => new ProductSimpleOutput
            {
                Id = p.Id,
                ProductName = p.ProductName,
                ProtocolType = p.ProtocolType,
                ProductType = p.ProductType,
                DataFormat = p.DataFormat
            })
            .OrderBy(p => p.ProductName)
            .ToListAsync();

        return products;
    }

    /// <summary>
    /// 获取协议类型名称
    /// </summary>
    /// <param name="protocolType">协议类型</param>
    /// <returns>协议类型名称</returns>
    private static string GetProtocolTypeName(int protocolType)
    {
        return protocolType switch
        {
            1 => "MQTT",
            2 => "Modbus",
            _ => "未知协议"
        };
    }

    /// <summary>
    /// 获取产品类型名称
    /// </summary>
    /// <param name="productType">产品类型</param>
    /// <returns>产品类型名称</returns>
    private static string GetProductTypeName(int productType)
    {
        return productType switch
        {
            1 => "直连设备",
            2 => "网关设备",
            3 => "网关子设备",
            _ => "未知类型"
        };
    }

    /// <summary>
    /// 获取数据格式名称
    /// </summary>
    /// <param name="dataFormat">数据格式</param>
    /// <returns>数据格式名称</returns>
    private static string GetDataFormatName(int dataFormat)
    {
        return dataFormat switch
        {
            1 => "JSON",
            2 => "HEX",
            _ => "未知格式"
        };
    }
}