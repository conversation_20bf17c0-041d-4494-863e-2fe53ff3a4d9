// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Application.ProductServices.Dto;

/// <summary>
/// 模型指令输入DTO
/// </summary>
public class ModelInstructionInput
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string InstructionName { get; set; }

    /// <summary>
    /// 功能码
    /// </summary>
    public int FunctionCode { get; set; }

    /// <summary>
    /// 起始地址
    /// </summary>
    public int StartAddress { get; set; }

    /// <summary>
    /// 读取数量
    /// </summary>
    public int ReadCount { get; set; }

    /// <summary>
    /// 编码 (1:HEX 2:ASCII)
    /// </summary>
    public int Encode { get; set; } = 1;

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    public int ResponseTime { get; set; } = 2000;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 模型指令更新输入DTO
/// </summary>
public class UpdateModelInstructionInput : ModelInstructionInput
{
    /// <summary>
    /// 指令ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
/// 模型指令输出DTO
/// </summary>
public class ModelInstructionOutput
{
    /// <summary>
    /// 指令ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string InstructionName { get; set; }

    /// <summary>
    /// 功能码
    /// </summary>
    public int FunctionCode { get; set; }

    /// <summary>
    /// 功能码名称
    /// </summary>
    public string FunctionCodeName { get; set; }

    /// <summary>
    /// 起始地址
    /// </summary>
    public int StartAddress { get; set; }

    /// <summary>
    /// 读取数量
    /// </summary>
    public int ReadCount { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public int Encode { get; set; }

    /// <summary>
    /// 编码名称
    /// </summary>
    public string EncodeName { get; set; }

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    public int ResponseTime { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public long CreateBy { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public long? UpdateBy { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// 模型指令查询输入DTO
/// </summary>
public class ModelInstructionQueryInput : PaginationParams
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long? ModelId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string InstructionName { get; set; }


    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsEnabled { get; set; }
}

/// <summary>
/// 模型指令简单输出DTO (用于下拉选择等场景)
/// </summary>
public class ModelInstructionSimpleOutput
{
    /// <summary>
    /// 指令ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string InstructionName { get; set; }

    /// <summary>
    /// 功能码
    /// </summary>
    public int FunctionCode { get; set; }

    /// <summary>
    /// 功能码名称
    /// </summary>
    public string FunctionCodeName { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}
