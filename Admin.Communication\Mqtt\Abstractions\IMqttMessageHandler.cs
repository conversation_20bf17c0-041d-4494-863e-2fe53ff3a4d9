using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Models;

namespace Admin.Communication.Mqtt.Abstractions
{
    /// <summary>
    /// MQTT消息处理器接口
    /// </summary>
    public interface IMqttMessageHandler
    {
        /// <summary>
        /// 处理器名称
        /// </summary>
        string Name { get; }
        
        /// <summary>
        /// 处理器描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; set; }
        
        /// <summary>
        /// 支持的主题模式（支持通配符）
        /// </summary>
        string[] SupportedTopicPatterns { get; }
        
        /// <summary>
        /// 支持的消息类型
        /// </summary>
        Type[] SupportedMessageTypes { get; }
        
        /// <summary>
        /// 处理器优先级（数值越小优先级越高）
        /// </summary>
        int Priority { get; }
        
        /// <summary>
        /// 判断是否可以处理指定的消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>如果可以处理返回true，否则返回false</returns>
        bool CanHandle(MqttMessage message, MqttMessageContext context);
        
        /// <summary>
        /// 异步处理消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>处理结果</returns>
        Task<MqttMessageHandleResult> HandleAsync(MqttMessage message, MqttMessageContext context);
        
        /// <summary>
        /// 初始化处理器
        /// </summary>
        /// <returns>初始化任务</returns>
        Task InitializeAsync();
        
        /// <summary>
        /// 销毁处理器
        /// </summary>
        /// <returns>销毁任务</returns>
        Task DisposeAsync();
    }
    
    /// <summary>
    /// 消息处理上下文
    /// </summary>
    public class MqttMessageContext
    {
        /// <summary>
        /// 客户端连接信息
        /// </summary>
        public MqttClientConnection ClientConnection { get; set; }
        
        /// <summary>
        /// 消息接收时间
        /// </summary>
        public DateTime ReceivedTime { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 消息来源
        /// </summary>
        public string? Source { get; set; }
        
        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }
        
        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 是否需要响应
        /// </summary>
        public bool RequiresResponse { get; set; }
    }
    
    /// <summary>
    /// 消息处理结果
    /// </summary>
    public class MqttMessageHandleResult
    {
        /// <summary>
        /// 是否处理成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }
        
        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception? Exception { get; set; }
        
        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }
        
        /// <summary>
        /// 是否需要继续传递给其他处理器
        /// </summary>
        public bool ContinueProcessing { get; set; } = true;
        
        /// <summary>
        /// 响应消息
        /// </summary>
        public MqttMessage? ResponseMessage { get; set; }
        
        /// <summary>
        /// 处理结果数据
        /// </summary>
        public Dictionary<string, object> ResultData { get; set; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="continueProcessing">是否继续处理</param>
        /// <returns>成功结果</returns>
        public static MqttMessageHandleResult Success(bool continueProcessing = true)
        {
            return new MqttMessageHandleResult
            {
                IsSuccess = true,
                ContinueProcessing = continueProcessing
            };
        }
        
        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="exception">异常</param>
        /// <param name="continueProcessing">是否继续处理</param>
        /// <returns>失败结果</returns>
        public static MqttMessageHandleResult Failure(string errorMessage, Exception? exception = null, bool continueProcessing = true)
        {
            return new MqttMessageHandleResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Exception = exception,
                ContinueProcessing = continueProcessing
            };
        }
    }

    /// <summary>
    /// 消息转换器接口
    /// </summary>
    public interface IMqttMessageTransformer
    {
        /// <summary>
        /// 转换器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 转换器优先级（数值越小优先级越高）
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 判断是否可以转换指定的消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>如果可以转换返回true，否则返回false</returns>
        bool CanTransform(MqttMessage message, MqttMessageContext context);

        /// <summary>
        /// 转换消息
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>转换后的消息</returns>
        Task<MqttMessage> TransformAsync(MqttMessage message, MqttMessageContext context);
    }

    /// <summary>
    /// 消息验证器接口
    /// </summary>
    public interface IMqttMessageValidator
    {
        /// <summary>
        /// 验证器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 验证器优先级（数值越小优先级越高）
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 判断是否可以验证指定的消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>如果可以验证返回true，否则返回false</returns>
        bool CanValidate(MqttMessage message, MqttMessageContext context);

        /// <summary>
        /// 验证消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>验证结果</returns>
        Task<MqttMessageValidationResult> ValidateAsync(MqttMessage message, MqttMessageContext context);
    }

    /// <summary>
    /// 消息验证结果
    /// </summary>
    public class MqttMessageValidationResult
    {
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 验证错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 验证详细信息
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 创建成功验证结果
        /// </summary>
        /// <returns>成功结果</returns>
        public static MqttMessageValidationResult Success()
        {
            return new MqttMessageValidationResult { IsValid = true };
        }

        /// <summary>
        /// 创建失败验证结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="errorCode">错误代码</param>
        /// <returns>失败结果</returns>
        public static MqttMessageValidationResult Failure(string errorMessage, string? errorCode = null)
        {
            return new MqttMessageValidationResult
            {
                IsValid = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode
            };
        }
    }

    /// <summary>
    /// 消息处理管道接口
    /// </summary>
    public interface IMqttMessagePipeline
    {
        /// <summary>
        /// 添加消息转换器
        /// </summary>
        /// <param name="transformer">转换器</param>
        void AddTransformer(IMqttMessageTransformer transformer);

        /// <summary>
        /// 添加消息验证器
        /// </summary>
        /// <param name="validator">验证器</param>
        void AddValidator(IMqttMessageValidator validator);

        /// <summary>
        /// 执行消息管道处理
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>处理后的消息和验证结果</returns>
        Task<MqttMessagePipelineResult> ProcessAsync(MqttMessage message, MqttMessageContext context);
    }

    /// <summary>
    /// 消息管道处理结果
    /// </summary>
    public class MqttMessagePipelineResult
    {
        /// <summary>
        /// 处理后的消息
        /// </summary>
        public MqttMessage? ProcessedMessage { get; set; }

        /// <summary>
        /// 验证结果
        /// </summary>
        public List<MqttMessageValidationResult> ValidationResults { get; set; } = new List<MqttMessageValidationResult>();

        /// <summary>
        /// 是否所有验证都通过
        /// </summary>
        public bool IsValid => ValidationResults.All(r => r.IsValid);

        /// <summary>
        /// 转换历史
        /// </summary>
        public List<string> TransformationHistory { get; set; } = new List<string>();

        /// <summary>
        /// 处理是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
} 