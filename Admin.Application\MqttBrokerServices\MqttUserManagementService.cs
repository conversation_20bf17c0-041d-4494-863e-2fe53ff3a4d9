// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.MqttBrokerServices.Dto;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Core.DataEncryption.Encryptions;
using Admin.Core.ExceptionExtensions;
using Admin.Multiplex.Contracts.Consts;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mapster;

namespace Admin.Application.MqttBrokerServices;

/// <summary>
/// MQTT用户管理应用服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.MQTT)]
public class MqttUserManagementService(
    IMqttUserService mqttUserService,
    ILogger<MqttUserManagementService> logger) : ApplicationService
{
    private readonly IMqttUserService _mqttUserService = mqttUserService;
    private readonly ILogger<MqttUserManagementService> _logger = logger;

    /// <summary>
    /// 添加MQTT用户
    /// </summary>
    /// <param name="input">用户信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> AddAsync(AddMqttUserInput input)
    {
        _logger.LogInformation("添加MQTT用户: {Username}", input.Username);
    
        // 检查用户是否已存在
        if (await _mqttUserService.UserExistsAsync(input.Username))
        {
            throw PersistdValidateException.Message($"用户名 '{input.Username}' 已存在");
        }

        // 创建用户实体
        var user = input.Adapt<MqttUserEntity>();
        // 加密密码
        // user.Password = MD5Encryption.Encrypt(input.Password, false, false);
        user.Password = input.Password;

        var result = await _mqttUserService.AddUserAsync(user);
        if (!result)
        {
            throw PersistdValidateException.Message("添加MQTT用户失败");
        }

        _logger.LogInformation("MQTT用户 {Username} 添加成功", input.Username);
        return result;
    }

    /// <summary>
    /// 更新MQTT用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="input">更新信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateAsync(string username, UpdateMqttUserInput input)
    {
        _logger.LogInformation("更新MQTT用户: {Username}", username);

        // 获取现有用户
        var existingUser = await _mqttUserService.GetUserAsync(username);
        if (existingUser == null)
        {
            throw PersistdValidateException.Message($"用户 '{username}' 不存在");
        }

        // 更新用户信息
        existingUser.ClientIdLimit = input.ClientIdLimit;
        existingUser.ExpireTime = input.ExpireTime;
        existingUser.Description = input.Description;
        existingUser.IsEnabled = input.IsEnabled;

        // 如果提供了新密码，则更新密码
        if (!string.IsNullOrEmpty(input.Password))
        {
            existingUser.Password = MD5Encryption.Encrypt(input.Password, false, false);
        }

        var result = await _mqttUserService.UpdateUserAsync(existingUser);
        if (!result)
        {
            throw PersistdValidateException.Message("更新MQTT用户失败");
        }

        _logger.LogInformation("MQTT用户 {Username} 更新成功", username);
        return result;
    }

    /// <summary>
    /// 删除MQTT用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteAsync(string username)
    {
        _logger.LogInformation("删除MQTT用户: {Username}", username);

        // 检查用户是否存在
        if (!await _mqttUserService.UserExistsAsync(username))
        {
            throw PersistdValidateException.Message($"用户 '{username}' 不存在");
        }

        var result = await _mqttUserService.DeleteUserAsync(username);
        if (!result)
        {
            throw PersistdValidateException.Message("删除MQTT用户失败");
        }

        _logger.LogInformation("MQTT用户 {Username} 删除成功", username);
        return result;
    }

    /// <summary>
    /// 分页查询MQTT用户
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页结果</returns>
    public async Task<PagedList<MqttUserOutput>> QueryAsync(MqttUserQueryInput input)
    {
        _logger.LogInformation("查询MQTT用户列表: PageIndex={PageIndex}, PageSize={PageSize}", input.PageIndex, input.PageSize);

        var (users, totalCount) = await _mqttUserService.QueryUsersAsync(
            input.PageIndex, input.PageSize, input.Username, input.IsEnabled);

        // 创建分页结果并映射到输出DTO
        var pagedList = new PagedList<MqttUserEntity>
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Total = totalCount,
            PageCount = (int)Math.Ceiling((double)totalCount / input.PageSize),
            Items = users
        };

        return pagedList.Adapt<PagedList<MqttUserOutput>>();
    }

    /// <summary>
    /// 获取MQTT用户详情
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户信息</returns>
    public async Task<MqttUserOutput> GetAsync(string username)
    {
        _logger.LogInformation("获取MQTT用户详情: {Username}", username);

        var user = await _mqttUserService.GetUserAsync(username);
        if (user == null)
        {
            throw PersistdValidateException.Message($"用户 '{username}' 不存在");
        }

        return user.Adapt<MqttUserOutput>();
    }

    /// <summary>
    /// 启用/禁用MQTT用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>是否成功</returns>
    public async Task<bool> ToggleAsync(string username, bool isEnabled)
    {
        _logger.LogInformation("切换MQTT用户状态: {Username}, IsEnabled={IsEnabled}", username, isEnabled);

        // 检查用户是否存在
        if (!await _mqttUserService.UserExistsAsync(username))
        {
            throw PersistdValidateException.Message($"用户 '{username}' 不存在");
        }

        var result = await _mqttUserService.ToggleUserAsync(username, isEnabled);
        if (!result)
        {
            throw PersistdValidateException.Message("更新用户状态失败");
        }

        _logger.LogInformation("MQTT用户 {Username} 状态更新成功: {Status}", username, isEnabled ? "启用" : "禁用");
        return result;
    }

    /// <summary>
    /// 重置MQTT用户密码
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="newPassword">新密码</param>
    /// <returns>是否成功</returns>
    public async Task<bool> ResetPasswordAsync(string username, string newPassword)
    {
        _logger.LogInformation("重置MQTT用户密码: {Username}", username);

        // 检查用户是否存在
        if (!await _mqttUserService.UserExistsAsync(username))
        {
            throw PersistdValidateException.Message($"用户 '{username}' 不存在");
        }

        var result = await _mqttUserService.ResetPasswordAsync(username, newPassword);
        if (!result)
        {
            throw PersistdValidateException.Message("重置密码失败");
        }

        _logger.LogInformation("MQTT用户 {Username} 密码重置成功", username);
        return result;
    }

    /// <summary>
    /// 为设备创建MQTT用户
    /// </summary>
    /// <param name="input">设备信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> CreateDeviceUserAsync(CreateDeviceMqttUserInput input)
    {
        _logger.LogInformation("为设备创建MQTT用户: {DeviceId}", input.DeviceId);

        // 检查设备用户是否已存在
        if (await _mqttUserService.UserExistsAsync(input.DeviceId))
        {
            _logger.LogWarning("设备MQTT用户已存在: {DeviceId}", input.DeviceId);
            return true; // 已存在则认为成功
        }

        var result = await _mqttUserService.CreateDeviceUserAsync(
            input.DeviceId, input.DeviceSecret, input.DeviceName, input.ExpireTime);
        if (!result)
        {
            throw PersistdValidateException.Message($"为设备 '{input.DeviceId}' 创建MQTT用户失败");
        }

        _logger.LogInformation("设备MQTT用户创建成功: {DeviceId}", input.DeviceId);
        return result;
    }

    /// <summary>
    /// 删除设备MQTT用户
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteDeviceUserAsync(string deviceId)
    {
        _logger.LogInformation("删除设备MQTT用户: {DeviceId}", deviceId);

        // 检查用户是否存在
        if (!await _mqttUserService.UserExistsAsync(deviceId))
        {
            _logger.LogWarning("设备MQTT用户不存在: {DeviceId}", deviceId);
            return true; // 不存在则认为删除成功
        }

        var result = await _mqttUserService.DeleteUserAsync(deviceId);
        if (!result)
        {
            throw PersistdValidateException.Message($"删除设备 '{deviceId}' 的MQTT用户失败");
        }

        _logger.LogInformation("设备MQTT用户删除成功: {DeviceId}", deviceId);
        return result;
    }
}
