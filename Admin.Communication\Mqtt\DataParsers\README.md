# 可扩展数据解析架构

## 概述

本架构采用策略模式和工厂模式实现了可扩展的MQTT设备数据解析系统，支持多种数据格式和主题类型，便于后续功能扩展。

## 架构组件

### 1. 核心接口

#### IDataParser - 数据解析器接口
- 定义了数据解析器的标准接口
- 支持不同数据格式的解析策略
- 提供CanParse和ParseAsync方法

#### ITopicResolver - 主题解析器接口
- 负责解析MQTT主题类型
- 确定数据格式和设备上下文
- 支持内置主题和配置主题

#### IDataParserFactory - 解析器工厂接口
- 管理和创建数据解析器实例
- 支持解析器的注册和发现
- 提供解析器缓存机制

### 2. 具体实现

#### JsonDataParser - JSON数据解析器
- 支持JSON格式数据解析
- 处理直连设备和网关设备数据
- 支持嵌套JSON结构解析

#### HexDataParser - HEX数据解析器
- 支持十六进制数据解析
- 支持Modbus RTU协议解析
- 支持二进制数据处理

#### DeviceTopicResolver - 设备主题解析器
- 解析设备主题类型（内置/配置）
- 查询设备、产品、模型信息
- 构建设备解析上下文

### 3. 数据模型

#### DeviceParseContext - 设备解析上下文
- 包含设备、产品、模型信息
- 提供解析过程中的所有上下文数据
- 支持扩展属性

#### DeviceDataParseResult - 解析结果
- 包含解析状态和错误信息
- 提供解析出的数据项列表
- 记录解析耗时信息

#### DeviceDataItem - 数据项
- 表示单个设备属性数据
- 包含属性信息和值
- 支持数据质量状态

## 使用方式

### 1. 服务注册

```csharp
// 在AdminCommunicationModule中注册服务
context.Services.AddDataParserServices();
```

### 2. 添加新的数据解析器

```csharp
// 1. 实现IDataParser接口
public class CustomDataParser : IDataParser
{
    public DataFormatEnum SupportedFormat => DataFormatEnum.Custom;
    public string Name => "CustomDataParser";
    public string Description => "自定义数据解析器";
    
    public bool CanParse(byte[] payload, DeviceParseContext context)
    {
        // 实现解析能力检查逻辑
    }
    
    public async Task<DeviceDataParseResult> ParseAsync(byte[] payload, DeviceParseContext context)
    {
        // 实现具体解析逻辑
    }
}

// 2. 注册到工厂
services.AddTransient<CustomDataParser>();
// 在工厂配置中注册
factory.RegisterParser(provider.GetRequiredService<CustomDataParser>());
```

### 3. 配置主题解析

通过DeviceTopicConfig表配置设备主题：
- DeviceId: 设备ID
- Topic: MQTT主题
- DataFormat: 数据格式（1:JSON, 2:HEX）
- AccessType: 访问类型

## 扩展点

### 1. 新增数据格式支持
- 实现IDataParser接口
- 在DataFormatEnum中添加新格式
- 注册到DataParserFactory

### 2. 自定义主题解析
- 实现ITopicResolver接口
- 支持自定义主题模式
- 替换默认的DeviceTopicResolver

### 3. 数据处理扩展
- 在DeviceDataItem中添加新属性
- 扩展DeviceParseContext上下文
- 自定义数据存储逻辑

## 性能优化

### 1. 缓存机制
- 主题配置缓存
- 解析器实例缓存
- 设备模型缓存

### 2. 异步处理
- 数据解析异步化
- 批量数据存储
- 非阻塞处理

### 3. 错误处理
- 解析失败降级策略
- 错误数据隔离
- 熔断机制

## 最佳实践

1. **解析器设计**：保持解析器无状态，支持并发调用
2. **错误处理**：提供详细的错误信息和异常处理
3. **性能考虑**：避免重复创建解析器实例
4. **扩展性**：通过接口和工厂模式支持新格式
5. **测试**：为每个解析器编写单元测试

## 示例

### JSON数据解析示例
```json
// 直连设备
{
  "temp": 25.5,
  "humi": 60.2
}

// 网关设备
{
  "IO": {
    "IN1": "DI1",
    "O1": "DO1"
  },
  "sensors": {
    "temp": 25.5,
    "humi": 60.2
  }
}
```

### HEX数据解析示例
```
// Modbus RTU响应: 01 03 02 02 15 12 54 CRC16
// 设备地址: 01, 功能码: 03, 数据长度: 02, 数据: 0215 1254
```
