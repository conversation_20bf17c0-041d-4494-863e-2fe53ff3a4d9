using System;

namespace Admin.Communication.Mqtt.Events
{
    /// <summary>
    /// MQTT消息接收事件参数
    /// </summary>
    public class MqttMessageReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// 消息负载
        /// </summary>
        public byte[] Payload { get; set; }

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int QoS { get; set; }

        /// <summary>
        /// 是否为保留消息
        /// </summary>
        public bool Retain { get; set; }
    }
} 