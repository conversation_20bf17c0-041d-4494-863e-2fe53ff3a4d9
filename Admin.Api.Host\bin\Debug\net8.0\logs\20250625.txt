[00:04:17] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125

[00:04:17] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[00:04:18] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[00:04:18] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[00:04:18] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[00:09:08] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125

[00:09:08] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[00:09:08] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[00:09:08] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[00:09:08] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[00:13:58] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2_1750781638000 连接被拒绝，原因: "BadUsernameOrPassword"

[00:25:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_1 尝试匿名连接，但匿名访问已被禁用

[00:25:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_1 连接被拒绝，原因: "BadUsernameOrPassword"

[00:25:38] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_2 尝试匿名连接，但匿名访问已被禁用

[00:25:38] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_2 连接被拒绝，原因: "BadUsernameOrPassword"

[00:25:40] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_3 使用用户名 wrong_user 认证失败，与配置的默认凭据不匹配

[00:25:40] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_3 连接被拒绝，原因: "BadUsernameOrPassword"

[00:25:44] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_5 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[00:25:44] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 test_client_5 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:15] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:15] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:20] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:20] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:30] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:30] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:35] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:35] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:40] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:40] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:45] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:45] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:50] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:50] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:21:55] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:21:55] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:00] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:00] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:05] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:05] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:10] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:10] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:15] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:15] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:20] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:20] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:25] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:30] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:30] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:51] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:51] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:22:56] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:22:56] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:01] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:01] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:06] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:06] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:11] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:11] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:16] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:16] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:21] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:21] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:26] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:26] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:31] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:31] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:51] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:51] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:23:56] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:23:56] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:01] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:01] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:06] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:06] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:11] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:11] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:16] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:16] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:21] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:21] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:31] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:31] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:51] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:51] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[14:24:56] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[14:24:56] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[15:19:35] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
发送PUBLISH消息失败，客户端: mqttx_a978efd2_1750832520000, Topic: test/UploadTopic
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketWriter.WritePacketAsync(Stream stream, MqttMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketWriter.cs:line 33
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendPublishAsync(MqttClientConnection connection, MqttPublishMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1394

[15:19:35] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
向客户端 mqttx_a978efd2_1750832520000 发送消息时发生错误
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketWriter.WritePacketAsync(Stream stream, MqttMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketWriter.cs:line 33
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendPublishAsync(MqttClientConnection connection, MqttPublishMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1394
   at Admin.Communication.Mqtt.Services.MqttBrokerService.PublishToSubscribersAsync(MqttPublishMessage message) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 836

[15:26:16] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
发送PUBLISH消息失败，客户端: mqttx_a978efd2_1750832520000, Topic: test/UploadTopic
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketWriter.WritePacketAsync(Stream stream, MqttMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketWriter.cs:line 33
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendPublishAsync(MqttClientConnection connection, MqttPublishMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1394

[15:26:16] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
向客户端 mqttx_a978efd2_1750832520000 发送消息时发生错误
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketWriter.WritePacketAsync(Stream stream, MqttMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketWriter.cs:line 33
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendPublishAsync(MqttClientConnection connection, MqttPublishMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1394
   at Admin.Communication.Mqtt.Services.MqttBrokerService.PublishToSubscribersAsync(MqttPublishMessage message) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 836

[15:33:16] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
发送PUBLISH消息失败，客户端: mqttx_a978efd2_1750832520000, Topic: test/UploadTopic
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketWriter.WritePacketAsync(Stream stream, MqttMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketWriter.cs:line 33
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendPublishAsync(MqttClientConnection connection, MqttPublishMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1394

[15:33:16] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
向客户端 mqttx_a978efd2_1750832520000 发送消息时发生错误
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketWriter.WritePacketAsync(Stream stream, MqttMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketWriter.cs:line 33
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendPublishAsync(MqttClientConnection connection, MqttPublishMessage message, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1394
   at Admin.Communication.Mqtt.Services.MqttBrokerService.PublishToSubscribersAsync(MqttPublishMessage message) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 836

[16:07:42] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[16:12:43] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[16:12:43] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[16:12:48] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[16:12:48] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[16:12:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[16:12:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[16:12:58] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 使用用户名 admin 认证失败，与配置的默认凭据不匹配

[16:12:58] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 123456 连接被拒绝，原因: "BadUsernameOrPassword"

[23:22:05] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125

[23:22:05] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[23:22:05] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[23:22:05] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 125
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[23:22:05] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[23:22:35] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

