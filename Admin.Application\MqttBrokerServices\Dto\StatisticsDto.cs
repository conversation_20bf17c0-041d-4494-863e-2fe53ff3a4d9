using System.ComponentModel.DataAnnotations;

namespace Admin.Application.MqttBrokerServices.Dto
{
    /// <summary>
    /// 总体统计概览DTO
    /// </summary>
    public class StatisticsOverviewDto
    {
        /// <summary>
        /// 代理服务信息
        /// </summary>
        public BrokerStatisticsDto Broker { get; set; } = new();

        /// <summary>
        /// 连接统计
        /// </summary>
        public ConnectionStatisticsDto Connections { get; set; } = new();

        /// <summary>
        /// 会话统计
        /// </summary>
        public SessionStatisticsOverviewDto Sessions { get; set; } = new();

        /// <summary>
        /// 消息统计
        /// </summary>
        public MessageStatisticsDto Messages { get; set; } = new();

        /// <summary>
        /// 订阅统计
        /// </summary>
        public SubscriptionStatisticsDto Subscriptions { get; set; } = new();

        /// <summary>
        /// 认证统计
        /// </summary>
        public AuthenticationStatisticsDto Authentication { get; set; } = new();
    }

    /// <summary>
    /// 代理服务统计DTO
    /// </summary>
    public class BrokerStatisticsDto
    {
        /// <summary>
        /// 是否运行中
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 运行时间
        /// </summary>
        public string Uptime { get; set; } = string.Empty;

        /// <summary>
        /// 版本信息
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 监听端口
        /// </summary>
        public int Port { get; set; }
    }

    /// <summary>
    /// 连接统计DTO
    /// </summary>
    public class ConnectionStatisticsDto
    {
        /// <summary>
        /// 当前连接数
        /// </summary>
        public int Current { get; set; }

        /// <summary>
        /// 总连接数（累计）
        /// </summary>
        public long Total { get; set; }

        /// <summary>
        /// 总断开连接数
        /// </summary>
        public long TotalDisconnections { get; set; }

        /// <summary>
        /// 异常断开连接数
        /// </summary>
        public long AbnormalDisconnections { get; set; }

        /// <summary>
        /// 最大并发连接数
        /// </summary>
        public int MaxConcurrent { get; set; }

        /// <summary>
        /// 平均连接持续时间
        /// </summary>
        public string AverageDuration { get; set; } = string.Empty;

        /// <summary>
        /// 按IP分组的连接数
        /// </summary>
        public Dictionary<string, int> ConnectionsByIp { get; set; } = new();

        /// <summary>
        /// 连接数最多的客户端
        /// </summary>
        public List<TopClientDto> TopClientsByConnections { get; set; } = new();
    }

    /// <summary>
    /// 会话统计概览DTO
    /// </summary>
    public class SessionStatisticsOverviewDto
    {
        /// <summary>
        /// 活跃会话数
        /// </summary>
        public int Active { get; set; }

        /// <summary>
        /// 暂停会话数
        /// </summary>
        public int Suspended { get; set; }

        /// <summary>
        /// 持久会话数
        /// </summary>
        public int Persistent { get; set; }

        /// <summary>
        /// 总创建会话数
        /// </summary>
        public long TotalCreated { get; set; }

        /// <summary>
        /// 过期会话数
        /// </summary>
        public int Expired { get; set; }
    }

    /// <summary>
    /// 消息统计DTO
    /// </summary>
    public class MessageStatisticsDto
    {
        /// <summary>
        /// 总发布消息数
        /// </summary>
        public long TotalPublished { get; set; }

        /// <summary>
        /// 总接收消息数
        /// </summary>
        public long TotalReceived { get; set; }

        /// <summary>
        /// 总传输字节数
        /// </summary>
        public long TotalBytesTransferred { get; set; }

        /// <summary>
        /// 发布速率（消息/秒）
        /// </summary>
        public double PublishRate { get; set; }

        /// <summary>
        /// 接收速率（消息/秒）
        /// </summary>
        public double ReceiveRate { get; set; }

        /// <summary>
        /// 平均消息大小（字节）
        /// </summary>
        public long AverageMessageSize { get; set; }

        /// <summary>
        /// 消息数最多的主题
        /// </summary>
        public List<TopTopicDto> TopTopicsByMessages { get; set; } = new();

        /// <summary>
        /// 按QoS分组的消息统计
        /// </summary>
        public List<MessageQosStatsDto> MessagesByQos { get; set; } = new();

        /// <summary>
        /// 保留消息数
        /// </summary>
        public long RetainedMessageCount { get; set; }
    }

    /// <summary>
    /// 订阅统计DTO
    /// </summary>
    public class SubscriptionStatisticsDto
    {
        /// <summary>
        /// 总订阅数
        /// </summary>
        public int TotalSubscriptions { get; set; }

        /// <summary>
        /// 唯一主题数
        /// </summary>
        public int UniqueTopics { get; set; }

        /// <summary>
        /// 平均每客户端订阅数
        /// </summary>
        public double AverageSubscriptionsPerClient { get; set; }

        /// <summary>
        /// 订阅数最多的主题
        /// </summary>
        public List<TopSubscribedTopicDto> TopSubscribedTopics { get; set; } = new();

        /// <summary>
        /// 通配符订阅数
        /// </summary>
        public int WildcardSubscriptions { get; set; }
    }

    /// <summary>
    /// 认证统计DTO
    /// </summary>
    public class AuthenticationStatisticsDto
    {
        /// <summary>
        /// 总认证尝试数
        /// </summary>
        public long TotalAttempts { get; set; }

        /// <summary>
        /// 成功认证数
        /// </summary>
        public long SuccessfulAttempts { get; set; }

        /// <summary>
        /// 失败认证数
        /// </summary>
        public long FailedAttempts { get; set; }

        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 最近认证失败记录
        /// </summary>
        public List<AuthenticationFailureDto> RecentFailures { get; set; } = new();
    }

    /// <summary>
    /// 性能指标DTO
    /// </summary>
    public class PerformanceMetricsDto
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// 内存使用情况
        /// </summary>
        public MemoryUsageDto MemoryUsage { get; set; } = new();

        /// <summary>
        /// 网络指标
        /// </summary>
        public NetworkMetricsDto NetworkMetrics { get; set; } = new();

        /// <summary>
        /// 系统指标
        /// </summary>
        public SystemMetricsDto SystemMetrics { get; set; } = new();

        /// <summary>
        /// 历史数据
        /// </summary>
        public List<TimeSeriesDataDto>? HistoricalData { get; set; }
    }

    /// <summary>
    /// 内存使用DTO
    /// </summary>
    public class MemoryUsageDto
    {
        /// <summary>
        /// 工作集内存（字节）
        /// </summary>
        public long WorkingSet { get; set; }

        /// <summary>
        /// 私有内存（字节）
        /// </summary>
        public long PrivateMemory { get; set; }

        /// <summary>
        /// 虚拟内存（字节）
        /// </summary>
        public long VirtualMemory { get; set; }

        /// <summary>
        /// GC总内存（字节）
        /// </summary>
        public long GcTotalMemory { get; set; }
    }

    /// <summary>
    /// 网络指标DTO
    /// </summary>
    public class NetworkMetricsDto
    {
        /// <summary>
        /// 每秒连接数
        /// </summary>
        public double ConnectionsPerSecond { get; set; }

        /// <summary>
        /// 每秒消息数
        /// </summary>
        public double MessagesPerSecond { get; set; }

        /// <summary>
        /// 每秒字节数
        /// </summary>
        public double BytesPerSecond { get; set; }

        /// <summary>
        /// 活跃连接数
        /// </summary>
        public int ActiveConnections { get; set; }

        /// <summary>
        /// 丢包率（百分比）
        /// </summary>
        public double PacketLoss { get; set; }
    }

    /// <summary>
    /// 系统指标DTO
    /// </summary>
    public class SystemMetricsDto
    {
        /// <summary>
        /// 线程数
        /// </summary>
        public int ThreadCount { get; set; }

        /// <summary>
        /// 句柄数
        /// </summary>
        public int HandleCount { get; set; }

        /// <summary>
        /// 运行时间（秒）
        /// </summary>
        public int UptimeSeconds { get; set; }

        /// <summary>
        /// GC收集次数
        /// </summary>
        public Dictionary<int, int> GcCollections { get; set; } = new();
    }

    /// <summary>
    /// 时间序列数据DTO
    /// </summary>
    public class TimeSeriesDataDto
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 数值
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// 顶级客户端DTO
    /// </summary>
    public class TopClientDto
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 连接数
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 最后连接时间
        /// </summary>
        public DateTime LastSeen { get; set; }
    }

    /// <summary>
    /// 顶级主题DTO
    /// </summary>
    public class TopTopicDto
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 消息数量
        /// </summary>
        public long MessageCount { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivity { get; set; }
    }

    /// <summary>
    /// 消息QoS统计DTO
    /// </summary>
    public class MessageQosStatsDto
    {
        /// <summary>
        /// QoS级别
        /// </summary>
        public int QosLevel { get; set; }

        /// <summary>
        /// 消息数量
        /// </summary>
        public long Count { get; set; }

        /// <summary>
        /// 百分比
        /// </summary>
        public double Percentage { get; set; }
    }

    /// <summary>
    /// 顶级订阅主题DTO
    /// </summary>
    public class TopSubscribedTopicDto
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 订阅者数量
        /// </summary>
        public int SubscriberCount { get; set; }

        /// <summary>
        /// 百分比
        /// </summary>
        public double Percentage { get; set; }
    }

    /// <summary>
    /// 认证失败DTO
    /// </summary>
    public class AuthenticationFailureDto
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 失败时间
        /// </summary>
        public DateTime FailureTime { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string Reason { get; set; } = string.Empty;
    }

    #region 请求和响应DTO

    /// <summary>
    /// 获取性能指标请求DTO
    /// </summary>
    public class GetPerformanceMetricsRequest
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 时间间隔
        /// </summary>
        public TimeSpan? Interval { get; set; }
    }

    /// <summary>
    /// 获取连接统计请求DTO
    /// </summary>
    public class GetConnectionStatisticsRequest
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 时间分组方式
        /// </summary>
        public TimeGroupBy? GroupBy { get; set; }
    }

    /// <summary>
    /// 获取连接统计响应DTO
    /// </summary>
    public class GetConnectionStatisticsResponse
    {
        /// <summary>
        /// 连接统计摘要
        /// </summary>
        public ConnectionStatisticsSummaryDto Summary { get; set; } = new();

        /// <summary>
        /// 按IP分组的连接统计
        /// </summary>
        public List<IpConnectionStatsDto> ConnectionsByIp { get; set; } = new();

        /// <summary>
        /// 按时间分组的连接统计
        /// </summary>
        public List<TimeSeriesDataDto> ConnectionsByTime { get; set; } = new();

        /// <summary>
        /// 顶级客户端
        /// </summary>
        public List<TopClientDto> TopClients { get; set; } = new();

        /// <summary>
        /// 连接持续时间分布
        /// </summary>
        public List<ConnectionDurationStatsDto> ConnectionDurationDistribution { get; set; } = new();
    }

    /// <summary>
    /// 连接统计摘要DTO
    /// </summary>
    public class ConnectionStatisticsSummaryDto
    {
        /// <summary>
        /// 当前连接数
        /// </summary>
        public int CurrentConnections { get; set; }

        /// <summary>
        /// 总连接数
        /// </summary>
        public long TotalConnections { get; set; }

        /// <summary>
        /// 总断开连接数
        /// </summary>
        public long TotalDisconnections { get; set; }

        /// <summary>
        /// 异常断开连接数
        /// </summary>
        public long AbnormalDisconnections { get; set; }

        /// <summary>
        /// 最大并发连接数
        /// </summary>
        public int MaxConcurrentConnections { get; set; }

        /// <summary>
        /// 平均连接持续时间（秒）
        /// </summary>
        public double AverageConnectionDuration { get; set; }

        /// <summary>
        /// 连接成功率（百分比）
        /// </summary>
        public double ConnectionSuccessRate { get; set; }
    }

    /// <summary>
    /// IP连接统计DTO
    /// </summary>
    public class IpConnectionStatsDto
    {
        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 连接数
        /// </summary>
        public int ConnectionCount { get; set; }

        /// <summary>
        /// 百分比
        /// </summary>
        public double Percentage { get; set; }
    }

    /// <summary>
    /// 连接持续时间统计DTO
    /// </summary>
    public class ConnectionDurationStatsDto
    {
        /// <summary>
        /// 时间范围
        /// </summary>
        public string Range { get; set; } = string.Empty;

        /// <summary>
        /// 连接数
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 获取消息统计请求DTO
    /// </summary>
    public class GetMessageStatisticsRequest
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 时间分组方式
        /// </summary>
        public TimeGroupBy? GroupBy { get; set; }

        /// <summary>
        /// 顶级主题数量
        /// </summary>
        public int? TopTopicsCount { get; set; }
    }

    /// <summary>
    /// 获取消息统计响应DTO
    /// </summary>
    public class GetMessageStatisticsResponse
    {
        /// <summary>
        /// 消息统计摘要
        /// </summary>
        public MessageStatisticsSummaryDto Summary { get; set; } = new();

        /// <summary>
        /// 按QoS分组的消息统计
        /// </summary>
        public List<MessageQosStatsDto> MessagesByQos { get; set; } = new();

        /// <summary>
        /// 按主题分组的消息统计
        /// </summary>
        public List<TopTopicDto> MessagesByTopic { get; set; } = new();

        /// <summary>
        /// 按时间分组的消息统计
        /// </summary>
        public List<TimeSeriesDataDto> MessagesByTime { get; set; } = new();

        /// <summary>
        /// 保留消息统计
        /// </summary>
        public RetainedMessageStatsDto RetainedMessages { get; set; } = new();
    }

    /// <summary>
    /// 消息统计摘要DTO
    /// </summary>
    public class MessageStatisticsSummaryDto
    {
        /// <summary>
        /// 总消息数
        /// </summary>
        public long TotalMessages { get; set; }

        /// <summary>
        /// 总发布消息数
        /// </summary>
        public long TotalPublished { get; set; }

        /// <summary>
        /// 总接收消息数
        /// </summary>
        public long TotalReceived { get; set; }

        /// <summary>
        /// 总传输字节数
        /// </summary>
        public long TotalBytesTransferred { get; set; }

        /// <summary>
        /// 平均消息大小
        /// </summary>
        public long AverageMessageSize { get; set; }

        /// <summary>
        /// 每秒消息数
        /// </summary>
        public double MessagesPerSecond { get; set; }

        /// <summary>
        /// 发布成功率（百分比）
        /// </summary>
        public double PublishSuccessRate { get; set; }
    }

    /// <summary>
    /// 保留消息统计DTO
    /// </summary>
    public class RetainedMessageStatsDto
    {
        /// <summary>
        /// 保留消息数量
        /// </summary>
        public long Count { get; set; }

        /// <summary>
        /// 总大小（字节）
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 顶级主题
        /// </summary>
        public List<TopTopicDto> TopTopics { get; set; } = new();
    }

    /// <summary>
    /// 获取主题分析请求DTO
    /// </summary>
    public class GetTopicAnalysisRequest
    {
        /// <summary>
        /// 顶级主题数量
        /// </summary>
        public int? TopTopicsCount { get; set; }

        /// <summary>
        /// 最大深度
        /// </summary>
        public int? MaxDepth { get; set; }

        /// <summary>
        /// 主题过滤器
        /// </summary>
        public string? TopicFilter { get; set; }
    }

    /// <summary>
    /// 获取主题分析响应DTO
    /// </summary>
    public class GetTopicAnalysisResponse
    {
        /// <summary>
        /// 主题分析摘要
        /// </summary>
        public TopicAnalysisSummaryDto Summary { get; set; } = new();

        /// <summary>
        /// 订阅者最多的主题
        /// </summary>
        public List<TopicInfoDto> TopTopicsBySubscribers { get; set; } = new();

        /// <summary>
        /// 消息最多的主题
        /// </summary>
        public List<TopTopicDto> TopTopicsByMessages { get; set; } = new();

        /// <summary>
        /// 主题层次结构
        /// </summary>
        public TopicHierarchyDto TopicHierarchy { get; set; } = new();

        /// <summary>
        /// 通配符使用情况
        /// </summary>
        public WildcardUsageDto WildcardUsage { get; set; } = new();

        /// <summary>
        /// 主题详细信息
        /// </summary>
        public TopicDetailDto? TopicDetails { get; set; }
    }

    /// <summary>
    /// 主题分析摘要DTO
    /// </summary>
    public class TopicAnalysisSummaryDto
    {
        /// <summary>
        /// 总主题数
        /// </summary>
        public int TotalTopics { get; set; }

        /// <summary>
        /// 活跃主题数
        /// </summary>
        public int ActiveTopics { get; set; }

        /// <summary>
        /// 已订阅主题数
        /// </summary>
        public int SubscribedTopics { get; set; }

        /// <summary>
        /// 平均每主题订阅者数
        /// </summary>
        public double AverageSubscribersPerTopic { get; set; }
    }

    /// <summary>
    /// 主题信息DTO
    /// </summary>
    public class TopicInfoDto
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 订阅者数量
        /// </summary>
        public int SubscriberCount { get; set; }

        /// <summary>
        /// 消息数量
        /// </summary>
        public long MessageCount { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivity { get; set; }
    }

    /// <summary>
    /// 主题层次结构DTO
    /// </summary>
    public class TopicHierarchyDto
    {
        /// <summary>
        /// 最大深度
        /// </summary>
        public int MaxDepth { get; set; }

        /// <summary>
        /// 总层级数
        /// </summary>
        public int TotalLevels { get; set; }

        /// <summary>
        /// 层次结构
        /// </summary>
        public List<TopicLevelDto> Hierarchy { get; set; } = new();
    }

    /// <summary>
    /// 主题层级DTO
    /// </summary>
    public class TopicLevelDto
    {
        /// <summary>
        /// 层级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 主题数量
        /// </summary>
        public int TopicCount { get; set; }

        /// <summary>
        /// 主题列表
        /// </summary>
        public List<string> Topics { get; set; } = new();
    }

    /// <summary>
    /// 通配符使用情况DTO
    /// </summary>
    public class WildcardUsageDto
    {
        /// <summary>
        /// 总通配符主题数
        /// </summary>
        public int TotalWildcardTopics { get; set; }

        /// <summary>
        /// 单级通配符数量
        /// </summary>
        public int SingleLevelWildcards { get; set; }

        /// <summary>
        /// 多级通配符数量
        /// </summary>
        public int MultiLevelWildcards { get; set; }

        /// <summary>
        /// 通配符百分比
        /// </summary>
        public double WildcardPercentage { get; set; }
    }

    /// <summary>
    /// 主题详细信息DTO
    /// </summary>
    public class TopicDetailDto
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 订阅者数量
        /// </summary>
        public int SubscriberCount { get; set; }

        /// <summary>
        /// 消息数量
        /// </summary>
        public long MessageCount { get; set; }

        /// <summary>
        /// 总字节数
        /// </summary>
        public long TotalBytes { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// 订阅者列表
        /// </summary>
        public List<string> Subscribers { get; set; } = new();
    }

    /// <summary>
    /// 获取会话统计请求DTO
    /// </summary>
    public class GetSessionStatisticsRequest
    {
        // 可以根据需要添加过滤条件
    }

    /// <summary>
    /// 获取会话统计响应DTO
    /// </summary>
    public class GetSessionStatisticsResponse
    {
        /// <summary>
        /// 会话统计摘要
        /// </summary>
        public SessionStatisticsSummaryDto Summary { get; set; } = new();

        /// <summary>
        /// 按状态分组的会话统计
        /// </summary>
        public List<SessionStateStatsDto> SessionsByState { get; set; } = new();

        /// <summary>
        /// 按持续时间分组的会话统计
        /// </summary>
        public List<SessionDurationStatsDto> SessionsByDuration { get; set; } = new();

        /// <summary>
        /// 待发送消息统计
        /// </summary>
        public PendingMessagesStatsDto PendingMessagesStats { get; set; } = new();
    }

    /// <summary>
    /// 会话统计摘要DTO
    /// </summary>
    public class SessionStatisticsSummaryDto
    {
        /// <summary>
        /// 总会话数
        /// </summary>
        public int TotalSessions { get; set; }

        /// <summary>
        /// 活跃会话数
        /// </summary>
        public int ActiveSessions { get; set; }

        /// <summary>
        /// 暂停会话数
        /// </summary>
        public int SuspendedSessions { get; set; }

        /// <summary>
        /// 持久会话数
        /// </summary>
        public int PersistentSessions { get; set; }

        /// <summary>
        /// 清理会话数
        /// </summary>
        public int CleanSessions { get; set; }

        /// <summary>
        /// 平均会话持续时间（秒）
        /// </summary>
        public double AverageSessionDuration { get; set; }

        /// <summary>
        /// 有待发送消息的会话数
        /// </summary>
        public int SessionsWithPendingMessages { get; set; }
    }

    /// <summary>
    /// 会话状态统计DTO
    /// </summary>
    public class SessionStateStatsDto
    {
        /// <summary>
        /// 状态
        /// </summary>
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 百分比
        /// </summary>
        public double Percentage { get; set; }
    }

    /// <summary>
    /// 会话持续时间统计DTO
    /// </summary>
    public class SessionDurationStatsDto
    {
        /// <summary>
        /// 时间范围
        /// </summary>
        public string Range { get; set; } = string.Empty;

        /// <summary>
        /// 会话数
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 待发送消息统计DTO
    /// </summary>
    public class PendingMessagesStatsDto
    {
        /// <summary>
        /// 总待发送消息数
        /// </summary>
        public int TotalPendingMessages { get; set; }

        /// <summary>
        /// 有待发送消息的会话数
        /// </summary>
        public int SessionsWithPendingMessages { get; set; }

        /// <summary>
        /// 平均每会话待发送消息数
        /// </summary>
        public double AveragePendingMessagesPerSession { get; set; }

        /// <summary>
        /// 单个会话最大待发送消息数
        /// </summary>
        public int MaxPendingMessagesInSession { get; set; }
    }

    /// <summary>
    /// 获取历史统计请求DTO
    /// </summary>
    public class GetHistoricalStatisticsRequest
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        [Required]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Required]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 时间间隔
        /// </summary>
        public TimeSpan Interval { get; set; } = TimeSpan.FromMinutes(5);
    }

    /// <summary>
    /// 获取历史统计响应DTO
    /// </summary>
    public class GetHistoricalStatisticsResponse
    {
        /// <summary>
        /// 时间范围
        /// </summary>
        public TimeRangeDto TimeRange { get; set; } = new();

        /// <summary>
        /// 连接历史数据
        /// </summary>
        public List<TimeSeriesDataDto> ConnectionHistory { get; set; } = new();

        /// <summary>
        /// 消息历史数据
        /// </summary>
        public List<TimeSeriesDataDto> MessageHistory { get; set; } = new();

        /// <summary>
        /// 性能历史数据
        /// </summary>
        public List<TimeSeriesDataDto> PerformanceHistory { get; set; } = new();

        /// <summary>
        /// 历史摘要
        /// </summary>
        public HistoricalSummaryDto Summary { get; set; } = new();
    }

    /// <summary>
    /// 时间范围DTO
    /// </summary>
    public class TimeRangeDto
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 时间间隔
        /// </summary>
        public TimeSpan Interval { get; set; }
    }

    /// <summary>
    /// 历史摘要DTO
    /// </summary>
    public class HistoricalSummaryDto
    {
        /// <summary>
        /// 峰值连接数
        /// </summary>
        public int PeakConnections { get; set; }

        /// <summary>
        /// 峰值消息速率
        /// </summary>
        public double PeakMessageRate { get; set; }

        /// <summary>
        /// 平均运行时间（小时）
        /// </summary>
        public double AverageUptime { get; set; }

        /// <summary>
        /// 总停机时间
        /// </summary>
        public TimeSpan TotalDowntime { get; set; }
    }

    /// <summary>
    /// 导出统计请求DTO
    /// </summary>
    public class ExportStatisticsRequest
    {
        /// <summary>
        /// 导出格式
        /// </summary>
        [Required]
        public ExportFormat Format { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 包含的统计类型
        /// </summary>
        public List<StatisticsType> IncludeTypes { get; set; } = new();
    }

    /// <summary>
    /// 导出统计响应DTO
    /// </summary>
    public class ExportStatisticsResponse
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件数据（Base64编码）
        /// </summary>
        public string Data { get; set; } = string.Empty;

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; }
    }

    #endregion

    #region 枚举

    /// <summary>
    /// 时间分组方式
    /// </summary>
    public enum TimeGroupBy
    {
        /// <summary>
        /// 按分钟
        /// </summary>
        Minute,

        /// <summary>
        /// 按小时
        /// </summary>
        Hour,

        /// <summary>
        /// 按天
        /// </summary>
        Day,

        /// <summary>
        /// 按周
        /// </summary>
        Week,

        /// <summary>
        /// 按月
        /// </summary>
        Month
    }

    /// <summary>
    /// 导出格式
    /// </summary>
    public enum ExportFormat
    {
        /// <summary>
        /// JSON格式
        /// </summary>
        Json,

        /// <summary>
        /// CSV格式
        /// </summary>
        Csv,

        /// <summary>
        /// Excel格式
        /// </summary>
        Excel
    }

    /// <summary>
    /// 统计类型
    /// </summary>
    public enum StatisticsType
    {
        /// <summary>
        /// 连接统计
        /// </summary>
        Connection,

        /// <summary>
        /// 消息统计
        /// </summary>
        Message,

        /// <summary>
        /// 会话统计
        /// </summary>
        Session,

        /// <summary>
        /// 主题统计
        /// </summary>
        Topic,

        /// <summary>
        /// 性能统计
        /// </summary>
        Performance,

        /// <summary>
        /// 认证统计
        /// </summary>
        Authentication
    }

    #endregion

    #region 结果封装

    /// <summary>
    /// 统计结果封装
    /// </summary>
    public class StatisticsResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 成功结果
        /// </summary>
        public static StatisticsResult Success(string message, object? data = null)
        {
            return new StatisticsResult
            {
                IsSuccess = true,
                Message = message,
                Data = data
            };
        }

        /// <summary>
        /// 错误结果
        /// </summary>
        public static StatisticsResult Error(string errorMessage)
        {
            return new StatisticsResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    #endregion
} 