// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 设备历史数据
/// </summary>
[SugarTable("DEVICE_DATA_HISTORY")]
public partial class DeviceDataHistoryEntity : BaseEntity
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    [SugarColumn(ColumnName = "PROPERTY_NAME")]
    public string PropertyName { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    [SugarColumn(ColumnName = "DATA_TYPE")]
    public int DataType { get; set; }

    /// <summary>
    /// 属性值
    /// </summary>
    [SugarColumn(ColumnName = "PROPERTY_VALUE")]
    public string PropertyValue { get; set; }

    /// <summary>
    /// 数据时间
    /// </summary>
    [SugarColumn(ColumnName = "DATA_TIME")]
    public DateTime DataTime { get; set; }
}