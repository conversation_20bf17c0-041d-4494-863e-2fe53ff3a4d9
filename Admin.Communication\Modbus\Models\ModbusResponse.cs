// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Communication.Modbus.Models;

/// <summary>
/// Modbus响应数据模型
/// </summary>
public class ModbusResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 原始响应数据
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 解析后的寄存器值 (寄存器地址 -> 值)
    /// </summary>
    public Dictionary<int, object> RegisterValues { get; set; } = new();

    /// <summary>
    /// 指令ID
    /// </summary>
    public long InstructionId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 响应时间
    /// </summary>
    public DateTime ResponseTime { get; set; } = DateTime.UtcNow;
}
