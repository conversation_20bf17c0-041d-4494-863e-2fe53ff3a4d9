using Admin.Application.DeviceServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;

namespace Admin.Application.DeviceServices;

/// <summary>
/// 设备参数管理服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class DeviceParaService(ISqlSugarClient db, Repository<DeviceParaEntity> repository, ILogger<DeviceParaService> logger) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<DeviceParaEntity> _repository = repository;
    private readonly ILogger<DeviceParaService> _logger = logger;

    /// <summary>
    /// 分页查询设备参数
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>设备参数分页列表</returns>
    public async Task<PagedList<DeviceParaOutput>> QueryAsync(DeviceParaQueryInput input)
    {
        var query = _db.Queryable<DeviceParaEntity>()
            .WhereIF(input.DeviceId.HasValue, d => d.DeviceId == input.DeviceId!.Value)
            .WhereIF(!string.IsNullOrEmpty(input.Name), d => d.Name.Contains(input.Name!))
            .WhereIF(input.DataType.HasValue, d => d.DataType == input.DataType!.Value)
            .WhereIF(input.MonitorStatus.HasValue, d => d.MonitorStatus == input.MonitorStatus!.Value)
            .WhereIF(input.IsSave.HasValue, d => d.IsSave == input.IsSave!.Value)
            .OrderBy(d => d.DeviceId)
            .OrderBy(d => d.Sort);

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList.Adapt<PagedList<DeviceParaOutput>>();
    }

    /// <summary>
    /// 根据ID获取设备参数
    /// </summary>
    /// <param name="parameterId">参数ID</param>
    /// <returns>设备参数信息</returns>
    public async Task<DeviceParaOutput> GetByIdAsync(long parameterId)
    {
        var entity = await _repository.GetByIdAsync(parameterId) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
        return entity.Adapt<DeviceParaOutput>();
    }

    /// <summary>
    /// 根据设备ID获取参数列表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备参数列表</returns>
    public async Task<List<DeviceParaOutput>> GetByDeviceIdAsync(long deviceId)
    {
        var entities = await _db.Queryable<DeviceParaEntity>()
            .Where(d => d.DeviceId == deviceId)
            .OrderBy(d => d.InstructionId)
            .OrderBy(d => d.Sort)
            .ToListAsync();

        return entities.Adapt<List<DeviceParaOutput>>();
    }

    /// <summary>
    /// 根据设备ID获取简单参数列表（用于下拉选择）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备参数简单列表</returns>
    public async Task<List<DeviceParaSimpleOutput>> GetSimpleListByDeviceIdAsync(long deviceId)
    {
        var entities = await _db.Queryable<DeviceParaEntity>()
            .Where(d => d.DeviceId == deviceId && d.MonitorStatus == 1)
            .OrderBy(d => d.InstructionId)
            .OrderBy(d => d.Sort)
            .ToListAsync();

        return entities.Adapt<List<DeviceParaSimpleOutput>>();
    }

    /// <summary>
    /// 添加设备参数
    /// </summary>
    /// <param name="input">设备参数信息</param>
    /// <returns>设备参数信息</returns>
    public async Task<DeviceParaOutput> AddAsync(AddDeviceParaInput input)
    {
        // 验证设备是否存在
        await ValidateDeviceExistsAsync(input.DeviceId);

        // 验证参数标识符在同一设备下的唯一性
        await ValidateParameterKeyUniqueAsync(input.DeviceId, input.Key);

        // 创建设备参数实体
        var entity = input.Adapt<DeviceParaEntity>();

        // 保存到数据库
        var parameterId = await _repository.InsertReturnSnowflakeIdAsync(entity);
        entity.ParameterId = parameterId;

        _logger.LogInformation("设备参数添加成功: DeviceId={DeviceId}, ParameterId={ParameterId}, Name={Name}",
            input.DeviceId, parameterId, input.Name);

        return entity.Adapt<DeviceParaOutput>();
    }

    /// <summary>
    /// 更新设备参数
    /// </summary>
    /// <param name="input">设备参数信息</param>
    /// <returns>设备参数信息</returns>
    public async Task<DeviceParaOutput> UpdateAsync(UpdateDeviceParaInput input)
    {
        // 验证参数是否存在
        var entity = await _repository.GetByIdAsync(input.ParameterId) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

        // 验证设备是否存在
        await ValidateDeviceExistsAsync(input.DeviceId);

        // 验证参数标识符在同一设备下的唯一性（排除当前参数）
        await ValidateParameterKeyUniqueAsync(input.DeviceId, input.Key, input.ParameterId);

        // 更新实体属性
        var updatedEntity = input.Adapt<DeviceParaEntity>();
        await _repository.UpdateAsync(updatedEntity);

        _logger.LogInformation("设备参数更新成功: ParameterId={ParameterId}, Name={Name}",
            input.ParameterId, input.Name);

        return updatedEntity.Adapt<DeviceParaOutput>();
    }

    /// <summary>
    /// 删除设备参数
    /// </summary>
    /// <param name="parameterId">参数ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(long parameterId)
    {
        var entity = await _repository.GetByIdAsync(parameterId) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

        var result = await _repository.DeleteAsync(entity);

        if (result)
        {
            _logger.LogInformation("设备参数删除成功: ParameterId={ParameterId}, Name={Name}",
                parameterId, entity.Name);
        }

        return result;
    }

    /// <summary>
    /// 批量删除设备参数
    /// </summary>
    /// <param name="input">批量删除输入</param>
    /// <returns>删除结果</returns>
    public async Task<bool> BatchDeleteAsync(BatchDeleteDeviceParaInput input)
    {
        var entities = await _db.Queryable<DeviceParaEntity>()
            .Where(d => input.ParameterIds.Contains(d.ParameterId))
            .ToListAsync();

        if (entities.Count != input.ParameterIds.Count)
        {
            throw PersistdValidateException.Message("部分参数不存在，无法删除");
        }

        var result = await _db.Deleteable<DeviceParaEntity>()
            .Where(d => input.ParameterIds.Contains(d.ParameterId))
            .ExecuteCommandAsync();

        if (result > 0)
        {
            _logger.LogInformation("批量删除设备参数成功: 删除数量={Count}", result);
        }

        return result > 0;
    }

    /// <summary>
    /// 设置设备参数监控状态
    /// </summary>
    /// <param name="input">设置监控状态输入</param>
    /// <returns>设置结果</returns>
    public async Task<bool> SetMonitorStatusAsync(SetDeviceParaMonitorStatusInput input)
    {
        var result = await _db.Updateable<DeviceParaEntity>()
            .SetColumns(d => d.MonitorStatus == input.MonitorStatus)
            .Where(d => input.ParameterIds.Contains(d.ParameterId))
            .ExecuteCommandAsync();

        if (result > 0)
        {
            _logger.LogInformation("设备参数监控状态设置成功: 参数数量={Count}, 监控状态={MonitorStatus}",
                result, input.MonitorStatus);
        }

        return result > 0;
    }



    /// <summary>
    /// 验证设备是否存在
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    private async Task ValidateDeviceExistsAsync(long deviceId)
    {
        var exists = await _db.Queryable<DeviceEntity>()
            .Where(d => d.Id == deviceId)
            .AnyAsync();

        if (!exists)
        {
            throw PersistdValidateException.Message($"设备不存在: DeviceId={deviceId}");
        }
    }

    /// <summary>
    /// 验证参数标识符在同一设备下的唯一性
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="key">参数标识符</param>
    /// <param name="excludeParameterId">排除的参数ID（用于更新时排除自身）</param>
    private async Task ValidateParameterKeyUniqueAsync(long deviceId, string key, long? excludeParameterId = null)
    {
        var query = _db.Queryable<DeviceParaEntity>()
            .Where(d => d.DeviceId == deviceId && d.JsonKey == key);

        if (excludeParameterId.HasValue)
        {
            query = query.Where(d => d.ParameterId != excludeParameterId.Value);
        }

        var exists = await query.AnyAsync();

        if (exists)
        {
            throw PersistdValidateException.Message($"参数标识符在该设备下已存在: Key={key}");
        }
    }
}
