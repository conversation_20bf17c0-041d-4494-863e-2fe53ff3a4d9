namespace Admin.Multiplex.Contracts.Enums.Mqtt
{
    /// <summary>
    /// MQTT消息确认状态
    /// </summary>
    public enum MqttMessageAckStatusEnum
    {
        /// <summary>
        /// 无需确认
        /// </summary>
        NoAckRequired = 0,

        /// <summary>
        /// 等待PUBACK
        /// </summary>
        WaitingPubAck = 1,

        /// <summary>
        /// 等待PUBREC
        /// </summary>
        WaitingPubRec = 2,

        /// <summary>
        /// 等待PUBREL
        /// </summary>
        WaitingPubRel = 3,

        /// <summary>
        /// 等待PUBCOMP
        /// </summary>
        WaitingPubComp = 4,

        /// <summary>
        /// 未确认
        /// </summary>
        Unacknowledged = 5,

        /// <summary>
        /// 已发送PUBACK
        /// </summary>
        PubAcked = 6,

        /// <summary>
        /// 已发送PUBREC
        /// </summary>
        PubReceived = 7,

        /// <summary>
        /// 已发送PUBREL
        /// </summary>
        PubReleased = 8,

        /// <summary>
        /// 已发送PUBCOMP
        /// </summary>
        PubCompleted = 9,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout = 10,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 11
    }
} 