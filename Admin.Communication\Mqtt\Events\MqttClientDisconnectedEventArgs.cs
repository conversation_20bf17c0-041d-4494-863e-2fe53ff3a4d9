using System;

namespace Admin.Communication.Mqtt.Events
{
    /// <summary>
    /// MQTT客户端断开连接事件参数
    /// </summary>
    public class MqttClientDisconnectedEventArgs : EventArgs
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 是否是由于异常导致的断开连接
        /// </summary>
        public bool WasCleanDisconnect { get; set; }

        /// <summary>
        /// 断开连接的异常信息(如果有)
        /// </summary>
        public Exception Exception { get; set; }
    }
} 