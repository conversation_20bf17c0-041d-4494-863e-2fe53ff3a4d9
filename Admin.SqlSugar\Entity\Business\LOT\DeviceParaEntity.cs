﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;
/// <summary>
/// 设备参数表
/// 包含所有设备所有的参数属性
/// </summary>
[SugarTable("DEVICE_PARA")]
public partial class DeviceParaEntity
{
    /// <summary>
    /// 参数id
    /// </summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public long ParameterId { get; set; }

    /// <summary>
    /// 设备id
    /// </summary> 
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令id
    /// 产品协议为MQTT、数据格式为JSON的设备，指令id为0
    /// </summary>
    [SugarColumn(ColumnName = "INSTRUCTION_ID")]
    public long InstructionId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    [SugarColumn(ColumnName = "NAME")]
    public string Name { get; set; }

    /// <summary>
    /// 参数序号
    /// 产品协议为MQTT、数据格式为JSON的设备，参数序号为0
    /// </summary>
    [SugarColumn(ColumnName = "SORT")]
    public int Sort { get; set; }

    /// <summary>
    /// 参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
    /// 产品协议为Modbus、数据格式为HEX的设备，参数标识符为""
    /// </summary>
    [SugarColumn(ColumnName = "Json_key")]
    public string JsonKey { get; set; }

    /// <summary>
    /// Byte起始地址
    /// 用于Modbus协议解析
    /// </summary>
    [SugarColumn(ColumnName = "START_ADDRESS")]
    public int StartAddress { get; set; }

    /// <summary>
    /// Byte读取数量
    /// </summary>
    [SugarColumn(ColumnName = "START_COUNT")]
    public int StartCount { get; set; }


    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    [SugarColumn(ColumnName = "DATA_TYPE")]
    public int DataType { get; set; }

    /// <summary>
    /// 枚举说明
    /// 0=运行;1=停止
    /// 0=浮冲;1=均冲;2=放电
    /// </summary>
    [SugarColumn(ColumnName = "ENUM_DESCRIPTION")]
    public string EnumDescription { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "UNIT")]
    public string Unit { get; set; }


    /// <summary>
    /// 倍率
    /// </summary>
    [SugarColumn(ColumnName = "DIVISION_FACTOR")]
    public int DivisionFactor { get; set; }

    /// <summary>
    /// 小数位数
    /// </summary>
    [SugarColumn(ColumnName = "DECIMAL_PLACES")]
    public int DecimalPlaces { get; set; }

    /// <summary>
    /// 校正比例
    /// 解析值*校正比例+校正幅度，默认为1
    /// </summary>
    [SugarColumn(ColumnName = "CORRECTION_SCALE")]
    public decimal CorrectionScale { get; set; }

    /// <summary>
    /// 校正幅度
    /// 默认为0
    /// </summary>
    [SugarColumn(ColumnName = "CORRECTION_AMPLITUDE")]
    public decimal CorrectionAmplitude { get; set; }


    /// <summary>
    /// 报警上限
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_UPPER_LIMIT")]
    public decimal? AlarmUpperLimit { get; set; }

    /// <summary>
    /// 报警上限解除值  
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_UPPER_LIMIT_CLEAR_VALUE")]
    public decimal AlarmUpperLimitClearValue { get; set; }

    /// <summary>
    /// 报警下限
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_LOWER_LIMIT")]
    public decimal? AlarmLowerLimit { get; set; }

    /// <summary>
    /// 报警下限解除值
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_LOWER_LIMIT_CLEAR_VALUE")]
    public decimal AlarmLowerLimitClearValue { get; set; }

    /// <summary>
    /// 预警上限
    /// </summary>
    [SugarColumn(ColumnName = "WARNING_UPPER_LIMIT")]
    public decimal? WarningUpperLimit { get; set; }

    /// <summary>
    /// 预警上限解除值
    /// </summary>
    [SugarColumn(ColumnName = "WARNING_UPPER_LIMIT_CLEAR_VALUE")]
    public decimal? WarningUpperLimitClearValue { get; set; }

    /// <summary>
    /// 预警下限
    /// </summary>
    [SugarColumn(ColumnName = "WARNING_LOWER_LIMIT")]
    public decimal? WarningLowerLimit { get; set; }

    /// <summary>
    /// 预警下限解除值
    /// </summary>
    [SugarColumn(ColumnName = "WARNING_LOWER_LIMIT_CLEAR_VALUE")]
    public decimal? WarningLowerLimitClearValue { get; set; }


    /// <summary>
    /// 是否保存
    /// </summary>
    [SugarColumn(ColumnName = "IS_SAVE")]
    public bool IsSave { get; set; } = true;

    /// <summary>
    /// 保存幅度 
    /// 与上次保存值的偏差超过幅度，触发保存
    /// </summary>
    [SugarColumn(ColumnName = "SAVE_AMPLITUDE")]
    public decimal SaveAmplitude { get; set; }

    /// <summary>
    /// 保存幅度类型
    /// 0: 数值 1：百分比
    /// 默认数值
    /// </summary>
    [SugarColumn(ColumnName = "SAVE_AMPLITUDE_TYPE")]
    public int SaveAmplitudeType { get; set; }

    /// <summary>
    /// 保存间隔(毫秒)
    /// </summary>
    [SugarColumn(ColumnName = "SAVE_INTERVAL")]
    public int SaveInterval { get; set; }

    /// <summary>
    /// 告警等级 (1:紧急 2:严重 3：一般 4：预警)
    /// </summary>
    [SugarColumn(ColumnName = "ALARM_LEVEL")]
    public int AlarmLevel { get; set; }

    /// <summary>
    /// 监控状态
    /// 0: 不启用 1：启用
    /// </summary>
    [SugarColumn(ColumnName = "MONITOR_STATUS")]
    public int MonitorStatus { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    [SugarColumn(ColumnName = "DESCRIPTION")]
    public string Description { get; set; }
}
