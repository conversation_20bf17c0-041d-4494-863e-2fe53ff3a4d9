// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Multiplex.Contracts.BackgroundArgs;

/// <summary>
/// MQTT消息重试后台作业参数
/// </summary>
public class MqttMessageRetryArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }
    
    /// <summary>
    /// 消息ID
    /// </summary>
    public ushort MessageId { get; set; }
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
}
