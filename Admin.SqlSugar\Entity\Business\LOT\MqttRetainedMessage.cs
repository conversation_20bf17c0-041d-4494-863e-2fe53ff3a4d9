﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;
/// <summary>
/// MQTT保留消息实体
/// </summary>
[SugarTable("mqtt_retained_messages", "MQTT保留消息表")]
public class MqttRetainedMessage
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(ColumnName = "Id", ColumnDescription = "主键ID", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 消息主题
    /// </summary>
    [SugarColumn(ColumnName = "Topic", ColumnDescription = "消息主题", Length = 256, IsNullable = false)]
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 消息负载内容
    /// </summary>
    [SugarColumn(ColumnName = "Payload", ColumnDescription = "消息负载内容", ColumnDataType = "LONGTEXT")]
    public string? Payload { get; set; }

    /// <summary>
    /// 服务质量等级(0-2)
    /// </summary>
    [SugarColumn(ColumnName = "Qos", ColumnDescription = "服务质量等级(0-2)", IsNullable = false)]
    public int Qos { get; set; }

    /// <summary>
    /// 负载编码方式
    /// </summary>
    [SugarColumn(ColumnName = "Encoding", ColumnDescription = "负载编码方式", Length = 20, IsNullable = false)]
    public string Encoding { get; set; } = "Utf8";

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    [SugarColumn(ColumnName = "PublisherId", ColumnDescription = "发布者客户端ID", Length = 100)]
    public string? PublisherId { get; set; }

    /// <summary>
    /// 负载大小(字节)
    /// </summary>
    [SugarColumn(ColumnName = "PayloadSize", ColumnDescription = "负载大小(字节)", IsNullable = false)]
    public int PayloadSize { get; set; }

    /// <summary>
    /// 消息保留时间
    /// </summary>
    [SugarColumn(ColumnName = "RetainTime", ColumnDescription = "消息保留时间", IsNullable = false)]
    public DateTime RetainTime { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [SugarColumn(ColumnName = "LastUpdateTime", ColumnDescription = "最后更新时间", IsNullable = false)]
    public DateTime LastUpdateTime { get; set; }
}
