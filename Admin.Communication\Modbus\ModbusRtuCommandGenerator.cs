// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Communication.Modbus;

/// <summary>
/// Modbus RTU 指令生成器
/// </summary>
public static class ModbusRtuCommandGenerator
{
    /// <summary>
    /// 生成Modbus RTU读取指令
    /// 指令组成：ModbusAddr + FunctionCode + StartAddress + ReadCount + CRC16
    /// </summary>
    /// <param name="modbusAddr">设备地址 (1-247)</param>
    /// <param name="functionCode">功能码</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="readCount">读取数量</param>
    /// <returns>16进制格式的Modbus RTU指令</returns>
    public static string GenerateReadCommand(byte modbusAddr, byte functionCode, ushort startAddress, ushort readCount)
    {
        // 验证参数
        if (modbusAddr == 0 || modbusAddr > 247)
            throw new ArgumentOutOfRangeException(nameof(modbusAddr), "Modbus地址必须在1-247范围内");

        // 构建指令字节数组（不包含CRC）
        var commandBytes = new List<byte>
        {
            modbusAddr,
            functionCode
        };

        // 添加起始地址（高字节在前，大端序）
        commandBytes.Add((byte)(startAddress >> 8));
        commandBytes.Add((byte)(startAddress & 0xFF));

        // 添加读取数量（高字节在前，大端序）
        commandBytes.Add((byte)(readCount >> 8));
        commandBytes.Add((byte)(readCount & 0xFF));

        // 添加CRC16校验码
        var commandWithCrc = ModbusCrcCalculator.AppendCrc16(commandBytes.ToArray());

        // 转换为16进制字符串
        return BitConverter.ToString(commandWithCrc).Replace("-", "");
    }

    /// <summary>
    /// 生成Modbus RTU读取指令（使用int参数）
    /// </summary>
    /// <param name="modbusAddr">设备地址</param>
    /// <param name="functionCode">功能码</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="readCount">读取数量</param>
    /// <returns>16进制格式的Modbus RTU指令</returns>
    public static string GenerateReadCommand(int modbusAddr, int functionCode, int startAddress, int readCount)
    {
        return GenerateReadCommand(
            (byte)modbusAddr,
            (byte)functionCode,
            (ushort)startAddress,
            (ushort)readCount
        );
    }

    /// <summary>
    /// 生成Modbus RTU写单个寄存器指令
    /// 指令组成：ModbusAddr + 0x06 + RegisterAddress + RegisterValue + CRC16
    /// </summary>
    /// <param name="modbusAddr">设备地址</param>
    /// <param name="registerAddress">寄存器地址</param>
    /// <param name="registerValue">寄存器值</param>
    /// <returns>16进制格式的Modbus RTU指令</returns>
    public static string GenerateWriteSingleRegisterCommand(byte modbusAddr, ushort registerAddress, ushort registerValue)
    {
        const byte functionCode = 0x06; // 写单个寄存器

        var commandBytes = new List<byte>
        {
            modbusAddr,
            functionCode
        };

        // 添加寄存器地址（高字节在前）
        commandBytes.Add((byte)(registerAddress >> 8));
        commandBytes.Add((byte)(registerAddress & 0xFF));

        // 添加寄存器值（高字节在前）
        commandBytes.Add((byte)(registerValue >> 8));
        commandBytes.Add((byte)(registerValue & 0xFF));

        // 添加CRC16校验码
        var commandWithCrc = ModbusCrcCalculator.AppendCrc16(commandBytes.ToArray());

        return BitConverter.ToString(commandWithCrc).Replace("-", "");
    }

    /// <summary>
    /// 生成Modbus RTU写多个寄存器指令
    /// 指令组成：ModbusAddr + 0x10 + StartAddress + RegisterCount + ByteCount + RegisterValues + CRC16
    /// </summary>
    /// <param name="modbusAddr">设备地址</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="registerValues">寄存器值数组</param>
    /// <returns>16进制格式的Modbus RTU指令</returns>
    public static string GenerateWriteMultipleRegistersCommand(byte modbusAddr, ushort startAddress, ushort[] registerValues)
    {
        if (registerValues == null || registerValues.Length == 0)
            throw new ArgumentException("寄存器值数组不能为空", nameof(registerValues));

        const byte functionCode = 0x10; // 写多个寄存器
        var registerCount = (ushort)registerValues.Length;
        var byteCount = (byte)(registerCount * 2);

        var commandBytes = new List<byte>
        {
            modbusAddr,
            functionCode
        };

        // 添加起始地址（高字节在前）
        commandBytes.Add((byte)(startAddress >> 8));
        commandBytes.Add((byte)(startAddress & 0xFF));

        // 添加寄存器数量（高字节在前）
        commandBytes.Add((byte)(registerCount >> 8));
        commandBytes.Add((byte)(registerCount & 0xFF));

        // 添加字节数
        commandBytes.Add(byteCount);

        // 添加寄存器值（每个寄存器2字节，高字节在前）
        foreach (var value in registerValues)
        {
            commandBytes.Add((byte)(value >> 8));
            commandBytes.Add((byte)(value & 0xFF));
        }

        // 添加CRC16校验码
        var commandWithCrc = ModbusCrcCalculator.AppendCrc16(commandBytes.ToArray());

        return BitConverter.ToString(commandWithCrc).Replace("-", "");
    }

    /// <summary>
    /// 解析16进制指令字符串为字节数组
    /// </summary>
    /// <param name="hexCommand">16进制指令字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] ParseHexCommand(string hexCommand)
    {
        if (string.IsNullOrWhiteSpace(hexCommand))
            throw new ArgumentException("指令字符串不能为空", nameof(hexCommand));

        // 移除空格和连字符
        hexCommand = hexCommand.Replace(" ", "").Replace("-", "");

        if (hexCommand.Length % 2 != 0)
            throw new ArgumentException("16进制字符串长度必须为偶数", nameof(hexCommand));

        var bytes = new byte[hexCommand.Length / 2];
        for (int i = 0; i < bytes.Length; i++)
        {
            bytes[i] = Convert.ToByte(hexCommand.Substring(i * 2, 2), 16);
        }

        return bytes;
    }

    /// <summary>
    /// 验证Modbus RTU指令的CRC16校验码
    /// </summary>
    /// <param name="hexCommand">16进制指令字符串</param>
    /// <returns>校验是否通过</returns>
    public static bool VerifyCommand(string hexCommand)
    {
        try
        {
            var bytes = ParseHexCommand(hexCommand);
            return ModbusCrcCalculator.VerifyCrc16(bytes);
        }
        catch
        {
            return false;
        }
    }
}
