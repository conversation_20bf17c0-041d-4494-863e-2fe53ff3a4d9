// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Multiplex.Contracts.ControlCmd;

/// <summary>
/// 控制响应处理器接口
/// </summary>
public interface IControlResponseHandler
{
    /// <summary>
    /// 处理收到的响应消息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="payload">响应载荷</param>
    Task HandleControlResponseAsync(long deviceId, string payload);

    /// <summary>
    /// 清理超时的等待响应
    /// </summary>
    Task CleanupTimeoutResponsesAsync();
}
