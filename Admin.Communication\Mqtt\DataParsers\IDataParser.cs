using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Communication.Mqtt.DataParsers
{
    /// <summary>
    /// 数据解析器接口
    /// </summary>
    public interface IDataParser
    {
        /// <summary>
        /// 支持的数据格式
        /// </summary>
        DataFormatEnum SupportedFormat { get; }

        /// <summary>
        /// 解析器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 解析器描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 是否可以解析指定的载荷数据
        /// </summary>
        /// <param name="payload">载荷数据</param>
        /// <param name="context">解析上下文</param>
        /// <returns>是否可以解析</returns>
        bool CanParse(byte[] payload, DeviceParseContext context);

        /// <summary>
        /// 异步解析设备数据
        /// </summary>
        /// <param name="payload">载荷数据</param>
        /// <param name="context">解析上下文</param>
        /// <returns>解析结果</returns>
        Task<DeviceDataParseResult> ParseAsync(byte[] payload, DeviceParseContext context);
    }

    /// <summary>
    /// 设备解析上下文
    /// </summary>
    public class DeviceParseContext
    {
        /// <summary>
        /// 设备信息
        /// </summary>
        public DeviceEntity Device { get; set; } = null!;

        /// <summary>
        /// 产品模型信息
        /// </summary>
        public ProductModelEntity ProductModel { get; set; } = null!;

        /// <summary>
        /// 产品信息
        /// </summary>
        public ProductEntity Product { get; set; } = null!;

        /// <summary>
        /// 模型属性列表
        /// </summary>
        public List<ModelPropertyEntity> ModelProperties { get; set; } = new();

        /// <summary>
        /// 主题信息
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 是否为网关设备
        /// </summary>
        public bool IsGatewayDevice { get; set; }

        /// <summary>
        /// 主题配置信息（如果是配置主题）
        /// </summary>
        public DeviceTopicConfigEntity? TopicConfig { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 设备数据解析结果
    /// </summary>
    public class DeviceDataParseResult
    {
        /// <summary>
        /// 是否解析成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 解析出的设备数据项
        /// </summary>
        public List<DeviceDataItem> DataItems { get; set; } = new();

        /// <summary>
        /// 解析的原始数据
        /// </summary>
        public string? RawData { get; set; }

        /// <summary>
        /// 解析耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="dataItems">数据项列表</param>
        /// <param name="rawData">原始数据</param>
        /// <returns>成功结果</returns>
        public static DeviceDataParseResult Success(List<DeviceDataItem> dataItems, string? rawData = null)
        {
            return new DeviceDataParseResult
            {
                IsSuccess = true,
                DataItems = dataItems ?? new List<DeviceDataItem>(),
                RawData = rawData
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="exception">异常信息</param>
        /// <returns>失败结果</returns>
        public static DeviceDataParseResult Failure(string errorMessage, Exception? exception = null)
        {
            return new DeviceDataParseResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Exception = exception
            };
        }
    }

    /// <summary>
    /// 设备数据项
    /// </summary>
    public class DeviceDataItem
    {
        /// <summary>
        /// 属性键
        /// </summary>
        public string PropertyKey { get; set; } = string.Empty;

        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropertyName { get; set; } = string.Empty;

        /// <summary>
        /// 属性值
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public int DataType { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 模型属性ID
        /// </summary>
        public long? PropertyId { get; set; }

        /// <summary>
        /// 是否为已定义属性
        /// </summary>
        public bool IsDefined { get; set; }

        /// <summary>
        /// 数据质量状态
        /// </summary>
        public int DataStatus { get; set; } = 1;

        /// <summary>
        /// 数据时间
        /// </summary>
        public DateTime DataTime { get; set; } = DateTime.Now;
    }
}
