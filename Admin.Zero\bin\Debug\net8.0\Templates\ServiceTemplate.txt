﻿
using @NameSpace@.Dtos;

namespace @NameSpace@;
/// <summary>
/// @ClassName@服务
/// </summary>
public class @ClassName@Service(ISqlSugarClient db) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagedList<@ClassName@Output>> GetPagedListAsync(GetPagedListInput input)
    {
        var pagedList = await _db.Queryable<@ClassName@Entity>().ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList.Adapt<PagedList<@ClassName@Output>>();
    }

    /// <summary>
    /// 单条查询
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<@ClassName@Output> GetAsync(long id)
    {
        var entity = await _db.Queryable<@ClassName@Entity>().FirstAsync(x => x.Id == id);
        return entity.Adapt<@ClassName@Output>();
    }

    /// <summary>
    /// 添加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<long> AddAsync(Add@ClassName@Input input)
    {
        var entity = input.Adapt<@ClassName@Entity>();
        return await _db.Insertable(entity).ExecuteReturnSnowflakeIdAsync();
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task PutAsync(long id, Put@ClassName@Input input)
    {
        var entity = await _db.Queryable<@ClassName@Entity>().FirstAsync(x => x.Id == id) ?? throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
        var newEntity = input.Adapt(entity);
        _ = await _db.Updateable(newEntity).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task DeleteAsync(long id)
    {
        var entity = await _db.Queryable<@ClassName@Entity>().FirstAsync(x => x.Id == id) ?? throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
        _ = await _db.Deleteable(entity).ExecuteCommandAsync();
    }
}
