﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Models.Results;
/// <summary>
/// 认证结果
/// </summary>
public class AuthenticationResult
{
    /// <summary>
    /// 是否认证成功
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 认证失败原因
    /// </summary>
    public string FailureReason { get; set; }

    /// <summary>
    /// 用户角色
    /// </summary>
    public string[] Roles { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static AuthenticationResult Success(params string[] roles)
    {
        return new AuthenticationResult
        {
            IsAuthenticated = true,
            Roles = roles ?? Array.Empty<string>()
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static AuthenticationResult Failure(string reason)
    {
        return new AuthenticationResult
        {
            IsAuthenticated = false,
            FailureReason = reason
        };
    }
}
