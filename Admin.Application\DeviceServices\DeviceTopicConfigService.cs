// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.DeviceServices.Dto;
using Admin.Application.MqttBrokerServices;
using Admin.Application.MqttBrokerServices.Dto;
using Admin.Communication.Mqtt.Configuration;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;

namespace Admin.Application.DeviceServices;

/// <summary>
/// 设备主题配置服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class DeviceTopicConfigService(ISqlSugarClient db, Repository<DeviceTopicConfigEntity> repository, IMqttAclManagementService mqttAclService, ILogger<DeviceTopicConfigService> logger) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<DeviceTopicConfigEntity> _repository = repository;
    private readonly IMqttAclManagementService _mqttAclService = mqttAclService;
    private readonly ILogger<DeviceTopicConfigService> _logger = logger;

    /// <summary>
    /// 分页查询设备主题配置
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页结果</returns>
    public async Task<PagedList<DeviceTopicConfigOutput>> GetPagedListAsync(DeviceTopicConfigQueryInput input)
    {
        var query = _db.Queryable<DeviceTopicConfigEntity>()
            .WhereIF(!input.DeviceId.IsNullOrEmpty(), x => x.DeviceId.Contains(input.DeviceId))
            .WhereIF(!input.TemplateName.IsNullOrEmpty(), x => x.Name.Contains(input.TemplateName))
            .WhereIF(!input.Topic.IsNullOrEmpty(), x => x.Topic.Contains(input.Topic))
            .WhereIF(input.AccessType.HasValue, x => x.AccessType == input.AccessType.Value)
            .OrderBy(x => x.Priority)
            .OrderBy(x => x.CreateTime, OrderByType.Desc);

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList.Adapt<PagedList<DeviceTopicConfigOutput>>();
    }

    /// <summary>
    /// 根据ID获取设备主题配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <returns>设备主题配置信息</returns>
    public async Task<DeviceTopicConfigOutput> GetByIdAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

        return entity.Adapt<DeviceTopicConfigOutput>();
    }

    /// <summary>
    /// 根据设备ID获取主题配置列表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>主题配置列表</returns>
    public async Task<List<DeviceTopicConfigOutput>> GetByDeviceIdAsync(string deviceId)
    {
        if (string.IsNullOrEmpty(deviceId))
            throw PersistdValidateException.Message("设备ID不能为空");

        var entities = await _db.Queryable<DeviceTopicConfigEntity>()
            .Where(x => x.DeviceId == deviceId)
            .OrderBy(x => x.Priority)
            .ToListAsync();

        return entities.Adapt<List<DeviceTopicConfigOutput>>();
    }

    /// <summary>
    /// 添加设备主题配置
    /// </summary>
    /// <param name="input">配置信息</param>
    /// <returns>配置实体信息</returns>
    public async Task<DeviceTopicConfigOutput> AddAsync(AddDeviceTopicConfigInput input)
    {
        _logger.LogInformation("开始添加设备主题配置: DeviceId={DeviceId}, TemplateName={TemplateName}, Topic={Topic}", 
            input.DeviceId, input.TemplateName, input.Topic);

        // 验证设备是否存在
        await ValidateDeviceExistsAsync(input.DeviceId);

        // 验证主题格式
        ValidateTopicFormat(input.Topic);

        // 检查同一设备下模板名称是否重复
        await ValidateTemplateNameUniqueAsync(input.DeviceId, input.TemplateName);

        // 检查同一设备下主题是否重复
        await ValidateTopicUniqueAsync(input.DeviceId, input.Topic);

        var entity = input.Adapt<DeviceTopicConfigEntity>();
        var configId = await _repository.InsertReturnSnowflakeIdAsync(entity);
        entity.Id = configId;

        // 更新设备实体的CustomPublishTopic属性
        await UpdateDeviceCustomPublishTopicAsync(input.DeviceId, input.Topic);

        // 创建对应的MQTT ACL规则
        await CreateTopicAclRuleAsync(input.DeviceId, input.Topic, input.AccessType);

        _logger.LogInformation("设备主题配置添加成功: ConfigId={ConfigId}, DeviceId={DeviceId}", configId, input.DeviceId);
        return entity.Adapt<DeviceTopicConfigOutput>();
    }

    /// <summary>
    /// 删除设备主题配置
    /// </summary>
    /// <param name="id">配置ID</param>
    public async Task DeleteAsync(long id)
    {
        _logger.LogInformation("开始删除设备主题配置: ConfigId={ConfigId}", id);

        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

        await _repository.DeleteAsync(entity);

        _logger.LogInformation("设备主题配置删除成功: ConfigId={ConfigId}, DeviceId={DeviceId}", id, entity.DeviceId);
    }

    /// <summary>
    /// 批量删除设备主题配置
    /// </summary>
    /// <param name="ids">配置ID列表</param>
    public async Task BatchDeleteAsync(List<long> ids)
    {
        if (ids == null || ids.Count == 0)
            throw PersistdValidateException.Message("配置ID列表不能为空");

        _logger.LogInformation("开始批量删除设备主题配置: Count={Count}", ids.Count);

        var entities = await _db.Queryable<DeviceTopicConfigEntity>()
            .Where(x => ids.Contains(x.Id))
            .ToListAsync();

        if (entities.Count != ids.Count)
        {
            throw PersistdValidateException.Message("部分配置不存在，无法删除");
        }

        await _repository.DeleteAsync(entities);

        _logger.LogInformation("批量删除设备主题配置成功: Count={Count}", entities.Count);
    }



    /// <summary>
    /// 根据设备ID删除所有主题配置
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    public async Task DeleteByDeviceIdAsync(string deviceId)
    {
        if (string.IsNullOrEmpty(deviceId))
            throw PersistdValidateException.Message("设备ID不能为空");

        _logger.LogInformation("开始删除设备的所有主题配置: DeviceId={DeviceId}", deviceId);

        var deleteCount = await _db.Deleteable<DeviceTopicConfigEntity>()
            .Where(x => x.DeviceId == deviceId)
            .ExecuteCommandAsync();

        _logger.LogInformation("删除设备主题配置完成: DeviceId={DeviceId}, 删除数量={Count}", deviceId, deleteCount);
    }



    /// <summary>
    /// 验证设备是否存在
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    private async Task ValidateDeviceExistsAsync(string deviceId)
    {
        var deviceExists = await _db.Queryable<DeviceEntity>()
            .Where(x => x.DeviceId == deviceId)
            .AnyAsync();

        if (!deviceExists)
        {
            throw PersistdValidateException.Message($"设备不存在: {deviceId}");
        }
    }

    /// <summary>
    /// 验证主题格式
    /// </summary>
    /// <param name="topic">主题</param>
    private static void ValidateTopicFormat(string topic)
    {
        if (string.IsNullOrEmpty(topic))
            throw PersistdValidateException.Message("主题不能为空");

        // 基本的MQTT主题格式验证
        if (topic.Contains("//") || topic.StartsWith("/") == false)
        {
            throw PersistdValidateException.Message("主题格式不正确，必须以'/'开头且不能包含连续的'/'");
        }

        // 检查是否包含非法字符
        var invalidChars = new[] { '+', '#' };
        if (topic.IndexOfAny(invalidChars) >= 0)
        {
            throw PersistdValidateException.Message("主题不能包含通配符字符 '+' 或 '#'");
        }
    }

    /// <summary>
    /// 验证模板名称唯一性
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="templateName">模板名称</param>
    /// <param name="excludeId">排除的配置ID（用于更新时）</param>
    private async Task ValidateTemplateNameUniqueAsync(string deviceId, string templateName, long? excludeId = null)
    {
        var query = _db.Queryable<DeviceTopicConfigEntity>()
            .Where(x => x.DeviceId == deviceId && x.Name == templateName);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var exists = await query.AnyAsync();
        if (exists)
        {
            throw PersistdValidateException.Message($"设备 {deviceId} 下已存在模板名称 '{templateName}'");
        }
    }

    /// <summary>
    /// 验证主题唯一性
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="topic">主题</param>
    /// <param name="excludeId">排除的配置ID（用于更新时）</param>
    private async Task ValidateTopicUniqueAsync(string deviceId, string topic, long? excludeId = null)
    {
        var query = _db.Queryable<DeviceTopicConfigEntity>()
            .Where(x => x.DeviceId == deviceId && x.Topic == topic);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var exists = await query.AnyAsync();
        if (exists)
        {
            throw PersistdValidateException.Message($"设备 {deviceId} 下已存在主题 '{topic}'");
        }
    }

    /// <summary>
    /// 更新设备的CustomPublishTopic属性
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="topic">主题</param>
    private async Task UpdateDeviceCustomPublishTopicAsync(string deviceId, string topic)
    {
        try
        {
            var updateResult = await _db.Updateable<DeviceEntity>()
                .SetColumns(x => x.CustomPublishTopic == topic)
                .Where(x => x.DeviceId == deviceId)
                .ExecuteCommandAsync();

            if (updateResult > 0)
            {
                _logger.LogInformation("更新设备CustomPublishTopic成功: DeviceId={DeviceId}, Topic={Topic}", deviceId, topic);
            }
            else
            {
                _logger.LogWarning("更新设备CustomPublishTopic失败，设备可能不存在: DeviceId={DeviceId}", deviceId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备CustomPublishTopic异常: DeviceId={DeviceId}, Topic={Topic}, Error={Error}",
                deviceId, topic, ex.Message);
            // 不抛出异常，避免影响主题配置的创建
        }
    }

    /// <summary>
    /// 为主题配置创建对应的MQTT ACL规则
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="topic">主题</param>
    /// <param name="accessType">访问类型(0=All, 1=Publish, 2=Subscribe)</param>
    private async Task CreateTopicAclRuleAsync(string deviceId, string topic, int accessType)
    {
        try
        {
            // 根据访问类型确定ACL规则的权限
            var aclAccessType = accessType switch
            {
                0 => MqttAccessType.All,      // All
                1 => MqttAccessType.Publish,  // Publish
                2 => MqttAccessType.Subscribe, // Subscribe
                _ => MqttAccessType.All
            };

            // 创建单个ACL规则
            var aclInput = new AddAclRuleInput
            {
                RuleName = $"TopicConfig_{deviceId}_{Guid.NewGuid():N}",
                ClientId = deviceId,
                Topic = topic,
                AccessType = aclAccessType,
                Allow = true,
                Priority = 100,
                Description = $"设备 {deviceId} 的主题配置ACL规则"
            };

            var aclId = await _mqttAclService.AddAsync(aclInput);

            _logger.LogInformation("主题配置ACL规则创建成功: DeviceId={DeviceId}, Topic={Topic}, AccessType={AccessType}, AclId={AclId}",
                deviceId, topic, accessType, aclId);
        }
        catch (Exception ex)
        {
            // ACL规则创建失败不影响主题配置的创建，只记录警告日志
            _logger.LogWarning(ex, "主题配置ACL规则创建失败: DeviceId={DeviceId}, Topic={Topic}, Error={Error}",
                deviceId, topic, ex.Message);
        }
    }
}
