using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Admin.Communication.Mqtt.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace Admin.Communication.Mqtt.Extensions
{
    /// <summary>
    /// MQTT服务注册扩展
    /// </summary>
    public static class MqttServiceExtensions
    {
        /// <summary>
        /// 添加MQTT代理服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddMqttBroker(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<MqttBrokerOptions>(configuration.GetSection("Mqtt:Broker"));
            services.AddSingleton<IMqttBroker, MqttBrokerService>();
            services.AddSingleton<IMqttConnectionManager, MqttConnectionManager>();
            services.AddHostedService<MqttBrokerHostedService>();
            
            return services;
        }
        
        /// <summary>
        /// 添加完整的MQTT服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddMqttServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddMqttBroker(configuration);
            
            return services;
        }
    }
} 