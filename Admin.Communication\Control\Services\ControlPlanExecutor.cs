// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Control.Interfaces;
using Admin.Communication.Control.Models;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Admin.Communication.Control.Services;

/// <summary>
/// 控制计划执行器接口
/// </summary>
public interface IControlPlanExecutor
{
    /// <summary>
    /// 执行控制计划
    /// </summary>
    /// <param name="planId">计划ID</param>
    /// <param name="source">执行来源</param>
    /// <returns>执行结果</returns>
    Task<BatchExecuteResult> ExecutePlanAsync(long planId, string source = "定时执行");

    /// <summary>
    /// 检查计划是否应该执行（用于定时任务）
    /// </summary>
    /// <param name="planId">计划ID</param>
    /// <returns>是否应该执行</returns>
    Task<bool> ShouldExecutePlanAsync(long planId);
}

/// <summary>
/// 控制计划执行器
/// </summary>
public class ControlPlanExecutor(
    ISqlSugarClient db,
    ICommandExecutor commandExecutor,
    ILogger<ControlPlanExecutor> logger) : IControlPlanExecutor
{
    private readonly ISqlSugarClient _db = db;
    private readonly ICommandExecutor _commandExecutor = commandExecutor;
    private readonly ILogger<ControlPlanExecutor> _logger = logger;

    /// <summary>
    /// 执行控制计划
    /// </summary>
    /// <param name="planId">计划ID</param>
    /// <param name="source">执行来源</param>
    /// <returns>执行结果</returns>
    public async Task<BatchExecuteResult> ExecutePlanAsync(long planId, string source = "定时执行")
    {
        try
        {
            _logger.LogInformation("开始执行控制计划: PlanId={PlanId}, Source={Source}", planId, source);

            // 1. 获取控制计划
            var plan = await GetControlPlanAsync(planId);
            if (plan == null)
            {
                _logger.LogWarning("控制计划不存在: PlanId={PlanId}", planId);
                return new BatchExecuteResult { TotalCount = 0, SuccessCount = 0, FailedCount = 0 };
            }

            // 2. 验证计划是否启用
            if (plan.IsAppControl != 1)
            {
                _logger.LogWarning("控制计划未启用: PlanId={PlanId}, Name={Name}", planId, plan.Name);
                return new BatchExecuteResult { TotalCount = 0, SuccessCount = 0, FailedCount = 0 };
            }

            // 3. 解析指令ID列表
            var commandIds = ParseCommandIds(plan.CmdIds);
            if (commandIds.Count == 0)
            {
                _logger.LogWarning("控制计划没有配置指令: PlanId={PlanId}, Name={Name}", planId, plan.Name);
                return new BatchExecuteResult { TotalCount = 0, SuccessCount = 0, FailedCount = 0 };
            }

            // 4. 执行批量指令
            var batchRequest = new BatchExecuteCommandRequest
            {
                CommandIds = commandIds,
                IntervalMs = plan.CmdInterval,
                Source = $"{source} - {plan.Name}",
                Username = "System"
            };

            var result = await _commandExecutor.BatchExecuteAsync(batchRequest);

            _logger.LogInformation("控制计划执行完成: PlanId={PlanId}, Name={Name}, Total={Total}, Success={Success}, Failed={Failed}",
                planId, plan.Name, result.TotalCount, result.SuccessCount, result.FailedCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行控制计划时发生错误: PlanId={PlanId}", planId);
            return new BatchExecuteResult
            {
                TotalCount = 0,
                SuccessCount = 0,
                FailedCount = 1,
                Results = new List<CommandExecuteResult> { CommandExecuteResult.Failed($"执行控制计划异常: {ex.Message}") }
            };
        }
    }

    /// <summary>
    /// 检查计划是否应该执行（用于定时任务）
    /// </summary>
    /// <param name="planId">计划ID</param>
    /// <returns>是否应该执行</returns>
    public async Task<bool> ShouldExecutePlanAsync(long planId)
    {
        try
        {
            var plan = await GetControlPlanAsync(planId);
            if (plan == null || plan.IsAppControl != 1 || plan.TriggerType != 1)
            {
                return false;
            }

            var now = DateTime.Now;
            return plan.ExecuteType switch
            {
                1 => ShouldExecuteByTimePoint(plan.TimePoint, now), // 时间点执行
                2 => ShouldExecuteByInterval(plan.Interval, plan.IntervalUnit, now), // 间隔执行
                _ => false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查控制计划执行条件时发生错误: PlanId={PlanId}", planId);
            return false;
        }
    }

    /// <summary>
    /// 获取控制计划
    /// </summary>
    private async Task<ControlPlanEntity?> GetControlPlanAsync(long planId)
    {
        return await _db.Queryable<ControlPlanEntity>().Where(cp => cp.Id == planId).FirstAsync();
    }

    /// <summary>
    /// 解析指令ID列表
    /// </summary>
    private static List<long> ParseCommandIds(string cmdIds)
    {
        if (string.IsNullOrEmpty(cmdIds)) return new List<long>();

        var result = new List<long>();
        var idStrings = cmdIds.Split(',', StringSplitOptions.RemoveEmptyEntries);

        foreach (var idString in idStrings)
        {
            if (long.TryParse(idString.Trim(), out var id))
            {
                result.Add(id);
            }
        }
        return result;
    }

    /// <summary>
    /// 检查是否应该按时间点执行
    /// </summary>
    private static bool ShouldExecuteByTimePoint(string? timePoint, DateTime now)
    {
        if (string.IsNullOrEmpty(timePoint)) return false;

        var timePoints = timePoint.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var currentTime = now.ToString("HH:mm");
        return timePoints.Any(tp => tp.Trim() == currentTime);
    }

    /// <summary>
    /// 检查是否应该按间隔执行
    /// </summary>
    private static bool ShouldExecuteByInterval(int? interval, int? intervalUnit, DateTime now)
    {
        if (!interval.HasValue || !intervalUnit.HasValue || interval <= 0) return false;

        var minutes = intervalUnit == 1 ? interval.Value : interval.Value * 60;
        var totalMinutes = now.Hour * 60 + now.Minute;
        return totalMinutes % minutes == 0;
    }
}
