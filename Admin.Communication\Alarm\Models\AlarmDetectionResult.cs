// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Multiplex.Contracts.Enums;

namespace Admin.Communication.Alarm.Models;

/// <summary>
/// 告警检测结果
/// </summary>
public class AlarmDetectionResult
{
    /// <summary>
    /// 是否有告警
    /// </summary>
    public bool HasAlarm { get; set; }

    /// <summary>
    /// 告警级别
    /// </summary>
    public AlarmLevelEnum AlarmLevel { get; set; }

    /// <summary>
    /// 告警类型
    /// </summary>
    public AlarmTypeEnum AlarmType { get; set; }

    /// <summary>
    /// 当前值
    /// </summary>
    public decimal? CurrentValue { get; set; }

    /// <summary>
    /// 触发阈值
    /// </summary>
    public decimal? ThresholdValue { get; set; }

    /// <summary>
    /// 清除阈值
    /// </summary>
    public decimal? ClearValue { get; set; }

    /// <summary>
    /// 是否为上限告警
    /// </summary>
    public bool IsUpperLimit { get; set; }

    /// <summary>
    /// 告警描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 创建无告警结果
    /// </summary>
    public static AlarmDetectionResult NoAlarm(string description = "正常")
    {
        return new AlarmDetectionResult
        {
            HasAlarm = false,
            Description = description
        };
    }

    /// <summary>
    /// 创建参数超限告警结果
    /// </summary>
    public static AlarmDetectionResult ParameterAlarm(
        AlarmLevelEnum level,
        decimal currentValue,
        decimal thresholdValue,
        decimal? clearValue,
        bool isUpperLimit,
        AlarmTypeEnum alarmType)
    {
        return new AlarmDetectionResult
        {
            HasAlarm = true,
            AlarmType = alarmType,
            AlarmLevel = level,
            CurrentValue = currentValue,
            ThresholdValue = thresholdValue,
            ClearValue = clearValue,
            IsUpperLimit = isUpperLimit,
            Description = $"参数{(isUpperLimit ? "上限" : "下限")}超限"
        };
    }

    /// <summary>
    /// 创建通讯失败告警结果
    /// </summary>
    public static AlarmDetectionResult CommunicationFailure(AlarmLevelEnum level = AlarmLevelEnum.Critical)
    {
        return new AlarmDetectionResult
        {
            HasAlarm = true,
            AlarmType = AlarmTypeEnum.CommunicationFailure,
            AlarmLevel = level,
            Description = "通讯失败"
        };
    }
}
