[10:38:28] [ERR]  
SELECT Count(*) FROM `LOT_PRODUCT_MODEL` `pm` Left JOIN `IOT_PRODUCT` `p` ON ( `pm`.`PRODUCT_ID` = `p`.`ID` )   

[10:38:29] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Table 'cms.iot_product' doesn't exist",
  "details": "MySqlException: Table 'cms.iot_product' doesn't exist\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetCountAsync()\r\n   at SqlSugar.QueryableProvider`1.CountAsync()\r\n   at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)\r\n   at Admin.SqlSugar.PagedListExtensions.ToPurestPagedListAsync[TResult](ISugarQueryable`1 queryable, Int32 pageIndex, Int32 pageSize) in D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\PagedListExtensions.cs:line 49\r\n   at Admin.Application.ProductServices.ProductModelService.GetPagedListAsync(ProductModelQueryInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ProductServices\\ProductModelService.cs:line 49\r\n   at lambda_method1353(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1146,
    "SqlState": "42S02"
  },
  "validationErrors": null
}


[10:38:56] [ERR]  
SELECT Count(*) FROM `IOT_PRODUCT`  

[10:38:56] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Table 'cms.iot_product' doesn't exist",
  "details": "MySqlException: Table 'cms.iot_product' doesn't exist\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetCountAsync()\r\n   at SqlSugar.QueryableProvider`1.CountAsync()\r\n   at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)\r\n   at Admin.SqlSugar.PagedListExtensions.ToPurestPagedListAsync[TResult](ISugarQueryable`1 queryable, Int32 pageIndex, Int32 pageSize) in D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\PagedListExtensions.cs:line 49\r\n   at Admin.Application.ProductServices.ProductService.GetPagedListAsync(ProductQueryInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ProductServices\\ProductService.cs:line 31\r\n   at lambda_method1396(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1146,
    "SqlState": "42S02"
  },
  "validationErrors": null
}


[10:39:54] [ERR]  
SELECT Count(*) FROM `LOT_PRODUCT_MODEL` `pm` Left JOIN `IOT_PRODUCT` `p` ON ( `pm`.`PRODUCT_ID` = `p`.`ID` )   

[10:39:54] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Table 'cms.iot_product' doesn't exist",
  "details": "MySqlException: Table 'cms.iot_product' doesn't exist\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetCountAsync()\r\n   at SqlSugar.QueryableProvider`1.CountAsync()\r\n   at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)\r\n   at Admin.SqlSugar.PagedListExtensions.ToPurestPagedListAsync[TResult](ISugarQueryable`1 queryable, Int32 pageIndex, Int32 pageSize)\r\n   at Admin.Application.ProductServices.ProductModelService.GetPagedListAsync(ProductModelQueryInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ProductServices\\ProductModelService.cs:line 49\r\n   at lambda_method1435(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1146,
    "SqlState": "42S02"
  },
  "validationErrors": null
}


[10:40:35] [ERR]  
SELECT  `pm`.`ID` AS `Id` , `pm`.`PRODUCT_ID` AS `ProductId` , `p`.`PRODUCT_NAME` AS `ProductName` , `pm`.`MODEL_NAME` AS `ModelName` , `pm`.`DEVICE_TYPE` AS `DeviceType` , `pm`.`GROUP_KEY` AS `GroupKey` , `pm`.`DESCRIPTION` AS `Description` , `pm`.`IS_ENABLED` AS `IsEnabled` , `pm`.`CREATE_TIME` AS `CreateTime` , `pm`.`UPDATE_TIME` AS `UpdateTime` , `pm`.`CREATE_BY` AS `CreateBy` , `pm`.`UPDATE_BY` AS `UpdateBy` , `pm`.`REMARK` AS `Remark`  FROM `LOT_PRODUCT_MODEL` `pm` Left JOIN `LOT_PRODUCT` `p` ON ( `pm`.`PRODUCT_ID` = `p`.`ID` )     ORDER BY `pm`.`CreateTime` DESC LIMIT 0,10

[10:40:36] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Unknown column 'pm.CreateTime' in 'order clause'",
  "details": "MySqlException: Unknown column 'pm.CreateTime' in 'order clause'\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)\r\n   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()\r\n   at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)\r\n   at Admin.SqlSugar.PagedListExtensions.ToPurestPagedListAsync[TResult](ISugarQueryable`1 queryable, Int32 pageIndex, Int32 pageSize) in D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\PagedListExtensions.cs:line 49\r\n   at Admin.Application.ProductServices.ProductModelService.GetPagedListAsync(ProductModelQueryInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ProductServices\\ProductModelService.cs:line 49\r\n   at lambda_method1175(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1054,
    "SqlState": "42S22"
  },
  "validationErrors": null
}


[10:43:18] [ERR]  
SELECT  `pm`.`ID` AS `Id` , `pm`.`PRODUCT_ID` AS `ProductId` , `p`.`PRODUCT_NAME` AS `ProductName` , `pm`.`MODEL_NAME` AS `ModelName` , `pm`.`DEVICE_TYPE` AS `DeviceType` , `pm`.`GROUP_KEY` AS `GroupKey` , `pm`.`DESCRIPTION` AS `Description` , `pm`.`IS_ENABLED` AS `IsEnabled` , `pm`.`CREATE_TIME` AS `CreateTime` , `pm`.`UPDATE_TIME` AS `UpdateTime` , `pm`.`CREATE_BY` AS `CreateBy` , `pm`.`UPDATE_BY` AS `UpdateBy` , `pm`.`REMARK` AS `Remark`  FROM `LOT_PRODUCT_MODEL` `pm` Left JOIN `LOT_PRODUCT` `p` ON ( `pm`.`PRODUCT_ID` = `p`.`ID` )     ORDER BY `pm`.`CreateTime` DESC LIMIT 0,10

[10:43:18] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Unknown column 'pm.CreateTime' in 'order clause'",
  "details": "MySqlException: Unknown column 'pm.CreateTime' in 'order clause'\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)\r\n   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()\r\n   at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)\r\n   at Admin.SqlSugar.PagedListExtensions.ToPurestPagedListAsync[TResult](ISugarQueryable`1 queryable, Int32 pageIndex, Int32 pageSize) in D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\PagedListExtensions.cs:line 49\r\n   at Admin.Application.ProductServices.ProductModelService.GetPagedListAsync(ProductModelQueryInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ProductServices\\ProductModelService.cs:line 49\r\n   at lambda_method1177(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1054,
    "SqlState": "42S22"
  },
  "validationErrors": null
}


[11:04:28] [ERR]  
SELECT  `pm`.`ID` AS `Id` , `pm`.`PRODUCT_ID` AS `ProductId` , `p`.`PRODUCT_NAME` AS `ProductName` , `pm`.`MODEL_NAME` AS `ModelName` , `pm`.`DEVICE_TYPE` AS `DeviceType` , `pm`.`GROUP_KEY` AS `GroupKey` , `pm`.`DESCRIPTION` AS `Description` , `pm`.`IS_ENABLED` AS `IsEnabled` , `pm`.`CREATE_TIME` AS `CreateTime` , `pm`.`UPDATE_TIME` AS `UpdateTime` , `pm`.`CREATE_BY` AS `CreateBy` , `pm`.`UPDATE_BY` AS `UpdateBy` , `pm`.`REMARK` AS `Remark`  FROM `LOT_PRODUCT_MODEL` `pm` Left JOIN `LOT_PRODUCT` `p` ON ( `pm`.`PRODUCT_ID` = `p`.`ID` )    WHERE ( `pm`.`PRODUCT_ID` = @ProductId0 )  ORDER BY `pm`.`CreateTime` DESC LIMIT 0,10

[11:04:28] [ERR] Volo.Abp.AspNetCore.Mvc.ExceptionHandling.AbpExceptionFilter 
{
  "code": null,
  "message": "Unknown column 'pm.CreateTime' in 'order clause'",
  "details": "MySqlException: Unknown column 'pm.CreateTime' in 'order clause'\r\nSTACK TRACE:    at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)\r\n   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)\r\n   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()\r\n   at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)\r\n   at Admin.SqlSugar.PagedListExtensions.ToPurestPagedListAsync[TResult](ISugarQueryable`1 queryable, Int32 pageIndex, Int32 pageSize) in D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\PagedListExtensions.cs:line 49\r\n   at Admin.Application.ProductServices.ProductModelService.GetPagedListAsync(ProductModelQueryInput input) in D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ProductServices\\ProductModelService.cs:line 49\r\n   at lambda_method1661(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n",
  "data": {
    "Server Error Code": 1054,
    "SqlState": "42S22"
  },
  "validationErrors": null
}


[22:35:12] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 连接被拒绝，原因: "BadUsernameOrPassword"

[22:42:23] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 连接被拒绝，原因: "BadUsernameOrPassword"

[22:42:27] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 连接被拒绝，原因: "BadUsernameOrPassword"

[22:42:32] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 连接被拒绝，原因: "BadUsernameOrPassword"

[22:42:36] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端 mqttx_a978efd2 连接被拒绝，原因: "BadUsernameOrPassword"

