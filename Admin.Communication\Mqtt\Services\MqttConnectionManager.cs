using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Models.Statistics;
using Admin.Core.ExceptionExtensions;
using Admin.SqlSugar.Entity.Business.LOT;
using Volo.Abp.BackgroundJobs;
using SqlSugar;
using Admin.Multiplex.Contracts.Enums.Mqtt;
using Admin.Communication.Mqtt.Events;
using Admin.Communication.Mqtt.Models.Results;
using System.Timers;
using Yitter.IdGenerator;
using Admin.Multiplex.Contracts.BackgroundArgs;

namespace Admin.Communication.Mqtt.Services
{
    /// <summary>
    /// MQTT连接管理器实现
    /// </summary>
    public class MqttConnectionManager : IMqttConnectionManager
    {
        private readonly ILogger<MqttConnectionManager> _logger;
        private readonly MqttBrokerOptions _options;
        private readonly ISqlSugarClient _db;
        private readonly IBackgroundJobManager _backgroundJobManager;
        private readonly IMqttUserService _mqttUserService;
        
        // 连接池
        private readonly ConcurrentDictionary<string, MqttClientConnection> _connections = new ConcurrentDictionary<string, MqttClientConnection>();
        
        // 会话管理
        private readonly ConcurrentDictionary<string, MqttSession> _sessions = new ConcurrentDictionary<string, MqttSession>();
        
        // 连接状态管理
        private readonly ConcurrentDictionary<string, MqttConnectionStatusInfo> _connectionStatuses = new ConcurrentDictionary<string, MqttConnectionStatusInfo>();
        
        // IP连接数统计
        private readonly ConcurrentDictionary<string, int> _connectionsByIp = new ConcurrentDictionary<string, int>();
        
        // 黑名单管理
        private readonly ConcurrentDictionary<string, DateTime> _clientBlacklist = new ConcurrentDictionary<string, DateTime>();
        private readonly ConcurrentDictionary<string, DateTime> _ipBlacklist = new ConcurrentDictionary<string, DateTime>();
        
        // 连接限制配置
        private volatile int _maxConnections = 10000;
        private volatile int _maxConnectionsPerIp = 100;
        
        // 统计信息
        private readonly ConnectionStatistics _statistics = new ConnectionStatistics();
        private readonly object _statisticsLock = new object();
        
        // 统计定时器（保留用于实时统计更新）
        private readonly System.Timers.Timer _statisticsTimer;
        
        // 释放标志
        private volatile bool _disposed = false;

        // 数据库操作同步锁，防止并发访问冲突
        private readonly SemaphoreSlim _dbOperationSemaphore = new(1, 1);

        public MqttConnectionManager(ILogger<MqttConnectionManager> logger, IOptions<MqttBrokerOptions> options, ISqlSugarClient db, IBackgroundJobManager backgroundJobManager, IMqttUserService mqttUserService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _db = db ?? throw new ArgumentNullException(nameof(db));
            _backgroundJobManager = backgroundJobManager ?? throw new ArgumentNullException(nameof(backgroundJobManager));
            _mqttUserService = mqttUserService;
            
            // 只保留统计定时器，清理任务交给后台服务处理
            _statisticsTimer = new System.Timers.Timer(TimeSpan.FromMinutes(1).TotalMilliseconds); // 每分钟更新统计
            _statisticsTimer.Elapsed += OnStatisticsTimer;
            _statisticsTimer.AutoReset = true;
            _statisticsTimer.Start();
            
            _logger.LogInformation("MQTT连接管理器已初始化，最大连接数: {MaxConnections}, 单IP最大连接数: {MaxConnectionsPerIp}", 
                _maxConnections, _maxConnectionsPerIp);
        }

        #region 连接生命周期管理

        public async Task<ConnectionResult> AddConnectionAsync(MqttClientConnection connection, CancellationToken cancellationToken = default)
        {
            if (connection == null) throw PersistdValidateException.Message("连接对象不能为空");
            if (string.IsNullOrEmpty(connection.ClientId)) throw PersistdValidateException.Message("客户端ID不能为空");
            
            try
            {
                var clientId = connection.ClientId;
                var ipAddress = connection.RemoteEndPoint?.Address?.ToString() ?? "unknown";
                
                _logger.LogDebug("尝试添加连接: ClientId={ClientId}, IP={IpAddress}", clientId, ipAddress);
                
                // 验证新连接
                var validationResult = ValidateNewConnection(clientId, ipAddress);
                if (!validationResult.IsAllowed)
                {
                    _logger.LogWarning("连接被拒绝: ClientId={ClientId}, 原因={Reason}", clientId, validationResult.RejectReason);
                    RecordConnectionRejection();
                    return ConnectionResult.Failure(validationResult.RejectReason);
                }
                
                bool existingReplaced = false;
                bool sessionPresent = false;
                
                // 检查是否存在旧连接
                if (_connections.TryGetValue(clientId, out var existingConnection))
                {
                    _logger.LogInformation("客户端 {ClientId} 已存在连接，替换旧连接", clientId);
                    await RemoveConnectionAsync(clientId, DisconnectReasonEnum.ReplacedByNewConnection, true, cancellationToken);
                    existingReplaced = true;
                }
                
                // 添加新连接
                _connections[clientId] = connection;

                // 更新IP连接数
                _connectionsByIp.AddOrUpdate(ipAddress, 1, (key, value) => value + 1);
                
                // 更新连接状态
                var statusInfo = MqttConnectionStatusInfo.Create(clientId, ConnectionStatusEnum.Connected);
                _connectionStatuses[clientId] = statusInfo;
                
                // 获取或创建会话
                var session = await GetOrCreateSessionAsync(clientId, connection.CleanSession);
                sessionPresent = !connection.CleanSession && session != null;

                // 更新统计信息
                RecordConnection();

                // 触发连接事件
                OnClientConnected(new MqttClientConnectedEventArgs
                {
                    ClientInfo = CreateClientInfo(connection)
                });

                // 触发状态变化事件
                OnConnectionStatusChanged(new ConnectionStatusChangedEventArgs
                {
                    ClientId = clientId,
                    OldStatus = ConnectionStatusEnum.Disconnected,
                    NewStatus = ConnectionStatusEnum.Connected
                });
                
                _logger.LogInformation("客户端连接成功: ClientId={ClientId}, IP={IpAddress}, SessionPresent={SessionPresent}", 
                    clientId, ipAddress, sessionPresent);
                
                return ConnectionResult.Success(sessionPresent, existingReplaced);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加连接时发生错误: ClientId={ClientId}", connection.ClientId);
                return ConnectionResult.Failure($"添加连接失败: {ex.Message}");
            }
        }

        public async Task RemoveConnectionAsync(string clientId, DisconnectReasonEnum reason = DisconnectReasonEnum.NormalDisconnection, bool sendDisconnect = true, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(clientId)) throw PersistdValidateException.Message("客户端ID不能为空");
            
            try
            {
                if (!_connections.TryRemove(clientId, out var connection))
                {
                    _logger.LogDebug("尝试移除不存在的连接: ClientId={ClientId}", clientId);
                    return;
                }
                
                var ipAddress = connection.RemoteEndPoint?.Address?.ToString() ?? "unknown";
                
                _logger.LogDebug("移除连接: ClientId={ClientId}, IP={IpAddress}, Reason={Reason}", clientId, ipAddress, reason);
                
                // 更新IP连接数
                _connectionsByIp.AddOrUpdate(ipAddress, 0, (key, value) => Math.Max(0, value - 1));
                if (_connectionsByIp.TryGetValue(ipAddress, out var count) && count == 0)
                {
                    _connectionsByIp.TryRemove(ipAddress, out _);
                }
                
                // 更新连接状态
                var statusInfo = MqttConnectionStatusInfo.Create(clientId, ConnectionStatusEnum.Disconnected, 
                    $"断开原因: {reason}");
                _connectionStatuses[clientId] = statusInfo;
                
                // 处理会话
                if (_sessions.TryGetValue(clientId, out var session))
                {
                    if (connection.CleanSession)
                    {
                        await CleanupSessionAsync(clientId);
                    }
                    else
                    {
                        session.Suspend(); // 持久会话暂停
                    }
                }

                // 关闭连接
                try
                {
                connection.Close();
                }
                catch (Exception ex)
                {
                    _logger.LogDebug("关闭连接时发生异常: {Message}", ex.Message);
                }

                // 更新统计信息
                RecordDisconnection(reason != DisconnectReasonEnum.NormalDisconnection && reason != DisconnectReasonEnum.ClientDisconnect);

                // 触发断开连接事件
                OnClientDisconnected(new MqttClientDisconnectedEventArgs
                {
                    ClientId = clientId,
                    WasCleanDisconnect = reason == DisconnectReasonEnum.NormalDisconnection || reason == DisconnectReasonEnum.ClientDisconnect
                });
                
                // 触发状态变化事件
                OnConnectionStatusChanged(new ConnectionStatusChangedEventArgs
                {
                    ClientId = clientId,
                    OldStatus = ConnectionStatusEnum.Connected,
                    NewStatus = ConnectionStatusEnum.Disconnected
                });
                
                _logger.LogInformation("客户端连接已移除: ClientId={ClientId}, IP={IpAddress}, Reason={Reason}", 
                    clientId, ipAddress, reason);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除连接时发生错误: ClientId={ClientId}", clientId);
            }
        }

        public void UpdateConnectionActivity(string clientId)
        {
            if (string.IsNullOrEmpty(clientId)) return;
            
            if (_connections.TryGetValue(clientId, out var connection))
            {
                connection.UpdateActivity();
                
                if (_sessions.TryGetValue(clientId, out var session))
                {
                    session.UpdateActivity();
                }
            }
        }

        public async Task CleanupTimeoutConnectionsAsync(CancellationToken cancellationToken = default)
        {
            var timeoutConnections = new List<string>();
            
            foreach (var kvp in _connections)
            {
                var connection = kvp.Value;
                if (!connection.IsActive())
                {
                    timeoutConnections.Add(kvp.Key);
                }
            }
            
            foreach (var clientId in timeoutConnections)
            {
                if (_connections.TryGetValue(clientId, out var connection))
                {
                    _logger.LogInformation("清理超时连接: ClientId={ClientId}, LastActivity={LastActivity}", 
                        clientId, connection.LastActivityTime);
                    
                    // 触发超时事件
                    OnConnectionTimeout(new ConnectionTimeoutEventArgs
                    {
                        ClientId = clientId,
                        LastActivity = connection.LastActivityTime,
                        TimeoutSeconds = connection.KeepAlive
                    });
                    
                    await RemoveConnectionAsync(clientId, DisconnectReasonEnum.Timeout, false, cancellationToken);
                }
            }
            
            if (timeoutConnections.Count > 0)
            {
                _logger.LogInformation("清理了 {Count} 个超时连接", timeoutConnections.Count);
            }
        }

        #endregion

        #region 连接池管理

        public MqttClientConnection GetConnection(string clientId)
        {
            if (string.IsNullOrEmpty(clientId)) return null;

            _connections.TryGetValue(clientId, out var connection);
            return connection;
        }

        public bool IsConnected(string clientId)
        {
            if (string.IsNullOrEmpty(clientId)) return false;
            
            return _connections.ContainsKey(clientId);
        }

        public IReadOnlyList<MqttClientConnection> GetAllConnections()
        {
            return _connections.Values.ToList();
        }

        public int GetConnectionCount()
        {
            return _connections.Count;
        }

        #endregion

        #region 连接限制和控制

        public ConnectionValidationResult ValidateNewConnection(string clientId, string ipAddress)
        {
            // 检查服务器是否在维护中
            if (_disposed)
            {
                return ConnectionValidationResult.Reject(ConnectionRejectCodeEnum.ServerMaintenance, "服务器维护中");
            }
            
            // 检查最大连接数
            if (_connections.Count >= _maxConnections)
            {
                return ConnectionValidationResult.Reject(ConnectionRejectCodeEnum.MaxConnectionsExceeded, 
                    $"超出最大连接数限制: {_maxConnections}");
            }
            
            // 检查单IP最大连接数
            if (_connectionsByIp.TryGetValue(ipAddress, out var ipConnections) && ipConnections >= _maxConnectionsPerIp)
            {
                return ConnectionValidationResult.Reject(ConnectionRejectCodeEnum.MaxConnectionsPerIpExceeded, 
                    $"IP {ipAddress} 超出最大连接数限制: {_maxConnectionsPerIp}");
            }
            
            // 检查客户端黑名单
            if (_clientBlacklist.ContainsKey(clientId))
            {
                return ConnectionValidationResult.Reject(ConnectionRejectCodeEnum.ClientBlacklisted, 
                    $"客户端 {clientId} 在黑名单中");
            }
            
            // 检查IP黑名单
            if (_ipBlacklist.ContainsKey(ipAddress))
            {
                return ConnectionValidationResult.Reject(ConnectionRejectCodeEnum.IpBlacklisted, 
                    $"IP {ipAddress} 在黑名单中");
            }
            
            return ConnectionValidationResult.Allow();
        }

        public void SetMaxConnections(int maxConnections)
        {
            if (maxConnections <= 0) throw PersistdValidateException.Message("最大连接数必须大于0");
            _maxConnections = maxConnections;
            _logger.LogInformation("最大连接数已更新为: {MaxConnections}", maxConnections);
        }

        public void SetMaxConnectionsPerIp(int maxConnectionsPerIp)
        {
            if (maxConnectionsPerIp <= 0) throw PersistdValidateException.Message("单IP最大连接数必须大于0");
            _maxConnectionsPerIp = maxConnectionsPerIp;
            _logger.LogInformation("单IP最大连接数已更新为: {MaxConnectionsPerIp}", maxConnectionsPerIp);
        }

        public void AddToBlacklist(string clientId)
        {
            if (string.IsNullOrEmpty(clientId)) throw new ArgumentException("客户端ID不能为空", nameof(clientId));
            
            _clientBlacklist[clientId] = DateTime.Now;
            _logger.LogWarning("客户端 {ClientId} 已添加到黑名单", clientId);
            
            // 断开现有连接
            if (_connections.ContainsKey(clientId))
            {
                _ = Task.Run(() => RemoveConnectionAsync(clientId, DisconnectReasonEnum.AuthorizationFailure, true));
            }
        }

        public void RemoveFromBlacklist(string clientId)
        {
            if (string.IsNullOrEmpty(clientId)) return;
            
            if (_clientBlacklist.TryRemove(clientId, out _))
            {
                _logger.LogInformation("客户端 {ClientId} 已从黑名单移除", clientId);
            }
        }

        #endregion

        #region 连接认证和授权

        public async Task<AuthenticationResult> ValidateCredentialsAsync(string clientId, string username, string password, string ipAddress)
        {
            try
            {
                // 允许匿名访问
                if (_options.AllowAnonymousAccess)
                {
                    if (string.IsNullOrEmpty(username))
                    {
                        return AuthenticationResult.Success("anonymous");
                    }
                }
                else
                {
                    // 不允许匿名访问时，必须提供凭据
                    if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                    {
                        RecordAuthenticationFailure();
                        return AuthenticationResult.Failure("匿名访问被禁用，必须提供用户名和密码");
                    }
                }
                
                // 验证用户凭据
                if (!string.IsNullOrEmpty(username))
                {
                    // 优先使用MQTT用户服务进行验证
                    if (_mqttUserService != null)
                    {
                        var result = await _mqttUserService.ValidateCredentialsAsync(username, password, clientId);
                        if (result.IsAuthenticated)
                        {
                            // 异步更新用户登录信息
                            _ = _mqttUserService.UpdateUserLastLoginAsync(username, ipAddress);
                            return AuthenticationResult.Success(result.Role);
                        }
                        else
                        {
                            RecordAuthenticationFailure();
                            return AuthenticationResult.Failure(result.Message);
                        }
                    }
                    else
                    {
                        // 回退到配置文件验证
                        _logger.LogWarning("MQTT用户服务未注册，回退到配置文件验证");
                        return FallbackValidateCredentials(clientId, username, password);
                    }
                }
                
                return AuthenticationResult.Success("anonymous");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证凭据时发生错误: ClientId={ClientId}, Username={Username}", clientId, username);
                RecordAuthenticationFailure();
                return AuthenticationResult.Failure("认证过程中发生错误");
            }
        }

        /// <summary>
        /// 回退到配置文件验证方式
        /// </summary>
        private AuthenticationResult FallbackValidateCredentials(string clientId, string username, string password)
        {
            try
            {
                if (_options.Users.Count > 0)
                {
                    // 使用配置的用户列表验证
                    if (_options.Users.TryGetValue(username, out var expectedPassword))
                    {
                        if (expectedPassword == password)
                        {
                            return AuthenticationResult.Success("user");
                        }
                        else
                        {
                            RecordAuthenticationFailure();
                            return AuthenticationResult.Failure($"用户 {username} 密码错误");
                        }
                    }
                    else
                    {
                        RecordAuthenticationFailure();
                        return AuthenticationResult.Failure($"用户 {username} 不存在");
                    }
                }
                else
                {
                    // 使用默认凭据验证
                    if (username == _options.Username && password == _options.Password)
                    {
                        return AuthenticationResult.Success("admin");
                    }
                    else
                    {
                        RecordAuthenticationFailure();
                        return AuthenticationResult.Failure("用户名或密码错误");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "回退验证凭据时发生错误: ClientId={ClientId}, Username={Username}", clientId, username);
                RecordAuthenticationFailure();
                return AuthenticationResult.Failure("认证过程中发生错误");
            }
        }

        public AuthorizationResult CheckConnectionPermission(string clientId, string ipAddress)
        {
            try
            {
                // 检查客户端黑名单
                if (_clientBlacklist.ContainsKey(clientId))
                {
                    return AuthorizationResult.Failure($"客户端 {clientId} 在黑名单中");
                }
                
                // 检查IP黑名单
                if (_ipBlacklist.ContainsKey(ipAddress))
                {
                    return AuthorizationResult.Failure($"IP {ipAddress} 在黑名单中");
                }
                
                // TODO: 实现更复杂的授权逻辑，如基于角色的访问控制(RBAC)
                
                return AuthorizationResult.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查连接权限时发生错误: ClientId={ClientId}, IP={IpAddress}", clientId, ipAddress);
                return AuthorizationResult.Failure("权限检查过程中发生错误");
            }
        }

        public AuthorizationResult CheckPublishPermission(string clientId, string username, string topic)
        {
            return CheckTopicPermission(clientId, username, topic, MqttAccessTypeEnum.Publish);
        }

        public AuthorizationResult CheckSubscribePermission(string clientId, string username, string topic)
        {
            return CheckTopicPermission(clientId, username, topic, MqttAccessTypeEnum.Subscribe);
        }

        public AuthorizationResult CheckTopicPermission(string clientId, string username, string topic, MqttAccessTypeEnum accessType)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(clientId))
                {
                    return AuthorizationResult.Failure("客户端ID不能为空");
                }

                if (string.IsNullOrEmpty(topic))
                {
                    return AuthorizationResult.Failure("主题不能为空");
                }

                // 如果没有配置ACL规则，默认允许所有访问
                if (_options.AclRules == null || _options.AclRules.Count == 0)
                {
                    _logger.LogDebug("没有配置ACL规则，默认允许访问: ClientId={ClientId}, Topic={Topic}, AccessType={AccessType}", 
                        clientId, topic, accessType);
                    return AuthorizationResult.Success();
                }

                // 查找匹配的ACL规则
                var matchingRules = FindMatchingAclRules(clientId, username, topic, accessType);

                if (matchingRules.Count == 0)
                {
                    // 没有匹配的规则，根据默认策略决定
                    _logger.LogDebug("没有找到匹配的ACL规则，默认拒绝访问: ClientId={ClientId}, Username={Username}, Topic={Topic}, AccessType={AccessType}", 
                        clientId, username, topic, accessType);
                    return AuthorizationResult.Failure($"没有权限访问主题 '{topic}'");
                }

                // 按优先级处理规则：先处理拒绝规则，再处理允许规则
                // 如果有任何拒绝规则匹配，则拒绝访问
                var denyRules = matchingRules.Where(r => !r.Allow).ToList();
                if (denyRules.Any())
                {
                    var denyRule = denyRules.First();
                    _logger.LogDebug("找到拒绝规则，拒绝访问: ClientId={ClientId}, Username={Username}, Topic={Topic}, AccessType={AccessType}, Rule={@Rule}", 
                        clientId, username, topic, accessType, denyRule);
                    return AuthorizationResult.Failure($"访问主题 '{topic}' 被ACL规则拒绝");
                }

                // 如果有允许规则匹配，则允许访问
                var allowRules = matchingRules.Where(r => r.Allow).ToList();
                if (allowRules.Any())
                {
                    var allowRule = allowRules.First();
                    _logger.LogDebug("找到允许规则，允许访问: ClientId={ClientId}, Username={Username}, Topic={Topic}, AccessType={AccessType}, Rule={@Rule}", 
                        clientId, username, topic, accessType, allowRule);
                    return AuthorizationResult.Success();
                }

                // 理论上不应该到达这里，但为了安全起见，默认拒绝
                _logger.LogWarning("ACL规则处理异常，默认拒绝访问: ClientId={ClientId}, Username={Username}, Topic={Topic}, AccessType={AccessType}", 
                    clientId, username, topic, accessType);
                return AuthorizationResult.Failure($"ACL规则处理异常，拒绝访问主题 '{topic}'");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查主题权限时发生错误: ClientId={ClientId}, Username={Username}, Topic={Topic}, AccessType={AccessType}", 
                    clientId, username, topic, accessType);
                return AuthorizationResult.Failure("主题权限检查过程中发生错误");
            }
        }

        /// <summary>
        /// 查找匹配的ACL规则
        /// </summary>
        private List<MqttAclEntry> FindMatchingAclRules(string clientId, string username, string topic, MqttAccessTypeEnum accessType)
        {
            var matchingRules = new List<MqttAclEntry>();

            foreach (var rule in _options.AclRules)
            {
                // 检查访问类型是否匹配
                if (rule.AccessType != MqttAccessType.All && rule.AccessType != ConvertAccessType(accessType))
                {
                    continue;
                }

                // 检查用户名是否匹配（null或空表示匹配所有用户）
                if (!string.IsNullOrEmpty(rule.Username) && !string.Equals(rule.Username, username, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                // 检查客户端ID是否匹配（null或空表示匹配所有客户端）
                if (!string.IsNullOrEmpty(rule.ClientId) && !string.Equals(rule.ClientId, clientId, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                // 检查主题是否匹配（支持通配符）
                if (IsTopicMatch(topic, rule.Topic))
                {
                    matchingRules.Add(rule);
                }
            }

            return matchingRules;
        }

        /// <summary>
        /// 检查主题是否匹配ACL规则（支持MQTT通配符）
        /// </summary>
        private bool IsTopicMatch(string topic, string pattern)
        {
            if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(pattern))
            {
                return false;
            }

            // 完全匹配
            if (string.Equals(topic, pattern, StringComparison.Ordinal))
            {
                return true;
            }

            // 处理通配符
            return IsTopicMatchWithWildcards(topic, pattern);
        }

        /// <summary>
        /// 使用通配符匹配主题
        /// </summary>
        private bool IsTopicMatchWithWildcards(string topic, string pattern)
        {
            // 将主题和模式按'/'分割
            var topicParts = topic.Split('/');
            var patternParts = pattern.Split('/');

            return MatchTopicParts(topicParts, patternParts, 0, 0);
        }

        /// <summary>
        /// 转换访问类型枚举
        /// </summary>
        private MqttAccessType ConvertAccessType(MqttAccessTypeEnum accessType)
        {
            return accessType switch
            {
                MqttAccessTypeEnum.Publish => MqttAccessType.Publish,
                MqttAccessTypeEnum.Subscribe => MqttAccessType.Subscribe,
                _ => MqttAccessType.All
            };
        }

        /// <summary>
        /// 递归匹配主题部分
        /// </summary>
        private bool MatchTopicParts(string[] topicParts, string[] patternParts, int topicIndex, int patternIndex)
        {
            // 如果模式已经结束
            if (patternIndex >= patternParts.Length)
            {
                // 如果主题也结束，则匹配成功
                return topicIndex >= topicParts.Length;
            }

            // 如果当前模式部分是多级通配符 '#'
            if (patternParts[patternIndex] == "#")
            {
                // '#' 必须是最后一个部分
                if (patternIndex == patternParts.Length - 1)
                {
                    return true; // 匹配剩余的所有主题部分
                }
                else
                {
                    return false; // '#' 不在最后位置，无效模式
                }
            }

            // 如果主题已经结束但模式没有结束
            if (topicIndex >= topicParts.Length)
            {
                return false;
            }

            // 如果当前模式部分是单级通配符 '+'
            if (patternParts[patternIndex] == "+")
            {
                // '+' 匹配当前主题部分，继续匹配下一部分
                return MatchTopicParts(topicParts, patternParts, topicIndex + 1, patternIndex + 1);
            }

            // 精确匹配当前部分
            if (string.Equals(topicParts[topicIndex], patternParts[patternIndex], StringComparison.Ordinal))
            {
                return MatchTopicParts(topicParts, patternParts, topicIndex + 1, patternIndex + 1);
            }

            // 不匹配
            return false;
        }

        #endregion

        #region 连接监控和统计

        public ConnectionStatistics GetConnectionStatistics()
        {
            lock (_statisticsLock)
            {
                var stats = new ConnectionStatistics
                {
                    CurrentConnections = _connections.Count,
                    TotalConnections = _statistics.TotalConnections,
                    TotalDisconnections = _statistics.TotalDisconnections,
                    AbnormalDisconnections = _statistics.AbnormalDisconnections,
                    AuthenticationFailures = _statistics.AuthenticationFailures,
                    ConnectionRejections = _statistics.ConnectionRejections,
                    MaxConcurrentConnections = _statistics.MaxConcurrentConnections,
                    AverageConnectionDuration = _statistics.AverageConnectionDuration,
                    ConnectionsByIp = new Dictionary<string, int>(_connectionsByIp)
                };
                
                return stats;
            }
        }

        public int GetConnectionCountByIp(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress)) return 0;
            
            _connectionsByIp.TryGetValue(ipAddress, out var count);
            return count;
        }

        public IReadOnlyList<MqttClientInfo> GetClientInfos()
        {
            var clientInfos = new List<MqttClientInfo>();

            foreach (var connection in _connections.Values)
            {
                var clientInfo = CreateClientInfo(connection);
                clientInfos.Add(clientInfo);
            }
            
            return clientInfos;
        }

        #endregion

        #region 连接事件管理

        public event EventHandler<MqttClientConnectedEventArgs> ClientConnected;
        public event EventHandler<MqttClientDisconnectedEventArgs> ClientDisconnected;
        public event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
        public event EventHandler<ConnectionTimeoutEventArgs> ConnectionTimeout;

        protected virtual void OnClientConnected(MqttClientConnectedEventArgs e)
        {
            ClientConnected?.Invoke(this, e);
        }

        protected virtual void OnClientDisconnected(MqttClientDisconnectedEventArgs e)
        {
            ClientDisconnected?.Invoke(this, e);
        }

        protected virtual void OnConnectionStatusChanged(ConnectionStatusChangedEventArgs e)
        {
            ConnectionStatusChanged?.Invoke(this, e);
        }

        protected virtual void OnConnectionTimeout(ConnectionTimeoutEventArgs e)
        {
            ConnectionTimeout?.Invoke(this, e);
        }

        #endregion

        #region 会话管理

        public async Task<MqttSession> GetOrCreateSessionAsync(string clientId, bool cleanSession)
        {
            if (string.IsNullOrEmpty(clientId)) throw new ArgumentException("客户端ID不能为空", nameof(clientId));
            
            try
            {
                if (cleanSession)
                {
                    // 清除会话：移除现有会话，创建新的临时会话
                    if (_sessions.TryRemove(clientId, out var existingSession))
                    {
                        existingSession.Cleanup();
                    }
                    
                    var newSession = new MqttSession(clientId, true);
                    _sessions[clientId] = newSession;
                    
                    _logger.LogDebug("创建新的清除会话: ClientId={ClientId}", clientId);
                    return newSession;
                }
                else
                {
                    // 持久会话：获取现有会话或创建新会话
                    if (_sessions.TryGetValue(clientId, out var existingSession))
                    {
                        if (!existingSession.IsExpired())
                        {
                            existingSession.Activate();
                            _logger.LogDebug("恢复内存中的持久会话: ClientId={ClientId}", clientId);
                            return existingSession;
                        }
                        else
                        {
                            // 会话已过期，清理并创建新会话
                            _sessions.TryRemove(clientId, out _);
                            existingSession.Cleanup();
                        }
                    }
                    
                    // 尝试从数据库恢复持久会话
                    var databaseSession = await LoadSessionFromDatabaseAsync(clientId);
                    if (databaseSession != null)
                    {
                        // 将从数据库恢复的会话添加到内存中
                        _sessions[clientId] = databaseSession;
                        databaseSession.Activate();
                        _logger.LogDebug("从数据库恢复持久会话: ClientId={ClientId}", clientId);
                        return databaseSession;
                    }
                    
                    // 创建新的持久会话
                    var newSession = new MqttSession(clientId, false);
                    _sessions[clientId] = newSession;
                    
                    _logger.LogDebug("创建新的持久会话: ClientId={ClientId}", clientId);
                    return newSession;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取或创建会话时发生错误: ClientId={ClientId}", clientId);
                throw;
            }
        }

        public async Task CleanupSessionAsync(string clientId)
        {
            if (string.IsNullOrEmpty(clientId)) return;
            
            try
            {
                if (_sessions.TryRemove(clientId, out var session))
                {
                    session.Cleanup();
                    
                    // 如果是持久会话，还需要从数据库中删除
                    if (session.IsPersistent && _options.PersistSessions)
                    {
                        await CleanupExpiredSessionFromDatabaseAsync(clientId);
                        _logger.LogDebug("清理持久会话（包括数据库记录）: ClientId={ClientId}", clientId);
                    }
                    else
                    {
                        _logger.LogDebug("清理会话: ClientId={ClientId}", clientId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理会话时发生错误: ClientId={ClientId}", clientId);
            }
        }

        public bool HasPersistentSession(string clientId)
        {
            if (string.IsNullOrEmpty(clientId)) return false;
            
            if (_sessions.TryGetValue(clientId, out var session))
            {
                return session.IsPersistent && !session.IsExpired();
            }
            
            return false;
        }

        public async Task SaveSessionAsync(MqttSession session)
        {
            if (session == null) throw new ArgumentNullException(nameof(session));

            // 1. 保存到内存（保持现有逻辑）
            _sessions[session.ClientId] = session;

            // 2. 只有持久会话才需要保存到数据库
            if (!session.IsPersistent)
            {
                _logger.LogDebug("清除会话不需要持久化: ClientId={ClientId}", session.ClientId);
                return;
            }

            // 3. 检查配置是否启用持久化
            if (!_options.PersistSessions)
            {
                _logger.LogDebug("会话持久化已禁用: ClientId={ClientId}", session.ClientId);
                return;
            }

            // 使用信号量防止并发数据库操作冲突
            await _dbOperationSemaphore.WaitAsync();
            try
            {
                // 4. 转换为数据库实体
                var sessionEntity = MapToSessionEntity(session);

                // 5. 检查数据库中是否已存在该会话
                var existingSession = await _db.Queryable<MqttSessionEntity>()
                    .Where(x => x.ClientId == session.ClientId)
                    .FirstAsync();

                if (existingSession != null)
                {
                    // 更新现有会话
                    sessionEntity.Id = existingSession.Id;
                    await _db.Updateable(sessionEntity).ExecuteCommandAsync();
                    _logger.LogDebug("更新持久会话到数据库: ClientId={ClientId}, Id={Id}", session.ClientId, existingSession.Id);
                }
                else
                {
                    // 插入新会话
                    var newId = await _db.Insertable(sessionEntity).ExecuteReturnSnowflakeIdAsync();
                    _logger.LogDebug("保存新持久会话到数据库: ClientId={ClientId}, Id={Id}", session.ClientId, newId);
                }

                // 6. 保存会话的订阅信息（如果需要）
                await SaveSessionSubscriptionsAsync(session);
                
                // 7. 保存会话的待发送消息（如果需要）
                await SaveSessionPendingMessagesAsync(session);
                
                _logger.LogDebug("会话持久化完成: ClientId={ClientId}", session.ClientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存会话时发生错误: ClientId={ClientId}", session.ClientId);
                throw;
            }
            finally
            {
                _dbOperationSemaphore.Release();
            }
        }

        /// <summary>
        /// 将MqttSession映射为MqttSessionEntity
        /// </summary>
        private MqttSessionEntity MapToSessionEntity(MqttSession session)
        {
            // 获取连接信息用于补充实体字段
            var connection = _connections.TryGetValue(session.ClientId, out var conn) ? conn : null;
            
            return new MqttSessionEntity
            {
                ClientId = session.ClientId,
                CleanSession = session.CleanSession,
                CreatedTime = session.CreatedTime,
                LastActivityTime = session.LastActivityTime,
                ExpiryTime = session.ExpiryTime,
                State = (int)session.State,
                Username = connection?.Username ?? string.Empty,
                IpAddress = connection?.RemoteEndPoint?.Address?.ToString() ?? string.Empty,
                KeepAlive = connection?.KeepAlive ?? 0,
                ProtocolVersion = connection?.ProtocolVersion.ToString() ?? "3.1.1"
            };
        }

        /// <summary>
        /// 保存会话订阅信息
        /// </summary>
        private async Task SaveSessionSubscriptionsAsync(MqttSession session)
        {
            try
            {
                var subscriptions = session.GetSubscriptions();
                if (!subscriptions.Any()) return;

                // 先删除旧的订阅记录
                await _db.Deleteable<MqttSubscriptionEntity>()
                    .Where(x => x.ClientId == session.ClientId)
                    .ExecuteCommandAsync();

                // 插入新的订阅记录
                var subscriptionEntities = subscriptions.Select(sub => new MqttSubscriptionEntity
                {
                    Id = YitIdHelper.NextId(),
                    ClientId = session.ClientId,
                    TopicFilter = sub.TopicFilter,
                    Qos = sub.QualityOfService,
                    SubscribedTime = DateTime.Now,
                    IsWildcard = sub.TopicFilter.Contains('+') || sub.TopicFilter.Contains('#'),
                    Status = 1, // 1-活跃
                    MatchCount = 0
                }).ToList();

                if (subscriptionEntities.Any())
                {
                    await _db.Insertable(subscriptionEntities).ExecuteCommandAsync();
                    _logger.LogDebug("保存会话订阅信息: ClientId={ClientId}, Count={Count}", 
                        session.ClientId, subscriptionEntities.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存会话订阅信息时发生错误: ClientId={ClientId}", session.ClientId);
                // 不抛出异常，避免影响主要的会话保存流程
            }
        }

        /// <summary>
        /// 保存会话待发送消息
        /// </summary>
        public async Task SaveSessionPendingMessagesAsync(MqttSession session)
        {
            try
            {
                var pendingMessages = session.GetPendingMessages();
                var awaitingAckMessages = session.GetAwaitingAckMessages();
                var awaitingCompMessages = session.GetAwaitingCompMessages();

                // 如果没有任何消息需要保存，直接返回
                if (pendingMessages.Count == 0 && awaitingAckMessages.Count == 0 && awaitingCompMessages.Count == 0)
                    return;

                // 先删除旧的消息记录
                await _db.Deleteable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == session.ClientId)
                    .ExecuteCommandAsync();

                var messageEntities = new List<MqttPendingMessageEntity>();

                // 1. 保存待发送消息
                foreach (var msg in pendingMessages)
                {
                    var entity = CreatePendingMessageEntity(msg, session.ClientId);
                    entity.Status = (int)MqttMessageStatusEnum.Pending;
                    entity.AckStatus = (int)MqttMessageAckStatusEnum.NoAckRequired;
                    messageEntities.Add(entity);
                }

                // 2. 保存待确认消息（QoS 1）
                foreach (var kvp in awaitingAckMessages)
                {
                    var entity = CreatePendingMessageEntityFromMessage(kvp.Value, session.ClientId, kvp.Key);
                    entity.Status = (int)MqttMessageStatusEnum.Sent; // 已发送，等待确认
                    entity.AckStatus = (int)MqttMessageAckStatusEnum.WaitingPubAck;
                    entity.ExpiryTime = DateTime.Now.AddMinutes(30); // 30分钟后过期
                    messageEntities.Add(entity);
                }

                // 3. 保存待完成消息（QoS 2）
                foreach (var kvp in awaitingCompMessages)
                {
                    var entity = CreatePendingMessageEntityFromMessage(kvp.Value, session.ClientId, kvp.Key);
                    entity.Status = (int)MqttMessageStatusEnum.Sent; // 已发送，等待确认
                    entity.AckStatus = (int)MqttMessageAckStatusEnum.WaitingPubRec; // 等待PUBREC
                    entity.ExpiryTime = DateTime.Now.AddMinutes(30); // 30分钟后过期
                    messageEntities.Add(entity);
                }

                if (messageEntities.Count > 0)
                {
                    await _db.Insertable(messageEntities).ExecuteCommandAsync();
                    _logger.LogDebug("保存会话消息: ClientId={ClientId}, 待发送={PendingCount}, 待确认={AwaitingAckCount}, 待完成={AwaitingCompCount}",
                        session.ClientId, pendingMessages.Count, awaitingAckMessages.Count, awaitingCompMessages.Count);

                    // 为需要确认的消息安排重试任务
                    await ScheduleMessageRetryJobsAsync(session.ClientId, awaitingAckMessages, awaitingCompMessages);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存会话待发送消息时发生错误: ClientId={ClientId}", session.ClientId);
                // 不抛出异常，避免影响主要的会话保存流程
            }
        }

        /// <summary>
        /// 从MqttMessage创建MqttPendingMessageEntity
        /// </summary>
        private MqttPendingMessageEntity CreatePendingMessageEntity(MqttMessage msg, string clientId)
        {
            // 处理MqttPublishMessage类型
            if (msg is MqttPublishMessage publishMsg)
            {
                return new MqttPendingMessageEntity
                {
                    Id = YitIdHelper.NextId(), // 生成雪花ID
                    MessageId = publishMsg.MessageId.ToString(),
                    ClientId = clientId,
                    Topic = publishMsg.Topic ?? string.Empty,
                    Payload = publishMsg.Payload ?? Array.Empty<byte>(),
                    Qos = publishMsg.QualityOfService,
                    Retain = publishMsg.Retain,
                    Duplicate = publishMsg.IsDuplicate,
                    PacketId = publishMsg.MessageId,
                    MessageType = 1, // PUBLISH
                    Priority = 2, // 普通优先级
                    CreatedTime = DateTime.Now,
                    ScheduleTime = null,
                    ExpiryTime = null,
                    RetryCount = 0,
                    MaxRetryCount = 3,
                    LastRetryTime = null,
                    ErrorMessage = string.Empty,
                    PublisherId = string.Empty
                };
            }

            // 处理基础MqttMessage类型
            return new MqttPendingMessageEntity
            {
                Id = YitIdHelper.NextId(), // 生成雪花ID
                MessageId = msg.MessageId.ToString(),
                ClientId = clientId,
                Topic = string.Empty, // 基础消息类型没有Topic
                Payload = Array.Empty<byte>(),
                Qos = msg.QualityOfService,
                Retain = msg.Retain,
                Duplicate = msg.IsDuplicate,
                PacketId = msg.MessageId,
                MessageType = msg.MessageType,
                Priority = 2, // 普通优先级
                CreatedTime = DateTime.Now,
                ScheduleTime = null,
                ExpiryTime = null,
                RetryCount = 0,
                MaxRetryCount = 3,
                LastRetryTime = null,
                ErrorMessage = string.Empty,
                PublisherId = string.Empty
            };
        }

        /// <summary>
        /// 从字典中的MqttMessage创建MqttPendingMessageEntity
        /// </summary>
        private MqttPendingMessageEntity CreatePendingMessageEntityFromMessage(MqttMessage msg, string clientId, ushort packetId)
        {
            var entity = CreatePendingMessageEntity(msg, clientId);
            entity.PacketId = packetId; // 使用字典的Key作为PacketId
            return entity;
        }

        /// <summary>
        /// 从数据库实体创建MqttMessage对象
        /// </summary>
        private MqttMessage CreateMessageFromEntity(MqttPendingMessageEntity entity)
        {
            // 根据消息类型创建相应的消息对象
            if (entity.MessageType == 1) // PUBLISH消息
            {
                return new MqttPublishMessage
                {
                    MessageId = ushort.TryParse(entity.MessageId, out var publishMsgId) ? publishMsgId : (ushort)0,
                    Topic = entity.Topic ?? string.Empty,
                    Payload = entity.Payload ?? Array.Empty<byte>(),
                    QualityOfService = (byte)entity.Qos,
                    Retain = entity.Retain,
                    IsDuplicate = entity.Duplicate
                };
            }

            // 其他类型的消息
            return new MqttMessage
            {
                MessageId = ushort.TryParse(entity.MessageId, out var baseMsgId) ? baseMsgId : (ushort)0,
                MessageType = (byte)entity.MessageType,
                QualityOfService = (byte)entity.Qos,
                Retain = entity.Retain,
                IsDuplicate = entity.Duplicate
            };
        }

        /// <summary>
        /// 为需要确认的消息安排重试任务
        /// </summary>
        private async Task ScheduleMessageRetryJobsAsync(string clientId,
            IReadOnlyDictionary<ushort, MqttMessage> awaitingAckMessages,
            IReadOnlyDictionary<ushort, MqttMessage> awaitingCompMessages)
        {
            try
            {
                // 为QoS 1消息（等待PUBACK）安排重试任务
                foreach (var kvp in awaitingAckMessages)
                {
                    var retryArgs = new MqttMessageRetryArgs
                    {
                        ClientId = clientId,
                        MessageId = kvp.Key,
                        RetryCount = 0,
                        MaxRetryCount = 3
                    };

                    // 30秒后开始第一次重试
                    await _backgroundJobManager.EnqueueAsync(retryArgs, delay: TimeSpan.FromSeconds(30));
                    
                    _logger.LogDebug("已安排QoS 1消息重试任务: ClientId={ClientId}, MessageId={MessageId}", 
                        clientId, kvp.Key);
                }

                // 为QoS 2消息（等待PUBREC/PUBCOMP）安排重试任务
                foreach (var kvp in awaitingCompMessages)
                {
                    var retryArgs = new MqttMessageRetryArgs
                    {
                        ClientId = clientId,
                        MessageId = kvp.Key,
                        RetryCount = 0,
                        MaxRetryCount = 3
                    };

                    // 30秒后开始第一次重试
                    await _backgroundJobManager.EnqueueAsync(retryArgs, delay: TimeSpan.FromSeconds(30));
                    
                    _logger.LogDebug("已安排QoS 2消息重试任务: ClientId={ClientId}, MessageId={MessageId}", 
                        clientId, kvp.Key);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "安排消息重试任务时发生错误: ClientId={ClientId}", clientId);
            }
        }

        /// <summary>
        /// 从数据库加载持久会话
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>会话信息，如果不存在则返回null</returns>
        public async Task<MqttSession> LoadSessionFromDatabaseAsync(string clientId)
        {
            if (string.IsNullOrEmpty(clientId))
                return null;

            // 使用信号量防止并发数据库操作冲突
            await _dbOperationSemaphore.WaitAsync();
            try
            {
                // 检查配置是否启用持久化
                if (!_options.PersistSessions)
                {
                    _logger.LogDebug("会话持久化已禁用，跳过数据库加载: ClientId={ClientId}", clientId);
                    return null;
                }

                // 从数据库查询会话
                var sessionEntity = await _db.Queryable<MqttSessionEntity>()
                    .Where(x => x.ClientId == clientId && !x.CleanSession)
                    .FirstAsync();

                if (sessionEntity == null)
                {
                    _logger.LogDebug("数据库中未找到持久会话: ClientId={ClientId}", clientId);
                    return null;
                }

                // 检查会话是否过期
                if (sessionEntity.ExpiryTime.HasValue && DateTime.Now > sessionEntity.ExpiryTime.Value)
                {
                    _logger.LogDebug("数据库中的会话已过期: ClientId={ClientId}, ExpiryTime={ExpiryTime}", 
                        clientId, sessionEntity.ExpiryTime);
                    
                    // 清理过期会话
                    await CleanupExpiredSessionFromDatabaseAsync(clientId);
                    return null;
                }

                // 将数据库实体转换为会话对象
                var session = MapToMqttSession(sessionEntity);

                // 加载会话的订阅信息
                await LoadSessionSubscriptionsAsync(session);

                // 加载会话的待发送消息
                await LoadSessionPendingMessagesAsync(session);

                _logger.LogInformation("从数据库恢复持久会话: ClientId={ClientId}, SessionId={SessionId}", 
                    clientId, session.SessionId);

                return session;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从数据库加载会话时发生错误: ClientId={ClientId}", clientId);
                return null;
            }
            finally
            {
                _dbOperationSemaphore.Release();
            }
        }

        /// <summary>
        /// 将MqttSessionEntity映射为MqttSession
        /// </summary>
        private MqttSession MapToMqttSession(MqttSessionEntity entity)
        {
            var session = new MqttSession(entity.ClientId, entity.CleanSession)
            {
                SessionId = Guid.NewGuid().ToString("N"), // 重新生成SessionId
                CreatedTime = entity.CreatedTime,
                LastActivityTime = entity.LastActivityTime,
                ExpiryTime = entity.ExpiryTime,
                State = (SessionState)entity.State
            };

            return session;
        }

        /// <summary>
        /// 加载会话的订阅信息
        /// </summary>
        private async Task LoadSessionSubscriptionsAsync(MqttSession session)
        {
            try
            {
                var subscriptionEntities = await _db.Queryable<MqttSubscriptionEntity>()
                    .Where(x => x.ClientId == session.ClientId && x.Status == 1) // 只通过ClientId过滤，加载活跃的订阅
                    .ToListAsync();

                foreach (var subEntity in subscriptionEntities)
                {
                    var subscription = new MqttSubscription(subEntity.TopicFilter, (byte)subEntity.Qos);
                    
                    session.AddSubscription(subscription);
                }

                _logger.LogDebug("加载会话订阅信息: ClientId={ClientId}, Count={Count}", 
                    session.ClientId, subscriptionEntities.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载会话订阅信息时发生错误: ClientId={ClientId}", session.ClientId);
            }
        }

        /// <summary>
        /// 加载会话的待发送消息
        /// </summary>
        private async Task LoadSessionPendingMessagesAsync(MqttSession session)
        {
            try
            {
                var allMessageEntities = await _db.Queryable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == session.ClientId)
                    .OrderBy(x => x.Priority, OrderByType.Desc) // 按优先级排序
                    .OrderBy(x => x.CreatedTime) // 再按创建时间排序
                    .ToListAsync();

                if (!allMessageEntities.Any())
                {
                    _logger.LogDebug("客户端 {ClientId} 没有需要恢复的消息", session.ClientId);
                    return;
                }

                var pendingCount = 0;
                var awaitingAckCount = 0;
                var awaitingCompCount = 0;

                foreach (var msgEntity in allMessageEntities)
                {
                    var message = CreateMessageFromEntity(msgEntity);
                    
                    // 根据消息状态和确认状态决定如何恢复
                    var status = (MqttMessageStatusEnum)msgEntity.Status;
                    var ackStatus = (MqttMessageAckStatusEnum)msgEntity.AckStatus;

                    switch (status)
                    {
                        case MqttMessageStatusEnum.Pending:
                            // 待发送消息，加入待发送队列
                    session.EnqueueMessage(message);
                            pendingCount++;
                            break;

                        case MqttMessageStatusEnum.Sent:
                            // 已发送但未确认的消息，根据确认状态恢复
                            if (ackStatus == MqttMessageAckStatusEnum.WaitingPubAck && msgEntity.PacketId.HasValue)
                            {
                                // QoS 1 待确认消息
                                session.RestoreAwaitingAckMessage((ushort)msgEntity.PacketId.Value, message);
                                awaitingAckCount++;
                            }
                            else if (ackStatus == MqttMessageAckStatusEnum.WaitingPubRec || 
                                   ackStatus == MqttMessageAckStatusEnum.WaitingPubComp)
                            {
                                // QoS 2 待完成消息
                                if (msgEntity.PacketId.HasValue)
                                {
                                    session.RestoreAwaitingCompMessage((ushort)msgEntity.PacketId.Value, message);
                                    awaitingCompCount++;
                                }
                            }
                            break;

                        case MqttMessageStatusEnum.Failed:
                            // 失败的消息，如果还有重试次数，重新加入待发送队列
                            if (msgEntity.RetryCount < msgEntity.MaxRetryCount)
                            {
                                session.EnqueueMessage(message);
                                pendingCount++;
                            }
                            break;

                        case MqttMessageStatusEnum.Expired:
                        case MqttMessageStatusEnum.Acknowledged:
                            // 过期或已确认的消息，忽略（可以考虑清理）
                            break;

                        default:
                            _logger.LogWarning("未知的消息状态: {Status}, MessageId: {MessageId}", 
                                status, msgEntity.MessageId);
                            break;
                    }
                }

                _logger.LogDebug("恢复会话消息: ClientId={ClientId}, 待发送={PendingCount}, 待确认={AwaitingAckCount}, 待完成={AwaitingCompCount}", 
                    session.ClientId, pendingCount, awaitingAckCount, awaitingCompCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载会话待发送消息时发生错误: ClientId={ClientId}", session.ClientId);
            }
        }

        /// <summary>
        /// 清理数据库中的过期会话
        /// </summary>
        private async Task CleanupExpiredSessionFromDatabaseAsync(string clientId)
        {
            // 使用信号量防止并发数据库操作冲突
            await _dbOperationSemaphore.WaitAsync();
            try
            {
                // 删除会话记录
                await _db.Deleteable<MqttSessionEntity>()
                    .Where(x => x.ClientId == clientId)
                    .ExecuteCommandAsync();

                // 删除相关的订阅记录
                await _db.Deleteable<MqttSubscriptionEntity>()
                    .Where(x => x.ClientId == clientId)
                    .ExecuteCommandAsync();

                // 删除相关的待发送消息记录
                await _db.Deleteable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == clientId)
                    .ExecuteCommandAsync();

                _logger.LogDebug("清理数据库中的过期会话: ClientId={ClientId}", clientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理数据库中的过期会话时发生错误: ClientId={ClientId}", clientId);
            }
            finally
            {
                _dbOperationSemaphore.Release();
            }
        }

        /// <summary>
        /// 清理过期会话（内存和数据库）
        /// 供后台服务调用
        /// </summary>
        public async Task CleanupExpiredSessionsAsync()
        {
            try
            {
                // 1. 清理内存中的过期会话
                var expiredSessions = new List<string>();
                
                foreach (var kvp in _sessions)
                {
                    var session = kvp.Value;
                    if (session.IsExpired())
                    {
                        expiredSessions.Add(kvp.Key);
                    }
                }
                
                foreach (var clientId in expiredSessions)
                {
                    if (_sessions.TryRemove(clientId, out var session))
                    {
                        session.Cleanup();
                        _logger.LogDebug("清理过期会话: ClientId={ClientId}", clientId);
                    }
                }
                
                if (expiredSessions.Count > 0)
                {
                    _logger.LogInformation("清理了 {Count} 个内存中的过期会话", expiredSessions.Count);
                }

                // 2. 清理数据库中的过期会话
                if (_options.PersistSessions)
                {
                    await CleanupExpiredSessionsFromDatabaseAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期会话时发生错误");
            }
        }

        /// <summary>
        /// 清理数据库中的所有过期会话
        /// </summary>
        private async Task CleanupExpiredSessionsFromDatabaseAsync()
        {
            try
            {
                var now = DateTime.Now;
                
                // 查找过期的会话
                var expiredSessionIds = await _db.Queryable<MqttSessionEntity>()
                    .Where(x => x.ExpiryTime.HasValue && x.ExpiryTime < now)
                    .Select(x => x.ClientId)
                    .ToListAsync();

                if (!expiredSessionIds.Any())
                    return;

                // 删除过期会话的订阅记录
                await _db.Deleteable<MqttSubscriptionEntity>()
                    .Where(x => expiredSessionIds.Contains(x.ClientId))
                    .ExecuteCommandAsync();

                // 删除过期会话的待发送消息记录
                await _db.Deleteable<MqttPendingMessageEntity>()
                    .Where(x => expiredSessionIds.Contains(x.ClientId))
                    .ExecuteCommandAsync();

                // 删除过期会话记录
                var deletedCount = await _db.Deleteable<MqttSessionEntity>()
                    .Where(x => x.ExpiryTime.HasValue && x.ExpiryTime < now)
                    .ExecuteCommandAsync();

                if (deletedCount > 0)
                {
                    _logger.LogInformation("从数据库清理了 {Count} 个过期持久会话", deletedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理数据库中的过期会话时发生错误");
            }
        }

        #endregion

        #region 私有方法

        private MqttClientInfo CreateClientInfo(MqttClientConnection connection)
        {
            var subscriptions = new List<string>();
            if (_sessions.TryGetValue(connection.ClientId, out var session))
            {
                subscriptions.AddRange(session.GetSubscriptions().Select(s => s.TopicFilter));
            }
            
            return new MqttClientInfo
            {
                ClientId = connection.ClientId,
                IpAddress = connection.RemoteEndPoint?.ToString(),
                ConnectedTime = connection.ConnectedTime,
                Username = connection.Username,
                CleanSession = connection.CleanSession,
                KeepAlive = connection.KeepAlive,
                SubscribedTopics = subscriptions
            };
        }

        private void RecordConnection()
        {
            lock (_statisticsLock)
            {
                _statistics.TotalConnections++;
                var currentCount = _connections.Count;
                if (currentCount > _statistics.MaxConcurrentConnections)
                {
                    _statistics.MaxConcurrentConnections = currentCount;
                }
            }
        }

        private void RecordDisconnection(bool isAbnormal)
        {
            lock (_statisticsLock)
            {
                _statistics.TotalDisconnections++;
                if (isAbnormal)
                {
                    _statistics.AbnormalDisconnections++;
                }
            }
        }

        private void RecordAuthenticationFailure()
        {
            lock (_statisticsLock)
            {
                _statistics.AuthenticationFailures++;
            }
        }

        private void RecordConnectionRejection()
        {
            lock (_statisticsLock)
            {
                _statistics.ConnectionRejections++;
            }
        }

        private void OnStatisticsTimer(object sender, ElapsedEventArgs e)
        {
            try
            {
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "统计信息更新失败");
            }
        }

        private void UpdateStatistics()
        {
            lock (_statisticsLock)
            {
                // 计算平均连接持续时间
                if (_statistics.TotalDisconnections > 0)
                {
                    var totalDuration = 0.0;
                    var count = 0;
                    
                    foreach (var connection in _connections.Values)
                    {
                        var duration = (DateTime.Now - connection.ConnectedTime).TotalSeconds;
                        totalDuration += duration;
                        count++;
                    }
                    
                    if (count > 0)
                    {
                        _statistics.AverageConnectionDuration = totalDuration / count;
                    }
                }
                
                // 更新IP连接统计
                _statistics.ConnectionsByIp.Clear();
                foreach (var kvp in _connectionsByIp)
                {
                    _statistics.ConnectionsByIp[kvp.Key] = kvp.Value;
                }
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            
            try
            {
                _statisticsTimer?.Stop();
                _statisticsTimer?.Dispose();
                
                // 断开所有连接
                var disconnectTasks = new List<Task>();
                foreach (var clientId in _connections.Keys.ToList())
                {
                    disconnectTasks.Add(RemoveConnectionAsync(clientId, DisconnectReasonEnum.ServerDisconnect, false));
                }
                
                Task.WaitAll(disconnectTasks.ToArray(), TimeSpan.FromSeconds(30));
                
                // 清理会话
                foreach (var session in _sessions.Values)
                {
                    session.Cleanup();
                }

                _connections.Clear();
                _sessions.Clear();
                _connectionStatuses.Clear();
                _connectionsByIp.Clear();
                _clientBlacklist.Clear();
                _ipBlacklist.Clear();

                // 释放数据库操作信号量
                _dbOperationSemaphore?.Dispose();

                _logger.LogInformation("MQTT连接管理器已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放MQTT连接管理器时发生错误");
            }
        }

        #endregion

        /// <summary>
        /// 更新待发送消息状态
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="status">新状态</param>
        /// <param name="errorMessage">错误信息（可选）</param>
        public async Task UpdatePendingMessageStatusAsync(string messageId, int status, string? errorMessage = null)
        {
            try
            {
                if (!_options.PersistSessions)
                    return;

                var updateFields = new MqttPendingMessageEntity
                {
                    Status = status,
                    ErrorMessage = errorMessage
                };

                if (status == (int)MqttMessageStatusEnum.Sending) // 发送中
                {
                    updateFields.LastRetryTime = DateTime.Now;
                }
                else if (status == (int)MqttMessageStatusEnum.Acknowledged) // 已确认
                {
                    updateFields.AckTime = DateTime.Now;
                    updateFields.AckStatus = (int)MqttMessageAckStatusEnum.NoAckRequired;
                }

                await _db.Updateable(updateFields)
                    .Where(x => x.MessageId == messageId)
                    .UpdateColumns(x => new { x.Status, x.ErrorMessage, x.LastRetryTime, x.AckTime, x.AckStatus })
                    .ExecuteCommandAsync();

                _logger.LogDebug("更新待发送消息状态: MessageId={MessageId}, Status={Status}", messageId, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新待发送消息状态时发生错误: MessageId={MessageId}", messageId);
            }
        }

        /// <summary>
        /// 更新消息确认状态（新增方法）
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="packetId">包ID</param>
        /// <param name="ackStatus">确认状态</param>
        /// <param name="messageStatus">消息状态（可选）</param>
        public async Task UpdateMessageAckStatusAsync(string clientId, ushort packetId, MqttMessageAckStatusEnum ackStatus, MqttMessageStatusEnum? messageStatus = null)
        {
            try
            {
                if (!_options.PersistSessions)
                    return;

                var updateFields = new MqttPendingMessageEntity
                {
                    AckStatus = (int)ackStatus
                };

                if (messageStatus.HasValue)
                {
                    updateFields.Status = (int)messageStatus.Value;
                    
                    if (messageStatus.Value == MqttMessageStatusEnum.Acknowledged)
                    {
                        updateFields.AckTime = DateTime.Now;
                    }
                }

                if (messageStatus.HasValue)
                {
                    await _db.Updateable(updateFields)
                        .Where(x => x.ClientId == clientId && x.PacketId == packetId)
                        .UpdateColumns(it => new { it.AckStatus, it.Status, it.AckTime })
                        .ExecuteCommandAsync();
                }
                else
                {
                    await _db.Updateable(updateFields)
                        .Where(x => x.ClientId == clientId && x.PacketId == packetId)
                        .UpdateColumns(it => new { it.AckStatus })
                        .ExecuteCommandAsync();
                }

                _logger.LogDebug("更新消息确认状态: ClientId={ClientId}, PacketId={PacketId}, AckStatus={AckStatus}, MessageStatus={MessageStatus}", 
                    clientId, packetId, ackStatus, messageStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新消息确认状态时发生错误: ClientId={ClientId}, PacketId={PacketId}", clientId, packetId);
            }
        }

        /// <summary>
        /// 增加消息重试次数
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="errorMessage">错误信息</param>
        public async Task IncrementMessageRetryCountAsync(string messageId, string? errorMessage = null)
        {
            try
            {
                if (!_options.PersistSessions)
                    return;

                var message = await _db.Queryable<MqttPendingMessageEntity>()
                    .Where(x => x.MessageId == messageId)
                    .FirstAsync();

                if (message != null)
                {
                    message.RetryCount++;
                    message.LastRetryTime = DateTime.Now;
                    message.ErrorMessage = errorMessage;

                    // 如果超过最大重试次数，标记为失败
                    if (message.RetryCount >= message.MaxRetryCount)
                    {
                        message.Status = 5; // 发送失败
                    }

                    await _db.Updateable(message)
                        .UpdateColumns(x => new { x.RetryCount, x.LastRetryTime, x.ErrorMessage, x.Status })
                        .ExecuteCommandAsync();

                    _logger.LogDebug("增加消息重试次数: MessageId={MessageId}, RetryCount={RetryCount}", 
                        messageId, message.RetryCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "增加消息重试次数时发生错误: MessageId={MessageId}", messageId);
            }
        }

        /// <summary>
        /// 清理过期的待发送消息
        /// </summary>
        public async Task CleanupExpiredPendingMessagesAsync()
        {
            try
            {
                if (!_options.PersistSessions)
                    return;

                var now = DateTime.Now;
                
                // 删除过期的待发送消息
                var deletedCount = await _db.Deleteable<MqttPendingMessageEntity>()
                    .Where(x => x.ExpiryTime.HasValue && x.ExpiryTime < now)
                    .ExecuteCommandAsync();

                // 删除状态为失败或已过期的消息（保留一段时间后清理）
                var cleanupTime = now.AddDays(-7); // 保留7天
                var cleanupCount = await _db.Deleteable<MqttPendingMessageEntity>()
                    .Where(x => (x.Status == 5 || x.Status == 6) && x.CreatedTime < cleanupTime)
                    .ExecuteCommandAsync();

                if (deletedCount > 0 || cleanupCount > 0)
                {
                    _logger.LogInformation("清理过期待发送消息: 过期消息={ExpiredCount}, 失败消息={FailedCount}", 
                        deletedCount, cleanupCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期待发送消息时发生错误");
            }
        }

        /// <summary>
        /// 获取客户端的待发送消息统计
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>消息统计信息</returns>
        public async Task<PendingMessageStatistics> GetPendingMessageStatisticsAsync(string clientId)
        {
            try
            {
                if (!_options.PersistSessions)
                    return new PendingMessageStatistics();

                var statistics = await _db.Queryable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == clientId)
                    .GroupBy(x => x.Status)
                    .Select(g => new { Status = g.Status, Count = SqlFunc.AggregateCount(g.Status) })
                    .ToListAsync();

                var result = new PendingMessageStatistics
                {
                    ClientId = clientId
                };

                foreach (var stat in statistics)
                {
                    switch (stat.Status)
                    {
                        case 1: result.PendingCount = stat.Count; break;
                        case 2: result.SendingCount = stat.Count; break;
                        case 3: result.SentCount = stat.Count; break;
                        case 4: result.AcknowledgedCount = stat.Count; break;
                        case 5: result.FailedCount = stat.Count; break;
                        case 6: result.ExpiredCount = stat.Count; break;
                    }
                }

                result.TotalCount = statistics.Sum(s => s.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取待发送消息统计时发生错误: ClientId={ClientId}", clientId);
                return new PendingMessageStatistics { ClientId = clientId };
            }
        }

        /// <summary>
        /// 保存QoS 2临时存储的消息到数据库
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="qos2Messages">QoS 2消息字典</param>
        public async Task SaveQos2ReceivedMessagesAsync(string clientId, IReadOnlyDictionary<ushort, MqttMessage> qos2Messages)
        {
            try
            {
                if (!qos2Messages.Any())
                    return;

                var messageEntities = qos2Messages.Select(kvp => new MqttPendingMessageEntity
                {
                    MessageId = kvp.Value.MessageId.ToString(),
                    ClientId = clientId,
                    Topic = (kvp.Value as MqttPublishMessage)?.Topic ?? string.Empty,
                    Payload = (kvp.Value as MqttPublishMessage)?.Payload ?? Array.Empty<byte>(),
                    Qos = kvp.Value.QualityOfService,
                    Retain = kvp.Value.Retain,
                    Duplicate = kvp.Value.IsDuplicate,
                    PacketId = kvp.Key, // 使用字典的Key作为PacketId
                    MessageType = 1, // PUBLISH
                    Priority = 2, // 普通优先级
                    CreatedTime = DateTime.Now,
                    ScheduleTime = null,
                    ExpiryTime = DateTime.Now.AddMinutes(30), // 30分钟后过期
                    RetryCount = 0,
                    MaxRetryCount = 3,
                    LastRetryTime = null,
                    Status = (int)MqttMessageStatusEnum.Sent, // 已发送，等待PUBREL
                    AckStatus = (int)MqttMessageAckStatusEnum.WaitingPubRel, // 等待PUBREL
                    ErrorMessage = string.Empty,
                    PublisherId = string.Empty
                }).ToList();

                await _db.Insertable(messageEntities).ExecuteCommandAsync();
                
                _logger.LogDebug("保存QoS 2临时消息: ClientId={ClientId}, 消息数量={Count}", 
                    clientId, qos2Messages.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存QoS 2临时消息时发生错误: ClientId={ClientId}", clientId);
            }
        }

        /// <summary>
        /// 从数据库加载QoS 2临时消息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>QoS 2消息字典</returns>
        public async Task<Dictionary<ushort, MqttMessage>> LoadQos2ReceivedMessagesAsync(string clientId)
        {
            try
            {
                var messageEntities = await _db.Queryable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == clientId && 
                               x.AckStatus == (int)MqttMessageAckStatusEnum.WaitingPubRel)
                    .ToListAsync();

                var qos2Messages = new Dictionary<ushort, MqttMessage>();

                foreach (var msgEntity in messageEntities)
                {
                    if (msgEntity.PacketId.HasValue)
                    {
                        var message = CreateMessageFromEntity(msgEntity);

                        qos2Messages[(ushort)msgEntity.PacketId.Value] = message;
                    }
                }

                _logger.LogDebug("加载QoS 2临时消息: ClientId={ClientId}, 消息数量={Count}", 
                    clientId, qos2Messages.Count);

                return qos2Messages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载QoS 2临时消息时发生错误: ClientId={ClientId}", clientId);
                return new Dictionary<ushort, MqttMessage>();
            }
        }

        /// <summary>
        /// 清理QoS 2临时消息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="messageId">消息ID（可选，如果不指定则清理所有）</param>
        public async Task CleanupQos2ReceivedMessagesAsync(string clientId, ushort? messageId = null)
        {
            try
            {
                var query = _db.Deleteable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == clientId && 
                               x.AckStatus == (int)MqttMessageAckStatusEnum.WaitingPubRel);

                if (messageId.HasValue)
                {
                    query = query.Where(x => x.PacketId == messageId.Value);
                }

                await query.ExecuteCommandAsync();
                
                _logger.LogDebug("清理QoS 2临时消息: ClientId={ClientId}, MessageId={MessageId}", 
                    clientId, messageId?.ToString() ?? "All");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理QoS 2临时消息时发生错误: ClientId={ClientId}", clientId);
            }
        }

        /// <summary>
        /// 获取QoS 2临时消息（用于恢复）
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>QoS 2消息列表</returns>
        public async Task<List<MqttPublishMessage>> GetQos2ReceivedMessagesAsync(string clientId)
        {
            try
            {
                var messageEntities = await _db.Queryable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == clientId && 
                               x.AckStatus == (int)MqttMessageAckStatusEnum.WaitingPubRel)
                    .ToListAsync();

                var qos2Messages = new List<MqttPublishMessage>();

                foreach (var msgEntity in messageEntities)
                {
                    if (msgEntity.PacketId.HasValue)
                    {
                        var message = new MqttPublishMessage
                        {
                            MessageId = (ushort)msgEntity.PacketId.Value,
                            Topic = msgEntity.Topic ?? string.Empty,
                            Payload = msgEntity.Payload ?? Array.Empty<byte>(),
                            QualityOfService = (byte)msgEntity.Qos,
                            Retain = msgEntity.Retain,
                            IsDuplicate = msgEntity.Duplicate
                        };

                        qos2Messages.Add(message);
                    }
                }

                _logger.LogDebug("获取QoS 2临时消息: ClientId={ClientId}, 消息数量={Count}", 
                    clientId, qos2Messages.Count);

                return qos2Messages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取QoS 2临时消息时发生错误: ClientId={ClientId}", clientId);
                return new List<MqttPublishMessage>();
            }
        }
    }
} 