﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Core.Cache;
using Admin.Core.File;
using Admin.Core.File.Containers;
using Admin.Core.Ip2region;
using Admin.Core.Signalr;
using Admin.Core.SnowFlakeId;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Admin.SqlSugar;

using Serilog;
using Serilog.Events;

using Volo.Abp.AspNetCore.SignalR;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.FileSystem;
using Volo.Abp.Modularity;
using Volo.Abp.Timing;

namespace Admin.Core
{
    [DependsOn(typeof(AdminSqlSugarModule), typeof(AbpAspNetCoreSignalRModule), typeof(AbpBlobStoringFileSystemModule))]
    public class AdminCoreModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();

            context.Services.AddSnowflakeId();
            context.Services.AddPurestCache();
            context.Services.AddPurestSignalr();
            context.Services.AddIp2region();
            context.Services.AddFileStorage();

            ConfigFileStorage(configuration);
            ConfigSerilog(context);
            ConfigClock();
        }

        private void ConfigClock()
        {
            Configure<AbpClockOptions>(options =>
            {
                options.Kind = DateTimeKind.Local;
            });
        }

        private void ConfigFileStorage(IConfiguration configuration)
        {
            Configure<AbpBlobStoringOptions>(options =>
            {
                var fileSystemOptions = configuration.GetRequiredSection(nameof(FileSystemOptions)).Get<FileSystemOptions>();
                options.Containers.Configure<ProfileSystemContainer>(container =>
                {
                    container.UseFileSystem(fileSystem =>
                    {
                        fileSystem.BasePath = fileSystemOptions.Path ?? Path.Combine(AppContext.BaseDirectory, "FileSystem");
                    });
                });
            });
        }

        private void ConfigSerilog(ServiceConfigurationContext context)
        {
            var template = "[{Timestamp:HH:mm:ss}] [{Level:u3}] {SourceContext} {NewLine}{Message:lj}{NewLine}{Exception}{NewLine}";
            context.Services.AddSerilog(options =>
            {
                // 框架原本配置
                //options.MinimumLevel.Override("Microsoft", LogEventLevel.Warning);
                //options.WriteTo.Console();
                //options.WriteTo.File($"{AppContext.BaseDirectory}/logs/.txt", LogEventLevel.Warning, template, rollingInterval: RollingInterval.Day, shared: true);

                // 开发配置
                // 设置全局最小日志级别为Debug
                options.MinimumLevel.Debug();

                // 覆盖Microsoft相关日志为Warning级别，减少噪音
                options.MinimumLevel.Override("Microsoft", LogEventLevel.Warning);
                options.MinimumLevel.Override("System", LogEventLevel.Warning);

                // 控制台输出Debug级别及以上的日志
                options.WriteTo.Console(LogEventLevel.Debug, template);

                // 文件输出Information级别及以上的日志
                options.WriteTo.File($"{AppContext.BaseDirectory}/logs/.txt", LogEventLevel.Information, template, rollingInterval: RollingInterval.Day, shared: true);
            });
        }
    }
}
