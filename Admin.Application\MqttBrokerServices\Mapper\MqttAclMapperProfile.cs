// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.MqttBrokerServices.Dto;
using Admin.Communication.Mqtt.Configuration;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.MqttBrokerServices.Mapper;

/// <summary>
/// MQTT ACL映射配置
/// </summary>
public class MqttAclMapperProfile : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // AddAclRuleInput -> MqttAclRuleEntity
        config.ForType<AddAclRuleInput, MqttAclRuleEntity>()
            .IgnoreNullValues(true)
            .Map(dest => dest.AccessType, src => (int)src.AccessType);

        // PutAclRuleInput -> MqttAclRuleEntity
        config.ForType<PutAclRuleInput, MqttAclRuleEntity>()
            .IgnoreNullValues(true)
            .Map(dest => dest.AccessType, src => (int)src.AccessType);

        // MqttAclRuleEntity -> AclRuleOutput
        config.ForType<MqttAclRuleEntity, AclRuleOutput>()
            .Map(dest => dest.AccessType, src => (MqttAccessType)src.AccessType);
    }
} 