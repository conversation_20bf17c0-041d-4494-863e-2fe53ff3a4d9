﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;
/// <summary>
/// MQTT ACL权限规则
/// </summary>
[SugarTable("MQTT_ACL_RULE")]
public class MqttAclRuleEntity : BaseEntity
{
    /// <summary>
    /// 规则名称
    /// </summary>
    [SugarColumn(ColumnName = "RULE_NAME")]
    public string RuleName { get; set; } = string.Empty;

    /// <summary>
    /// 优先级 (数值越大优先级越高)
    /// </summary>
    [SugarColumn(ColumnName = "PRIORITY")]
    public int Priority { get; set; } = 100;

    /// <summary>
    /// 访问类型 (0=All, 1=Publish, 2=Subscribe)
    /// </summary>
    [SugarColumn(ColumnName = "ACCESS_TYPE")]
    public int AccessType { get; set; }

    /// <summary>
    /// 权限 (1=允许, 0=拒绝)
    /// </summary>
    [SugarColumn(ColumnName = "ALLOW")]
    public bool Allow { get; set; } = true;

    /// <summary>
    /// 用户名 (为空表示适用于所有用户)
    /// </summary>
    [SugarColumn(ColumnName = "USERNAME", IsNullable = true)]
    public string? Username { get; set; }

    /// <summary>
    /// 客户端ID (为空表示适用于所有客户端)
    /// </summary>
    [SugarColumn(ColumnName = "CLIENT_ID", IsNullable = true)]
    public string? ClientId { get; set; }

    /// <summary>
    /// IP地址 (为空表示适用于所有IP)
    /// </summary>
    [SugarColumn(ColumnName = "IP_ADDRESS", IsNullable = true)]
    public string? IpAddress { get; set; }

    /// <summary>
    /// 主题 (支持通配符)
    /// </summary>
    [SugarColumn(ColumnName = "TOPIC")]
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 规则描述
    /// </summary>
    [SugarColumn(ColumnName = "DESCRIPTION", IsNullable = true)]
    public string? Description { get; set; }

    /// <summary>
    /// 是否激活 (1=激活, 0=禁用)
    /// </summary>
    [SugarColumn(ColumnName = "IS_ACTIVE")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 生效开始时间
    /// </summary>
    [SugarColumn(ColumnName = "EFFECTIVE_START_TIME", IsNullable = true)]
    public DateTime? EffectiveStartTime { get; set; }

    /// <summary>
    /// 生效结束时间
    /// </summary>
    [SugarColumn(ColumnName = "EFFECTIVE_END_TIME", IsNullable = true)]
    public DateTime? EffectiveEndTime { get; set; }
}
