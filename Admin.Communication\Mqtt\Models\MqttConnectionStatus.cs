using System;
using Admin.Multiplex.Contracts.Enums.Mqtt;

namespace Admin.Communication.Mqtt.Models
{
    /// <summary>
    /// MQTT连接状态
    /// </summary>
    public enum ConnectionStatus
    {
        /// <summary>
        /// 未连接
        /// </summary>
        Disconnected = 0,

        /// <summary>
        /// 正在连接
        /// </summary>
        Connecting = 1,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected = 2,

        /// <summary>
        /// 正在断开连接
        /// </summary>
        Disconnecting = 3,

        /// <summary>
        /// 连接超时
        /// </summary>
        Timeout = 4,

        /// <summary>
        /// 连接错误
        /// </summary>
        Error = 5,

        /// <summary>
        /// 认证中
        /// </summary>
        Authenticating = 6,

        /// <summary>
        /// 认证失败
        /// </summary>
        AuthenticationFailed = 7,

        /// <summary>
        /// 授权失败
        /// </summary>
        AuthorizationFailed = 8,

        /// <summary>
        /// 被拒绝
        /// </summary>
        Rejected = 9
    }

    /// <summary>
    /// MQTT连接状态信息
    /// </summary>
    public class MqttConnectionStatusInfo
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public ConnectionStatusEnum Status { get; set; }

        /// <summary>
        /// 状态变化时间
        /// </summary>
        public DateTime StatusChangedTime { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string? StatusDescription { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 连接持续时间
        /// </summary>
        public TimeSpan ConnectionDuration => Status == ConnectionStatusEnum.Connected 
            ? DateTime.Now - StatusChangedTime 
            : TimeSpan.Zero;

        /// <summary>
        /// 是否为活跃状态
        /// </summary>
        public bool IsActive => Status == ConnectionStatusEnum.Connected || Status == ConnectionStatusEnum.Connecting;

        /// <summary>
        /// 是否为错误状态
        /// </summary>
        public bool IsError => Status == ConnectionStatusEnum.Error;

        /// <summary>
        /// 创建连接状态信息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="status">状态</param>
        /// <param name="description">描述</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>状态信息</returns>
        public static MqttConnectionStatusInfo Create(string clientId, ConnectionStatusEnum status, string? description = null, string? errorMessage = null)
        {
            return new MqttConnectionStatusInfo
            {
                ClientId = clientId,
                Status = status,
                StatusChangedTime = DateTime.Now,
                StatusDescription = description ?? GetDefaultStatusDescription(status),
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// 获取默认状态描述
        /// </summary>
        /// <param name="status">状态</param>
        /// <returns>状态描述</returns>
        private static string GetDefaultStatusDescription(ConnectionStatusEnum status)
        {
            return status switch
            {
                ConnectionStatusEnum.Disconnected => "已断开连接",
                ConnectionStatusEnum.Connecting => "正在连接",
                ConnectionStatusEnum.Connected => "已连接",
                ConnectionStatusEnum.Disconnecting => "正在断开连接",
                ConnectionStatusEnum.Suspended => "已暂停",
                ConnectionStatusEnum.Error => "连接错误",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="newStatus">新状态</param>
        /// <param name="description">描述</param>
        /// <param name="errorMessage">错误信息</param>
        public void UpdateStatus(ConnectionStatusEnum newStatus, string? description = null, string? errorMessage = null)
        {
            Status = newStatus;
            StatusChangedTime = DateTime.Now;
            StatusDescription = description ?? GetDefaultStatusDescription(newStatus);
            ErrorMessage = errorMessage;
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            var result = $"ClientId: {ClientId}, Status: {Status}, Time: {StatusChangedTime:yyyy-MM-dd HH:mm:ss}";
            
            if (!string.IsNullOrEmpty(StatusDescription))
            {
                result += $", Description: {StatusDescription}";
            }
            
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                result += $", Error: {ErrorMessage}";
            }
            
            return result;
        }
    }
} 