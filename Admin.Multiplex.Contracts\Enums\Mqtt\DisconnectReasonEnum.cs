namespace Admin.Multiplex.Contracts.Enums.Mqtt
{
    /// <summary>
    /// 断开连接原因
    /// </summary>
    public enum DisconnectReasonEnum
    {
        /// <summary>
        /// 正常断开
        /// </summary>
        NormalDisconnection,

        /// <summary>
        /// 客户端请求断开
        /// </summary>
        ClientDisconnect,

        /// <summary>
        /// 服务器主动断开
        /// </summary>
        ServerDisconnect,

        /// <summary>
        /// 连接超时
        /// </summary>
        Timeout,

        /// <summary>
        /// 网络错误
        /// </summary>
        NetworkError,

        /// <summary>
        /// 协议错误
        /// </summary>
        ProtocolError,

        /// <summary>
        /// 认证失败
        /// </summary>
        AuthenticationFailure,

        /// <summary>
        /// 授权失败
        /// </summary>
        AuthorizationFailure,

        /// <summary>
        /// 被新连接替换
        /// </summary>
        ReplacedByNewConnection
    }
} 