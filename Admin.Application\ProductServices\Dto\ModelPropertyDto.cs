// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Application.ProductServices.Dto;

/// <summary>
/// 模型属性输入DTO
/// </summary>
public class ModelPropertyInput
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 指令ID
    /// </summary>
    public long InstructionId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 参数序号
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 枚举说明
    /// 示例: 0=运行;1=停止
    /// 0=浮冲;1=均冲;2=放电
    /// </summary>
    public string EnumDescription { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 倍率
    /// </summary>
    public int DivisionFactor { get; set; }

    /// <summary>
    /// 小数位数
    /// </summary>
    public int DecimalPlaces { get; set; }

    /// <summary>
    /// 校正比例
    /// 解析值*校正比例+校正幅度，默认为1
    /// </summary>
    public decimal CorrectionScale { get; set; } = 1;

    /// <summary>
    /// 校正幅度
    /// 默认为0
    /// </summary>
    public decimal CorrectionAmplitude { get; set; } = 0;

    /// <summary>
    /// 报警上限
    /// </summary>
    public decimal AlarmUpperLimit { get; set; }

    /// <summary>
    /// 报警上限解除值
    /// </summary>
    public decimal AlarmUpperLimitClearValue { get; set; }

    /// <summary>
    /// 报警下限
    /// </summary>
    public decimal AlarmLowerLimit { get; set; }

    /// <summary>
    /// 报警下限解除值
    /// </summary>
    public decimal AlarmLowerLimitClearValue { get; set; }

    /// <summary>
    /// 预警上限
    /// </summary>
    public decimal WarningUpperLimit { get; set; }

    /// <summary>
    /// 预警上限解除值
    /// </summary>
    public decimal WarningUpperLimitClearValue { get; set; }

    /// <summary>
    /// 预警下限
    /// </summary>
    public decimal WarningLowerLimit { get; set; }

    /// <summary>
    /// 预警下限解除值
    /// </summary>
    public decimal WarningLowerLimitClearValue { get; set; }

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool IsSave { get; set; } = true;

    /// <summary>
    /// 保存幅度
    /// 与上次保存值的偏差超过幅度，触发保存
    /// </summary>
    public decimal SaveAmplitude { get; set; }

    /// <summary>
    /// 保存幅度类型
    /// 0: 数值 1：百分比
    /// 默认数值
    /// </summary>
    public int SaveAmplitudeType { get; set; }

    /// <summary>
    /// 保存间隔(毫秒)
    /// </summary>
    public int SaveInterval { get; set; }

    /// <summary>
    /// 监控状态
    /// 0: 不启用 1：启用
    /// </summary>
    public int MonitorStatus { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 模型属性更新输入DTO
/// </summary>
public class UpdateModelPropertyInput : ModelPropertyInput
{
    /// <summary>
    /// 属性ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
/// 模型属性输出DTO
/// </summary>
public class ModelPropertyOutput
{
    /// <summary>
    /// 属性ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 指令ID
    /// </summary>
    public long InstructionId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 参数序号
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 数据类型名称
    /// </summary>
    public string DataTypeName { get; set; }

    /// <summary>
    /// 枚举说明
    /// 示例: 0=运行;1=停止
    /// 0=浮冲;1=均冲;2=放电
    /// </summary>
    public string EnumDescription { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 倍率
    /// </summary>
    public int DivisionFactor { get; set; }

    /// <summary>
    /// 小数位数
    /// </summary>
    public int DecimalPlaces { get; set; }

    /// <summary>
    /// 校正比例
    /// 解析值*校正比例+校正幅度，默认为1
    /// </summary>
    public decimal CorrectionScale { get; set; }

    /// <summary>
    /// 校正幅度
    /// 默认为0
    /// </summary>
    public decimal CorrectionAmplitude { get; set; }

    /// <summary>
    /// 报警上限
    /// </summary>
    public decimal AlarmUpperLimit { get; set; }

    /// <summary>
    /// 报警上限解除值
    /// </summary>
    public decimal AlarmUpperLimitClearValue { get; set; }

    /// <summary>
    /// 报警下限
    /// </summary>
    public decimal AlarmLowerLimit { get; set; }

    /// <summary>
    /// 报警下限解除值
    /// </summary>
    public decimal AlarmLowerLimitClearValue { get; set; }

    /// <summary>
    /// 预警上限
    /// </summary>
    public decimal WarningUpperLimit { get; set; }

    /// <summary>
    /// 预警上限解除值
    /// </summary>
    public decimal WarningUpperLimitClearValue { get; set; }

    /// <summary>
    /// 预警下限
    /// </summary>
    public decimal WarningLowerLimit { get; set; }

    /// <summary>
    /// 预警下限解除值
    /// </summary>
    public decimal WarningLowerLimitClearValue { get; set; }

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool IsSave { get; set; }

    /// <summary>
    /// 保存幅度
    /// 与上次保存值的偏差超过幅度，触发保存
    /// </summary>
    public decimal SaveAmplitude { get; set; }

    /// <summary>
    /// 保存幅度类型
    /// 0: 数值 1：百分比
    /// 默认数值
    /// </summary>
    public int SaveAmplitudeType { get; set; }

    /// <summary>
    /// 保存间隔(毫秒)
    /// </summary>
    public int SaveInterval { get; set; }

    /// <summary>
    /// 监控状态
    /// 0: 不启用 1：启用
    /// </summary>
    public int MonitorStatus { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreateBy { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdateBy { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// 模型属性查询输入DTO
/// </summary>
public class ModelPropertyQueryInput : PaginationParams
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long? ModelId { get; set; }

    /// <summary>
    /// 指令ID
    /// </summary>
    public long? InstructionId { get; set; }

    /// <summary>
    /// 参数标识符
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public int? DataType { get; set; }

    /// <summary>
    /// 监控状态
    /// </summary>
    public int? MonitorStatus { get; set; }

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool? IsSave { get; set; }
}

/// <summary>
/// 模型属性简单输出DTO (用于下拉选择等场景)
/// </summary>
public class ModelPropertySimpleOutput
{
    /// <summary>
    /// 属性ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 参数标识符
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 数据类型名称
    /// </summary>
    public string DataTypeName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }
}

/// <summary>
/// 批量添加模型属性输入DTO
/// </summary>
public class BatchAddModelPropertyInput
{
    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 属性列表
    /// </summary>
    public List<ModelPropertyInput> Properties { get; set; } = [];
}

// 为了保持向后兼容，保留原有的DTO类名作为别名
/// <summary>
/// 产品属性输入DTO (向后兼容别名)
/// </summary>
public class ProductPropertyInput : ModelPropertyInput { }

/// <summary>
/// 产品属性更新输入DTO (向后兼容别名)
/// </summary>
public class UpdateProductPropertyInput : UpdateModelPropertyInput { }

/// <summary>
/// 产品属性输出DTO (向后兼容别名)
/// </summary>
public class ProductPropertyOutput : ModelPropertyOutput { }

/// <summary>
/// 产品属性查询输入DTO (向后兼容别名)
/// </summary>
public class ProductPropertyQueryInput : ModelPropertyQueryInput { }

/// <summary>
/// 产品属性简单输出DTO (向后兼容别名)
/// </summary>
public class ProductPropertySimpleOutput : ModelPropertySimpleOutput { }

/// <summary>
/// 批量添加产品属性输入DTO (向后兼容别名)
/// </summary>
public class BatchAddProductPropertyInput : BatchAddModelPropertyInput { }