using Admin.Communication.Mqtt.DataParsers;
using Admin.Communication.Mqtt.TopicResolvers;
using Admin.Communication.Mqtt.TopicFilters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Admin.Communication.Extensions
{
    /// <summary>
    /// 数据解析器服务扩展
    /// </summary>
    public static class DataParserServiceExtensions
    {
        /// <summary>
        /// 添加数据解析器服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddDataParserServices(this IServiceCollection services)
        {
            // 注册主题过滤器
            services.TryAddScoped<ITopicFilter, DeviceTopicFilter>();

            // 注册预置主题管理器
            services.TryAddSingleton<PresetTopicManager>();

            // 注册主题解析器
            services.TryAddScoped<ITopicResolver, DeviceTopicResolver>();

            // 注册数据解析器工厂
            services.TryAddSingleton<IDataParserFactory, DataParserFactory>();

            // 注册具体的数据解析器
            services.TryAddTransient<JsonDataParser>();
            services.TryAddTransient<HexDataParser>();

            // 配置数据解析器工厂
            services.AddSingleton<IDataParserFactory>(provider =>
            {
                var factory = new DataParserFactory();

                // 注册JSON解析器
                var jsonParser = provider.GetRequiredService<JsonDataParser>();
                factory.RegisterParser(jsonParser);

                // 注册HEX解析器
                var hexParser = provider.GetRequiredService<HexDataParser>();
                factory.RegisterParser(hexParser);

                return factory;
            });

            return services;
        }
    }
}
