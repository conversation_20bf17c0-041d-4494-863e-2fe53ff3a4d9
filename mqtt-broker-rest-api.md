# MQTT代理服务 REST API 设计文档

## API 概览

基于当前MQTT代理的实现程度，设计一套完整的REST API接口，用于管理和监控MQTT代理服务。API支持代理服务管理、连接管理、会话管理、统计监控、权限控制等功能。

### 基础信息
- **API版本**: v1
- **基础路径**: `/api/v1/mqtt`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

---

## 1. 代理服务管理 API (完成)

### 1.1 启动代理服务
```http
POST /api/v1/mqtt/broker/start
```

**请求参数**:
```json
{
  "port": 1883,
  "configuration": {
    "maxConnections": 10000,
    "maxConnectionsPerIp": 100,
    "connectionTimeout": 60,
    "enableTls": false,
    "allowAnonymousAccess": true
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "MQTT代理服务已启动",
  "data": {
    "port": 1883,
    "isRunning": true,
    "startTime": "2024-01-15T10:30:00Z"
  }
}
```

### 1.2 停止代理服务
```http
POST /api/v1/mqtt/broker/stop
```

**响应**:
```json
{
  "success": true,
  "message": "MQTT代理服务已停止",
  "data": {
    "isRunning": false,
    "stopTime": "2024-01-15T12:30:00Z"
  }
}
```

### 1.3 获取代理服务状态
```http
GET /api/v1/mqtt/broker/status
```

**响应**:
```json
{
  "success": true,
  "data": {
    "isRunning": true,
    "port": 1883,
    "startTime": "2024-01-15T10:30:00Z",
    "uptime": "02:15:30",
    "version": "1.0.0",
    "configuration": {
      "maxConnections": 10000,
      "maxConnectionsPerIp": 100,
      "connectionTimeout": 60,
      "enableRetainedMessages": true,
      "enableStatistics": true,
      "allowAnonymousAccess": true
    }
  }
}
```

### 1.4 重启代理服务
```http
POST /api/v1/mqtt/broker/restart
```

**请求参数**:
```json
{
  "gracefulShutdown": true,
  "shutdownTimeout": 30
}
```

**响应**:
```json
{
  "success": true,
  "message": "MQTT代理服务重启成功",
  "data": {
    "restartTime": "2024-01-15T12:45:00Z",
    "previousUptime": "02:15:00",
    "port": 1883
  }
}
```

---

## 2. 连接管理 API （完成）

### 2.1 获取所有连接
```http
GET /api/v1/mqtt/connections
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20)
- `clientId`: 客户端ID过滤
- `username`: 用户名过滤
- `ipAddress`: IP地址过滤
- `status`: 连接状态过滤

**响应**:
```json
{
  "success": true,
  "data": {
    "connections": [
      {
        "clientId": "device_001",
        "username": "iot_user",
        "ipAddress": "*************:54321",
        "connectedTime": "2024-01-15T10:30:15Z",
        "lastActivityTime": "2024-01-15T12:30:15Z",
        "keepAlive": 60,
        "cleanSession": true,
        "protocolVersion": "3.1.1",
        "status": "Connected",
        "subscribedTopics": ["sensors/+/temperature", "devices/device_001/status"]
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalCount": 150,
      "totalPages": 8
    }
  }
}
```

### 2.2 获取单个连接详情
```http
GET /api/v1/mqtt/connections/{clientId}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "clientId": "device_001",
    "username": "iot_user",
    "ipAddress": "*************:54321",
    "connectedTime": "2024-01-15T10:30:15Z",
    "lastActivityTime": "2024-01-15T12:30:15Z",
    "connectionDuration": "02:00:00",
    "keepAlive": 60,
    "cleanSession": true,
    "protocolVersion": "3.1.1",
    "status": "Connected",
    "subscribedTopics": [
      {
        "topicFilter": "sensors/+/temperature",
        "qos": 1,
        "subscribedTime": "2024-01-15T10:31:00Z"
      }
    ],
    "statistics": {
      "messagesSent": 1250,
      "messagesReceived": 3420,
      "bytesTransferred": 156780,
      "lastMessageTime": "2024-01-15T12:29:45Z"
    }
  }
}
```

### 2.3 断开指定连接
```http
DELETE /api/v1/mqtt/connections/{clientId}
```

**请求参数**:
```json
{
  "reason": "Administrative disconnect",
  "sendDisconnectMessage": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "客户端连接已断开",
  "data": {
    "clientId": "device_001",
    "disconnectTime": "2024-01-15T12:35:00Z",
    "reason": "Administrative disconnect"
  }
}
```

### 2.4 批量断开连接
```http
POST /api/v1/mqtt/connections/batch-disconnect
```

**请求参数**:
```json
{
  "clientIds": ["device_001", "device_002", "device_003"],
  "reason": "Server maintenance",
  "sendDisconnectMessage": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "批量断开连接完成",
  "data": {
    "totalRequested": 3,
    "successfulDisconnects": 2,
    "failedDisconnects": 1,
    "results": [
      {
        "clientId": "device_001",
        "success": true,
        "disconnectTime": "2024-01-15T12:35:00Z"
      },
      {
        "clientId": "device_002",
        "success": true,
        "disconnectTime": "2024-01-15T12:35:01Z"
      },
      {
        "clientId": "device_003",
        "success": false,
        "error": "Client not found"
      }
    ]
  }
}
```

---

## 3. 会话管理 API （完成）

### 3.1 获取所有会话
```http
GET /api/v1/mqtt/sessions
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20)
- `clientId`: 客户端ID过滤
- `state`: 会话状态过滤 (Active, Suspended, Terminated)
- `persistent`: 是否持久会话 (true/false)

**响应**:
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "sessionId": "sess_abc123",
        "clientId": "device_001",
        "cleanSession": false,
        "state": "Active",
        "createdTime": "2024-01-15T10:30:15Z",
        "lastActivityTime": "2024-01-15T12:30:15Z",
        "expiryTime": "2024-01-16T10:30:15Z",
        "subscriptionCount": 3,
        "pendingMessageCount": 5,
        "awaitingAckCount": 2,
        "awaitingCompCount": 0
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalCount": 45,
      "totalPages": 3
    }
  }
}
```

### 3.2 获取单个会话详情
```http
GET /api/v1/mqtt/sessions/{sessionId}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "sessionId": "sess_abc123",
    "clientId": "device_001",
    "cleanSession": false,
    "state": "Active",
    "createdTime": "2024-01-15T10:30:15Z",
    "lastActivityTime": "2024-01-15T12:30:15Z",
    "expiryTime": "2024-01-16T10:30:15Z",
    "duration": "02:00:00",
    "isExpired": false,
    "subscriptions": [
      {
        "topicFilter": "sensors/+/temperature",
        "qos": 1,
        "subscribedTime": "2024-01-15T10:31:00Z"
      }
    ],
    "pendingMessages": [
      {
        "messageId": 1234,
        "topic": "devices/device_001/command",
        "qos": 1,
        "payload": "eyJjbWQiOiJyZXN0YXJ0In0=",
        "enqueuedTime": "2024-01-15T12:25:00Z"
      }
    ],
    "statistics": {
      "subscriptionCount": 3,
      "pendingMessageCount": 5,
      "sentMessageCount": 1250,
      "receivedMessageCount": 3420,
      "failedMessageCount": 2,
      "lastSentMessageTime": "2024-01-15T12:29:45Z",
      "lastReceivedMessageTime": "2024-01-15T12:29:30Z"
    }
  }
}
```

### 3.3 清理会话
```http
DELETE /api/v1/mqtt/sessions/{sessionId}
```

**响应**:
```json
{
  "success": true,
  "message": "会话已清理",
  "data": {
    "sessionId": "sess_abc123",
    "clientId": "device_001",
    "cleanupTime": "2024-01-15T12:40:00Z",
    "deletedSubscriptions": 3,
    "deletedPendingMessages": 5
  }
}
```

### 3.4 暂停/恢复会话
```http
PATCH /api/v1/mqtt/sessions/{sessionId}/state
```

**请求参数**:
```json
{
  "action": "suspend", // 可选值: suspend, resume, terminate
  "reason": "Client maintenance"
}
```

**响应**:
```json
{
  "success": true,
  "message": "会话状态已更新",
  "data": {
    "sessionId": "sess_abc123",
    "clientId": "device_001",
    "oldState": "Active",
    "newState": "Suspended",
    "updateTime": "2024-01-15T12:45:00Z"
  }
}
```

---

## 4. 订阅管理 API （安全和权限）

### 4.1 获取所有订阅
```http
GET /api/v1/mqtt/subscriptions
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20)
- `clientId`: 客户端ID过滤
- `topicFilter`: 主题过滤器
- `qos`: QoS级别过滤

**响应**:
```json
{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "clientId": "device_001",
        "topicFilter": "sensors/+/temperature",
        "qos": 1,
        "subscribedTime": "2024-01-15T10:31:00Z",
        "messageCount": 245
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalCount": 320,
      "totalPages": 16
    }
  }
}
```

### 4.2 获取主题的订阅者
```http
GET /api/v1/mqtt/subscriptions/topic/{topic}/subscribers
```

**查询参数**:
- `exactMatch`: 是否精确匹配 (默认: false)

**响应**:
```json
{
  "success": true,
  "data": {
    "topic": "sensors/room1/temperature",
    "subscriberCount": 5,
    "subscribers": [
      {
        "clientId": "device_001",
        "username": "iot_user",
        "qos": 1,
        "subscribedTime": "2024-01-15T10:31:00Z",
        "matchingPattern": "sensors/+/temperature"
      }
    ]
  }
}
```

### 4.3 强制取消订阅
```http
DELETE /api/v1/mqtt/subscriptions
```

**请求参数**:
```json
{
  "clientId": "device_001",
  "topicFilter": "sensors/+/temperature"
}
```

**响应**:
```json
{
  "success": true,
  "message": "订阅已取消",
  "data": {
    "clientId": "device_001",
    "topicFilter": "sensors/+/temperature",
    "unsubscribedTime": "2024-01-15T12:50:00Z"
  }
}
```

---

## 5. 消息发布 API 完成

### 5.1 发布消息
```http
POST /api/v1/mqtt/messages/publish
```

**请求参数**:
```json
{
  "topic": "sensors/room1/temperature",
  "payload": "25.6", // 字符串或base64编码的二进制数据
  "qos": 1,
  "retain": false,
  "encoding": "utf8" // 可选值: utf8, base64
}
```

**响应**:
```json
{
  "success": true,
  "message": "消息发布成功",
  "data": {
    "messageId": "msg_xyz789",
    "topic": "sensors/room1/temperature",
    "qos": 1,
    "retain": false,
    "publishTime": "2024-01-15T12:55:00Z",
    "subscriberCount": 3,
    "payloadSize": 4
  }
}
```

### 5.2 批量发布消息
```http
POST /api/v1/mqtt/messages/batch-publish
```

**请求参数**:
```json
{
  "messages": [
    {
      "topic": "sensors/room1/temperature",
      "payload": "25.6",
      "qos": 1,
      "retain": false
    },
    {
      "topic": "sensors/room2/humidity",
      "payload": "60.2",
      "qos": 0,
      "retain": true
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "message": "批量消息发布完成",
  "data": {
    "totalMessages": 2,
    "successfulPublishes": 2,
    "failedPublishes": 0,
    "results": [
      {
        "topic": "sensors/room1/temperature",
        "success": true,
        "messageId": "msg_abc123",
        "publishTime": "2024-01-15T12:55:00Z",
        "subscriberCount": 3
      },
      {
        "topic": "sensors/room2/humidity",
        "success": true,
        "messageId": "msg_def456",
        "publishTime": "2024-01-15T12:55:01Z",
        "subscriberCount": 1
      }
    ]
  }
}
```

### 5.3 获取保留消息
```http
GET /api/v1/mqtt/messages/retained
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20)
- `topicPattern`: 主题模式过滤

**响应**:
```json
{
  "success": true,
  "data": {
    "retainedMessages": [
      {
        "topic": "sensors/room1/temperature",
        "payload": "25.6",
        "qos": 1,
        "retainTime": "2024-01-15T12:00:00Z",
        "payloadSize": 4,
        "encoding": "utf8"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalCount": 15,
      "totalPages": 1
    }
  }
}
```

### 5.4 清除保留消息
```http
DELETE /api/v1/mqtt/messages/retained/{topic}
```

**响应**:
```json
{
  "success": true,
  "message": "保留消息已清除",
  "data": {
    "topic": "sensors/room1/temperature",
    "clearTime": "2024-01-15T13:00:00Z"
  }
}
```

---

## 6. 统计监控 API （完成）

### 6.1 获取总体统计 
```http
GET /api/v1/mqtt/statistics/overview
```

**响应**:
```json
{
  "success": true,
  "data": {
    "broker": {
      "isRunning": true,
      "uptime": "02:30:15",
      "version": "1.0.0"
    },
    "connections": {
      "current": 150,
      "total": 2340,
      "totalDisconnections": 2190,
      "abnormalDisconnections": 45,
      "maxConcurrent": 280,
      "averageDuration": "01:45:30"
    },
    "sessions": {
      "active": 145,
      "suspended": 3,
      "persistent": 28,
      "totalCreated": 2340
    },
    "messages": {
      "totalPublished": 150420,
      "totalReceived": 148950,
      "retainedCount": 15,
      "qos0Count": 120340,
      "qos1Count": 28980,
      "qos2Count": 1100
    },
    "subscriptions": {
      "total": 320,
      "uniqueTopics": 125
    },
    "authentication": {
      "totalAttempts": 2450,
      "successfulAuth": 2340,
      "failedAuth": 110,
      "rejectedConnections": 55
    }
  }
}
```

### 6.2 获取实时性能指标
```http
GET /api/v1/mqtt/statistics/performance
```

**查询参数**:
- `interval`: 统计间隔 (1m, 5m, 15m, 1h)
- `duration`: 统计时长 (1h, 6h, 24h, 7d)

**响应**:
```json
{
  "success": true,
  "data": {
    "interval": "5m",
    "duration": "1h",
    "metrics": [
      {
        "timestamp": "2024-01-15T12:00:00Z",
        "connectionsPerSecond": 2.5,
        "messagesPerSecond": 145.2,
        "bytesPerSecond": 15672,
        "cpuUsage": 15.6,
        "memoryUsage": 128.5,
        "activeConnections": 148
      }
    ]
  }
}
```

### 6.3 获取连接统计
```http
GET /api/v1/mqtt/statistics/connections
```

**响应**:
```json
{
  "success": true,
  "data": {
    "currentConnections": 150,
    "totalConnections": 2340,
    "totalDisconnections": 2190,
    "abnormalDisconnections": 45,
    "authenticationFailures": 110,
    "connectionRejections": 55,
    "maxConcurrentConnections": 280,
    "averageConnectionDuration": "01:45:30",
    "connectionsByIp": {
      "*************": 5,
      "*************": 3,
      "*********": 8
    },
    "topClientsByConnections": [
      {
        "clientId": "gateway_001",
        "connectionCount": 15,
        "lastConnected": "2024-01-15T12:30:00Z"
      }
    ]
  }
}
```

### 6.4 获取消息统计
```http
GET /api/v1/mqtt/statistics/messages
```

**查询参数**:
- `period`: 统计周期 (hourly, daily, weekly)
- `from`: 开始时间
- `to`: 结束时间

**响应**:
```json
{
  "success": true,
  "data": {
    "period": "hourly",
    "totalPublished": 150420,
    "totalReceived": 148950,
    "retainedCount": 15,
    "qosDistribution": {
      "qos0": 120340,
      "qos1": 28980,
      "qos2": 1100
    },
    "hourlyStatistics": [
      {
        "hour": "2024-01-15T12:00:00Z",
        "published": 8420,
        "received": 8150,
        "averageSize": 156.7
      }
    ],
    "topTopicsByVolume": [
      {
        "topic": "sensors/+/temperature",
        "messageCount": 15240,
        "totalBytes": 152400
      }
    ]
  }
}
```

---

## 8. 用户管理 API (安全和权限)

### 8.1 获取用户列表
```http
GET /api/v1/mqtt/users
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20)
- `username`: 用户名过滤
- `isActive`: 是否激活过滤

**响应**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "username": "iot_user",
        "displayName": "IoT设备用户",
        "isActive": true,
        "createdTime": "2024-01-15T09:00:00Z",
        "lastLoginTime": "2024-01-15T12:30:00Z",
        "connectionCount": 15,
        "roles": ["device", "sensor"]
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalCount": 12,
      "totalPages": 1
    }
  }
}
```

### 8.2 添加用户
```http
POST /api/v1/mqtt/users
```

**请求参数**:
```json
{
  "username": "new_user",
  "password": "secure_password",
  "displayName": "新用户",
  "roles": ["device"],
  "isActive": true,
  "description": "新建的设备用户"
}
```

**响应**:
```json
{
  "success": true,
  "message": "用户已创建",
  "data": {
    "username": "new_user",
    "displayName": "新用户",
    "isActive": true,
    "createdTime": "2024-01-15T13:30:00Z",
    "roles": ["device"]
  }
}
```

### 8.3 更新用户
```http
PUT /api/v1/mqtt/users/{username}
```

**请求参数**:
```json
{
  "displayName": "更新的用户名",
  "roles": ["device", "admin"],
  "isActive": false,
  "description": "已禁用的用户"
}
```

**响应**:
```json
{
  "success": true,
  "message": "用户已更新",
  "data": {
    "username": "new_user",
    "displayName": "更新的用户名",
    "isActive": false,
    "updatedTime": "2024-01-15T13:35:00Z",
    "roles": ["device", "admin"]
  }
}
```

### 8.4 删除用户
```http
DELETE /api/v1/mqtt/users/{username}
```

**请求参数**:
```json
{
  "forceDisconnect": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "用户已删除",
  "data": {
    "username": "new_user",
    "deletedTime": "2024-01-15T13:40:00Z",
    "disconnectedConnections": 3
  }
}
```

---

## 9. 黑名单管理 API （安全和权限）

### 9.1 获取黑名单
```http
GET /api/v1/mqtt/blacklist
```

**查询参数**:
- `type`: 类型过滤 (clientId, ip, username)
- `page`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 20)

**响应**:
```json
{
  "success": true,
  "data": {
    "blacklistEntries": [
      {
        "id": "bl_001",
        "type": "clientId",
        "value": "malicious_client",
        "reason": "恶意连接",
        "addedTime": "2024-01-15T11:00:00Z",
        "addedBy": "admin",
        "expiryTime": null,
        "isActive": true
      },
      {
        "id": "bl_002",
        "type": "ip",
        "value": "*************",
        "reason": "频繁连接",
        "addedTime": "2024-01-15T11:30:00Z",
        "addedBy": "system",
        "expiryTime": "2024-01-16T11:30:00Z",
        "isActive": true
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalCount": 8,
      "totalPages": 1
    }
  }
}
```

### 9.2 添加到黑名单
```http
POST /api/v1/mqtt/blacklist
```

**请求参数**:
```json
{
  "type": "clientId",
  "value": "spam_client",
  "reason": "垃圾消息发送",
  "expiryTime": "2024-01-16T13:45:00Z", // 可选，永久黑名单设为null
  "forceDisconnect": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "已添加到黑名单",
  "data": {
    "id": "bl_003",
    "type": "clientId",
    "value": "spam_client",
    "reason": "垃圾消息发送",
    "addedTime": "2024-01-15T13:45:00Z",
    "addedBy": "admin",
    "expiryTime": "2024-01-16T13:45:00Z",
    "disconnectedConnections": 1
  }
}
```

### 9.3 从黑名单移除
```http
DELETE /api/v1/mqtt/blacklist/{blacklistId}
```

**响应**:
```json
{
  "success": true,
  "message": "已从黑名单移除",
  "data": {
    "id": "bl_003",
    "type": "clientId",
    "value": "spam_client",
    "removedTime": "2024-01-15T13:50:00Z",
    "removedBy": "admin"
  }
}
```

---

## 10. 主题分析 API （高级功能）

### 10.1 获取主题统计
```http
GET /api/v1/mqtt/topics/statistics
```

**查询参数**:
- `pattern`: 主题模式过滤
- `sortBy`: 排序字段 (messageCount, subscriberCount, lastActivity)
- `limit`: 返回数量限制 (默认: 100)

**响应**:
```json
{
  "success": true,
  "data": {
    "totalUniqueTopics": 125,
    "topics": [
      {
        "topic": "sensors/room1/temperature",
        "messageCount": 1250,
        "subscriberCount": 3,
        "lastMessageTime": "2024-01-15T12:55:00Z",
        "firstMessageTime": "2024-01-15T10:30:00Z",
        "averageMessageSize": 156.7,
        "qosDistribution": {
          "qos0": 850,
          "qos1": 380,
          "qos2": 20
        }
      }
    ]
  }
}
```

### 10.2 获取主题树
```http
GET /api/v1/mqtt/topics/tree
```

**查询参数**:
- `maxDepth`: 最大深度 (默认: 5)
- `minMessageCount`: 最小消息数过滤

**响应**:
```json
{
  "success": true,
  "data": {
    "topicTree": {
      "sensors": {
        "messageCount": 15420,
        "children": {
          "room1": {
            "messageCount": 5140,
            "children": {
              "temperature": {
                "messageCount": 2570,
                "subscriberCount": 3,
                "isLeaf": true
              },
              "humidity": {
                "messageCount": 2570,
                "subscriberCount": 2,
                "isLeaf": true
              }
            }
          }
        }
      }
    }
  }
}
```

---

## 11. 配置管理 API （已完成）

### 11.1 获取当前配置
```http
GET /api/v1/mqtt/configuration
```

**响应**:
```json
{
  "success": true,
  "data": {
    "broker": {
      "port": 1883,
      "maxConnections": 10000,
      "maxConnectionsPerIp": 100,
      "connectionTimeout": 60,
      "enableTls": false,
      "certificatePath": "",
      "allowAnonymousAccess": true,
      "enableRetainedMessages": true,
      "enableStatistics": true,
      "maxTopicLength": 256,
      "maxMessageSize": 1048576,
      "bindAddress": "0.0.0.0"
    },
    "authentication": {
      "defaultUsername": "admin",
      "defaultPassword": "***masked***",
      "requireAuthentication": false
    },
    "acl": {
      "enableAcl": true,
      "defaultPolicy": "allow",
      "rules": []
    }
  }
}
```

### 11.2 更新配置
```http
PUT /api/v1/mqtt/configuration
```

**请求参数**:
```json
{
  "broker": {
    "maxConnections": 15000,
    "maxConnectionsPerIp": 150,
    "connectionTimeout": 90,
    "enableRetainedMessages": false
  },
  "authentication": {
    "requireAuthentication": true,
    "defaultUsername": "mqtt_admin"
  },
  "acl": {
    "enableAcl": true,
    "defaultPolicy": "deny"
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "配置已更新",
  "data": {
    "updatedTime": "2024-01-15T14:00:00Z",
    "requiresRestart": false,
    "changedSettings": [
      "broker.maxConnections",
      "broker.maxConnectionsPerIp",
      "authentication.requireAuthentication"
    ]
  }
}
```

---

## 12. 导入导出 API （高级功能）

### 12.1 导出配置
```http
GET /api/v1/mqtt/export/configuration
```

**查询参数**:
- `format`: 导出格式 (json, yaml)
- `includePasswords`: 是否包含密码 (默认: false)

**响应**:
```json
{
  "success": true,
  "data": {
    "exportTime": "2024-01-15T14:05:00Z",
    "version": "1.0.0",
    "configuration": {
      // 完整配置数据
    }
  }
}
```

### 12.2 导入配置
```http
POST /api/v1/mqtt/import/configuration
```

**请求参数**:
```json
{
  "configuration": {
    // 完整配置数据
  },
  "validateOnly": false,
  "backupCurrent": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "配置导入成功",
  "data": {
    "importTime": "2024-01-15T14:10:00Z",
    "backupId": "backup_20240115_141000",
    "requiresRestart": true,
    "validationResults": {
      "isValid": true,
      "warnings": [],
      "errors": []
    }
  }
}
```

### 12.3 导出统计数据
```http
GET /api/v1/mqtt/export/statistics
```

**查询参数**:
- `from`: 开始时间
- `to`: 结束时间
- `format`: 导出格式 (json, csv)
- `categories`: 统计类别 (connections, messages, sessions)

**响应**:
```json
{
  "success": true,
  "data": {
    "exportTime": "2024-01-15T14:15:00Z",
    "period": {
      "from": "2024-01-15T00:00:00Z",
      "to": "2024-01-15T23:59:59Z"
    },
    "statistics": {
      // 详细统计数据
    }
  }
}
```

---

## 错误响应格式

所有API的错误响应都遵循统一格式：

```json
{
  "success": false,
  "error": {
    "code": "MQTT_BROKER_001",
    "message": "MQTT代理服务未运行",
    "details": "请先启动MQTT代理服务",
    "timestamp": "2024-01-15T14:20:00Z",
    "path": "/api/v1/mqtt/broker/status"
  }
}
```

### 常见错误代码

| 错误代码 | 描述 |
|---------|------|
| MQTT_BROKER_001 | 代理服务未运行 |
| MQTT_BROKER_002 | 代理服务启动失败 |
| MQTT_CONNECTION_001 | 客户端连接不存在 |
| MQTT_CONNECTION_002 | 连接断开失败 |
| MQTT_SESSION_001 | 会话不存在 |
| MQTT_SESSION_002 | 会话操作失败 |
| MQTT_PUBLISH_001 | 消息发布失败 |
| MQTT_PUBLISH_002 | 主题格式无效 |
| MQTT_ACL_001 | ACL规则不存在 |
| MQTT_ACL_002 | 权限检查失败 |
| MQTT_USER_001 | 用户不存在 |
| MQTT_USER_002 | 用户操作失败 |
| MQTT_CONFIG_001 | 配置无效 |
| MQTT_CONFIG_002 | 配置更新失败 |

---

## API 认证与授权

### JWT Token 格式
```json
{
  "sub": "admin_user",
  "role": "administrator",
  "permissions": [
    "mqtt:broker:manage",
    "mqtt:connections:view",
    "mqtt:connections:manage",
    "mqtt:sessions:manage",
    "mqtt:messages:publish",
    "mqtt:acl:manage",
    "mqtt:users:manage",
    "mqtt:statistics:view",
    "mqtt:configuration:manage"
  ],
  "exp": 1705329600,
  "iat": 1705326000
}
```

### 权限说明

| 权限 | 描述 |
|-----|------|
| mqtt:broker:manage | 代理服务管理权限 |
| mqtt:connections:view | 连接查看权限 |
| mqtt:connections:manage | 连接管理权限 |
| mqtt:sessions:manage | 会话管理权限 |
| mqtt:messages:publish | 消息发布权限 |
| mqtt:acl:manage | ACL规则管理权限 |
| mqtt:users:manage | 用户管理权限 |
| mqtt:statistics:view | 统计查看权限 |
| mqtt:configuration:manage | 配置管理权限 |

---

## 使用示例

### 启动代理并发布消息
```bash
# 1. 启动代理服务
curl -X POST http://localhost:5000/api/v1/mqtt/broker/start \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"port": 1883}'

# 2. 发布消息
curl -X POST http://localhost:5000/api/v1/mqtt/messages/publish \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "sensors/temperature",
    "payload": "25.6",
    "qos": 1,
    "retain": true
  }'

# 3. 查看连接状态
curl -X GET http://localhost:5000/api/v1/mqtt/connections \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 配置ACL权限
```bash
# 添加ACL规则
curl -X POST http://localhost:5000/api/v1/mqtt/acl/rules \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "priority": 100,
    "accessType": "publish",
    "permission": "allow",
    "username": "sensor_user",
    "topic": "sensors/+/data"
  }'
```

---

此REST API设计基于当前MQTT代理的完整实现，提供了全面的管理和监控功能，支持企业级的MQTT代理服务管理需求。 