// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Modbus.Models;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Collections.Concurrent;
using System.Text;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Admin.Communication.Modbus.Workers;

/// <summary>
/// Modbus指令调度服务
/// 负责按指令级别调度Modbus RTU指令的执行
/// 支持每条指令独立的时间参数：ReadInterval、ResponseTime、RetryCount
/// </summary>
public class ModbusInstructionSchedulerService : AsyncPeriodicBackgroundWorkerBase
{
    private readonly ILogger<ModbusInstructionSchedulerService> _logger;
    private readonly IConfiguration _configuration;
    
    // 指令调度状态存储 (DeviceInstructionId -> InstructionScheduleState)
    private readonly ConcurrentDictionary<long, InstructionScheduleState> _instructionSchedules = new();
    
    // 配置参数
    private readonly int _schedulerInterval;
    private readonly int _maxConcurrentDevices;
    private readonly int _deviceInstructionDelay;

    public ModbusInstructionSchedulerService(
        AbpAsyncTimer timer, 
        IServiceScopeFactory serviceScopeFactory,
        IConfiguration configuration,
        ILogger<ModbusInstructionSchedulerService> logger) 
        : base(timer, serviceScopeFactory)
    {
        _logger = logger;
        _configuration = configuration;
        
        // 从配置读取参数
        _schedulerInterval = _configuration.GetValue<int>("Modbus:SchedulerInterval", 1000);
        _maxConcurrentDevices = _configuration.GetValue<int>("Modbus:MaxConcurrentDevices", 10);
        _deviceInstructionDelay = _configuration.GetValue<int>("Modbus:DeviceInstructionDelay", 100);
        
        // 设置调度器执行间隔
        Timer.Period = _schedulerInterval;
        
        _logger.LogInformation("Modbus指令调度服务初始化完成: Interval={Interval}ms, MaxDevices={MaxDevices}", 
            _schedulerInterval, _maxConcurrentDevices);
    }

    /// <summary>
    /// 调度器主循环
    /// </summary>
    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        try
        {
            using var scope = workerContext.ServiceProvider.CreateScope();
            var db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
            var mqttBroker = scope.ServiceProvider.GetRequiredService<IMqttBroker>();

            // 1. 刷新指令调度状态
            await RefreshInstructionSchedulesAsync(db);

            // 2. 获取需要执行的指令
            var currentTime = DateTime.UtcNow;
            var readyInstructions = _instructionSchedules.Values
                .Where(x => x.Status == InstructionExecutionStatus.Ready && 
                           x.NextExecutionTime <= currentTime)
                .ToList();

            if (readyInstructions.Count == 0)
            {
                return;
            }

            _logger.LogDebug("准备执行 {Count} 条指令", readyInstructions.Count);

            // 3. 按设备分组并发执行
            var deviceGroups = readyInstructions
                .GroupBy(x => x.DeviceId)
                .Take(_maxConcurrentDevices) // 限制并发设备数量
                .ToList();

            var executionTasks = deviceGroups.Select(async deviceGroup =>
            {
                try
                {
                    await ExecuteDeviceInstructionsAsync(deviceGroup.Key, deviceGroup.ToList(), mqttBroker);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行设备指令时发生错误: DeviceId={DeviceId}", deviceGroup.Key);
                }
            });

            await Task.WhenAll(executionTasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Modbus指令调度器执行时发生错误");
        }
    }

    /// <summary>
    /// 刷新指令调度状态
    /// </summary>
    private async Task RefreshInstructionSchedulesAsync(ISqlSugarClient db)
    {
        try
        {
            // 查询所有启用的Modbus设备指令
            var deviceInstructions = await db.Queryable<DeviceInstructionEntity>()
                .LeftJoin<DeviceEntity>((di, d) => di.DeviceId == d.Id)
                .LeftJoin<ProductEntity>((di, d, p) => d.ModelId == p.Id)
                .Where((di, d, p) => di.IsEnabled &&
                                   p.ProtocolType == (int)ProtocolTypeEnum.Modbus &&
                                   p.DataFormat == (int)DataFormatEnum.Hex)
                .Select((di, d, p) => new
                {
                    di.Id,
                    di.DeviceId,
                    di.InstructionName,
                    di.send_str,
                    // 使用默认值，因为数据库表可能还没有这些字段
                    ReadInterval = 10000,    // 默认10秒
                    ResponseTime = 2000,     // 默认2秒
                    RetryCount = 3,          // 默认重试3次
                    di.IsEnabled
                })
                .ToListAsync();

            // 更新或添加指令调度状态
            foreach (var instruction in deviceInstructions)
            {
                if (!_instructionSchedules.TryGetValue(instruction.Id, out var schedule))
                {
                    // 新指令，创建调度状态
                    schedule = new InstructionScheduleState
                    {
                        DeviceInstructionId = instruction.Id,
                        DeviceId = instruction.DeviceId,
                        InstructionName = instruction.InstructionName,
                        Command = instruction.send_str,
                        ReadInterval = instruction.ReadInterval,
                        ResponseTime = instruction.ResponseTime,
                        RetryCount = instruction.RetryCount,
                        Status = InstructionExecutionStatus.Ready,
                        NextExecutionTime = DateTime.UtcNow.AddSeconds(1) // 1秒后开始执行
                    };

                    _instructionSchedules.TryAdd(instruction.Id, schedule);
                    
                    _logger.LogDebug("添加新指令调度: DeviceId={DeviceId}, Instruction={Instruction}", 
                        instruction.DeviceId, instruction.InstructionName);
                }
                else
                {
                    // 更新现有指令参数
                    schedule.ReadInterval = instruction.ReadInterval;
                    schedule.ResponseTime = instruction.ResponseTime;
                    schedule.RetryCount = instruction.RetryCount;
                    
                    // 如果指令被禁用，更新状态
                    if (!instruction.IsEnabled && schedule.Status != InstructionExecutionStatus.Disabled)
                    {
                        schedule.Status = InstructionExecutionStatus.Disabled;
                        _logger.LogDebug("禁用指令: DeviceId={DeviceId}, Instruction={Instruction}", 
                            instruction.DeviceId, instruction.InstructionName);
                    }
                    else if (instruction.IsEnabled && schedule.Status == InstructionExecutionStatus.Disabled)
                    {
                        schedule.Status = InstructionExecutionStatus.Ready;
                        schedule.NextExecutionTime = DateTime.UtcNow.AddSeconds(1);
                        _logger.LogDebug("启用指令: DeviceId={DeviceId}, Instruction={Instruction}", 
                            instruction.DeviceId, instruction.InstructionName);
                    }
                }
            }

            // 移除已删除的指令
            var activeInstructionIds = deviceInstructions.Select(x => x.Id).ToHashSet();
            var toRemove = _instructionSchedules.Keys.Where(id => !activeInstructionIds.Contains(id)).ToList();
            
            foreach (var id in toRemove)
            {
                if (_instructionSchedules.TryRemove(id, out var removed))
                {
                    _logger.LogDebug("移除指令调度: DeviceId={DeviceId}, Instruction={Instruction}", 
                        removed.DeviceId, removed.InstructionName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新指令调度状态时发生错误");
        }
    }

    /// <summary>
    /// 执行单个设备的指令列表
    /// </summary>
    private async Task ExecuteDeviceInstructionsAsync(long deviceId, List<InstructionScheduleState> instructions, IMqttBroker mqttBroker)
    {
        // 按下次执行时间排序
        var sortedInstructions = instructions.OrderBy(x => x.NextExecutionTime).ToList();

        foreach (var instruction in sortedInstructions)
        {
            try
            {
                await ExecuteInstructionAsync(instruction, mqttBroker);

                // 设备内指令间的最小间隔，避免设备过载
                if (sortedInstructions.IndexOf(instruction) < sortedInstructions.Count - 1)
                {
                    await Task.Delay(_deviceInstructionDelay);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行指令时发生错误: DeviceId={DeviceId}, Instruction={Instruction}",
                    deviceId, instruction.InstructionName);

                await HandleInstructionError(instruction, ex);
            }
        }
    }

    /// <summary>
    /// 执行单条指令
    /// </summary>
    private async Task ExecuteInstructionAsync(InstructionScheduleState instruction, IMqttBroker mqttBroker)
    {
        _logger.LogDebug("开始执行指令: DeviceId={DeviceId}, Instruction={Instruction}, Command={Command}",
            instruction.DeviceId, instruction.InstructionName, instruction.Command);

        // 1. 更新状态为执行中
        instruction.Status = InstructionExecutionStatus.Executing;
        instruction.LastExecutionTime = DateTime.UtcNow;
        instruction.SendTime = DateTime.UtcNow;

        // 2. 创建响应等待任务
        instruction.PendingResponse = new TaskCompletionSource<ModbusResponse>();

        // 3. 注册到响应处理器
        ModbusResponseHandler.RegisterPendingInstruction(instruction);

        try
        {
            // 4. 发布MQTT指令
            var topic = $"/devices/{instruction.DeviceId}/modbus/command/down";
            var payload = Encoding.UTF8.GetBytes(instruction.Command);

            await mqttBroker.PublishAsync(topic, payload);

            // 5. 更新状态为等待响应
            instruction.Status = InstructionExecutionStatus.WaitingResponse;

            _logger.LogDebug("指令已发送，等待响应: DeviceId={DeviceId}, Topic={Topic}",
                instruction.DeviceId, topic);

            // 6. 等待响应（带超时）
            var timeoutTask = Task.Delay(instruction.ResponseTime);
            var completedTask = await Task.WhenAny(instruction.PendingResponse.Task, timeoutTask);

            if (completedTask == timeoutTask)
            {
                // 超时处理
                await HandleInstructionTimeout(instruction);
            }
            else
            {
                // 响应成功
                var response = await instruction.PendingResponse.Task;
                await HandleInstructionSuccess(instruction, response);
            }
        }
        finally
        {
            // 清理响应等待
            ModbusResponseHandler.RemovePendingInstruction(instruction.DeviceId.ToString());
        }
    }

    /// <summary>
    /// 处理指令超时
    /// </summary>
    private async Task HandleInstructionTimeout(InstructionScheduleState instruction)
    {
        instruction.CurrentRetryCount++;

        if (instruction.CurrentRetryCount < instruction.RetryCount)
        {
            // 还有重试次数，准备重试
            instruction.Status = InstructionExecutionStatus.Retrying;
            instruction.NextExecutionTime = DateTime.UtcNow.AddMilliseconds(500); // 500ms后重试

            _logger.LogWarning("指令超时，准备重试: DeviceId={DeviceId}, Instruction={Instruction}, Retry={Retry}/{MaxRetry}",
                instruction.DeviceId, instruction.InstructionName,
                instruction.CurrentRetryCount, instruction.RetryCount);
        }
        else
        {
            // 重试次数用完，标记失败并安排下次执行
            instruction.Status = InstructionExecutionStatus.Failed;
            instruction.CurrentRetryCount = 0;
            instruction.NextExecutionTime = DateTime.UtcNow.AddMilliseconds(instruction.ReadInterval);

            _logger.LogError("指令执行失败，重试次数已用完: DeviceId={DeviceId}, Instruction={Instruction}",
                instruction.DeviceId, instruction.InstructionName);
        }

        instruction.PendingResponse = null;
    }

    /// <summary>
    /// 处理指令执行成功
    /// </summary>
    private async Task HandleInstructionSuccess(InstructionScheduleState instruction, ModbusResponse response)
    {
        instruction.Status = InstructionExecutionStatus.Success;
        instruction.CurrentRetryCount = 0;
        instruction.NextExecutionTime = DateTime.UtcNow.AddMilliseconds(instruction.ReadInterval);
        instruction.PendingResponse = null;

        _logger.LogDebug("指令执行成功: DeviceId={DeviceId}, Instruction={Instruction}, RegisterCount={RegisterCount}",
            instruction.DeviceId, instruction.InstructionName, response.RegisterValues.Count);

        // 处理响应数据（解析并保存设备数据）
        await ProcessModbusResponseAsync(instruction, response);
    }

    /// <summary>
    /// 处理指令执行错误
    /// </summary>
    private async Task HandleInstructionError(InstructionScheduleState instruction, Exception ex)
    {
        instruction.Status = InstructionExecutionStatus.Failed;
        instruction.CurrentRetryCount = 0;
        instruction.NextExecutionTime = DateTime.UtcNow.AddMilliseconds(instruction.ReadInterval);
        instruction.PendingResponse = null;

        _logger.LogError(ex, "指令执行发生异常: DeviceId={DeviceId}, Instruction={Instruction}",
            instruction.DeviceId, instruction.InstructionName);
    }

    /// <summary>
    /// 处理Modbus响应数据
    /// </summary>
    private async Task ProcessModbusResponseAsync(InstructionScheduleState instruction, ModbusResponse response)
    {
        try
        {
            if (!response.IsSuccess || response.RegisterValues.Count == 0)
            {
                _logger.LogWarning("Modbus响应无效或无数据: DeviceId={DeviceId}, Error={Error}",
                    instruction.DeviceId, response.ErrorMessage);
                return;
            }

            // 这里可以添加设备数据保存逻辑
            // 例如：保存到实时数据表和历史数据表
            _logger.LogDebug("处理Modbus响应数据: DeviceId={DeviceId}, RegisterValues={RegisterValues}",
                instruction.DeviceId, string.Join(", ", response.RegisterValues.Select(kv => $"{kv.Key}={kv.Value}")));

            // TODO: 实现设备数据保存逻辑
            // await SaveDeviceDataAsync(instruction.DeviceId, response.RegisterValues);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理Modbus响应数据时发生错误: DeviceId={DeviceId}", instruction.DeviceId);
        }
    }

    /// <summary>
    /// 获取指令调度状态（用于监控和调试）
    /// </summary>
    public Dictionary<long, InstructionScheduleState> GetInstructionSchedules()
    {
        return new Dictionary<long, InstructionScheduleState>(_instructionSchedules);
    }

    /// <summary>
    /// 手动触发指令执行（用于测试）
    /// </summary>
    public async Task TriggerInstructionAsync(long deviceInstructionId)
    {
        if (_instructionSchedules.TryGetValue(deviceInstructionId, out var instruction))
        {
            instruction.NextExecutionTime = DateTime.UtcNow;
            instruction.Status = InstructionExecutionStatus.Ready;

            _logger.LogInformation("手动触发指令执行: DeviceId={DeviceId}, Instruction={Instruction}",
                instruction.DeviceId, instruction.InstructionName);
        }
    }
}
