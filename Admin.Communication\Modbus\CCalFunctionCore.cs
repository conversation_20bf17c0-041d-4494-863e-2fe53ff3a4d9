using System;
using System.Text;

namespace ProtocolParseCore
{
    /// <summary>
    /// 核心计算函数类 - 包含常用的数据转换和校验方法
    /// </summary>
    public static class CCalFunctionCore
    {
        #region 数据校验方法

        /// <summary>
        /// CRC校验
        /// </summary>
        public static byte BitCRC(byte[] data)
        {
            byte crcLow = 0xFF;
            byte crcHigh = 0xFF;
            byte poly1 = 0xA0;
            byte poly2 = 0x01;

            for (int i = 0; i < data.Length; i++)
            {
                crcLow ^= data[i];
                for (int j = 0; j < 8; j++)
                {
                    byte tempLow = crcLow;
                    byte tempHigh = crcHigh;
                    
                    crcLow = (byte)(crcLow >> 1);
                    crcHigh = (byte)(crcHigh >> 1);
                    
                    if ((tempHigh & 1) == 1)
                        crcLow = (byte)(crcLow + 128);
                    
                    if ((tempLow & 1) == 1)
                    {
                        crcLow ^= poly2;
                        crcHigh ^= poly1;
                    }
                }
            }
            
            return (byte)((crcLow == 0 && crcHigh == 0) ? 0 : 1);
        }

        /// <summary>
        /// 7E协议校验
        /// </summary>
        public static bool CheckSum7E(string source)
        {
            if (!source.StartsWith("~"))
                return true;

            if (!source.EndsWith(" ") && !source.EndsWith("\r"))
                source += "\r";

            string checksum = source.Substring(source.Length - 5, 4);
            source = source.Substring(1, source.Length - 6);
            
            byte[] bytes = Encoding.ASCII.GetBytes(source);
            long sum = 0;
            
            foreach (byte b in bytes)
            {
                sum += b;
                sum %= 65536;
            }
            
            sum ^= 0xFFFF;
            sum = (sum + 1) % 65536;
            
            string calculated = sum.ToString("X").PadLeft(4, '0');
            return !calculated.Equals(checksum, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 检查7E协议格式
        /// </summary>
        public static bool Check7ECode(string source)
        {
            if (!source.StartsWith("~"))
                return false;

            for (int i = 1; i < source.Length - 1; i++)
            {
                char c = source[i];
                if (!((c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || c == ' ' || c == '\r'))
                    return false;
            }
            return true;
        }

        #endregion

        #region 数值转换方法

        /// <summary>
        /// 双字节转数值
        /// </summary>
        public static decimal Word2Value(byte high, byte low)
        {
            return new decimal(high * 256 + low);
        }

        /// <summary>
        /// 双字节补码转数值
        /// </summary>
        public static decimal Word2ValueOfComplement(byte high, byte low)
        {
            if (high > 127)
            {
                high ^= 0xFF;
                low ^= 0xFF;
                return new decimal(-(high * 256 + low + 1));
            }
            else
            {
                return new decimal(high * 256 + low);
            }
        }

        /// <summary>
        /// 四字节转数值
        /// </summary>
        public static decimal DoubleWord2Value(byte b1, byte b2, byte b3, byte b4)
        {
            decimal result = new decimal(b1);
            result = decimal.Add(decimal.Multiply(result, 256m), new decimal(b2));
            result = decimal.Add(decimal.Multiply(result, 256m), new decimal(b3));
            return decimal.Add(decimal.Multiply(result, 256m), new decimal(b4));
        }

        /// <summary>
        /// 四字节补码转数值
        /// </summary>
        public static decimal DoubleWord2ValueOfComplement(byte b1, byte b2, byte b3, byte b4)
        {
            if (b1 > 127)
            {
                b1 ^= 0xFF;
                b2 ^= 0xFF;
                b3 ^= 0xFF;
                b4 ^= 0xFF;
                return new decimal(-((b1 * 256 + b2) * 256 * 256 + b3 * 256 + b4 + 1));
            }
            else
            {
                return new decimal((b1 * 256 + b2) * 256 * 256 + b3 * 256 + b4);
            }
        }

        /// <summary>
        /// 特殊十进制转换（用于特定设备）
        /// </summary>
        public static decimal Decimal2Decimal1(long dec)
        {
            decimal num = new decimal(dec - 51);
            return decimal.Subtract(num, new decimal((long)num / 16 * 6));
        }

        #endregion

        #region 进制转换方法

        /// <summary>
        /// ASCII十六进制转二进制
        /// </summary>
        public static string Asc2Bin(string ascHex)
        {
            StringBuilder result = new StringBuilder();
            
            foreach (char c in ascHex)
            {
                switch (c)
                {
                    case '0': result.Append("0000"); break;
                    case '1': result.Append("0001"); break;
                    case '2': result.Append("0010"); break;
                    case '3': result.Append("0011"); break;
                    case '4': result.Append("0100"); break;
                    case '5': result.Append("0101"); break;
                    case '6': result.Append("0110"); break;
                    case '7': result.Append("0111"); break;
                    case '8': result.Append("1000"); break;
                    case '9': result.Append("1001"); break;
                    case 'A': result.Append("1010"); break;
                    case 'B': result.Append("1011"); break;
                    case 'C': result.Append("1100"); break;
                    case 'D': result.Append("1101"); break;
                    case 'E': result.Append("1110"); break;
                    case 'F': result.Append("1111"); break;
                }
            }
            
            return result.ToString();
        }

        /// <summary>
        /// 二进制转整数
        /// </summary>
        public static int BinToInt(string binary)
        {
            int result = 0;
            int length = binary.Length;
            
            for (int i = 0; i < length; i++)
            {
                if (binary[length - 1 - i] == '1')
                {
                    result += (int)Math.Pow(2, i);
                }
            }
            
            return result;
        }

        /// <summary>
        /// 十六进制字符转十进制
        /// </summary>
        public static decimal MulChar2Decimal(string hexString)
        {
            decimal result = 0;
            
            foreach (char c in hexString)
            {
                result = decimal.Multiply(result, 16m);
                switch (c)
                {
                    case '0': result = decimal.Add(result, 0m); break;
                    case '1': result = decimal.Add(result, 1m); break;
                    case '2': result = decimal.Add(result, 2m); break;
                    case '3': result = decimal.Add(result, 3m); break;
                    case '4': result = decimal.Add(result, 4m); break;
                    case '5': result = decimal.Add(result, 5m); break;
                    case '6': result = decimal.Add(result, 6m); break;
                    case '7': result = decimal.Add(result, 7m); break;
                    case '8': result = decimal.Add(result, 8m); break;
                    case '9': result = decimal.Add(result, 9m); break;
                    case 'A': result = decimal.Add(result, 10m); break;
                    case 'B': result = decimal.Add(result, 11m); break;
                    case 'C': result = decimal.Add(result, 12m); break;
                    case 'D': result = decimal.Add(result, 13m); break;
                    case 'E': result = decimal.Add(result, 14m); break;
                    case 'F': result = decimal.Add(result, 15m); break;
                }
            }
            
            return result;
        }

        /// <summary>
        /// 十六进制字符串转字节数组
        /// </summary>
        public static byte[] HexChar2Byte(string hexString)
        {
            int length = hexString.Length;
            if (length % 2 != 0)
                length--;
            
            if (length == 0)
                return null;
            
            byte[] result = new byte[length / 2];
            
            for (int i = 0; i < length / 2; i++)
            {
                string highChar = hexString.Substring(i * 2, 1);
                string lowChar = hexString.Substring(i * 2 + 1, 1);
                
                byte high = Convert.ToByte(MulChar2Decimal(highChar));
                byte low = Convert.ToByte(MulChar2Decimal(lowChar));
                
                result[i] = (byte)(high * 16 + low);
            }
            
            return result;
        }

        /// <summary>
        /// 字节数组转十六进制字符串
        /// </summary>
        public static string HEX2ASC(byte[] hexBytes)
        {
            StringBuilder result = new StringBuilder();
            
            foreach (byte b in hexBytes)
            {
                string hex = b.ToString("X");
                if (hex.Length < 2)
                    hex = "0" + hex;
                result.Append(hex);
            }
            
            return result.ToString();
        }

        #endregion

        #region IEEE754 浮点数处理

        /// <summary>
        /// IEEE754 单精度浮点数解析
        /// </summary>
        public static double Ieee754(string hexFloat)
        {
            string binary = Asc2Bin(hexFloat);
            int sign = int.Parse(binary.Substring(0, 1));
            int exponent = BinToInt(binary.Substring(1, 8));
            int mantissa = BinToInt(binary.Substring(9, 23));

            double value = Math.Pow(-1.0, sign) * (1.0 + (double)mantissa / 8388608.0) * Math.Pow(2.0, exponent - 127);
            value = Math.Round(value, 2);

            if (value > 99999.0 || value < -99999.0)
                return 0.0;

            return value;
        }

        /// <summary>
        /// IEEE754 单精度浮点数解析（字节序颠倒）
        /// </summary>
        public static double Ieee754Invert(string hexFloat)
        {
            // 颠倒字节序：ABCD -> DCBA
            string inverted = hexFloat.Substring(6, 2) + hexFloat.Substring(4, 2) +
                             hexFloat.Substring(2, 2) + hexFloat.Substring(0, 2);

            string binary = Asc2Bin(inverted);
            int sign = int.Parse(binary.Substring(0, 1));
            int exponent = BinToInt(binary.Substring(1, 8));
            int mantissa = BinToInt(binary.Substring(9, 23));

            double value = Math.Pow(-1.0, sign) * (1.0 + (double)mantissa / 8388608.0) * Math.Pow(2.0, exponent - 127);
            value = Math.Round(value, 2);

            if (value > 99999.0 || value < -99999.0)
                return 0.0;

            return value;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// ASCII字符串转字节数组（用于内部计算）
        /// </summary>
        public static byte[] Asc2Byte(string asciiHex)
        {
            int length = asciiHex.Length;
            if (length % 2 != 0)
                length--;

            if (length == 0)
                return new byte[0];

            byte[] result = new byte[length / 2];

            for (int i = 0; i < length / 2; i++)
            {
                string highChar = asciiHex.Substring(i * 2, 1);
                string lowChar = asciiHex.Substring(i * 2 + 1, 1);

                byte high = OneChar2Byte(highChar);
                byte low = OneChar2Byte(lowChar);

                result[i] = (byte)(high * 16 + low);
            }

            return result;
        }

        /// <summary>
        /// 单个十六进制字符转字节
        /// </summary>
        private static byte OneChar2Byte(string hexChar)
        {
            if (hexChar.Length >= 1)
            {
                switch (hexChar)
                {
                    case "0": return 0;
                    case "1": return 1;
                    case "2": return 2;
                    case "3": return 3;
                    case "4": return 4;
                    case "5": return 5;
                    case "6": return 6;
                    case "7": return 7;
                    case "8": return 8;
                    case "9": return 9;
                    case "A": return 10;
                    case "B": return 11;
                    case "C": return 12;
                    case "D": return 13;
                    case "E": return 14;
                    case "F": return 15;
                }
            }
            return 0;
        }

        /// <summary>
        /// 计算字节和校验
        /// </summary>
        public static string ByteSum(string hexSource)
        {
            long sum = 0;
            byte[] data = Asc2Byte(hexSource);

            foreach (byte b in data)
            {
                sum += b;
            }

            sum %= 256;
            string result = sum.ToString("X");

            if (result.Length == 1)
                result = "0" + result;

            return result;
        }

        #endregion

        #region 扩展转换方法（偶尔使用）

        /// <summary>
        /// CRC16校验计算
        /// </summary>
        public static string Crc16(string hexSource)
        {
            byte crcLow = 0xFF;
            byte crcHigh = 0xFF;
            byte poly1 = 0x01;
            byte poly2 = 0xA0;

            byte[] data = HexChar2Byte(hexSource);

            foreach (byte b in data)
            {
                crcLow ^= b;

                for (int i = 0; i < 8; i++)
                {
                    byte tempHigh = crcHigh;
                    byte tempLow = crcLow;

                    crcHigh = (byte)(crcHigh / 2);
                    crcLow = (byte)(crcLow / 2);

                    if ((tempHigh & 1) == 1)
                        crcLow |= 0x80;

                    if ((tempLow & 1) == 1)
                    {
                        crcHigh ^= poly2;
                        crcLow ^= poly1;
                    }
                }
            }

            string lowHex = crcLow.ToString("X");
            string highHex = crcHigh.ToString("X");

            if (lowHex.Length == 1) lowHex = "0" + lowHex;
            if (highHex.Length == 1) highHex = "0" + highHex;

            return lowHex + highHex;
        }

        /// <summary>
        /// 单字节校验和检查
        /// </summary>
        public static bool CheckSumOfOneByte(byte[] data)
        {
            long sum = 0;

            for (int i = 0; i < data.Length - 1; i++)
            {
                sum += data[i];
            }

            sum %= 256;
            return sum != data[data.Length - 1];
        }

        /// <summary>
        /// 7E协议校验和计算
        /// </summary>
        public static string CalSum7E(string source)
        {
            source = source.Substring(1); // 移除开头的 ~
            byte[] bytes = Encoding.ASCII.GetBytes(source);
            long sum = 0;

            foreach (byte b in bytes)
            {
                sum += b;
                sum %= 65536;
            }

            sum ^= 0xFFFF;
            sum = (sum + 1) % 65536;

            return sum.ToString("X").PadLeft(4, '0');
        }

        #endregion
    }
}
