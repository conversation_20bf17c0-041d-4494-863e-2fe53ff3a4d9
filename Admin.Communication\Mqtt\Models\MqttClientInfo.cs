using System;
using System.Collections.Generic;

namespace Admin.Communication.Mqtt.Models
{
    /// <summary>
    /// MQTT客户端信息
    /// </summary>
    public class MqttClientInfo
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 客户端IP地址
        /// </summary>
        public string IpAddress { get; set; }

        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime ConnectedTime { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 是否使用了清除会话标志
        /// </summary>
        public bool CleanSession { get; set; }

        /// <summary>
        /// 保持连接时间(秒)
        /// </summary>
        public int KeepAlive { get; set; }

        /// <summary>
        /// 已订阅的主题列表
        /// </summary>
        public IReadOnlyList<string> SubscribedTopics { get; set; }
    }
} 