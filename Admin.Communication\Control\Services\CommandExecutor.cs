// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Control.Interfaces;
using Admin.Communication.Control.Models;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Admin.Communication.Control.Services;

/// <summary>
/// 指令执行器
/// </summary>
public class CommandExecutor(
    ISqlSugarClient db,
    IEnumerable<ICommandMediumExecutor> mediumExecutors,
    ILogger<CommandExecutor> logger) : ICommandExecutor
{
    private readonly ISqlSugarClient _db = db;
    private readonly IEnumerable<ICommandMediumExecutor> _mediumExecutors = mediumExecutors;
    private readonly ILogger<CommandExecutor> _logger = logger;

    /// <summary>
    /// 执行单个指令
    /// </summary>
    /// <param name="request">执行请求</param>
    /// <returns>执行结果</returns>
    public async Task<CommandExecuteResult> ExecuteAsync(ExecuteCommandRequest request)
    {
        try
        {
            _logger.LogInformation("开始执行指令: CommandId={CommandId}", request.CommandId);

            // 1. 获取指令信息
            var command = await GetCommandAsync(request.CommandId);
            if (command == null)
            {
                return CommandExecuteResult.Failed($"指令不存在: CommandId={request.CommandId}");
            }

            // 2. 验证指令是否启用
            if (command.IsAppControl != 1)
            {
                return CommandExecuteResult.Failed($"指令未启用: {command.CmdName}");
            }

            // 3. 验证目标设备
            var targetDeviceId = request.DeviceId ?? command.DeviceId;
            if (!await IsDeviceExistsAsync(targetDeviceId))
            {
                return CommandExecuteResult.Failed($"目标设备不存在: DeviceId={targetDeviceId}");
            }

            // 4. 查找对应的介质执行器
            var executor = _mediumExecutors.FirstOrDefault(e => e.CanHandle(command.CommunicationMedium));
            if (executor == null)
            {
                return CommandExecuteResult.Failed($"不支持的通讯介质: {command.CommunicationMedium}");
            }

            // 5. 执行指令
            var result = await executor.ExecuteAsync(command, request);

            _logger.LogInformation("指令执行完成: CommandId={CommandId}, Success={Success}, Result={Result}", 
                request.CommandId, result.IsSuccess, result.ControlResult);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行指令时发生错误: CommandId={CommandId}", request.CommandId);
            return CommandExecuteResult.Failed($"执行异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 批量执行指令
    /// </summary>
    /// <param name="request">批量执行请求</param>
    /// <returns>批量执行结果</returns>
    public async Task<BatchExecuteResult> BatchExecuteAsync(BatchExecuteCommandRequest request)
    {
        var result = new BatchExecuteResult
        {
            TotalCount = request.CommandIds.Count
        };

        _logger.LogInformation("开始批量执行指令: Count={Count}, Interval={IntervalMs}ms", 
            request.CommandIds.Count, request.IntervalMs);

        foreach (var commandId in request.CommandIds)
        {
            try
            {
                var executeRequest = new ExecuteCommandRequest
                {
                    CommandId = commandId,
                    Source = request.Source,
                    Username = request.Username
                };

                var executeResult = await ExecuteAsync(executeRequest);
                result.Results.Add(executeResult);

                if (executeResult.IsSuccess)
                {
                    result.SuccessCount++;
                }
                else
                {
                    result.FailedCount++;
                }

                // 如果不是最后一个指令，等待间隔时间
                if (commandId != request.CommandIds.Last() && request.IntervalMs > 0)
                {
                    await Task.Delay(request.IntervalMs);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量执行指令时发生错误: CommandId={CommandId}", commandId);
                
                result.Results.Add(CommandExecuteResult.Failed($"执行异常: {ex.Message}"));
                result.FailedCount++;
            }
        }

        _logger.LogInformation("批量执行指令完成: Total={Total}, Success={Success}, Failed={Failed}", 
            result.TotalCount, result.SuccessCount, result.FailedCount);

        return result;
    }

    /// <summary>
    /// 获取指令信息
    /// </summary>
    /// <param name="commandId">指令ID</param>
    /// <returns>指令实体</returns>
    private async Task<ControlCmdEntity?> GetCommandAsync(long commandId)
    {
        return await _db.Queryable<ControlCmdEntity>()
            .Where(c => c.Id == commandId)
            .FirstAsync();
    }

    /// <summary>
    /// 验证设备是否存在
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>是否存在</returns>
    private async Task<bool> IsDeviceExistsAsync(long deviceId)
    {
        return await _db.Queryable<DeviceEntity>()
            .Where(d => d.Id == deviceId)
            .AnyAsync();
    }
}
