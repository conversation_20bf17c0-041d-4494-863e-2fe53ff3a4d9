// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.DeviceServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;

namespace Admin.Application.DeviceServices;

/// <summary>
/// 设备指令服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class DeviceInstructionService(ISqlSugarClient db, Repository<DeviceInstructionEntity> repository, ILogger<DeviceInstructionService> logger) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<DeviceInstructionEntity> _repository = repository;
    private readonly ILogger<DeviceInstructionService> _logger = logger;

    /// <summary>
    /// 分页查询设备指令
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页结果</returns>
    public async Task<PagedList<DeviceInstructionOutput>> GetPagedListAsync(DeviceInstructionQueryInput input)
    {
        var query = _db.Queryable<DeviceInstructionEntity>()
            .WhereIF(input.DeviceId.HasValue, x => x.DeviceId == input.DeviceId.Value)
            .WhereIF(!input.InstructionName.IsNullOrEmpty(), x => x.InstructionName.Contains(input.InstructionName))
            .WhereIF(input.IsEnabled.HasValue, x => x.IsEnabled == input.IsEnabled.Value)
            .OrderBy(x => x.CreateTime, OrderByType.Desc);

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);

        // 手动映射分页结果
        var outputList = pagedList.Items.Select(MapToOutput).ToList();
        return new PagedList<DeviceInstructionOutput>
        {
            Items = outputList,
            Total = pagedList.Total,
            PageIndex = pagedList.PageIndex,
            PageSize = pagedList.PageSize
        };
    }

    /// <summary>
    /// 根据ID获取设备指令
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <returns>设备指令信息</returns>
    public async Task<DeviceInstructionOutput> GetByIdAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
        return MapToOutput(entity);
    }

    /// <summary>
    /// 根据设备ID获取指令列表
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>指令列表</returns>
    public async Task<List<DeviceInstructionOutput>> GetByDeviceIdAsync(long deviceId)
    {
        var entities = await _db.Queryable<DeviceInstructionEntity>()
            .Where(x => x.DeviceId == deviceId)
            .OrderBy(x => x.InstructionName)
            .ToListAsync();

        return entities.Select(MapToOutput).ToList();
    }

    /// <summary>
    /// 添加设备指令
    /// </summary>
    /// <param name="input">指令信息</param>
    /// <returns>指令信息</returns>
    public async Task<DeviceInstructionOutput> AddAsync(AddDeviceInstructionInput input)
    {
        // 验证设备是否存在
        await ValidateDeviceExistsAsync(input.DeviceId);

        // 验证指令名称是否重复
        await ValidateInstructionNameUniqueAsync(input.DeviceId, input.InstructionName);

        var entity = new DeviceInstructionEntity
        {
            DeviceId = input.DeviceId,
            InstructionName = input.InstructionName,
            send_str = input.SendStr,
            Encode = input.Encode,
            ResponseTime = input.ResponseTime,
            RetryCount = input.RetryCount,
            IsEnabled = input.IsEnabled,
            Remark = input.Remark
        };

        var instructionId = await _repository.InsertReturnSnowflakeIdAsync(entity);

        _logger.LogInformation("添加设备指令成功：设备ID={DeviceId}, 指令名称={InstructionName}, 指令ID={InstructionId}",
            input.DeviceId, input.InstructionName, instructionId);

        return await GetByIdAsync(instructionId);
    }

    /// <summary>
    /// 更新设备指令
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <param name="input">更新信息</param>
    /// <returns>更新后的指令信息</returns>
    public async Task<DeviceInstructionOutput> UpdateAsync(long id, UpdateDeviceInstructionInput input)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

        // 验证设备是否存在
        await ValidateDeviceExistsAsync(input.DeviceId);

        // 验证指令名称是否重复（排除当前记录）
        await ValidateInstructionNameUniqueAsync(input.DeviceId, input.InstructionName, id);

        // 手动映射字段
        entity.DeviceId = input.DeviceId;
        entity.InstructionName = input.InstructionName;
        entity.send_str = input.SendStr;
        entity.Encode = input.Encode;
        entity.ResponseTime = input.ResponseTime;
        entity.RetryCount = input.RetryCount;
        entity.IsEnabled = input.IsEnabled;
        entity.Remark = input.Remark;

        await _repository.UpdateAsync(entity);

        _logger.LogInformation("更新设备指令成功：指令ID={InstructionId}, 设备ID={DeviceId}, 指令名称={InstructionName}",
            id, input.DeviceId, input.InstructionName);

        return await GetByIdAsync(id);
    }

    /// <summary>
    /// 删除设备指令
    /// </summary>
    /// <param name="id">指令ID</param>
    public async Task DeleteAsync(long id)
    {
        var entity = await _repository.GetByIdAsync(id) ?? 
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

        await _repository.DeleteAsync(entity);

        _logger.LogInformation("删除设备指令成功：指令ID={InstructionId}, 设备ID={DeviceId}, 指令名称={InstructionName}", 
            id, entity.DeviceId, entity.InstructionName);
    }

    /// <summary>
    /// 批量删除设备指令
    /// </summary>
    /// <param name="ids">指令ID列表</param>
    public async Task BatchDeleteAsync(List<long> ids)
    {
        if (ids == null || !ids.Any())
        {
            throw PersistdValidateException.Message("请选择要删除的指令");
        }

        var entities = await _db.Queryable<DeviceInstructionEntity>()
            .Where(x => ids.Contains(x.Id))
            .ToListAsync();

        if (entities.Count != ids.Count)
        {
            throw PersistdValidateException.Message("部分指令不存在");
        }

        await _repository.DeleteAsync(entities);

        _logger.LogInformation("批量删除设备指令成功：删除数量={Count}, 指令IDs={InstructionIds}",
            entities.Count, string.Join(",", ids));
    }

    /// <summary>
    /// 启用/禁用设备指令
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <param name="isEnabled">是否启用</param>
    public async Task SetEnabledAsync(long id, bool isEnabled)
    {
        var entity = await _repository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

        entity.IsEnabled = isEnabled;
        await _repository.UpdateAsync(entity);

        _logger.LogInformation("设置设备指令状态成功：指令ID={InstructionId}, 状态={IsEnabled}", id, isEnabled);
    }



    /// <summary>
    /// 获取设备指令简单列表（用于下拉选择等场景）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="enabledOnly">是否只返回启用的指令</param>
    /// <returns>简单指令列表</returns>
    public async Task<List<DeviceInstructionSimpleOutput>> GetSimpleListAsync(long deviceId, bool enabledOnly = true)
    {
        var query = _db.Queryable<DeviceInstructionEntity>()
            .Where(x => x.DeviceId == deviceId);

        if (enabledOnly)
        {
            query = query.Where(x => x.IsEnabled);
        }

        var entities = await query
            .OrderBy(x => x.InstructionName)
            .ToListAsync();

        return entities.Select(x => new DeviceInstructionSimpleOutput
        {
            Id = x.Id,
            InstructionName = x.InstructionName,
            SendStr = x.send_str,
            IsEnabled = x.IsEnabled
        }).ToList();
    }

    #region 私有方法

    /// <summary>
    /// 映射实体到输出DTO
    /// </summary>
    /// <param name="entity">设备指令实体</param>
    /// <returns>设备指令输出DTO</returns>
    private static DeviceInstructionOutput MapToOutput(DeviceInstructionEntity entity)
    {
        return new DeviceInstructionOutput
        {
            Id = entity.Id,
            DeviceId = entity.DeviceId,
            InstructionName = entity.InstructionName,
            SendStr = entity.send_str,
            Encode = entity.Encode,
            ResponseTime = entity.ResponseTime,
            RetryCount = entity.RetryCount,
            IsEnabled = entity.IsEnabled,
            CreateBy = entity.CreateBy,
            CreateTime = entity.CreateTime,
            UpdateBy = entity.UpdateBy,
            UpdateTime = entity.UpdateTime,
            Remark = entity.Remark
        };
    }

    /// <summary>
    /// 验证设备是否存在
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    private async Task ValidateDeviceExistsAsync(long deviceId)
    {
        var deviceExists = await _db.Queryable<DeviceEntity>()
            .Where(x => x.Id == deviceId)
            .AnyAsync();

        if (!deviceExists)
        {
            throw PersistdValidateException.Message("设备不存在");
        }
    }

    /// <summary>
    /// 验证指令名称是否唯一
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="instructionName">指令名称</param>
    /// <param name="excludeId">排除的指令ID（用于更新时验证）</param>
    private async Task ValidateInstructionNameUniqueAsync(long deviceId, string instructionName, long? excludeId = null)
    {
        var query = _db.Queryable<DeviceInstructionEntity>()
            .Where(x => x.DeviceId == deviceId && x.InstructionName == instructionName);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var exists = await query.AnyAsync();
        if (exists)
        {
            throw PersistdValidateException.Message($"设备指令名称 '{instructionName}' 已存在");
        }
    }

    #endregion
}
