{
  "AllowedHosts": "*",
  "SnowflakeIdOptions": {
    "WorkId": "1"
  },
  "FileSystemOptions": {
    "LimitSize": 1024, //单文件最大值
    "Path": "E:/文件上传测试" //为空时,默认当前程序运行目录/FileSystem下
  },
  "EnableGlobalUnitOfWork": true,
  "JwtOptions": {
    "SecretKey": "49BA59ABBE56E05749BA59ABBE56E057",
    "ExpiredMinutes": 60,
    "RefreshMinutes": 30 //RefreshMinutes必须小于ExpiredMinutes，如果为0则取消无感刷新
  },
  "ConnectionOptions": {
    "DbType": "Mysql",
    "ConnectionString": "Data Source=localhost;Database=CMS;User ID=root;Password=*****;pooling=true;port=3306;sslmode=none;CharSet=utf8;AllowPublicKeyRetrieval=True;"
  },
  "OAuth2Options": [
    {
      "Name": "gitee",
      "ClientId": "ClientId",
      "ClientSecret": "ClientSecret",
      "RedirectUri": "http://************:8848/oauth-callback"
    },
    {
      "Name": "github",
      "ClientId": "ClientId",
      "ClientSecret": "ClientSecret",
      "RedirectUri": "http://************:8848/oauth-callback"
    }
  ],
  "App": {
    "CorsOrigins": "http://localhost:4200,http://localhost:8080,http://localhost:8081,http://localhost:3000"
  },
  "ConnectionStrings": {
    "Default": "Server=localhost;Database=Admin;Trusted_Connection=True;TrustServerCertificate=True"
  },
  "Redis": {
    "Configuration": "127.0.0.1"
  },
  "AuthServer": {
    "Authority": "http://localhost:44354",
    "RequireHttpsMetadata": "false",
    "SwaggerClientId": "Admin_Swagger",
    "SwaggerClientSecret": "1q2w3e*"
  },
  "StringEncryption": {
    "DefaultPassPhrase": "Admin_8CFB2EC534E14D56"
  },
  "Mqtt": {
    "Broker": {
      "Port": 1883,
      "MaxConnections": 1000,
      "EnableAuthentication": false,
      "Username": "*****",
      "Password": "*****",
      "AllowAnonymousAccess": false
    },
    "DataProcessing": {
      "CustomTopicPrefix": "/custom/",
      "ForwardDataTopicsInProduction": false,
      "ForwardAllTopicsInDevelopment": true
    }
  },
  "Modbus": {
    "SchedulerInterval": 1000,
    "MaxConcurrentDevices": 10,
    "DeviceInstructionDelay": 100,
    "DefaultTimeout": 5000,
    "EnableRetry": true,
    "MaxRetryCount": 3
  },
  "DeviceOnlineDetection": {
    "CheckInterval": 30
  }
}
