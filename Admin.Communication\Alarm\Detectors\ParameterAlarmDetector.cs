// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Alarm.Models;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Alarm.Detectors;

/// <summary>
/// 参数告警检测器
/// </summary>
public class ParameterAlarmDetector
{
    private readonly ILogger<ParameterAlarmDetector> _logger;

    public ParameterAlarmDetector(ILogger<ParameterAlarmDetector> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 检测参数是否超限
    /// </summary>
    /// <param name="deviceParameter">设备参数配置</param>
    /// <param name="currentValue">当前值</param>
    /// <returns>告警检测结果</returns>
    public AlarmDetectionResult DetectParameterAlarm(DeviceParaEntity deviceParameter, decimal currentValue)
    {
        // 1. 检查监控总开关
        if (deviceParameter.MonitorStatus != 1)
        {
            return AlarmDetectionResult.NoAlarm("参数监控未启用");
        }

        // 2. 根据数据类型决定检测逻辑
        if (deviceParameter.DataType == 5) // 枚举类型
        {
            return DetectEnumParameterAlarm(deviceParameter, currentValue);
        }
        else if (deviceParameter.DataType == 1) // 模拟量类型
        {
            return DetectAnalogParameterAlarm(deviceParameter, currentValue);
        }

        return AlarmDetectionResult.NoAlarm("不支持的参数数据类型");
    }

    /// <summary>
    /// 检测枚举参数告警（只检测告警上/下限，不检测预警）
    /// </summary>
    private AlarmDetectionResult DetectEnumParameterAlarm(DeviceParaEntity deviceParameter, decimal currentValue)
    {
        // 枚举类型只检查告警级别，不检查预警级别
        if (HasAlarmThresholds(deviceParameter) && IsAlarmTriggered(deviceParameter, currentValue))
        {
            var isUpperLimit = IsUpperLimitTriggered(deviceParameter, currentValue, true);
            var thresholdValue = GetTriggeredThreshold(deviceParameter, currentValue, true);
            var clearValue = GetClearThreshold(deviceParameter, currentValue, true);

            return AlarmDetectionResult.ParameterAlarm(
                (AlarmLevelEnum)deviceParameter.AlarmLevel, // 使用参数配置的告警等级
                currentValue,
                thresholdValue,
                clearValue,
                isUpperLimit,
                AlarmTypeEnum.StatusAbnormal // 枚举类型使用状态异常类型
            );
        }

        return AlarmDetectionResult.NoAlarm("枚举参数值正常");
    }

    /// <summary>
    /// 检测模拟量参数告警（检测告警和预警）
    /// </summary>
    private AlarmDetectionResult DetectAnalogParameterAlarm(DeviceParaEntity deviceParameter, decimal currentValue)
    {
        // 优先检查告警级别 (报警)
        if (HasAlarmThresholds(deviceParameter) && IsAlarmTriggered(deviceParameter, currentValue))
        {
            var isUpperLimit = IsUpperLimitTriggered(deviceParameter, currentValue, true);
            var thresholdValue = GetTriggeredThreshold(deviceParameter, currentValue, true);
            var clearValue = GetClearThreshold(deviceParameter, currentValue, true);

            var alarmType = isUpperLimit ? AlarmTypeEnum.ValueOverUpperLimit : AlarmTypeEnum.ValueOverLowerLimit;

            return AlarmDetectionResult.ParameterAlarm(
                (AlarmLevelEnum)deviceParameter.AlarmLevel, // 使用参数配置的告警等级
                currentValue,
                thresholdValue,
                clearValue,
                isUpperLimit,
                alarmType
            );
        }

        // 再检查预警级别
        if (HasWarningThresholds(deviceParameter) && IsWarningTriggered(deviceParameter, currentValue))
        {
            var isUpperLimit = IsUpperLimitTriggered(deviceParameter, currentValue, false);
            var thresholdValue = GetTriggeredThreshold(deviceParameter, currentValue, false);
            var clearValue = GetClearThreshold(deviceParameter, currentValue, false);

            var alarmType = isUpperLimit ? AlarmTypeEnum.ValueOverUpperLimit : AlarmTypeEnum.ValueOverLowerLimit;

            return AlarmDetectionResult.ParameterAlarm(
                AlarmLevelEnum.Warning, // 预警固定为预警等级
                currentValue,
                thresholdValue,
                clearValue,
                isUpperLimit,
                alarmType
            );
        }

        return AlarmDetectionResult.NoAlarm("模拟量参数值正常");
    }

    /// <summary>
    /// 检查是否配置了告警阈值
    /// </summary>
    private bool HasAlarmThresholds(DeviceParaEntity parameter)
    {
        return parameter.AlarmUpperLimit.HasValue || parameter.AlarmLowerLimit.HasValue;
    }

    /// <summary>
    /// 检查是否配置了预警阈值
    /// </summary>
    private bool HasWarningThresholds(DeviceParaEntity parameter)
    {
        return parameter.WarningUpperLimit.HasValue || parameter.WarningLowerLimit.HasValue;
    }

    /// <summary>
    /// 检查是否触发告警（报警级别）
    /// </summary>
    private bool IsAlarmTriggered(DeviceParaEntity parameter, decimal value)
    {
        bool upperLimitTriggered = parameter.AlarmUpperLimit.HasValue && value > parameter.AlarmUpperLimit.Value;
        bool lowerLimitTriggered = parameter.AlarmLowerLimit.HasValue && value < parameter.AlarmLowerLimit.Value;

        return upperLimitTriggered || lowerLimitTriggered;
    }

    /// <summary>
    /// 检查是否触发预警
    /// </summary>
    private bool IsWarningTriggered(DeviceParaEntity parameter, decimal value)
    {
        bool upperLimitTriggered = parameter.WarningUpperLimit.HasValue && value > parameter.WarningUpperLimit.Value;
        bool lowerLimitTriggered = parameter.WarningLowerLimit.HasValue && value < parameter.WarningLowerLimit.Value;

        return upperLimitTriggered || lowerLimitTriggered;
    }

    /// <summary>
    /// 判断是否为上限触发
    /// </summary>
    private bool IsUpperLimitTriggered(DeviceParaEntity parameter, decimal value, bool isAlarmLevel)
    {
        if (isAlarmLevel)
        {
            return parameter.AlarmUpperLimit.HasValue && value > parameter.AlarmUpperLimit.Value;
        }
        else
        {
            return parameter.WarningUpperLimit.HasValue && value > parameter.WarningUpperLimit.Value;
        }
    }

    /// <summary>
    /// 获取触发的阈值
    /// </summary>
    private decimal GetTriggeredThreshold(DeviceParaEntity parameter, decimal value, bool isAlarmLevel)
    {
        if (isAlarmLevel)
        {
            return GetAlarmTriggeredThreshold(parameter, value);
        }
        else
        {
            return GetWarningTriggeredThreshold(parameter, value);
        }
    }

    /// <summary>
    /// 获取告警触发的阈值
    /// </summary>
    private decimal GetAlarmTriggeredThreshold(DeviceParaEntity parameter, decimal value)
    {
        // 优先检查上限
        if (parameter.AlarmUpperLimit.HasValue && value > parameter.AlarmUpperLimit.Value)
            return parameter.AlarmUpperLimit.Value;

        // 再检查下限
        if (parameter.AlarmLowerLimit.HasValue && value < parameter.AlarmLowerLimit.Value)
            return parameter.AlarmLowerLimit.Value;

        return 0;
    }

    /// <summary>
    /// 获取预警触发的阈值
    /// </summary>
    private decimal GetWarningTriggeredThreshold(DeviceParaEntity parameter, decimal value)
    {
        // 优先检查上限
        if (parameter.WarningUpperLimit.HasValue && value > parameter.WarningUpperLimit.Value)
            return parameter.WarningUpperLimit.Value;

        // 再检查下限
        if (parameter.WarningLowerLimit.HasValue && value < parameter.WarningLowerLimit.Value)
            return parameter.WarningLowerLimit.Value;

        return 0;
    }

    /// <summary>
    /// 获取清除阈值
    /// </summary>
    private decimal GetClearThreshold(DeviceParaEntity parameter, decimal value, bool isAlarmLevel)
    {
        if (isAlarmLevel)
        {
            return GetAlarmClearThreshold(parameter, value);
        }
        else
        {
            return GetWarningClearThreshold(parameter, value);
        }
    }

    /// <summary>
    /// 获取告警清除阈值
    /// </summary>
    private decimal GetAlarmClearThreshold(DeviceParaEntity parameter, decimal value)
    {
        // 如果是上限触发，返回上限清除值
        if (parameter.AlarmUpperLimit.HasValue && value > parameter.AlarmUpperLimit.Value)
            return parameter.AlarmUpperLimitClearValue;

        // 如果是下限触发，返回下限清除值
        if (parameter.AlarmLowerLimit.HasValue && value < parameter.AlarmLowerLimit.Value)
            return parameter.AlarmLowerLimitClearValue;

        return 0;
    }

    /// <summary>
    /// 获取预警清除阈值
    /// </summary>
    private decimal GetWarningClearThreshold(DeviceParaEntity parameter, decimal value)
    {
        // 如果是上限触发，返回上限清除值
        if (parameter.WarningUpperLimit.HasValue && value > parameter.WarningUpperLimit.Value)
            return parameter.WarningUpperLimitClearValue.Value;

        // 如果是下限触发，返回下限清除值
        if (parameter.WarningLowerLimit.HasValue && value < parameter.WarningLowerLimit.Value)
            return parameter.WarningLowerLimitClearValue.Value;

        return 0;
    }
}
