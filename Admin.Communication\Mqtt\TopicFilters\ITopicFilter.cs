using System.Threading.Tasks;
using Admin.Communication.Mqtt.Models;

namespace Admin.Communication.Mqtt.TopicFilters
{
    /// <summary>
    /// 主题过滤器接口
    /// </summary>
    public interface ITopicFilter
    {
        /// <summary>
        /// 判断主题是否需要进行数据处理
        /// </summary>
        /// <param name="topic">MQTT主题</param>
        /// <param name="deviceId">设备ID</param>
        /// <returns>过滤结果</returns>
        Task<TopicFilterResult> ShouldProcessAsync(string topic, string deviceId);
    }

    /// <summary>
    /// 主题过滤结果
    /// </summary>
    public class TopicFilterResult
    {
        /// <summary>
        /// 是否需要处理
        /// </summary>
        public bool ShouldProcess { get; set; }

        /// <summary>
        /// 是否转发给客户端
        /// </summary>
        public bool ShouldForward { get; set; }

        /// <summary>
        /// 主题类型
        /// </summary>
        public TopicType TopicType { get; set; }

        /// <summary>
        /// 原因说明
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 创建需要处理的结果
        /// </summary>
        /// <param name="topicType">主题类型</param>
        /// <param name="shouldForward">是否转发</param>
        /// <param name="reason">原因</param>
        /// <returns>过滤结果</returns>
        public static TopicFilterResult CreateProcessResult(TopicType topicType, bool shouldForward = false, string? reason = null)
        {
            return new TopicFilterResult
            {
                ShouldProcess = true,
                ShouldForward = shouldForward,
                TopicType = topicType,
                Reason = reason
            };
        }

        /// <summary>
        /// 创建不需要处理的结果
        /// </summary>
        /// <param name="shouldForward">是否转发</param>
        /// <param name="reason">原因</param>
        /// <returns>过滤结果</returns>
        public static TopicFilterResult CreateNoProcessResult(bool shouldForward = true, string? reason = null)
        {
            return new TopicFilterResult
            {
                ShouldProcess = false,
                ShouldForward = shouldForward,
                TopicType = TopicType.Unknown,
                Reason = reason
            };
        }
    }

    /// <summary>
    /// 主题类型枚举
    /// </summary>
    public enum TopicType
    {
        /// <summary>
        /// 未知主题
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 预置主题
        /// </summary>
        Preset = 1,

        /// <summary>
        /// 自定义主题
        /// </summary>
        Custom = 2
    }
}
