// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ProductServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ProductServices.Mapper;

/// <summary>
/// 模型指令实体到输出DTO的映射配置
/// </summary>
public class ModelInstructionEntityToOutputMapper : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // 配置 ModelInstructionEntity 到 ModelInstructionOutput 的映射，包含枚举名称转换
        config.ForType<ModelInstructionEntity, ModelInstructionOutput>()
            .Map(dest => dest.FunctionCodeName, src => GetFunctionCodeName(src.FunctionCode))
            .Map(dest => dest.EncodeName, src => GetEncodeName(src.Encode));

        // 配置 ModelInstructionEntity 到 ModelInstructionSimpleOutput 的映射
        config.ForType<ModelInstructionEntity, ModelInstructionSimpleOutput>()
            .Map(dest => dest.FunctionCodeName, src => GetFunctionCodeName(src.FunctionCode));
    }

    /// <summary>
    /// 获取功能码名称
    /// </summary>
    private static string GetFunctionCodeName(int functionCode) => functionCode switch
    {
        1 => "读线圈状态",
        2 => "读离散输入状态",
        3 => "读保持寄存器",
        4 => "读输入寄存器",
        5 => "写单个线圈",
        6 => "写单个寄存器",
        15 => "写多个线圈",
        16 => "写多个寄存器",
        _ => "未知功能码"
    };

    /// <summary>
    /// 获取编码名称
    /// </summary>
    private static string GetEncodeName(int encode) => encode switch
    {
        1 => "HEX",
        2 => "ASCII",
        _ => "未知编码"
    };
}
