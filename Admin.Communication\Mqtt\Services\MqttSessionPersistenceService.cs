using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Models.Statistics;
using Admin.Core.ExceptionExtensions;
using Admin.SqlSugar.Entity.Business.LOT;
using SqlSugar;

namespace Admin.Communication.Mqtt.Services
{
    /// <summary>
    /// MQTT会话持久化服务
    /// </summary>
    public class MqttSessionPersistenceService
    {
        private readonly ILogger<MqttSessionPersistenceService> _logger;
        private readonly MqttBrokerOptions _options;
        private readonly ISqlSugarClient _db;
        private readonly IMqttConnectionManager _connectionManager;

        public MqttSessionPersistenceService(
            ILogger<MqttSessionPersistenceService> logger,
            IOptions<MqttBrokerOptions> options,
            ISqlSugarClient db,
            IMqttConnectionManager connectionManager)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _db = db ?? throw new ArgumentNullException(nameof(db));
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        }

        /// <summary>
        /// 获取所有持久会话信息
        /// </summary>
        /// <returns>持久会话列表</returns>
        public async Task<List<MqttSessionEntity>> GetAllPersistentSessionsAsync()
        {
            try
            {
                if (!_options.PersistSessions)
                {
                    _logger.LogDebug("会话持久化已禁用");
                    return new List<MqttSessionEntity>();
                }

                var sessions = await _db.Queryable<MqttSessionEntity>()
                    .Where(x => !x.CleanSession)
                    .OrderBy(x => x.CreatedTime, OrderByType.Desc)
                    .ToListAsync();

                _logger.LogDebug("获取持久会话列表: Count={Count}", sessions.Count);
                return sessions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取持久会话列表时发生错误");
                return new List<MqttSessionEntity>();
            }
        }

        /// <summary>
        /// 获取会话详细信息（包括订阅和待发送消息）
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>会话详细信息</returns>
        public async Task<SessionDetailInfo> GetSessionDetailAsync(string clientId)
        {
            try
            {
                if (!_options.PersistSessions)
                {
                    return new SessionDetailInfo { ClientId = clientId };
                }

                // 获取会话基本信息
                var session = await _db.Queryable<MqttSessionEntity>()
                    .Where(x => x.ClientId == clientId)
                    .FirstAsync();

                if (session == null)
                {
                    return new SessionDetailInfo { ClientId = clientId };
                }

                // 获取订阅信息
                var subscriptions = await _db.Queryable<MqttSubscriptionEntity>()
                    .Where(x => x.ClientId == clientId)
                    .ToListAsync();

                // 获取待发送消息统计
                var messageStats = await _connectionManager.GetPendingMessageStatisticsAsync(clientId);

                return new SessionDetailInfo
                {
                    ClientId = clientId,
                    Session = session,
                    Subscriptions = subscriptions,
                    MessageStatistics = messageStats
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话详细信息时发生错误: ClientId={ClientId}", clientId);
                return new SessionDetailInfo { ClientId = clientId };
            }
        }

        /// <summary>
        /// 清理指定客户端的持久化数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> CleanupClientPersistenceAsync(string clientId)
        {
            try
            {
                if (!_options.PersistSessions)
                {
                    _logger.LogDebug("会话持久化已禁用，无需清理: ClientId={ClientId}", clientId);
                    return true;
                }

                // 删除待发送消息
                var messageCount = await _db.Deleteable<MqttPendingMessageEntity>()
                    .Where(x => x.ClientId == clientId)
                    .ExecuteCommandAsync();

                // 删除订阅信息
                var subscriptionCount = await _db.Deleteable<MqttSubscriptionEntity>()
                    .Where(x => x.ClientId == clientId)
                    .ExecuteCommandAsync();

                // 删除会话信息
                var sessionCount = await _db.Deleteable<MqttSessionEntity>()
                    .Where(x => x.ClientId == clientId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("清理客户端持久化数据: ClientId={ClientId}, Sessions={SessionCount}, Subscriptions={SubscriptionCount}, Messages={MessageCount}",
                    clientId, sessionCount, subscriptionCount, messageCount);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理客户端持久化数据时发生错误: ClientId={ClientId}", clientId);
                return false;
            }
        }

        /// <summary>
        /// 获取持久化统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public async Task<PersistenceStatistics> GetPersistenceStatisticsAsync()
        {
            try
            {
                if (!_options.PersistSessions)
                {
                    return new PersistenceStatistics
                    {
                        IsEnabled = false
                    };
                }

                var sessionCount = await _db.Queryable<MqttSessionEntity>()
                    .Where(x => !x.CleanSession)
                    .CountAsync();

                var subscriptionCount = await _db.Queryable<MqttSubscriptionEntity>()
                    .CountAsync();

                var messageStats = await _db.Queryable<MqttPendingMessageEntity>()
                    .GroupBy(x => x.Status)
                    .Select(g => new { Status = g.Status, Count = SqlFunc.AggregateCount(g.Status) })
                    .ToListAsync();

                var statistics = new PersistenceStatistics
                {
                    IsEnabled = true,
                    PersistentSessionCount = sessionCount,
                    TotalSubscriptionCount = subscriptionCount,
                    PendingMessageCount = messageStats.FirstOrDefault(x => x.Status == 1)?.Count ?? 0,
                    SendingMessageCount = messageStats.FirstOrDefault(x => x.Status == 2)?.Count ?? 0,
                    SentMessageCount = messageStats.FirstOrDefault(x => x.Status == 3)?.Count ?? 0,
                    AcknowledgedMessageCount = messageStats.FirstOrDefault(x => x.Status == 4)?.Count ?? 0,
                    FailedMessageCount = messageStats.FirstOrDefault(x => x.Status == 5)?.Count ?? 0,
                    ExpiredMessageCount = messageStats.FirstOrDefault(x => x.Status == 6)?.Count ?? 0,
                    TotalMessageCount = messageStats.Sum(x => x.Count)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取持久化统计信息时发生错误");
                return new PersistenceStatistics { IsEnabled = _options.PersistSessions };
            }
        }
    }
}