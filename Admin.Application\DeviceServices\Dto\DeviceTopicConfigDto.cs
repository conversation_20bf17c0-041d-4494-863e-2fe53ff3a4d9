using System.ComponentModel.DataAnnotations;

namespace Admin.Application.DeviceServices.Dto;

/// <summary>
/// 设备主题配置查询输入DTO
/// </summary>
public class DeviceTopicConfigQueryInput : PaginationParams
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public string? DeviceId { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string? TemplateName { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    public string? Topic { get; set; }

    /// <summary>
    /// 访问类型(0=All, 1=Publish, 2=Subscribe)
    /// </summary>
    public int? AccessType { get; set; }
}

/// <summary>
/// 添加设备主题配置输入DTO
/// </summary>
public class AddDeviceTopicConfigInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    [MaxLength(50, ErrorMessage = "设备ID最大长度为50个字符")]
    public string DeviceId { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    [Required(ErrorMessage = "模板名称不能为空")]
    [MaxLength(100, ErrorMessage = "模板名称最大长度为100个字符")]
    public string TemplateName { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    [Required(ErrorMessage = "主题不能为空")]
    [MaxLength(500, ErrorMessage = "主题最大长度为500个字符")]
    public string Topic { get; set; }

    /// <summary>
    /// 访问类型(0=All, 1=Publish, 2=Subscribe)
    /// </summary>
    [Range(0, 2, ErrorMessage = "访问类型值必须在0-2之间")]
    public int AccessType { get; set; }

    /// <summary>
    /// Payload编码 (1:JSON 2:HEX)
    /// </summary>
    [Range(1, 2, ErrorMessage = "数据格式值必须在1-2之间")]
    public int DataFormat { get; set; } = 1;

    /// <summary>
    /// 优先级
    /// </summary>
    [Range(1, 999, ErrorMessage = "优先级范围为1-999")]
    public int Priority { get; set; } = 100;

    /// <summary>
    /// 模板描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "描述最大长度为500个字符")]
    public string? Description { get; set; }
}

/// <summary>
/// 更新设备主题配置输入DTO
/// </summary>
public class UpdateDeviceTopicConfigInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    [MaxLength(50, ErrorMessage = "设备ID最大长度为50个字符")]
    public string DeviceId { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    [Required(ErrorMessage = "模板名称不能为空")]
    [MaxLength(100, ErrorMessage = "模板名称最大长度为100个字符")]
    public string TemplateName { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    [Required(ErrorMessage = "主题不能为空")]
    [MaxLength(500, ErrorMessage = "主题最大长度为500个字符")]
    public string Topic { get; set; }

    /// <summary>
    /// 访问类型(0=All, 1=Publish, 2=Subscribe)
    /// </summary>
    [Range(0, 2, ErrorMessage = "访问类型值必须在0-2之间")]
    public int AccessType { get; set; }

    /// <summary>
    /// Payload编码 (1:JSON 2:HEX)
    /// </summary>
    [Range(1, 2, ErrorMessage = "数据格式值必须在1-2之间")]
    public int DataFormat { get; set; } = 1;

    /// <summary>
    /// 优先级
    /// </summary>
    [Range(1, 999, ErrorMessage = "优先级范围为1-999")]
    public int Priority { get; set; } = 100;

    /// <summary>
    /// 模板描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "描述最大长度为500个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注最大长度为500个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 设备主题配置输出DTO
/// </summary>
public class DeviceTopicConfigOutput
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public string DeviceId { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// 访问类型(0=All, 1=Publish, 2=Subscribe)
    /// </summary>
    public int AccessType { get; set; }

    /// <summary>
    /// 访问类型描述
    /// </summary>
    public string AccessTypeDesc => AccessType switch
    {
        0 => "All",
        1 => "Publish",
        2 => "Subscribe",
        _ => "Unknown"
    };

    /// <summary>
    /// Payload编码 (1:JSON 2:HEX)
    /// </summary>
    public int DataFormat { get; set; }

    /// <summary>
    /// 数据格式描述
    /// </summary>
    public string DataFormatDesc => DataFormat switch
    {
        1 => "JSON",
        2 => "HEX",
        _ => "Unknown"
    };

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 模板描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public long CreateBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 修改人
    /// </summary>
    public long? UpdateBy { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 设备主题配置简单输出DTO (用于下拉选择等场景)
/// </summary>
public class DeviceTopicConfigSimpleOutput
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public string DeviceId { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// 访问类型描述
    /// </summary>
    public string AccessTypeDesc { get; set; }
}





/// <summary>
/// 复制设备主题配置输入DTO
/// </summary>
public class CopyDeviceTopicConfigInput
{
    /// <summary>
    /// 源设备ID
    /// </summary>
    [Required(ErrorMessage = "源设备ID不能为空")]
    public string SourceDeviceId { get; set; }

    /// <summary>
    /// 目标设备ID
    /// </summary>
    [Required(ErrorMessage = "目标设备ID不能为空")]
    public string TargetDeviceId { get; set; }

    /// <summary>
    /// 是否覆盖已存在的配置
    /// </summary>
    public bool Overwrite { get; set; } = false;
}

/// <summary>
/// 设备主题配置统计信息
/// </summary>
public class DeviceTopicConfigStatistics
{
    /// <summary>
    /// 总配置数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 发布类型配置数量
    /// </summary>
    public int PublishCount { get; set; }

    /// <summary>
    /// 订阅类型配置数量
    /// </summary>
    public int SubscribeCount { get; set; }

    /// <summary>
    /// 全部访问类型配置数量
    /// </summary>
    public int AllAccessCount { get; set; }

    /// <summary>
    /// JSON格式配置数量
    /// </summary>
    public int JsonFormatCount { get; set; }

    /// <summary>
    /// HEX格式配置数量
    /// </summary>
    public int HexFormatCount { get; set; }
}

/// <summary>
/// 更新优先级输入DTO
/// </summary>
public class UpdatePriorityInput
{
    /// <summary>
    /// 配置ID
    /// </summary>
    [Required(ErrorMessage = "配置ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    [Range(1, 999, ErrorMessage = "优先级范围为1-999")]
    public int Priority { get; set; }
}

/// <summary>
/// 批量更新优先级输入DTO
/// </summary>
public class BatchUpdatePriorityInput
{
    /// <summary>
    /// 优先级更新列表
    /// </summary>
    [Required(ErrorMessage = "更新列表不能为空")]
    [MinLength(1, ErrorMessage = "至少包含一个更新项")]
    public List<UpdatePriorityInput> Updates { get; set; }
}
