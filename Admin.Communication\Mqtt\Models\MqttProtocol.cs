namespace Admin.Communication.Mqtt.Models
{
    /// <summary>
    /// MQTT协议常量和枚举
    /// </summary>
    public static class MqttProtocol
    {
        /// <summary>
        /// MQTT协议版本
        /// </summary>
        public enum ProtocolVersion : byte
        {
            /// <summary>
            /// MQTT 3.1
            /// </summary>
            V310 = 3,

            /// <summary>
            /// MQTT 3.1.1
            /// </summary>
            V311 = 4
        }

        /// <summary>
        /// MQTT消息类型
        /// </summary>
        public enum MessageType : byte
        {
            /// <summary>
            /// 连接请求
            /// </summary>
            Connect = 1,

            /// <summary>
            /// 连接确认
            /// </summary>
            ConnAck = 2,

            /// <summary>
            /// 发布消息
            /// </summary>
            Publish = 3,

            /// <summary>
            /// 发布确认(QoS 1)
            /// </summary>
            PubAck = 4,

            /// <summary>
            /// 发布已接收(QoS 2第一阶段)
            /// </summary>
            PubRec = 5,

            /// <summary>
            /// 发布释放(QoS 2第二阶段)
            /// </summary>
            PubRel = 6,

            /// <summary>
            /// 发布完成(QoS 2第三阶段)
            /// </summary>
            PubComp = 7,

            /// <summary>
            /// 订阅请求
            /// </summary>
            Subscribe = 8,

            /// <summary>
            /// 订阅确认
            /// </summary>
            SubAck = 9,

            /// <summary>
            /// 取消订阅请求
            /// </summary>
            Unsubscribe = 10,

            /// <summary>
            /// 取消订阅确认
            /// </summary>
            UnsubAck = 11,

            /// <summary>
            /// 心跳请求
            /// </summary>
            PingReq = 12,

            /// <summary>
            /// 心跳响应
            /// </summary>
            PingResp = 13,

            /// <summary>
            /// 断开连接
            /// </summary>
            Disconnect = 14
        }

        /// <summary>
        /// MQTT连接响应码
        /// </summary>
        public enum ConnectReturnCode : byte
        {
            /// <summary>
            /// 连接已接受
            /// </summary>
            Accepted = 0,

            /// <summary>
            /// 不支持的协议版本
            /// </summary>
            UnacceptableProtocolVersion = 1,

            /// <summary>
            /// 客户端ID被拒绝
            /// </summary>
            IdentifierRejected = 2,

            /// <summary>
            /// 服务器不可用
            /// </summary>
            ServerUnavailable = 3,

            /// <summary>
            /// 用户名或密码错误
            /// </summary>
            BadUsernameOrPassword = 4,

            /// <summary>
            /// 未授权
            /// </summary>
            NotAuthorized = 5
        }

        /// <summary>
        /// MQTT服务质量等级
        /// </summary>
        public enum QualityOfService : byte
        {
            /// <summary>
            /// 最多一次(0)
            /// </summary>
            AtMostOnce = 0,

            /// <summary>
            /// 至少一次(1)
            /// </summary>
            AtLeastOnce = 1,

            /// <summary>
            /// 恰好一次(2)
            /// </summary>
            ExactlyOnce = 2
        }

        /// <summary>
        /// MQTT主题通配符
        /// </summary>
        public static class TopicWildcards
        {
            /// <summary>
            /// 单级通配符
            /// </summary>
            public const string SingleLevel = "+";

            /// <summary>
            /// 多级通配符
            /// </summary>
            public const string MultiLevel = "#";
        }

        /// <summary>
        /// MQTT固定头部标志
        /// </summary>
        public static class FixedHeaderFlags
        {
            /// <summary>
            /// 保留消息标志位
            /// </summary>
            public const byte RetainFlag = 0x01;

            /// <summary>
            /// QoS级别标志位(第1位)
            /// </summary>
            public const byte QoS1Flag = 0x02;

            /// <summary>
            /// QoS级别标志位(第2位)
            /// </summary>
            public const byte QoS2Flag = 0x04;

            /// <summary>
            /// 重复传输标志位
            /// </summary>
            public const byte DupFlag = 0x08;
        }

        /// <summary>
        /// MQTT连接标志
        /// </summary>
        public static class ConnectFlags
        {
            /// <summary>
            /// 清除会话标志
            /// </summary>
            public const byte CleanSession = 0x02;

            /// <summary>
            /// 遗嘱消息标志
            /// </summary>
            public const byte Will = 0x04;

            /// <summary>
            /// 遗嘱消息QoS级别(第1位)
            /// </summary>
            public const byte WillQoS1 = 0x08;

            /// <summary>
            /// 遗嘱消息QoS级别(第2位)
            /// </summary>
            public const byte WillQoS2 = 0x10;

            /// <summary>
            /// 遗嘱消息保留标志
            /// </summary>
            public const byte WillRetain = 0x20;

            /// <summary>
            /// 密码标志
            /// </summary>
            public const byte Password = 0x40;

            /// <summary>
            /// 用户名标志
            /// </summary>
            public const byte Username = 0x80;
        }

        /// <summary>
        /// MQTT协议名称
        /// </summary>
        public static class ProtocolName
        {
            /// <summary>
            /// MQTT 3.1
            /// </summary>
            public const string V310 = "MQIsdp";

            /// <summary>
            /// MQTT 3.1.1
            /// </summary>
            public const string V311 = "MQTT";
        }
    }
} 