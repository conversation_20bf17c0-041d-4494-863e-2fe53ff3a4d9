using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Protocol;
using Admin.Core.ExceptionExtensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Admin.Multiplex.Contracts.Enums.Mqtt;
using Admin.Communication.Mqtt.Events;

namespace Admin.Communication.Mqtt.Services
{
    /// <summary>
    /// MQTT代理服务实现
    /// </summary>
    public class MqttBrokerService : IMqttBroker, IDisposable
    {
        private readonly ILogger<MqttBrokerService> _logger;
        private readonly MqttBrokerOptions _options;
        private readonly IMqttConnectionManager _connectionManager;
        private readonly MqttPacketParser _packetParser;
        private readonly MqttPacketWriter _packetWriter;
        
        private TcpListener _tcpListener;
        private bool _isRunning;
        private int _port;
        private CancellationTokenSource _cancellationTokenSource;
        
        // 主题订阅管理
        private readonly ConcurrentDictionary<string, HashSet<string>> _topicSubscriptions = new ConcurrentDictionary<string, HashSet<string>>();
        
        // 保留消息存储
        private readonly ConcurrentDictionary<string, MqttPublishMessage> _retainedMessages = new ConcurrentDictionary<string, MqttPublishMessage>();
        
        /// <summary>
        /// QoS 2消息临时存储：ClientId -> (MessageId -> 消息)
        /// 用于存储收到的QoS 2消息，等待PUBREL确认
        /// </summary>
        private readonly ConcurrentDictionary<string, ConcurrentDictionary<ushort, MqttPublishMessage>> _qos2ReceivedMessages = new ConcurrentDictionary<string, ConcurrentDictionary<ushort, MqttPublishMessage>>();
        
        /// <summary>
        /// 获取代理服务器当前状态
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 获取代理服务器监听端口
        /// </summary>
        public int Port => _port;

        /// <summary>
        /// 当客户端连接时触发
        /// </summary>
        public event EventHandler<MqttClientConnectedEventArgs> ClientConnected;
        
        /// <summary>
        /// 当客户端断开连接时触发
        /// </summary>
        public event EventHandler<MqttClientDisconnectedEventArgs> ClientDisconnected;
        
        /// <summary>
        /// 当收到消息时触发
        /// </summary>
        public event EventHandler<MqttMessageReceivedEventArgs> MessageReceived;
        
        /// <summary>
        /// 当客户端订阅主题时触发
        /// </summary>
        public event EventHandler<MqttSubscriptionEventArgs> ClientSubscribed;
        
        /// <summary>
        /// 当客户端取消订阅主题时触发
        /// </summary>
        public event EventHandler<MqttSubscriptionEventArgs> ClientUnsubscribed;

        private readonly IMqttMessageDispatcher _messageDispatcher;

        /// <summary>
        /// 初始化MQTT代理服务
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="options">配置选项</param>
        /// <param name="connectionManager">连接管理器</param>
        /// <param name="messageDispatcher">消息分发器</param>
        public MqttBrokerService(
            ILogger<MqttBrokerService> logger,
            IOptions<MqttBrokerOptions> options,
            IMqttConnectionManager connectionManager,
            IMqttMessageDispatcher messageDispatcher)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _messageDispatcher = messageDispatcher ?? throw new ArgumentNullException(nameof(messageDispatcher));
            _packetParser = new MqttPacketParser();
            _packetWriter = new MqttPacketWriter();

            // 订阅连接管理器事件
            _connectionManager.ClientConnected += OnConnectionManagerClientConnected;
            _connectionManager.ClientDisconnected += OnConnectionManagerClientDisconnected;
        }

        /// <summary>
        /// 启动MQTT代理服务器
        /// </summary>
        /// <param name="port">监听端口，默认为1883</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        public async Task StartAsync(int port = 1883, CancellationToken cancellationToken = default)
        {
            if (_isRunning)
            {
                _logger.LogWarning("MQTT代理服务已经在运行中");
                return;
            }

            // 验证配置
            var validationResult = _options.Validate();
            if (!validationResult.IsValid)
            {
                _logger.LogError("MQTT代理配置无效: {ErrorMessage}", validationResult.ErrorMessage);
                throw PersistdValidateException.Message($"MQTT代理配置无效: {validationResult.ErrorMessage}");
            }

            // 如果传入了端口参数，则使用传入的端口，否则使用配置的端口
            _port = port > 0 ? port : _options.Port;
            
            try
            {
                _logger.LogInformation("正在启动MQTT代理服务，监听地址: {Address}:{Port}", _options.BindAddress, _port);
                
                // 创建TCP监听器
                _tcpListener = new TcpListener(_options.BindAddress, _port);
                _tcpListener.Start();
                
                _isRunning = true;
                _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                
                // 初始化消息分发器
                await _messageDispatcher.InitializeAsync();

                // 启动接受客户端连接的任务
                _ = AcceptClientsAsync(_cancellationTokenSource.Token);

                _logger.LogInformation("MQTT代理服务已启动");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动MQTT代理服务时发生错误");
                await StopAsync(cancellationToken);
                throw;
            }
        }

        /// <summary>
        /// 停止MQTT代理服务器
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            if (!_isRunning)
            {
                _logger.LogWarning("MQTT代理服务未在运行");
                return;
            }

            try
            {
                _logger.LogInformation("正在停止MQTT代理服务...");
                
                // 取消所有正在进行的操作
                _cancellationTokenSource?.Cancel();
                
                // 停止TCP监听器
                _tcpListener?.Stop();
                _tcpListener = null;
                
                _isRunning = false;
                _logger.LogInformation("MQTT代理服务已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止MQTT代理服务时发生错误");
                throw;
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 获取当前连接的客户端数量
        /// </summary>
        /// <returns>客户端数量</returns>
        public int GetConnectedClientCount()
        {
            return _connectionManager.GetConnectionCount();
        }

        /// <summary>
        /// 获取当前连接的客户端信息
        /// </summary>
        /// <returns>客户端信息列表</returns>
        public IReadOnlyList<MqttClientInfo> GetConnectedClients()
        {
            return _connectionManager.GetClientInfos();
        }

        /// <summary>
        /// 获取订阅指定主题的客户端数量
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>订阅数量</returns>
        public int GetSubscriptionCount(string topic)
        {
            if (string.IsNullOrEmpty(topic))
            {
                return 0;
            }

            var subscribers = new HashSet<string>();
            
            // 精确匹配主题
            if (_topicSubscriptions.TryGetValue(topic, out var exactSubscribers))
            {
                foreach (var subscriber in exactSubscribers)
                {
                    subscribers.Add(subscriber);
                }
            }

            // 通配符匹配 - 检查所有订阅的主题过滤器是否匹配当前主题
            foreach (var subscription in _topicSubscriptions)
            {
                // 跳过已经精确匹配的主题
                if (subscription.Key == topic)
                {
                    continue;
                }
                
                // 使用主题匹配器检查主题是否匹配订阅的主题过滤器
                if (MqttTopicMatcher.IsMatch(topic, subscription.Key))
                {
                    foreach (var subscriber in subscription.Value)
                    {
                        subscribers.Add(subscriber);
                    }
                }
            }
            
            return subscribers.Count;
        }

        /// <summary>
        /// 发布消息到指定主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息负载</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="retain">是否保留消息</param>
        /// <returns>发布任务</returns>
        public async Task PublishAsync(string topic, byte[] payload, int qos = 0, bool retain = false)
        {
            if (string.IsNullOrEmpty(topic))
            {
                throw PersistdValidateException.Message("主题不能为空");
            }

            if (qos < 0 || qos > 2)
            {
                throw PersistdValidateException.Message("QoS必须在0-2之间");
            }

            var message = new MqttPublishMessage
            {
                Topic = topic,
                Payload = payload,
                QualityOfService = (byte)qos,
                Retain = retain,
                MessageId = GenerateMessageId()
            };

            await PublishToSubscribersAsync(message);
        }

        /// <summary>
        /// 发布消息到指定主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息负载(字符串)</param>
        /// <param name="qos">服务质量等级</param>
        /// <param name="retain">是否保留消息</param>
        /// <returns>发布任务</returns>
        public async Task PublishAsync(string topic, string payload, int qos = 0, bool retain = false)
        {
            byte[] payloadBytes = string.IsNullOrEmpty(payload) ? Array.Empty<byte>() : Encoding.UTF8.GetBytes(payload);
            await PublishAsync(topic, payloadBytes, qos, retain);
        }

        /// <summary>
        /// 断开指定客户端的连接
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>断开连接任务</returns>
        public async Task DisconnectClientAsync(string clientId)
        {
            if (string.IsNullOrEmpty(clientId))
            {
                throw PersistdValidateException.Message("客户端ID不能为空");
            }

            await _connectionManager.RemoveConnectionAsync(clientId, DisconnectReasonEnum.ServerDisconnect, true);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 停止服务
            StopAsync().GetAwaiter().GetResult();
            
            // 取消订阅连接管理器事件
            if (_connectionManager != null)
            {
                _connectionManager.ClientConnected -= OnConnectionManagerClientConnected;
                _connectionManager.ClientDisconnected -= OnConnectionManagerClientDisconnected;
            }
            
            // 清理资源
            _cancellationTokenSource?.Dispose();
            _topicSubscriptions.Clear();
            _retainedMessages.Clear();
        }

        #region 私有方法

        /// <summary>
        /// 接受客户端连接
        /// </summary>
        private async Task AcceptClientsAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("开始接受客户端连接");
            
            try
            {
                while (!cancellationToken.IsCancellationRequested && _isRunning)
                {
                    // 接受客户端连接
                    TcpClient tcpClient = await _tcpListener.AcceptTcpClientAsync();
                    
                    // 设置客户端连接超时
                    tcpClient.ReceiveTimeout = _options.ConnectionTimeout * 1000;
                    tcpClient.SendTimeout = _options.ConnectionTimeout * 1000;
                    
                    // 处理客户端连接
                    _ = HandleClientConnectionAsync(tcpClient, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常退出
                _logger.LogInformation("接受客户端连接的操作已取消");
            }
            catch (Exception ex)
            {
                if (_isRunning)
                {
                    _logger.LogError(ex, "接受客户端连接时发生错误");
                }
            }
        }

        /// <summary>
        /// 处理客户端连接
        /// </summary>
        private async Task HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken)
        {
            var connection = new MqttClientConnection(tcpClient);
            
            try
            {
                _logger.LogDebug("新客户端连接: {RemoteEndPoint}", connection.RemoteEndPoint);
                
                // 读取CONNECT消息
                var connectMessage = await ReadConnectMessageAsync(connection, cancellationToken);
                
                if (connectMessage == null)
                {
                    _logger.LogWarning("客户端未发送CONNECT消息或消息格式错误: {RemoteEndPoint}", connection.RemoteEndPoint);
                    connection.Close();
                    return;
                }
                
                connection.ClientId = connectMessage.ClientId;
                connection.Username = connectMessage.Username;
                connection.CleanSession = connectMessage.CleanSession;
                connection.KeepAlive = connectMessage.KeepAlive;
                connection.ProtocolVersion = connectMessage.ProtocolVersion;
                
                // 验证连接
                var returnCode = await ValidateConnectionAsync(connectMessage, tcpClient);
                
                if (returnCode == MqttProtocol.ConnectReturnCode.Accepted)
                {
                    // 使用连接管理器添加连接
                    var connectionResult = await _connectionManager.AddConnectionAsync(connection, cancellationToken);
                    
                    if (connectionResult.IsSuccess)
                    {
                        // 发送CONNACK
                        await SendConnAckAsync(connection, returnCode, connectionResult.SessionPresent, cancellationToken);
                        
                        // 如果是持久会话，恢复会话状态
                        if (!connection.CleanSession)
                        {
                            // 从数据库加载持久会话（包括待发送消息）
                            var persistentSession = await _connectionManager.LoadSessionFromDatabaseAsync(connection.ClientId);
                            if (persistentSession != null)
                            {
                                // 将持久会话的数据恢复到当前会话
                                var currentSession = await _connectionManager.GetOrCreateSessionAsync(connection.ClientId, false);

                                // 恢复订阅信息
                                foreach (var subscription in persistentSession.GetSubscriptions())
                                {
                                    currentSession.AddSubscription(subscription);
                                }

                                // 恢复待发送消息
                                foreach (var message in persistentSession.GetPendingMessages())
                                {
                                    currentSession.EnqueueMessage(message);
                                }

                                // 恢复待确认消息
                                foreach (var kvp in persistentSession.GetAwaitingAckMessages())
                                {
                                    currentSession.AddAwaitingAckMessage(kvp.Key, kvp.Value);
                                }

                                // 恢复待完成消息
                                foreach (var kvp in persistentSession.GetAwaitingCompMessages())
                                {
                                    currentSession.AddAwaitingCompMessage(kvp.Key, kvp.Value);
                                }
                            }

                            // 恢复QoS 2临时消息
                            await RestoreQos2MessagesAsync(connection.ClientId);

                            _logger.LogDebug("已恢复持久会话状态: ClientId={ClientId}", connection.ClientId);
                        }
                        else
                        {
                            // 清理订阅
                            CleanupClientSubscriptions(connection.ClientId);
                            
                            _logger.LogDebug("已清理会话状态: ClientId={ClientId}", connection.ClientId);
                        }
                        
                        // 处理客户端消息
                        await ProcessClientMessagesAsync(connection, cancellationToken);
                    }
                    else
                    {
                        _logger.LogWarning("连接管理器拒绝连接: ClientId={ClientId}, 原因={Reason}", 
                            connection.ClientId, connectionResult.Message);
                        
                        // 发送CONNACK拒绝并断开连接
                        await SendConnAckAsync(connection, MqttProtocol.ConnectReturnCode.ServerUnavailable, false, cancellationToken);
                        connection.Close();
                    }
                }
                else
                {
                    _logger.LogWarning("客户端 {ClientId} 连接被拒绝，原因: {ReturnCode}", 
                        connectMessage.ClientId, returnCode);
                    
                    // 发送CONNACK并断开连接
                    await SendConnAckAsync(connection, returnCode, false, cancellationToken);
                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                if (ex is OperationCanceledException || cancellationToken.IsCancellationRequested)
                {
                    _logger.LogDebug("处理客户端连接被取消");
                }
                else
                {
                    _logger.LogError(ex, "处理客户端连接时发生错误: {RemoteEndPoint}", connection.RemoteEndPoint);
                }
                
                // 断开连接
                if (!string.IsNullOrEmpty(connection.ClientId))
                {
                    await _connectionManager.RemoveConnectionAsync(connection.ClientId, DisconnectReasonEnum.NetworkError, false, cancellationToken);
                }
                else
                {
                    connection.Close();
                }
            }
        }
        
        /// <summary>
        /// 发送CONNACK消息
        /// </summary>
        private async Task SendConnAckAsync(MqttClientConnection connection, MqttProtocol.ConnectReturnCode returnCode, bool sessionPresent, CancellationToken cancellationToken)
        {
            try
            {
                // 检查连接和流状态
                if (connection?.Stream == null || !connection.Stream.CanWrite)
                {
                    _logger.LogDebug("无法发送CONNACK消息，连接流不可用: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                // 检查TCP连接状态
                if (connection.TcpClient?.Connected != true)
                {
                    _logger.LogDebug("无法发送CONNACK消息，TCP连接已断开: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                // 构建CONNACK消息
                var connAckMessage = new MqttConnAckMessage
                {
                    ReturnCode = returnCode,
                    SessionPresent = sessionPresent
                };
                
                // 序列化消息
                var buffer = _packetWriter.WriteConnAck(connAckMessage);
                
                // 发送消息
                await connection.Stream.WriteAsync(buffer, 0, buffer.Length, cancellationToken);
                await connection.Stream.FlushAsync(cancellationToken);
                
                _logger.LogDebug("已发送CONNACK消息到客户端 {ClientId}, ReturnCode: {ReturnCode}, SessionPresent: {SessionPresent}", 
                    connection.ClientId, returnCode, sessionPresent);
            }
            catch (ObjectDisposedException)
            {
                _logger.LogDebug("发送CONNACK消息时连接已被释放: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
            }
            catch (IOException ioEx)
            {
                _logger.LogDebug("发送CONNACK消息时发生IO异常: ClientId={ClientId}, Message={Message}", connection?.ClientId ?? "Unknown", ioEx.Message);
            }
            catch (SocketException sockEx)
            {
                _logger.LogDebug("发送CONNACK消息时发生Socket异常: ClientId={ClientId}, Message={Message}", connection?.ClientId ?? "Unknown", sockEx.Message);
            }
            catch (InvalidOperationException invOpEx)
            {
                _logger.LogDebug("发送CONNACK消息时发生操作异常: ClientId={ClientId}, Message={Message}", connection?.ClientId ?? "Unknown", invOpEx.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送CONNACK消息时发生未预期异常: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                throw;
            }
        }
        
        /// <summary>
        /// 处理客户端消息
        /// </summary>
        private async Task ProcessClientMessagesAsync(MqttClientConnection connection, CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && connection.IsConnected && _connectionManager.IsConnected(connection.ClientId))
                {
                    try
                    {
                        // 读取消息
                        var message = await ReadMessageAsync(connection, cancellationToken);
                        
                        if (message == null)
                        {
                            _logger.LogDebug("客户端 {ClientId} 连接已关闭", connection.ClientId);
                            break;
                        }
                        
                        // 更新连接活动时间
                        _connectionManager.UpdateConnectionActivity(connection.ClientId);
                        
                        // 处理消息
                        await HandleMessageAsync(connection, message, cancellationToken);
                    }
                    catch (ObjectDisposedException)
                    {
                        _logger.LogDebug("客户端 {ClientId} 连接已被释放", connection.ClientId);
                        break;
                    }
                    catch (IOException ioEx)
                    {
                        _logger.LogDebug("客户端 {ClientId} IO异常: {Message}", connection.ClientId, ioEx.Message);
                        break;
                    }
                    catch (SocketException sockEx)
                    {
                        _logger.LogDebug("客户端 {ClientId} Socket异常: {Message}", connection.ClientId, sockEx.Message);
                        break;
                    }
                    catch (InvalidOperationException invOpEx)
                    {
                        _logger.LogDebug("客户端 {ClientId} 操作异常: {Message}", connection.ClientId, invOpEx.Message);
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogDebug("客户端 {ClientId} 消息处理被取消", connection.ClientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理客户端 {ClientId} 消息时发生未预期错误", connection.ClientId);
            }
            finally
            {
                // 使用连接管理器移除连接
                if (!string.IsNullOrEmpty(connection.ClientId))
                {
                    await _connectionManager.RemoveConnectionAsync(connection.ClientId, DisconnectReasonEnum.NormalDisconnection, false, cancellationToken);
                }
            }
        }
        
        /// <summary>
        /// 验证连接
        /// </summary>
        private async Task<MqttProtocol.ConnectReturnCode> ValidateConnectionAsync(MqttConnectMessage connectMessage, TcpClient tcpClient)
        {
            try
            {
                // 检查协议版本
                if (connectMessage.ProtocolVersion != (byte)MqttProtocol.ProtocolVersion.V311 &&
                    connectMessage.ProtocolVersion != (byte)MqttProtocol.ProtocolVersion.V310)
                {
                    return MqttProtocol.ConnectReturnCode.UnacceptableProtocolVersion;
                }

                // 检查客户端ID
                if (string.IsNullOrEmpty(connectMessage.ClientId))
                {
                    // MQTT 3.1.1允许空客户端ID，但必须设置CleanSession=true
                    if (connectMessage.ProtocolVersion == (byte)MqttProtocol.ProtocolVersion.V311 && connectMessage.CleanSession)
                    {
                        // 生成唯一的客户端ID
                        connectMessage.ClientId = Guid.NewGuid().ToString("N");
                    }
                    else
                    {
                        return MqttProtocol.ConnectReturnCode.IdentifierRejected;
                    }
                }

                // 使用连接管理器验证凭据
                var authResult = await _connectionManager.ValidateCredentialsAsync(
                    connectMessage.ClientId, 
                    connectMessage.Username, 
                    connectMessage.Password,
                    ((IPEndPoint)tcpClient.Client.RemoteEndPoint).Address.ToString());
                if (!authResult.IsAuthenticated)
                {
                    return MqttProtocol.ConnectReturnCode.BadUsernameOrPassword;
                }

                // 使用连接管理器检查连接权限
                var authzResult = _connectionManager.CheckConnectionPermission(connectMessage.ClientId, "unknown");
                if (!authzResult.IsAuthorized)
                {
                    return MqttProtocol.ConnectReturnCode.NotAuthorized;
                }

                return MqttProtocol.ConnectReturnCode.Accepted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证连接时发生错误: ClientId={ClientId}", connectMessage.ClientId);
                return MqttProtocol.ConnectReturnCode.ServerUnavailable;
            }
        }

        /// <summary>
        /// 读取CONNECT消息
        /// </summary>
        private async Task<MqttConnectMessage> ReadConnectMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken)
        {
            var message = await _packetParser.ParsePacketAsync(connection.Stream, cancellationToken);
            
            if (message.MessageType != (byte)MqttProtocol.MessageType.Connect)
            {
                throw new MqttProtocolException($"预期的第一个消息是CONNECT，但收到了: {message.MessageType}");
            }
            
            return (MqttConnectMessage)message;
        }

        /// <summary>
        /// 读取MQTT消息
        /// </summary>
        private async Task<MqttMessage> ReadMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken)
        {
            try
            {
                // 检查连接和流状态
                if (connection?.Stream == null || !connection.Stream.CanRead)
                {
                    return null;
                }
                
                return await _packetParser.ParsePacketAsync(connection.Stream, cancellationToken);
            }
            catch (ObjectDisposedException)
            {
                // 网络流已被释放
                return null;
            }
            catch (IOException)
            {
                // IO异常，通常表示连接已断开
                return null;
            }
            catch (SocketException)
            {
                // Socket异常，通常表示网络连接问题
                return null;
            }
            catch (InvalidOperationException)
            {
                // 无效操作，可能是流已关闭
                return null;
            }
            catch (OperationCanceledException)
            {
                // 操作被取消
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "读取客户端 {ClientId} 消息时发生异常", connection?.ClientId ?? "Unknown");
                return null;
            }
        }

        /// <summary>
        /// 处理消息
        /// </summary>
        private async Task HandleMessageAsync(MqttClientConnection connection, MqttMessage message, CancellationToken cancellationToken)
        {
            switch ((MqttProtocol.MessageType)message.MessageType)
            {
                case MqttProtocol.MessageType.Publish:
                    await HandlePublishAsync(connection, (MqttPublishMessage)message, cancellationToken);
                    break;
                case MqttProtocol.MessageType.PubAck:
                    await HandlePubAckAsync(connection, (MqttPubAckMessage)message);
                    break;
                case MqttProtocol.MessageType.PubRec:
                    await HandlePubRecAsync(connection, (MqttPubRecMessage)message, cancellationToken);
                    break;
                case MqttProtocol.MessageType.PubRel:
                    await HandlePubRelAsync(connection, (MqttPubRelMessage)message, cancellationToken);
                    break;
                case MqttProtocol.MessageType.PubComp:
                    await HandlePubCompAsync(connection, (MqttPubCompMessage)message);
                    break;
                case MqttProtocol.MessageType.Subscribe:
                    await HandleSubscribeAsync(connection, (MqttSubscribeMessage)message, cancellationToken);
                    break;
                case MqttProtocol.MessageType.Unsubscribe:
                    await HandleUnsubscribeAsync(connection, (MqttUnsubscribeMessage)message, cancellationToken);
                    break;
                case MqttProtocol.MessageType.PingReq:
                    await HandlePingReqAsync(connection, cancellationToken);
                    break;
                case MqttProtocol.MessageType.Disconnect:
                    await HandleDisconnectAsync(connection, cancellationToken);
                    break;
                default:
                    _logger.LogWarning("收到不支持的消息类型: {MessageType}", message.MessageType);
                    break;
            }
        }

        /// <summary>
        /// 处理PUBLISH消息
        /// </summary>
        private async Task HandlePublishAsync(MqttClientConnection connection, MqttPublishMessage message, CancellationToken cancellationToken)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的PUBLISH消息，主题: {Topic}, QoS: {QoS}", connection.ClientId, message.Topic, message.QualityOfService);
            
            // 验证主题和消息大小
            if (string.IsNullOrEmpty(message.Topic))
            {
                _logger.LogWarning("客户端 {ClientId} 发送了空主题", connection.ClientId);
                return;
            }
            
            if (!MqttTopicMatcher.IsValidTopic(message.Topic))
            {
                _logger.LogWarning("客户端 {ClientId} 发送了无效的主题: {Topic}", connection.ClientId, message.Topic);
                return;
            }
            
            if (message.Topic.Length > _options.MaxTopicLength)
            {
                _logger.LogWarning("客户端 {ClientId} 发送的主题超过最大长度: {TopicLength} > {MaxTopicLength}", 
                    connection.ClientId, message.Topic.Length, _options.MaxTopicLength);
                return;
            }
            
            if (message.Payload != null && message.Payload.Length > _options.MaxMessageSize)
            {
                _logger.LogWarning("客户端 {ClientId} 发送的消息超过最大大小: {PayloadSize} > {MaxMessageSize}", 
                    connection.ClientId, message.Payload?.Length, _options.MaxMessageSize);
                return;
            }
            
            // ACL权限检查 - 检查发布权限
            var publishPermission = _connectionManager.CheckPublishPermission(connection.ClientId, connection.Username, message.Topic);
            if (!publishPermission.IsAuthorized)
            {
                _logger.LogWarning("客户端 {ClientId} 没有发布主题 '{Topic}' 的权限: {Reason}", 
                    connection.ClientId, message.Topic, publishPermission.FailureReason);
                
                // 根据QoS级别发送相应的响应（即使权限被拒绝，也需要按协议发送响应）
                switch (message.QualityOfService)
                {
                    case 1: // QoS 1 - 发送PUBACK（可以考虑发送错误码，但MQTT 3.1.1 PUBACK没有返回码）
                        await SendPubAckAsync(connection, message.MessageId, cancellationToken);
                        break;
                    case 2: // QoS 2 - 发送PUBREC
                        await SendPubRecAsync(connection, message.MessageId, cancellationToken);
                        break;
                }
                return; // 权限被拒绝，不处理消息
            }
            
            // 根据QoS级别处理
            switch (message.QualityOfService)
            {
                case 0: // QoS 0 - 最多一次
                    // 直接发布消息，无需确认
                    await PublishToSubscribersAsync(message);
                    break;
                    
                case 1: // QoS 1 - 至少一次
                    // 发布消息，然后发送PUBACK
                    await PublishToSubscribersAsync(message);
                    await SendPubAckAsync(connection, message.MessageId, cancellationToken);
                    break;
                    
                case 2: // QoS 2 - 恰好一次
                    // 保存消息到临时存储，发送PUBREC，等待PUBREL
                    await StoreQos2MessageAsync(connection.ClientId, message);
                    await SendPubRecAsync(connection, message.MessageId, cancellationToken);
                    break;
                    
                default:
                    _logger.LogWarning("客户端 {ClientId} 发送了无效的QoS级别: {QoS}", connection.ClientId, message.QualityOfService);
                    break;
            }
            
            // 触发消息接收事件
            OnMessageReceived(new MqttMessageReceivedEventArgs
            {
                ClientId = connection.ClientId,
                Topic = message.Topic,
                Payload = message.Payload,
                QoS = message.QualityOfService,
                Retain = message.Retain
            });

            // 分发消息给处理器
            await DispatchMessageToHandlersAsync(connection, message);
        }

        /// <summary>
        /// 分发消息给处理器
        /// </summary>
        /// <param name="connection">客户端连接</param>
        /// <param name="message">MQTT发布消息</param>
        /// <returns>分发任务</returns>
        private async Task DispatchMessageToHandlersAsync(MqttClientConnection connection, MqttPublishMessage message)
        {
            try
            {
                // 创建MQTT消息对象
                var mqttMessage = new MqttPublishMessage
                {
                    Topic = message.Topic,
                    Payload = message.Payload,
                    QualityOfService = message.QualityOfService,
                    Retain = message.Retain,
                    MessageId = message.MessageId
                };

                // 创建消息处理上下文
                var context = new MqttMessageContext
                {
                    ClientConnection = connection,
                    ReceivedTime = DateTime.UtcNow,
                    Source = "MqttBroker",
                    SessionId = connection.ClientId
                };

                // 添加额外的上下文信息
                context.Properties["ClientId"] = connection.ClientId;
                context.Properties["Username"] = connection.Username ?? "";
                context.Properties["RemoteEndPoint"] = connection.RemoteEndPoint?.ToString() ?? "";

                // 分发消息给处理器
                var result = await _messageDispatcher.DispatchAsync(mqttMessage, context);

                if (!result.IsSuccess)
                {
                    _logger.LogWarning("消息分发失败: {ErrorMessage}, 主题: {Topic}, 客户端: {ClientId}",
                        result.ErrorMessage, message.Topic, connection.ClientId);
                }
                else
                {
                    _logger.LogDebug("消息分发成功: 主题: {Topic}, 客户端: {ClientId}, 处理器数量: {HandlerCount}",
                        message.Topic, connection.ClientId, result.ProcessedHandlerCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分发消息给处理器时发生异常: 主题: {Topic}, 客户端: {ClientId}",
                    message.Topic, connection.ClientId);
            }
        }

        /// <summary>
        /// 处理PUBACK消息(QoS 1)
        /// </summary>
        private async Task HandlePubAckAsync(MqttClientConnection connection, MqttPubAckMessage message)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的PUBACK消息，消息ID: {MessageId}", connection.ClientId, message.MessageId);

            try
            {
                // 1. 从会话中移除待确认消息
                var session = await _connectionManager.GetOrCreateSessionAsync(connection.ClientId, false);
                var acknowledgedMessage = session.RemoveAwaitingAckMessage(message.MessageId);

                if (acknowledgedMessage != null)
                {
                    // 安全地获取主题信息
                    var topic = acknowledgedMessage is MqttPublishMessage publishMessage ? publishMessage.Topic : "Unknown";

                    _logger.LogDebug("QoS 1消息已确认: ClientId={ClientId}, MessageId={MessageId}, Topic={Topic}",
                        connection.ClientId, message.MessageId, topic);

                    // 2. 更新数据库中的消息状态为已确认
                    await _connectionManager.UpdateMessageAckStatusAsync(
                        connection.ClientId,
                        message.MessageId,
                        MqttMessageAckStatusEnum.NoAckRequired,
                        MqttMessageStatusEnum.Acknowledged);

                    // 3. 保存会话状态（如果是持久会话）
                    if (session.IsPersistent)
                    {
                        await _connectionManager.SaveSessionAsync(session);
                    }
                }
                else
                {
                    _logger.LogWarning("收到未知的PUBACK消息: ClientId={ClientId}, MessageId={MessageId}",
                        connection.ClientId, message.MessageId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理PUBACK消息时发生错误: ClientId={ClientId}, MessageId={MessageId}",
                    connection.ClientId, message.MessageId);
            }
        }

        /// <summary>
        /// 处理PUBREC消息(QoS 2第一阶段)
        /// </summary>
        private async Task HandlePubRecAsync(MqttClientConnection connection, MqttPubRecMessage message, CancellationToken cancellationToken)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的PUBREC消息，消息ID: {MessageId}", connection.ClientId, message.MessageId);
            
            try
            {
                // 1. 从会话中移除待确认消息，转移到待完成消息
                var session = await _connectionManager.GetOrCreateSessionAsync(connection.ClientId, false);
                var acknowledgedMessage = session.RemoveAwaitingAckMessage(message.MessageId);
                
                if (acknowledgedMessage != null)
                {
                    // 安全地获取主题信息
                    var topic = acknowledgedMessage is MqttPublishMessage publishMessage ? publishMessage.Topic : "Unknown";

                    _logger.LogDebug("QoS 2消息第一阶段确认: ClientId={ClientId}, MessageId={MessageId}, Topic={Topic}",
                        connection.ClientId, message.MessageId, topic);

                    // 2. 将消息添加到待完成列表
                    session.AddAwaitingCompMessage(message.MessageId, acknowledgedMessage);
            
                    // 3. 更新数据库中的消息状态
                    await _connectionManager.UpdateMessageAckStatusAsync(
                        connection.ClientId, 
                        message.MessageId, 
                        MqttMessageAckStatusEnum.WaitingPubComp, 
                        MqttMessageStatusEnum.Sent);
                    
                    // 4. 保存会话状态（如果是持久会话）
                    if (session.IsPersistent)
                    {
                        await _connectionManager.SaveSessionAsync(session);
                    }
                }
                else
                {
                    _logger.LogWarning("收到未知的PUBREC消息: ClientId={ClientId}, MessageId={MessageId}", 
                        connection.ClientId, message.MessageId);
                }
                
                // 5. 发送PUBREL（无论是否找到消息都要发送，符合MQTT协议）
                await SendPubRelAsync(connection, message.MessageId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理PUBREC消息时发生错误: ClientId={ClientId}, MessageId={MessageId}", 
                    connection.ClientId, message.MessageId);
                
                // 即使发生错误也要发送PUBREL
                await SendPubRelAsync(connection, message.MessageId, cancellationToken);
            }
        }

        /// <summary>
        /// 处理PUBREL消息(QoS 2第二阶段)
        /// </summary>
        private async Task HandlePubRelAsync(MqttClientConnection connection, MqttPubRelMessage message, CancellationToken cancellationToken)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的PUBREL消息，消息ID: {MessageId}", connection.ClientId, message.MessageId);
            
            try
            {
                // 1. 从临时存储中获取消息
                var storedMessage = await RetrieveQos2MessageAsync(connection.ClientId, message.MessageId);
                
                if (storedMessage != null)
                {
                    _logger.LogDebug("处理QoS 2消息发布: ClientId={ClientId}, MessageId={MessageId}, Topic={Topic}", 
                        connection.ClientId, message.MessageId, storedMessage.Topic);
                    
                    // 2. 发布消息到订阅者
                    await PublishToSubscribersAsync(storedMessage);
                    
                    // 3. 触发消息接收事件
                    OnMessageReceived(new MqttMessageReceivedEventArgs
                    {
                        ClientId = connection.ClientId,
                        Topic = storedMessage.Topic,
                        Payload = storedMessage.Payload,
                        QoS = storedMessage.QualityOfService,
                        Retain = storedMessage.Retain
                    });

                    // 3.5. 分发消息给处理器
                    await DispatchMessageToHandlersAsync(connection, storedMessage);
                    
                    // 4. 从临时存储中移除消息
                    await RemoveQos2MessageAsync(connection.ClientId, message.MessageId);
                    
                    // 5. 如果是持久会话，更新数据库状态
                    var session = await _connectionManager.GetOrCreateSessionAsync(connection.ClientId, false);
                    if (session.IsPersistent)
                    {
                        await _connectionManager.UpdateMessageAckStatusAsync(
                            connection.ClientId, 
                            message.MessageId, 
                            MqttMessageAckStatusEnum.WaitingPubComp, 
                            MqttMessageStatusEnum.Sent);
                            
                            // 清理QoS 2临时消息的数据库记录
                            await _connectionManager.CleanupQos2ReceivedMessagesAsync(connection.ClientId, message.MessageId);
                    }
                }
                else
                {
                    _logger.LogWarning("收到未知的PUBREL消息: ClientId={ClientId}, MessageId={MessageId}", 
                        connection.ClientId, message.MessageId);
                }
                
                // 6. 发送PUBCOMP（无论是否找到消息都要发送，符合MQTT协议）
                await SendPubCompAsync(connection, message.MessageId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理PUBREL消息时发生错误: ClientId={ClientId}, MessageId={MessageId}", 
                    connection.ClientId, message.MessageId);
                
                // 即使发生错误也要发送PUBCOMP
                await SendPubCompAsync(connection, message.MessageId, cancellationToken);
            }
        }

        /// <summary>
        /// 处理PUBCOMP消息(QoS 2第三阶段)
        /// </summary>
        private async Task HandlePubCompAsync(MqttClientConnection connection, MqttPubCompMessage message)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的PUBCOMP消息，消息ID: {MessageId}", connection.ClientId, message.MessageId);
            
            try
            {
                // 1. 从会话中移除待完成消息
                var session = await _connectionManager.GetOrCreateSessionAsync(connection.ClientId, false);
                var completedMessage = session.RemoveAwaitingCompMessage(message.MessageId);
                
                if (completedMessage != null)
                {
                    // 安全地获取主题信息
                    var topic = completedMessage is MqttPublishMessage publishMessage ? publishMessage.Topic : "Unknown";

                    _logger.LogDebug("QoS 2消息已完成: ClientId={ClientId}, MessageId={MessageId}, Topic={Topic}",
                        connection.ClientId, message.MessageId, topic);

                    // 2. 更新数据库中的消息状态为已确认
                    await _connectionManager.UpdateMessageAckStatusAsync(
                        connection.ClientId, 
                        message.MessageId, 
                        MqttMessageAckStatusEnum.NoAckRequired, 
                        MqttMessageStatusEnum.Acknowledged);
                    
                    // 3. 保存会话状态（如果是持久会话）
                    if (session.IsPersistent)
                    {
                        await _connectionManager.SaveSessionAsync(session);
                    }
                }
                else
                {
                    _logger.LogWarning("收到未知的PUBCOMP消息: ClientId={ClientId}, MessageId={MessageId}", 
                        connection.ClientId, message.MessageId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理PUBCOMP消息时发生错误: ClientId={ClientId}, MessageId={MessageId}", 
                    connection.ClientId, message.MessageId);
            }
        }

        /// <summary>
        /// 从临时存储中检索QoS 2消息
        /// </summary>
        private async Task<MqttPublishMessage> RetrieveQos2MessageAsync(string clientId, ushort messageId)
        {
            if (_qos2ReceivedMessages.TryGetValue(clientId, out var clientMessages))
            {
                clientMessages.TryGetValue(messageId, out var message);
                return message;
            }
            return null;
        }

        /// <summary>
        /// 从临时存储中移除QoS 2消息
        /// </summary>
        private async Task RemoveQos2MessageAsync(string clientId, ushort messageId)
        {
            if (_qos2ReceivedMessages.TryGetValue(clientId, out var clientMessages))
            {
                clientMessages.TryRemove(messageId, out _);
                
                // 如果客户端没有更多QoS 2消息，移除整个字典
                if (clientMessages.IsEmpty)
                {
                    _qos2ReceivedMessages.TryRemove(clientId, out _);
                }
            }
        }

        /// <summary>
        /// 清理客户端的所有QoS 2消息
        /// </summary>
        private void CleanupClientQos2Messages(string clientId)
        {
            _qos2ReceivedMessages.TryRemove(clientId, out _);
            _logger.LogDebug("已清理客户端 {ClientId} 的所有QoS 2临时消息", clientId);
        }

        /// <summary>
        /// 查找主题的订阅者
        /// </summary>
        private List<string> FindSubscribers(string topic)
        {
            var result = new List<string>();
            
            // 精确匹配
            if (_topicSubscriptions.TryGetValue(topic, out var exactSubscribers))
            {
                result.AddRange(exactSubscribers);
            }
            
            // 通配符匹配
            foreach (var subscription in _topicSubscriptions)
            {
                // 跳过已经精确匹配的主题
                if (subscription.Key == topic)
                {
                    continue;
                }
                
                // 使用主题匹配器检查主题是否匹配订阅的主题过滤器
                if (MqttTopicMatcher.IsMatch(topic, subscription.Key))
                {
                    result.AddRange(subscription.Value);
                }
            }
            
            // 去重
            return result.Distinct().ToList();
        }

        /// <summary>
        /// 生成消息ID
        /// </summary>
        private ushort GenerateMessageId()
        {
            // 简单实现，实际应该确保唯一性
            return (ushort)new Random().Next(1, ushort.MaxValue);
        }

        /// <summary>
        /// 触发客户端连接事件
        /// </summary>
        private void OnClientConnected(MqttClientConnectedEventArgs e)
        {
            ClientConnected?.Invoke(this, e);
        }

        /// <summary>
        /// 触发客户端断开连接事件
        /// </summary>
        private void OnClientDisconnected(MqttClientDisconnectedEventArgs e)
        {
            ClientDisconnected?.Invoke(this, e);
        }

        /// <summary>
        /// 触发消息接收事件
        /// </summary>
        private void OnMessageReceived(MqttMessageReceivedEventArgs e)
        {
            MessageReceived?.Invoke(this, e);
        }

        /// <summary>
        /// 触发客户端订阅事件
        /// </summary>
        private void OnClientSubscribed(MqttSubscriptionEventArgs e)
        {
            ClientSubscribed?.Invoke(this, e);
        }

        /// <summary>
        /// 触发客户端取消订阅事件
        /// </summary>
        private void OnClientUnsubscribed(MqttSubscriptionEventArgs e)
        {
            ClientUnsubscribed?.Invoke(this, e);
        }

        /// <summary>
        /// 添加订阅
        /// </summary>
        private void AddSubscription(string clientId, string topic, byte qos)
        {
            _topicSubscriptions.AddOrUpdate(
                topic,
                new HashSet<string> { clientId },
                (_, subscribers) =>
                {
                    subscribers.Add(clientId);
                    return subscribers;
                });
        }

        /// <summary>
        /// 移除订阅
        /// </summary>
        private void RemoveSubscription(string clientId, string topic)
        {
            if (_topicSubscriptions.TryGetValue(topic, out var subscribers))
            {
                subscribers.Remove(clientId);
                
                if (subscribers.Count == 0)
                {
                    _topicSubscriptions.TryRemove(topic, out _);
                }
            }
        }

        /// <summary>
        /// 发送保留消息
        /// </summary>
        private async Task SendRetainedMessagesAsync(string clientId, string topicFilter, CancellationToken cancellationToken)
        {
            if (!_options.EnableRetainedMessages)
            {
                return;
            }
            
            if (!_connectionManager.IsConnected(clientId))
            {
                return;
            }
            
            // 查找匹配主题过滤器的保留消息
            foreach (var retainedMessage in _retainedMessages.Values)
            {
                if (MqttTopicMatcher.IsMatch(retainedMessage.Topic, topicFilter))
                {
                    _logger.LogDebug("向客户端 {ClientId} 发送保留消息，主题: {Topic}", clientId, retainedMessage.Topic);
                    
                    // 创建一个新的消息，保留原始主题和负载
                    var message = new MqttPublishMessage
                    {
                        Topic = retainedMessage.Topic,
                        Payload = retainedMessage.Payload,
                        QualityOfService = retainedMessage.QualityOfService,
                        Retain = true,
                        MessageId = GenerateMessageId()
                    };
                    
                    await SendPublishAsync(clientId, message, CancellationToken.None);
                }
            }
        }

        /// <summary>
        /// 发送PUBLISH消息到客户端
        /// </summary>
        private async Task SendPublishAsync(string clientId, MqttPublishMessage message, CancellationToken cancellationToken)
        {
            try
            {
                var connection = _connectionManager.GetConnection(clientId);
                if (connection == null)
                {
                    _logger.LogWarning("无法发送消息到客户端 {ClientId}，连接不存在", clientId);
                    return;
                }

                // 检查连接状态
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送PUBLISH消息，连接状态无效: ClientId={ClientId}", clientId);
                    return;
                }
                
                // 使用WritePacketAsync方法写入消息
                await _packetWriter.WritePacketAsync(connection.Stream, message, cancellationToken);
                
                _logger.LogDebug("已发送PUBLISH消息到客户端 {ClientId}, Topic: {Topic}, QoS: {QoS}", 
                    clientId, message.Topic, message.QualityOfService);
            }
            catch (ObjectDisposedException)
            {
                _logger.LogDebug("发送PUBLISH消息时连接已被释放: ClientId={ClientId}", clientId);
            }
            catch (IOException ioEx)
            {
                _logger.LogDebug("发送PUBLISH消息时发生IO异常: ClientId={ClientId}, Message={Message}", clientId, ioEx.Message);
            }
            catch (SocketException sockEx)
            {
                _logger.LogDebug("发送PUBLISH消息时发生Socket异常: ClientId={ClientId}, Message={Message}", clientId, sockEx.Message);
            }
            catch (InvalidOperationException invOpEx)
            {
                _logger.LogDebug("发送PUBLISH消息时发生操作异常: ClientId={ClientId}, Message={Message}", clientId, invOpEx.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送PUBLISH消息失败，客户端: {ClientId}, Topic: {Topic}", 
                    clientId, message.Topic);
            }
        }

        /// <summary>
        /// 连接管理器客户端连接事件处理
        /// </summary>
        private void OnConnectionManagerClientConnected(object sender, MqttClientConnectedEventArgs e)
        {
            _logger.LogInformation("客户端已通过连接管理器连接: {ClientId}, IP: {IpAddress}", 
                e.ClientInfo.ClientId, e.ClientInfo.IpAddress);
            
            // 转发事件
            OnClientConnected(e);
        }

        /// <summary>
        /// 连接管理器客户端断开连接事件处理
        /// </summary>
        private void OnConnectionManagerClientDisconnected(object sender, MqttClientDisconnectedEventArgs e)
        {
            _logger.LogInformation("客户端已通过连接管理器断开连接: {ClientId}, 是否正常断开: {WasClean}", 
                e.ClientId, e.WasCleanDisconnect);
            
            // 如果是清除会话，则清理订阅
            var connection = _connectionManager.GetConnection(e.ClientId);
            if (connection != null && connection.CleanSession)
            {
                CleanupClientSubscriptions(e.ClientId);
            }
            
            // 转发事件
            OnClientDisconnected(e);
        }

        /// <summary>
        /// 清理客户端订阅信息
        /// </summary>
        private void CleanupClientSubscriptions(string clientId)
        {
            // 获取客户端的所有订阅
            var subscriptions = GetClientSubscriptions(clientId).ToList();
            
            foreach (var topic in subscriptions)
            {
                RemoveSubscription(clientId, topic);
            }
            
            // 清理QoS 2临时消息
            CleanupClientQos2Messages(clientId);
            
            _logger.LogDebug("已清理客户端 {ClientId} 的订阅信息和临时消息", clientId);
        }

        /// <summary>
        /// 获取客户端订阅的主题
        /// </summary>
        private IEnumerable<string> GetClientSubscriptions(string clientId)
        {
            return _topicSubscriptions
                .Where(t => t.Value.Contains(clientId))
                .Select(t => t.Key);
        }

        /// <summary>
        /// 检查连接状态是否有效
        /// </summary>
        private bool IsConnectionValid(MqttClientConnection connection)
        {
            if (connection == null)
                return false;

            if (connection.Stream == null || !connection.Stream.CanWrite)
                return false;

            if (connection.TcpClient?.Connected != true)
                return false;

            return true;
        }

        /// <summary>
        /// 存储QoS 2消息到临时存储和持久化
        /// </summary>
        private async Task StoreQos2MessageAsync(string clientId, MqttPublishMessage message)
        {
            try
            {
                // 1. 存储到内存中的临时存储
                var clientMessages = _qos2ReceivedMessages.GetOrAdd(clientId, _ => new ConcurrentDictionary<ushort, MqttPublishMessage>());
                clientMessages[message.MessageId] = message;
                
                _logger.LogDebug("QoS 2消息已存储到临时存储: ClientId={ClientId}, MessageId={MessageId}, Topic={Topic}", 
                    clientId, message.MessageId, message.Topic);

                // 2. 如果是持久会话，保存到数据库
                var session = await _connectionManager.GetOrCreateSessionAsync(clientId, false);
                if (session.IsPersistent)
                {
                    // 创建用于持久化的消息副本
                    var persistMessage = new MqttPublishMessage
                    {
                        MessageId = message.MessageId,
                        MessageType = message.MessageType,
                        Topic = message.Topic,
                        Payload = message.Payload,
                        QualityOfService = message.QualityOfService,
                        Retain = message.Retain,
                        IsDuplicate = message.IsDuplicate,
                        CreatedTime = message.CreatedTime,
                        ProtocolVersion = message.ProtocolVersion
                    };

                    var qos2Messages = new Dictionary<ushort, MqttMessage>
                    {
                        [message.MessageId] = persistMessage
                    };

                    await _connectionManager.SaveQos2ReceivedMessagesAsync(clientId, qos2Messages);
                    
                    _logger.LogDebug("QoS 2消息已持久化: ClientId={ClientId}, MessageId={MessageId}", 
                        clientId, message.MessageId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "存储QoS 2消息时发生错误: ClientId={ClientId}, MessageId={MessageId}", 
                    clientId, message.MessageId);
            }
        }

        /// <summary>
        /// 恢复QoS 2临时消息
        /// </summary>
        private async Task RestoreQos2MessagesAsync(string clientId)
        {
            try
            {
                // 获取所有QoS 2消息
                var qos2Messages = await _connectionManager.GetQos2ReceivedMessagesAsync(clientId);
                
                if (qos2Messages != null && qos2Messages.Count > 0)
                {
                    _logger.LogDebug("正在恢复QoS 2临时消息: ClientId={ClientId}", clientId);
                    
                    foreach (var message in qos2Messages)
                    {
                        await StoreQos2MessageAsync(clientId, message);
                    }
                    
                    _logger.LogDebug("已恢复QoS 2临时消息: ClientId={ClientId}", clientId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复QoS 2临时消息时发生错误: ClientId={ClientId}", clientId);
            }
        }

        /// <summary>
        /// 发布消息到订阅者
        /// </summary>
        private async Task PublishToSubscribersAsync(MqttPublishMessage message)
        {
            try
            {
                // 查找订阅者
                var subscribers = FindSubscribers(message.Topic);
                
                if (!subscribers.Any())
                {
                    _logger.LogDebug("主题 '{Topic}' 没有订阅者", message.Topic);
                    return;
                }

                _logger.LogDebug("向 {Count} 个订阅者发布消息，主题: {Topic}", subscribers.Count, message.Topic);

                // 处理保留消息
                if (message.Retain)
                {
                    if (message.Payload?.Length > 0)
                    {
                        // 保存保留消息
                        _retainedMessages[message.Topic] = message;
                        _logger.LogDebug("保存保留消息，主题: {Topic}", message.Topic);
                    }
                    else
                    {
                        // 空负载的保留消息会删除之前的保留消息
                        _retainedMessages.TryRemove(message.Topic, out _);
                        _logger.LogDebug("删除保留消息，主题: {Topic}", message.Topic);
                    }
                }

                // 并发发送消息到所有订阅者
                var sendTasks = subscribers.Select(async clientId =>
                {
                    try
                    {
                        // 检查订阅权限
                        var connection = _connectionManager.GetConnection(clientId);
                        if (connection == null)
                        {
                            _logger.LogDebug("订阅者 {ClientId} 连接不存在，跳过发送", clientId);
                            return;
                        }

                        var subscribePermission = _connectionManager.CheckSubscribePermission(clientId, connection.Username, message.Topic);
                        if (!subscribePermission.IsAuthorized)
                        {
                            _logger.LogDebug("订阅者 {ClientId} 没有订阅主题 '{Topic}' 的权限", clientId, message.Topic);
                            return;
                        }

                        // 创建发送消息的副本
                        var publishMessage = new MqttPublishMessage
                        {
                            Topic = message.Topic,
                            Payload = message.Payload,
                            QualityOfService = message.QualityOfService,
                            Retain = message.Retain,
                            MessageId = GenerateMessageId()
                        };

                        // 根据QoS级别处理消息发送
                        await SendMessageToSubscriberAsync(clientId, publishMessage);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "向订阅者 {ClientId} 发送消息时发生错误", clientId);
                    }
                });

                await Task.WhenAll(sendTasks);
                
                _logger.LogDebug("消息发布完成，主题: {Topic}", message.Topic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布消息到订阅者时发生错误，主题: {Topic}", message.Topic);
            }
        }

        /// <summary>
        /// 发送消息到单个订阅者
        /// </summary>
        private async Task SendMessageToSubscriberAsync(string clientId, MqttPublishMessage message)
        {
            try
            {
                var session = await _connectionManager.GetOrCreateSessionAsync(clientId, false);
                
                switch (message.QualityOfService)
                {
                    case 0: // QoS 0 - 最多一次
                        await SendPublishAsync(clientId, message, CancellationToken.None);
                        break;
                        
                    case 1: // QoS 1 - 至少一次
                        // 添加到待确认消息列表
                        session.AddAwaitingAckMessage(message.MessageId, message);
                        
                        // 发送消息
                        await SendPublishAsync(clientId, message, CancellationToken.None);
                        
                        // 持久化待确认消息（如果是持久会话）
                        if (session.IsPersistent)
                        {
                            await _connectionManager.SaveSessionPendingMessagesAsync(session);
                        }
                        break;
                        
                    case 2: // QoS 2 - 恰好一次
                        // 添加到待完成消息列表
                        session.AddAwaitingCompMessage(message.MessageId, message);
                        
                        // 发送消息
                        await SendPublishAsync(clientId, message, CancellationToken.None);
                        
                        // 持久化待完成消息（如果是持久会话）
                        if (session.IsPersistent)
                        {
                            await _connectionManager.SaveSessionPendingMessagesAsync(session);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送消息到订阅者 {ClientId} 时发生错误", clientId);
            }
        }

        /// <summary>
        /// 发送PUBACK消息
        /// </summary>
        private async Task SendPubAckAsync(MqttClientConnection connection, ushort messageId, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送PUBACK消息，连接状态无效: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                var pubAckMessage = new MqttPubAckMessage
                {
                    MessageId = messageId
                };

                await _packetWriter.WritePacketAsync(connection.Stream, pubAckMessage, cancellationToken);
                
                _logger.LogDebug("已发送PUBACK消息到客户端 {ClientId}, MessageId: {MessageId}", 
                    connection.ClientId, messageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送PUBACK消息失败: ClientId={ClientId}, MessageId={MessageId}", 
                    connection?.ClientId ?? "Unknown", messageId);
            }
        }

        /// <summary>
        /// 发送PUBREC消息
        /// </summary>
        private async Task SendPubRecAsync(MqttClientConnection connection, ushort messageId, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送PUBREC消息，连接状态无效: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                var pubRecMessage = new MqttPubRecMessage
                {
                    MessageId = messageId
                };

                await _packetWriter.WritePacketAsync(connection.Stream, pubRecMessage, cancellationToken);
                
                _logger.LogDebug("已发送PUBREC消息到客户端 {ClientId}, MessageId: {MessageId}", 
                    connection.ClientId, messageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送PUBREC消息失败: ClientId={ClientId}, MessageId={MessageId}", 
                    connection?.ClientId ?? "Unknown", messageId);
            }
        }

        /// <summary>
        /// 发送PUBREL消息
        /// </summary>
        private async Task SendPubRelAsync(MqttClientConnection connection, ushort messageId, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送PUBREL消息，连接状态无效: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                var pubRelMessage = new MqttPubRelMessage
                {
                    MessageId = messageId
                };

                await _packetWriter.WritePacketAsync(connection.Stream, pubRelMessage, cancellationToken);
                
                _logger.LogDebug("已发送PUBREL消息到客户端 {ClientId}, MessageId: {MessageId}", 
                    connection.ClientId, messageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送PUBREL消息失败: ClientId={ClientId}, MessageId={MessageId}", 
                    connection?.ClientId ?? "Unknown", messageId);
            }
        }

        /// <summary>
        /// 发送PUBCOMP消息
        /// </summary>
        private async Task SendPubCompAsync(MqttClientConnection connection, ushort messageId, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送PUBCOMP消息，连接状态无效: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                var pubCompMessage = new MqttPubCompMessage
                {
                    MessageId = messageId
                };

                await _packetWriter.WritePacketAsync(connection.Stream, pubCompMessage, cancellationToken);
                
                _logger.LogDebug("已发送PUBCOMP消息到客户端 {ClientId}, MessageId: {MessageId}", 
                    connection.ClientId, messageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送PUBCOMP消息失败: ClientId={ClientId}, MessageId={MessageId}", 
                    connection?.ClientId ?? "Unknown", messageId);
            }
        }

        /// <summary>
        /// 处理订阅消息
        /// </summary>
        private async Task HandleSubscribeAsync(MqttClientConnection connection, MqttSubscribeMessage message, CancellationToken cancellationToken)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的SUBSCRIBE消息，订阅数量: {Count}", 
                connection.ClientId, message.Subscriptions?.Length ?? 0);

            try
            {
                var returnCodes = new List<byte>();
                
                if (message.Subscriptions != null)
                {
                    foreach (var subscription in message.Subscriptions)
                    {
                        try
                        {
                            // 检查订阅权限
                            var subscribePermission = _connectionManager.CheckSubscribePermission(
                                connection.ClientId, connection.Username, subscription.TopicFilter);

                            if (!subscribePermission.IsAuthorized)
                            {
                                _logger.LogWarning("客户端 {ClientId} 没有订阅主题 '{Topic}' 的权限: {Reason}",
                                    connection.ClientId, subscription.TopicFilter, subscribePermission.FailureReason);
                                returnCodes.Add(0x80); // 订阅失败
                                continue;
                            }

                            // 验证主题过滤器
                            if (!MqttTopicMatcher.IsValidTopicFilter(subscription.TopicFilter))
                            {
                                _logger.LogWarning("客户端 {ClientId} 订阅了无效的主题过滤器: {Topic}",
                                    connection.ClientId, subscription.TopicFilter);
                                returnCodes.Add(0x80); // 订阅失败
                                continue;
                            }

                            // 添加订阅
                            AddSubscription(connection.ClientId, subscription.TopicFilter, subscription.QualityOfService);

                            // 添加到会话订阅
                            var session = await _connectionManager.GetOrCreateSessionAsync(connection.ClientId, false);
                            session.AddSubscription(subscription);

                            // 保存会话（如果是持久会话）
                            if (session.IsPersistent)
                            {
                                await _connectionManager.SaveSessionAsync(session);
                            }

                            // 返回授予的QoS级别
                            returnCodes.Add(subscription.QualityOfService);

                            _logger.LogDebug("客户端 {ClientId} 成功订阅主题: {Topic}, QoS: {QoS}",
                                connection.ClientId, subscription.TopicFilter, subscription.QualityOfService);

                            // 触发订阅事件
                            OnClientSubscribed(new MqttSubscriptionEventArgs
                            {
                                ClientId = connection.ClientId,
                                Topic = subscription.TopicFilter,
                                QoS = subscription.QualityOfService
                            });

                            // 发送保留消息
                            await SendRetainedMessagesAsync(connection.ClientId, subscription.TopicFilter, cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "处理订阅时发生错误: ClientId={ClientId}, Topic={Topic}",
                                connection.ClientId, subscription.TopicFilter);
                            returnCodes.Add(0x80); // 订阅失败
                        }
                    }
                }

                // 发送SUBACK
                await SendSubAckAsync(connection, message.MessageId, returnCodes.ToArray(), cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理SUBSCRIBE消息时发生错误: ClientId={ClientId}", connection.ClientId);
            }
        }

        /// <summary>
        /// 处理取消订阅消息
        /// </summary>
        private async Task HandleUnsubscribeAsync(MqttClientConnection connection, MqttUnsubscribeMessage message, CancellationToken cancellationToken)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的UNSUBSCRIBE消息，取消订阅数量: {Count}",
                connection.ClientId, message.TopicFilters?.Length ?? 0);

            try
            {
                if (message.TopicFilters != null)
                {
                    foreach (var topic in message.TopicFilters)
                    {
                        try
                        {
                            // 移除订阅
                            RemoveSubscription(connection.ClientId, topic);
                            
                            // 从会话中移除订阅
                            var session = await _connectionManager.GetOrCreateSessionAsync(connection.ClientId, false);
                            session.RemoveSubscription(topic);
                            
                            // 保存会话（如果是持久会话）
                            if (session.IsPersistent)
                            {
                                await _connectionManager.SaveSessionAsync(session);
                            }
                            
                            _logger.LogDebug("客户端 {ClientId} 成功取消订阅主题: {Topic}", 
                                connection.ClientId, topic);
                            
                            // 触发取消订阅事件
                            OnClientUnsubscribed(new MqttSubscriptionEventArgs
                            {
                                ClientId = connection.ClientId,
                                Topic = topic,
                                QoS = 0
                            });
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "处理取消订阅时发生错误: ClientId={ClientId}, Topic={Topic}", 
                                connection.ClientId, topic);
                        }
                    }
                }

                // 发送UNSUBACK
                await SendUnsubAckAsync(connection, message.MessageId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理UNSUBSCRIBE消息时发生错误: ClientId={ClientId}", connection.ClientId);
            }
        }

        /// <summary>
        /// 处理PINGREQ消息
        /// </summary>
        private async Task HandlePingReqAsync(MqttClientConnection connection, CancellationToken cancellationToken)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的PINGREQ消息", connection.ClientId);

            try
            {
                // 发送PINGRESP
                await SendPingRespAsync(connection, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理PINGREQ消息时发生错误: ClientId={ClientId}", connection.ClientId);
            }
        }

        /// <summary>
        /// 处理DISCONNECT消息
        /// </summary>
        private async Task HandleDisconnectAsync(MqttClientConnection connection, CancellationToken cancellationToken)
        {
            _logger.LogDebug("收到客户端 {ClientId} 的DISCONNECT消息", connection.ClientId);

            try
            {
                // 客户端主动断开连接，使用连接管理器移除连接
                await _connectionManager.RemoveConnectionAsync(connection.ClientId, DisconnectReasonEnum.ClientDisconnect, false, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理DISCONNECT消息时发生错误: ClientId={ClientId}", connection.ClientId);
            }
        }

        /// <summary>
        /// 发送SUBACK消息
        /// </summary>
        private async Task SendSubAckAsync(MqttClientConnection connection, ushort messageId, byte[] returnCodes, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送SUBACK消息，连接状态无效: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                var subAckMessage = new MqttSubAckMessage
                {
                    MessageId = messageId,
                    ReturnCodes = returnCodes
                };

                await _packetWriter.WritePacketAsync(connection.Stream, subAckMessage, cancellationToken);
                
                _logger.LogDebug("已发送SUBACK消息到客户端 {ClientId}, MessageId: {MessageId}", 
                    connection.ClientId, messageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送SUBACK消息失败: ClientId={ClientId}, MessageId={MessageId}", 
                    connection?.ClientId ?? "Unknown", messageId);
            }
        }

        /// <summary>
        /// 发送UNSUBACK消息
        /// </summary>
        private async Task SendUnsubAckAsync(MqttClientConnection connection, ushort messageId, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送UNSUBACK消息，连接状态无效: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                var unsubAckMessage = new MqttUnsubAckMessage
                {
                    MessageId = messageId
                };

                await _packetWriter.WritePacketAsync(connection.Stream, unsubAckMessage, cancellationToken);
                
                _logger.LogDebug("已发送UNSUBACK消息到客户端 {ClientId}, MessageId: {MessageId}", 
                    connection.ClientId, messageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送UNSUBACK消息失败: ClientId={ClientId}, MessageId={MessageId}", 
                    connection?.ClientId ?? "Unknown", messageId);
            }
        }

        /// <summary>
        /// 发送PINGRESP消息
        /// </summary>
        private async Task SendPingRespAsync(MqttClientConnection connection, CancellationToken cancellationToken)
        {
            try
            {
                if (!IsConnectionValid(connection))
                {
                    _logger.LogDebug("无法发送PINGRESP消息，连接状态无效: ClientId={ClientId}", connection?.ClientId ?? "Unknown");
                    return;
                }

                var pingRespMessage = new MqttPingRespMessage();

                await _packetWriter.WritePacketAsync(connection.Stream, pingRespMessage, cancellationToken);
                
                _logger.LogDebug("已发送PINGRESP消息到客户端 {ClientId}", connection.ClientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送PINGRESP消息失败: ClientId={ClientId}", 
                    connection?.ClientId ?? "Unknown");
            }
        }

        #endregion
    }
} 