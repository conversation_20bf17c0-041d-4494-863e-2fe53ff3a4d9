using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Admin.Communication.Mqtt.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Mvc;
using Admin.Application.MqttBrokerServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;
using Admin.SqlSugar;
using SqlSugar;
using Admin.Multiplex.Contracts.Consts;
using Admin.Core.ExceptionExtensions;
using Admin.Multiplex.Contracts.Enums;
using System.Linq;

namespace Admin.Application.MqttBrokerServices
{
    /// <summary>
    /// MQTT ACL权限管理服务接口
    /// </summary>
    public interface IMqttAclManagementService
    {
        /// <summary>
        /// 获取ACL规则列表
        /// </summary>
        Task<PagedList<AclRuleOutput>> GetPagedListAsync(GetAclRulesInput input);

        /// <summary>
        /// 获取单个ACL规则详情
        /// </summary>
        Task<AclRuleOutput> GetAsync(long id);

        /// <summary>
        /// 添加ACL规则
        /// </summary>
        Task<long> AddAsync(AddAclRuleInput input);

        /// <summary>
        /// 更新ACL规则
        /// </summary>
        Task PutAsync(long id, PutAclRuleInput input);

        /// <summary>
        /// 删除ACL规则
        /// </summary>
        Task DeleteAsync(long id);

        /// <summary>
        /// 批量删除ACL规则
        /// </summary>
        Task BatchDeleteAsync(List<long> ids);

        /// <summary>
        /// 测试权限
        /// </summary>
        Task<PermissionTestOutput> TestPermissionAsync(TestPermissionInput input);

        /// <summary>
        /// 启用/禁用ACL规则
        /// </summary>
        Task ToggleAsync(long id, bool isActive);

        /// <summary>
        /// 导入ACL规则
        /// </summary>
        Task<ImportResultOutput> ImportAsync(List<AddAclRuleInput> rules);

        /// <summary>
        /// 导出ACL规则
        /// </summary>
        Task<List<AclRuleOutput>> ExportAsync();

        /// <summary>
        /// 为设备创建ACL规则
        /// </summary>
        Task<DeviceAclRulesResult> CreateDeviceAclRulesAsync(CreateDeviceAclRulesInput input);

        /// <summary>
        /// 删除设备的ACL规则
        /// </summary>
        Task<bool> DeleteDeviceAclRulesAsync(DeleteDeviceAclRulesInput input);

        /// <summary>
        /// 查询设备的ACL规则
        /// </summary>
        Task<List<AclRuleOutput>> QueryDeviceAclRulesAsync(QueryDeviceAclRulesInput input);

        /// <summary>
        /// 更新设备ACL规则（当设备信息变更时）
        /// </summary>
        Task<DeviceAclRulesResult> UpdateDeviceAclRulesAsync(string oldDeviceId, CreateDeviceAclRulesInput newDeviceInfo);
    }

    /// <summary>
    /// MQTT ACL权限管理服务
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.MQTT)]
    public class MqttAclManagementService(ISqlSugarClient db, Repository<MqttAclRuleEntity> aclRuleRepository,
        IMqttBroker mqttBroker, IMqttConnectionManager connectionManager,
        ILogger<MqttAclManagementService> logger, IOptions<MqttBrokerOptions> options) : ApplicationService, IMqttAclManagementService
    {
        private readonly ISqlSugarClient _db = db;
        private readonly Repository<MqttAclRuleEntity> _aclRuleRepository = aclRuleRepository;
        private readonly IMqttBroker _mqttBroker = mqttBroker ?? throw new ArgumentNullException(nameof(mqttBroker));
        private readonly IMqttConnectionManager _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        private readonly ILogger<MqttAclManagementService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly MqttBrokerOptions _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        /// <summary>
        /// 分页查询
        /// </summary>
        public async Task<PagedList<AclRuleOutput>> GetPagedListAsync(GetAclRulesInput input)
        {
            _logger.LogDebug("获取ACL规则列表，页码: {PageIndex}, 每页大小: {PageSize}", input.PageIndex, input.PageSize);

            var query = _db.Queryable<MqttAclRuleEntity>();

            // 应用过滤条件
            query = query.WhereIF(!input.RuleName.IsNullOrEmpty(), r => r.RuleName.Contains(input.RuleName))
                         .WhereIF(!input.Username.IsNullOrEmpty(), r => r.Username != null && r.Username.Contains(input.Username))
                         .WhereIF(!input.ClientId.IsNullOrEmpty(), r => r.ClientId != null && r.ClientId.Contains(input.ClientId))
                         .WhereIF(!input.Topic.IsNullOrEmpty(), r => r.Topic.Contains(input.Topic))
                         .WhereIF(input.IsActive.HasValue, r => r.IsActive == input.IsActive.Value);

            if (!string.IsNullOrEmpty(input.AccessType))
            {
                if (Enum.TryParse<MqttAccessType>(input.AccessType, true, out var accessType))
                {
                    query = query.Where(r => r.AccessType == (int)accessType);
                }
            }

            if (!string.IsNullOrEmpty(input.Permission))
            {
                var isAllow = input.Permission.Equals("allow", StringComparison.OrdinalIgnoreCase);
                query = query.Where(r => r.Allow == isAllow);
            }

            // 排序 (按优先级降序)
            var pagedList = await query.OrderByDescending(r => r.Priority)
                                      .OrderBy(r => r.CreateTime)
                                      .ToPurestPagedListAsync(input.PageIndex, input.PageSize);

            _logger.LogDebug("获取ACL规则列表成功，总数: {TotalCount}, 当前页: {PageIndex}", pagedList.Total, input.PageIndex);

            return pagedList.Adapt<PagedList<AclRuleOutput>>();
        }

        /// <summary>
        /// 单条查询
        /// </summary>
        public async Task<AclRuleOutput> GetAsync(long id)
        {
            var entity = await _aclRuleRepository.GetByIdAsync(id) ?? throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
            return entity.Adapt<AclRuleOutput>();
        }

        /// <summary>
        /// 添加
        /// </summary>
        public async Task<long> AddAsync(AddAclRuleInput input)
        {
            _logger.LogInformation("添加ACL规则，规则名称: {RuleName}, 主题: {Topic}, 访问类型: {AccessType}, 权限: {Permission}", 
                input.RuleName, input.Topic, input.AccessType, input.Allow ? "允许" : "拒绝");

            // 验证主题格式
            if (!IsValidTopicPattern(input.Topic))
            {
                throw PersistdValidateException.Message($"无效的主题格式: {input.Topic}");
            }

            // 检查规则名称是否已存在
            var existingRule = await _db.Queryable<MqttAclRuleEntity>()
                .Where(r => r.RuleName == input.RuleName)
                .FirstAsync();
            if (existingRule != null)
            {
                throw PersistdValidateException.Message($"规则名称 '{input.RuleName}' 已存在");
            }

            var entity = input.Adapt<MqttAclRuleEntity>();
            var id = await _aclRuleRepository.InsertReturnSnowflakeIdAsync(entity);

            // 更新MQTT代理的ACL配置
            await UpdateMqttBrokerAclConfigAsync();

            _logger.LogInformation("ACL规则添加成功，规则ID: {RuleId}", id);
            return id;
        }

        /// <summary>
        /// 编辑
        /// </summary>
        public async Task PutAsync(long id, PutAclRuleInput input)
        {
            var entity = await _aclRuleRepository.GetByIdAsync(id) ?? throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

            // 验证主题格式
            if (!IsValidTopicPattern(input.Topic))
            {
                throw PersistdValidateException.Message($"无效的主题格式: {input.Topic}");
            }

            // 检查规则名称是否与其他规则冲突
            var nameConflictRule = await _db.Queryable<MqttAclRuleEntity>()
                .Where(r => r.RuleName == input.RuleName && r.Id != id)
                .FirstAsync();
            if (nameConflictRule != null)
            {
                throw PersistdValidateException.Message($"规则名称 '{input.RuleName}' 已被其他规则使用");
            }

            _logger.LogInformation("更新ACL规则，规则ID: {RuleId}, 主题: {Topic}", id, input.Topic);

            entity = input.Adapt(entity);
            await _aclRuleRepository.UpdateAsync(entity);

            // 更新MQTT代理的ACL配置
            await UpdateMqttBrokerAclConfigAsync();

            _logger.LogInformation("ACL规则更新成功，规则ID: {RuleId}", id);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public async Task DeleteAsync(long id)
        {
            var entity = await _aclRuleRepository.GetByIdAsync(id) ?? throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);
            
            await _aclRuleRepository.DeleteAsync(entity);

            // 更新MQTT代理的ACL配置
            await UpdateMqttBrokerAclConfigAsync();

            _logger.LogInformation("ACL规则删除成功，规则ID: {RuleId}", id);
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        public async Task BatchDeleteAsync(List<long> ids)
        {
            if (ids == null || ids.Count == 0)
            {
                throw PersistdValidateException.Message("规则ID列表不能为空");
            }

            var deleteCount = await _db.Deleteable<MqttAclRuleEntity>()
                .Where(r => ids.Contains(r.Id))
                .ExecuteCommandAsync();

            // 更新MQTT代理的ACL配置
            await UpdateMqttBrokerAclConfigAsync();

            _logger.LogInformation("批量删除ACL规则完成，删除数量: {DeleteCount}", deleteCount);
        }

        /// <summary>
        /// 测试权限
        /// </summary>
        public async Task<PermissionTestOutput> TestPermissionAsync(TestPermissionInput input)
        {
            _logger.LogDebug("测试权限，客户端ID: {ClientId}, 用户名: {Username}, 主题: {Topic}, 访问类型: {AccessType}", 
                input.ClientId, input.Username, input.Topic, input.AccessType);

            // 获取所有激活的规则
            var activeRules = await GetActiveRulesAsync();
            
            // 查找匹配的规则
            var matchingRules = FindMatchingRules(input.ClientId, input.Username, input.Topic, input.AccessType, activeRules);

            var result = new PermissionTestOutput
            {
                TestTime = DateTime.Now,
                AllMatchedRules = matchingRules.Select(r => new MatchedRuleOutput
                {
                    Id = r.Id,
                    Priority = r.Priority,
                    Permission = r.Allow ? "allow" : "deny",
                    MatchedPattern = r.Topic,
                    MatchType = "topic"
                }).ToList()
            };

            // 权限判断逻辑：优先检查拒绝规则，如果有拒绝规则匹配则拒绝访问
            var denyRules = matchingRules.Where(r => !r.Allow).ToList();
            if (denyRules.Any())
            {
                result.IsAuthorized = false;
                result.FailureReason = "访问被ACL规则拒绝";
                result.MatchedRule = result.AllMatchedRules.FirstOrDefault(r => r.Permission == "deny");
            }
            else
            {
                // 如果有允许规则匹配，则允许访问
                var allowRules = matchingRules.Where(r => r.Allow).ToList();
                if (allowRules.Any())
                {
                    result.IsAuthorized = true;
                    result.MatchedRule = result.AllMatchedRules.FirstOrDefault(r => r.Permission == "allow");
                }
                else
                {
                    result.IsAuthorized = false;
                    result.FailureReason = "没有找到匹配的ACL规则，默认拒绝访问";
                }
            }

            _logger.LogDebug("权限测试完成，结果: {IsAuthorized}, 匹配规则数: {MatchedRulesCount}", 
                result.IsAuthorized, result.AllMatchedRules.Count);

            return result;
        }

        /// <summary>
        /// 启用/禁用ACL规则
        /// </summary>
        public async Task ToggleAsync(long id, bool isActive)
        {
            var entity = await _aclRuleRepository.GetByIdAsync(id) ?? throw PersistdValidateException.Message(ErrorTipsEnum.NoResult);

            entity.IsActive = isActive;
            await _aclRuleRepository.UpdateAsync(entity);

            // 更新MQTT代理的ACL配置
            await UpdateMqttBrokerAclConfigAsync();

            _logger.LogInformation("ACL规则状态更新成功，规则ID: {RuleId}, 状态: {IsActive}", id, isActive);
        }

        /// <summary>
        /// 导入ACL规则
        /// </summary>
        public async Task<ImportResultOutput> ImportAsync(List<AddAclRuleInput> rules)
        {
            if (rules == null || rules.Count == 0)
            {
                throw PersistdValidateException.Message("导入规则列表不能为空");
            }

            var importedRules = new List<AclRuleOutput>();
            var failedRules = new List<string>();

            foreach (var rule in rules)
            {
                try
                {
                    var id = await AddAsync(rule);
                    var entity = await _aclRuleRepository.GetByIdAsync(id);
                    importedRules.Add(entity.Adapt<AclRuleOutput>());
                }
                catch (Exception ex)
                {
                    failedRules.Add($"规则名称: {rule.RuleName}, 错误: {ex.Message}");
                }
            }

            _logger.LogInformation("导入ACL规则完成，成功: {SuccessCount}, 失败: {FailCount}", 
                importedRules.Count, failedRules.Count);

            return new ImportResultOutput
            {
                ImportedRules = importedRules,
                FailedRules = failedRules,
                TotalRequested = rules.Count,
                SuccessCount = importedRules.Count,
                FailCount = failedRules.Count,
                ImportTime = DateTime.Now
            };
        }

        /// <summary>
        /// 导出ACL规则
        /// </summary>
        public async Task<List<AclRuleOutput>> ExportAsync()
        {
            var allRules = await _db.Queryable<MqttAclRuleEntity>()
                .OrderByDescending(r => r.Priority)
                .OrderBy(r => r.CreateTime)
                .ToListAsync();

            _logger.LogInformation("导出ACL规则完成，规则数: {RuleCount}", allRules.Count);

            return allRules.Adapt<List<AclRuleOutput>>();
        }

        #region 私有方法

        /// <summary>
        /// 获取激活的规则列表
        /// </summary>
        private async Task<List<MqttAclRuleEntity>> GetActiveRulesAsync()
        {
            var now = DateTime.Now;
            return await _db.Queryable<MqttAclRuleEntity>()
                .Where(r => r.IsActive && 
                    (r.EffectiveStartTime == null || r.EffectiveStartTime <= now) &&
                    (r.EffectiveEndTime == null || r.EffectiveEndTime >= now))
                .ToListAsync();
        }

        /// <summary>
        /// 更新MQTT代理的ACL配置
        /// </summary>
        private async Task UpdateMqttBrokerAclConfigAsync()
        {
            try
            {
                // 获取所有激活的规则
                var activeRules = await GetActiveRulesAsync();
                var aclEntries = activeRules
                    .OrderByDescending(r => r.Priority)
                    .Select(r => new MqttAclEntry
                    {
                        Username = r.Username,
                        ClientId = r.ClientId,
                        Topic = r.Topic,
                        AccessType = (MqttAccessType)r.AccessType,
                        Allow = r.Allow
                    })
                    .ToList();

                // 更新配置
                _options.AclRules.Clear();
                _options.AclRules.AddRange(aclEntries);

                _logger.LogDebug("MQTT代理ACL配置已更新，激活规则数: {ActiveRuleCount}", aclEntries.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新MQTT代理ACL配置时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 查找匹配的规则
        /// </summary>
        private List<MqttAclRuleEntity> FindMatchingRules(string clientId, string username, string topic, MqttAccessType accessType, List<MqttAclRuleEntity> rules)
        {
            var matchingRules = new List<MqttAclRuleEntity>();

            foreach (var rule in rules.OrderByDescending(r => r.Priority))
            {
                // 检查访问类型是否匹配
                var ruleAccessType = (MqttAccessType)rule.AccessType;
                if (ruleAccessType != MqttAccessType.All && ruleAccessType != accessType)
                {
                    continue;
                }

                // 检查用户名是否匹配
                if (!string.IsNullOrEmpty(rule.Username) && !rule.Username.Equals(username, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                // 检查客户端ID是否匹配
                if (!string.IsNullOrEmpty(rule.ClientId) && !rule.ClientId.Equals(clientId, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                // 检查主题是否匹配（支持MQTT通配符）
                if (IsTopicMatch(topic, rule.Topic))
                {
                    matchingRules.Add(rule);
                }
            }

            return matchingRules;
        }

        /// <summary>
        /// 检查主题是否匹配ACL规则（支持MQTT通配符）
        /// </summary>
        private bool IsTopicMatch(string actualTopic, string ruleTopicPattern)
        {
            if (string.IsNullOrEmpty(actualTopic) || string.IsNullOrEmpty(ruleTopicPattern))
            {
                return false;
            }

            // 如果规则主题和实际主题完全相同，直接匹配
            if (actualTopic == ruleTopicPattern)
            {
                return true;
            }

            // 分割主题层级
            var actualParts = actualTopic.Split('/');
            var patternParts = ruleTopicPattern.Split('/');

            return IsTopicPatternMatch(actualParts, patternParts, 0, 0);
        }

        /// <summary>
        /// 递归检查主题模式匹配
        /// </summary>
        private bool IsTopicPatternMatch(string[] actualParts, string[] patternParts, int actualIndex, int patternIndex)
        {
            // 如果模式索引到达末尾
            if (patternIndex >= patternParts.Length)
            {
                return actualIndex >= actualParts.Length;
            }

            var patternPart = patternParts[patternIndex];

            // 处理 # 通配符（匹配零个或多个层级）
            if (patternPart == "#")
            {
                return true; // # 匹配剩余的所有层级
            }

            // 如果实际主题索引到达末尾，但模式还有内容
            if (actualIndex >= actualParts.Length)
            {
                return false;
            }

            var actualPart = actualParts[actualIndex];

            // 处理 + 通配符（匹配单个层级）
            if (patternPart == "+")
            {
                return IsTopicPatternMatch(actualParts, patternParts, actualIndex + 1, patternIndex + 1);
            }

            // 精确匹配
            if (patternPart == actualPart)
            {
                return IsTopicPatternMatch(actualParts, patternParts, actualIndex + 1, patternIndex + 1);
            }

            return false;
        }

        /// <summary>
        /// 验证主题格式是否有效
        /// </summary>
        private bool IsValidTopicPattern(string topic)
        {
            if (string.IsNullOrEmpty(topic))
            {
                return false;
            }

            // 检查是否包含无效字符
            if (topic.Contains('\0') || topic.Contains('\r') || topic.Contains('\n'))
            {
                return false;
            }

            // 检查通配符使用是否正确
            var parts = topic.Split('/');
            for (int i = 0; i < parts.Length; i++)
            {
                var part = parts[i];
                
                // 检查 # 通配符
                if (part == "#")
                {
                    // # 必须是最后一个部分
                    if (i != parts.Length - 1)
                    {
                        return false;
                    }
                }
                else if (part.Contains("#"))
                {
                    // # 不能与其他字符混合
                    return false;
                }
                
                // 检查 + 通配符
                if (part.Contains("+") && part != "+")
                {
                    // + 不能与其他字符混合
                    return false;
                }
            }

            return true;
        }

        #endregion

        #region 设备ACL规则管理

        /// <summary>
        /// 为设备创建ACL规则
        /// </summary>
        public async Task<DeviceAclRulesResult> CreateDeviceAclRulesAsync(CreateDeviceAclRulesInput input)
        {
            _logger.LogInformation("为设备创建ACL规则: DeviceId={DeviceId}", input.DeviceId);

            var result = new DeviceAclRulesResult
            {
                DeviceId = input.DeviceId,
                CreateTime = DateTime.Now
            };

            // 获取设备ACL规则模板
            var ruleTemplates = await GetDeviceAclRuleTemplatesAsync(input);

            foreach (var template in ruleTemplates)
            {
                try
                {
                    // 替换模板中的占位符
                    var ruleName = ReplacePlaceholders(template.RuleNameTemplate, input);
                    var topic = ReplacePlaceholders(template.TopicTemplate, input);
                    var description = ReplacePlaceholders(template.DescriptionTemplate ?? "", input);

                    // 检查规则是否已存在
                    var existingRule = await _db.Queryable<MqttAclRuleEntity>()
                        .Where(r => r.Topic == topic)
                        .FirstAsync();

                    if (existingRule != null)
                    {
                        _logger.LogWarning("ACL规则已存在，跳过创建: {RuleName}", ruleName);
                        continue;
                    }

                    // 创建ACL规则实体
                    var aclRule = new MqttAclRuleEntity
                    {
                        RuleName = ruleName,
                        Priority = template.Priority,
                        AccessType = (int)template.AccessType,
                        Allow = template.Allow,
                        Username = input.DeviceId, // 用户名设置为设备ID
                        ClientId = input.DeviceId, // 客户端ID设置为设备ID
                        Topic = topic,
                        Description = description,
                        IsActive = true
                    };

                    // 保存规则
                    var ruleId = await _aclRuleRepository.InsertReturnSnowflakeIdAsync(aclRule);

                    result.CreatedRules.Add(new CreatedAclRule
                    {
                        RuleId = ruleId,
                        RuleName = ruleName,
                        Topic = topic,
                        AccessType = template.AccessType,
                        Allow = template.Allow
                    });

                    _logger.LogDebug("设备ACL规则创建成功: RuleId={RuleId}, RuleName={RuleName}", ruleId, ruleName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建设备ACL规则失败: Template={Template}", template.RuleNameTemplate);

                    result.FailedRules.Add(new FailedAclRule
                    {
                        RuleName = ReplacePlaceholders(template.RuleNameTemplate, input),
                        Topic = ReplacePlaceholders(template.TopicTemplate, input),
                        AccessType = template.AccessType,
                        FailureReason = ex.Message
                    });
                }
            }

            result.TotalRequested = ruleTemplates.Count;
            result.SuccessCount = result.CreatedRules.Count;
            result.FailCount = result.FailedRules.Count;

            // 更新MQTT代理的ACL配置
            if (result.SuccessCount > 0)
            {
                await UpdateMqttBrokerAclConfigAsync();
            }

            _logger.LogInformation("设备ACL规则创建完成: DeviceId={DeviceId}, 成功={SuccessCount}, 失败={FailCount}",
                input.DeviceId, result.SuccessCount, result.FailCount);

            return result;
        }

        /// <summary>
        /// 删除设备的ACL规则
        /// </summary>
        public async Task<bool> DeleteDeviceAclRulesAsync(DeleteDeviceAclRulesInput input)
        {
            _logger.LogInformation("删除设备ACL规则: DeviceId={DeviceId}, DeleteAllRelated={DeleteAllRelated}",
                input.DeviceId, input.DeleteAllRelated);

            try
            {
                var query = _db.Queryable<MqttAclRuleEntity>();

                if (input.DeleteAllRelated)
                {
                    // 删除所有与设备相关的规则（用户名或客户端ID匹配）
                    query = query.Where(r => r.Username == input.DeviceId || r.ClientId == input.DeviceId);
                }
                else
                {
                    // 只删除标准的设备规则（规则名称包含设备ID）
                    query = query.Where(r => r.RuleName.Contains($"Device_{input.DeviceId}_"));
                }

                var rulesToDelete = await query.ToListAsync();

                if (rulesToDelete.Any())
                {
                    await _db.Deleteable(rulesToDelete).ExecuteCommandAsync();

                    // 更新MQTT代理的ACL配置
                    await UpdateMqttBrokerAclConfigAsync();

                    _logger.LogInformation("设备ACL规则删除成功: DeviceId={DeviceId}, 删除数量={Count}",
                        input.DeviceId, rulesToDelete.Count);
                }
                else
                {
                    _logger.LogInformation("未找到需要删除的设备ACL规则: DeviceId={DeviceId}", input.DeviceId);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备ACL规则失败: DeviceId={DeviceId}", input.DeviceId);
                return false;
            }
        }

        /// <summary>
        /// 查询设备的ACL规则
        /// </summary>
        public async Task<List<AclRuleOutput>> QueryDeviceAclRulesAsync(QueryDeviceAclRulesInput input)
        {
            try
            {
                var query = _db.Queryable<MqttAclRuleEntity>();

                if (input.OnlyActive)
                {
                    query = query.Where(r => r.IsActive);
                }

                if (!string.IsNullOrEmpty(input.DeviceId))
                {
                    query = query.Where(r => r.Username == input.DeviceId || r.ClientId == input.DeviceId);
                }

                if (!string.IsNullOrEmpty(input.ClientId))
                {
                    query = query.Where(r => r.ClientId == input.ClientId);
                }

                if (!string.IsNullOrEmpty(input.TopicFilter))
                {
                    query = query.Where(r => r.Topic.Contains(input.TopicFilter));
                }

                if (input.AccessType.HasValue)
                {
                    query = query.Where(r => r.AccessType == (int)input.AccessType.Value);
                }

                var rules = await query
                    .OrderByDescending(r => r.Priority)
                    .OrderBy(r => r.CreateTime)
                    .ToListAsync();

                return rules.Adapt<List<AclRuleOutput>>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询设备ACL规则失败");
                return new List<AclRuleOutput>();
            }
        }

        /// <summary>
        /// 更新设备ACL规则（当设备信息变更时）
        /// </summary>
        public async Task<DeviceAclRulesResult> UpdateDeviceAclRulesAsync(string oldDeviceId, CreateDeviceAclRulesInput newDeviceInfo)
        {
            _logger.LogInformation("更新设备ACL规则: OldDeviceId={OldDeviceId}, NewDeviceId={NewDeviceId}",
                oldDeviceId, newDeviceInfo.DeviceId);

            // 删除旧的ACL规则
            await DeleteDeviceAclRulesAsync(new DeleteDeviceAclRulesInput
            {
                DeviceId = oldDeviceId,
                DeleteAllRelated = true
            });

            // 创建新的ACL规则
            return await CreateDeviceAclRulesAsync(newDeviceInfo);
        }

        /// <summary>
        /// 获取设备ACL规则模板（系统预置模板）
        /// </summary>
        ///
        private Task<List<DeviceAclRuleTemplate>> GetDeviceAclRuleTemplatesAsync(CreateDeviceAclRulesInput input)
        {
            var templates = new List<DeviceAclRuleTemplate>
            {
                // 设备消息上报
                new DeviceAclRuleTemplate
                {
                    RuleNameTemplate = "Device_{DeviceId}_MessageUp",
                    TopicTemplate = "/devices/{device_id}/messages/control/up",
                    AccessType = MqttAccessType.Publish,
                    Allow = true,
                    DataFormat = 1,
                    Priority = 100,
                    DescriptionTemplate = "设备消息上报"
                },

                // 平台下发消息给设备
                new DeviceAclRuleTemplate
                {
                    RuleNameTemplate = "Device_{DeviceId}_MessageDown",
                    TopicTemplate = "/devices/{device_id}/messages/control/down",
                    AccessType = MqttAccessType.Subscribe,
                    Allow = true,
                    DataFormat = 1,
                    Priority = 101,
                    DescriptionTemplate = "平台下发消息给设备"
                },

                // 平台下发消息指令给设备
                new DeviceAclRuleTemplate
                {
                    RuleNameTemplate = "Device_{DeviceId}_CommandDown",
                    TopicTemplate = "/devices/{device_id}/messages/control/command/down",
                    AccessType = MqttAccessType.Subscribe,
                    Allow = true,
                    DataFormat = 2,
                    Priority = 102,
                    DescriptionTemplate = "平台下发消息指令给设备"
                },

                // 设备消息上报响应指令
                new DeviceAclRuleTemplate
                {
                    RuleNameTemplate = "Device_{DeviceId}_CommandUp",
                    TopicTemplate = "/devices/{device_id}/messages/control/commands/up",
                    AccessType = MqttAccessType.Publish,
                    Allow = true,
                    DataFormat = 2,
                    Priority = 103,
                    DescriptionTemplate = "设备消息上报响应指令"
                },

                // 设备上报属性数据
                new DeviceAclRuleTemplate
                {
                    RuleNameTemplate = "Device_{DeviceId}_PropertiesReport",
                    TopicTemplate = "/devices/{device_id}/properties/report",
                    AccessType = MqttAccessType.Publish,
                    Allow = true,
                    DataFormat = 1,
                    Priority = 104,
                    DescriptionTemplate = "设备上报属性数据"
                },

                // 网关批量上报属性数据
                new DeviceAclRuleTemplate
                {
                    RuleNameTemplate = "Device_{DeviceId}_GatewayPropertiesReport",
                    TopicTemplate = "/devices/{device_id}/gateway/sub_devices/properties/report",
                    AccessType = MqttAccessType.Publish,
                    Allow = true,
                    DataFormat = 1,
                    Priority = 105,
                    DescriptionTemplate = "网关批量上报属性数据"
                }
            };

            _logger.LogInformation("获取到 {Count} 个系统预置Topic模板", templates.Count);

            return Task.FromResult(templates);
        }

        /// <summary>
        /// 替换模板中的占位符
        /// </summary>
        private string ReplacePlaceholders(string template, CreateDeviceAclRulesInput input)
        {
            if (string.IsNullOrEmpty(template))
                return string.Empty;

            return template
                .Replace("{DeviceId}", input.DeviceId)
                .Replace("{device_id}", input.DeviceId)  // 支持小写格式
                .Replace("{DeviceName}", input.DeviceName ?? input.DeviceId)
                .Replace("{TopicPrefix}", input.TopicPrefix ?? input.DeviceId);
        }

        #endregion
    }
}