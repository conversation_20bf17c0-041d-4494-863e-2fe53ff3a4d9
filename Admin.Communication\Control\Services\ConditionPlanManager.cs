// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Collections.Concurrent;

namespace Admin.Communication.Control.Services;

/// <summary>
/// 条件计划缓存项
/// </summary>
public class ConditionPlanCache
{
    public long PlanId { get; set; }
    public string PlanName { get; set; }
    public long ParameterId { get; set; }
    public int Method { get; set; } // 1:大于 2:小于 3:等于
    public decimal ReferenceValue { get; set; }
    public string CmdIds { get; set; }
    public int CmdInterval { get; set; }
    public DateTime LastExecuteTime { get; set; } = DateTime.MinValue;
    public int CooldownMinutes { get; set; } = 5; // 冷却时间，防止频繁触发
}

/// <summary>
/// 条件计划管理器接口
/// </summary>
public interface IConditionPlanManager
{
    /// <summary>
    /// 初始化条件计划缓存
    /// </summary>
    Task InitializeAsync();

    /// <summary>
    /// 刷新条件计划缓存
    /// </summary>
    Task RefreshCacheAsync();

    /// <summary>
    /// 检查并执行条件计划
    /// </summary>
    /// <param name="parameterId">参数ID</param>
    /// <param name="currentValue">当前值</param>
    Task CheckAndExecuteAsync(long parameterId, decimal currentValue);

    /// <summary>
    /// 添加条件计划到缓存
    /// </summary>
    Task AddConditionPlanAsync(long planId);

    /// <summary>
    /// 从缓存中移除条件计划
    /// </summary>
    Task RemoveConditionPlanAsync(long planId);
}

/// <summary>
/// 条件计划管理器
/// </summary>
public class ConditionPlanManager(
    ISqlSugarClient db,
    IControlPlanExecutor planExecutor,
    ILogger<ConditionPlanManager> logger) : IConditionPlanManager
{
    private readonly ISqlSugarClient _db = db;
    private readonly IControlPlanExecutor _planExecutor = planExecutor;
    private readonly ILogger<ConditionPlanManager> _logger = logger;

    // 按参数ID分组的条件计划缓存
    private readonly ConcurrentDictionary<long, List<ConditionPlanCache>> _conditionPlansByParameter = new();

    /// <summary>
    /// 初始化条件计划缓存
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("开始初始化条件计划缓存");
            await RefreshCacheAsync();
            _logger.LogInformation("条件计划缓存初始化完成，共加载 {Count} 个参数的条件计划", _conditionPlansByParameter.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化条件计划缓存时发生错误");
        }
    }

    /// <summary>
    /// 刷新条件计划缓存
    /// </summary>
    public async Task RefreshCacheAsync()
    {
        try
        {
            // 清空现有缓存
            _conditionPlansByParameter.Clear();

            // 查询所有启用的条件执行计划
            var conditionPlans = await _db.Queryable<ControlPlanEntity>()
                .Where(cp => cp.IsAppControl == 1)      // 启用状态
                .Where(cp => cp.TriggerType == 2)       // 条件执行
                .Where(cp => cp.ParameterId != null)    // 有参数配置
                .ToListAsync();

            // 按参数ID分组缓存
            foreach (var plan in conditionPlans)
            {
                var cache = new ConditionPlanCache
                {
                    PlanId = plan.Id,
                    PlanName = plan.Name,
                    ParameterId = plan.ParameterId,
                    Method = plan.Method!,
                    ReferenceValue = plan.ReferenceValue!,
                    CmdIds = plan.CmdIds,
                    CmdInterval = plan.CmdInterval
                };

                var parameterId = plan.ParameterId;
                _conditionPlansByParameter.AddOrUpdate(
                    parameterId,
                    new List<ConditionPlanCache> { cache },
                    (key, existing) =>
                    {
                        existing.Add(cache);
                        return existing;
                    });
            }

            _logger.LogDebug("刷新条件计划缓存完成，共 {PlanCount} 个计划，{ParameterCount} 个参数", 
                conditionPlans.Count, _conditionPlansByParameter.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新条件计划缓存时发生错误");
        }
    }

    /// <summary>
    /// 检查并执行条件计划
    /// </summary>
    /// <param name="parameterId">参数ID</param>
    /// <param name="currentValue">当前值</param>
    public async Task CheckAndExecuteAsync(long parameterId, decimal currentValue)
    {
        try
        {
            if (!_conditionPlansByParameter.TryGetValue(parameterId, out var plans))
            {
                return; // 该参数没有关联的条件计划
            }

            var now = DateTime.Now;
            var executedCount = 0;

            foreach (var plan in plans)
            {
                try
                {
                    // 检查冷却时间
                    if (now.Subtract(plan.LastExecuteTime).TotalMinutes < plan.CooldownMinutes)
                    {
                        _logger.LogDebug("条件计划 {PlanName} 在冷却期内，跳过执行", plan.PlanName);
                        continue;
                    }

                    // 检查条件是否满足
                    if (!IsConditionMet(plan, currentValue))
                    {
                        continue;
                    }

                    _logger.LogInformation("条件计划触发: PlanId={PlanId}, Name={PlanName}, Parameter={ParameterId}, Value={Value}, Condition={Method} {ReferenceValue}",
                        plan.PlanId, plan.PlanName, parameterId, currentValue, GetMethodName(plan.Method), plan.ReferenceValue);

                    // 执行控制计划
                    var result = await _planExecutor.ExecutePlanAsync(plan.PlanId, $"条件触发 - 参数{parameterId}={currentValue}");

                    // 更新最后执行时间
                    plan.LastExecuteTime = now;

                    _logger.LogInformation("条件计划执行完成: PlanId={PlanId}, Total={Total}, Success={Success}, Failed={Failed}",
                        plan.PlanId, result.TotalCount, result.SuccessCount, result.FailedCount);

                    executedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行条件计划时发生错误: PlanId={PlanId}, Name={PlanName}", plan.PlanId, plan.PlanName);
                }
            }

            if (executedCount > 0)
            {
                _logger.LogInformation("参数 {ParameterId} 触发了 {Count} 个条件计划", parameterId, executedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查条件计划时发生错误: ParameterId={ParameterId}", parameterId);
        }
    }

    /// <summary>
    /// 添加条件计划到缓存
    /// </summary>
    public async Task AddConditionPlanAsync(long planId)
    {
        try
        {
            var plan = await _db.Queryable<ControlPlanEntity>()
                .Where(cp => cp.Id == planId)
                .Where(cp => cp.TriggerType == 2)
                .FirstAsync();

            if (plan?.ParameterId != null)
            {
                var cache = new ConditionPlanCache
                {
                    PlanId = plan.Id,
                    PlanName = plan.Name,
                    ParameterId = plan.ParameterId,
                    Method = plan.Method!,
                    ReferenceValue = plan.ReferenceValue,
                    CmdIds = plan.CmdIds,
                    CmdInterval = plan.CmdInterval
                };

                _conditionPlansByParameter.AddOrUpdate(
                    plan.ParameterId,
                    new List<ConditionPlanCache> { cache },
                    (key, existing) =>
                    {
                        existing.RemoveAll(p => p.PlanId == planId); // 移除旧的
                        existing.Add(cache); // 添加新的
                        return existing;
                    });

                _logger.LogDebug("添加条件计划到缓存: PlanId={PlanId}, ParameterId={ParameterId}", planId, plan.ParameterId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加条件计划到缓存时发生错误: PlanId={PlanId}", planId);
        }
    }

    /// <summary>
    /// 从缓存中移除条件计划
    /// </summary>
    public async Task RemoveConditionPlanAsync(long planId)
    {
        await Task.CompletedTask;

        try
        {
            foreach (var kvp in _conditionPlansByParameter)
            {
                kvp.Value.RemoveAll(p => p.PlanId == planId);
                if (kvp.Value.Count == 0)
                {
                    _conditionPlansByParameter.TryRemove(kvp.Key, out _);
                }
            }

            _logger.LogDebug("从缓存中移除条件计划: PlanId={PlanId}", planId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存中移除条件计划时发生错误: PlanId={PlanId}", planId);
        }
    }

    /// <summary>
    /// 检查条件是否满足
    /// </summary>
    private static bool IsConditionMet(ConditionPlanCache plan, decimal currentValue)
    {
        return plan.Method switch
        {
            1 => currentValue > plan.ReferenceValue,  // 大于
            2 => currentValue < plan.ReferenceValue,  // 小于
            3 => currentValue == plan.ReferenceValue, // 等于
            _ => false
        };
    }

    /// <summary>
    /// 获取方法名称
    /// </summary>
    private static string GetMethodName(int method)
    {
        return method switch
        {
            1 => ">",
            2 => "<",
            3 => "=",
            _ => "?"
        };
    }
}
