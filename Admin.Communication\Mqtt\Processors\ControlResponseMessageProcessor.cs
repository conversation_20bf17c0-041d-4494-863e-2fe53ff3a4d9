// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Services;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.RegularExpressions;

namespace Admin.Communication.Mqtt.Processors;

/// <summary>
/// 控制响应消息处理器
/// 处理设备控制响应消息：/devices/{device_id}/messages/control/up
/// </summary>
public class ControlResponseMessageProcessor(
    IControlResponseHandler responseHandler,
    ILogger<ControlResponseMessageProcessor> logger) : IMqttMessageHandler
{
    private readonly IControlResponseHandler _responseHandler = responseHandler;
    private readonly ILogger<ControlResponseMessageProcessor> _logger = logger;

    // 控制响应主题正则表达式
    private static readonly Regex ControlResponseTopicRegex = new(@"^/devices/(\d+)/messages/control/up$", RegexOptions.Compiled);

    /// <summary>
    /// 处理器名称
    /// </summary>
    public string Name => "ControlResponseMessageProcessor";

    /// <summary>
    /// 处理器描述
    /// </summary>
    public string Description => "控制响应消息处理器 - 处理设备控制指令的响应消息";

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 支持的主题模式
    /// </summary>
    public string[] SupportedTopicPatterns => new[] { "/devices/+/messages/control/up" };

    /// <summary>
    /// 支持的消息类型
    /// </summary>
    public Type[] SupportedMessageTypes => new[] { typeof(object) };

    /// <summary>
    /// 处理器优先级
    /// </summary>
    public int Priority => 100;

    /// <summary>
    /// 判断是否可以处理指定的消息
    /// </summary>
    /// <param name="message">MQTT消息</param>
    /// <param name="context">消息处理上下文</param>
    /// <returns>是否可以处理</returns>
    public bool CanHandle(MqttMessage message, MqttMessageContext context)
    {
        if (message is MqttPublishMessage publishMessage)
        {
            return ControlResponseTopicRegex.IsMatch(publishMessage.Topic);
        }
        return false;
    }

    /// <summary>
    /// 异步处理消息
    /// </summary>
    /// <param name="message">MQTT消息</param>
    /// <param name="context">消息处理上下文</param>
    /// <returns>处理结果</returns>
    public async Task<MqttMessageHandleResult> HandleAsync(MqttMessage message, MqttMessageContext context)
    {
        try
        {
            if (message is not MqttPublishMessage publishMessage)
            {
                return MqttMessageHandleResult.Failure("消息类型错误，只能处理发布消息");
            }

            _logger.LogDebug("收到控制响应消息: Topic={Topic}, PayloadSize={Size}",
                publishMessage.Topic, publishMessage.Payload.Length);

            // 从主题中提取设备ID
            var match = ControlResponseTopicRegex.Match(publishMessage.Topic);
            if (!match.Success)
            {
                _logger.LogWarning("控制响应主题格式错误: {Topic}", publishMessage.Topic);
                return MqttMessageHandleResult.Failure("主题格式错误");
            }

            if (!long.TryParse(match.Groups[1].Value, out var deviceId))
            {
                _logger.LogWarning("无法解析设备ID: {DeviceIdStr}", match.Groups[1].Value);
                return MqttMessageHandleResult.Failure("设备ID格式错误");
            }

            // 解析响应内容
            var responseContent = Encoding.UTF8.GetString(publishMessage.Payload);

            _logger.LogInformation("处理控制响应: DeviceId={DeviceId}, Content={Content}",
                deviceId, responseContent);

            // 调用响应处理器
            await _responseHandler.HandleControlResponseAsync(deviceId, responseContent);

            return MqttMessageHandleResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理控制响应消息时发生错误");
            return MqttMessageHandleResult.Failure(ex.Message, ex);
        }
    }

    /// <summary>
    /// 初始化处理器
    /// </summary>
    /// <returns>初始化任务</returns>
    public Task InitializeAsync()
    {
        _logger.LogInformation("控制响应消息处理器已初始化");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 销毁处理器
    /// </summary>
    /// <returns>销毁任务</returns>
    public Task DisposeAsync()
    {
        _logger.LogInformation("控制响应消息处理器已销毁");
        return Task.CompletedTask;
    }
}
