// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Volo.Abp.DependencyInjection;

namespace Admin.Communication.Alarm.Services;

/// <summary>
/// 告警事件生成器服务
/// </summary>
public class AlarmEventGeneratorService : ITransientDependency
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<AlarmEventGeneratorService> _logger;

    public AlarmEventGeneratorService(ISqlSugarClient db, ILogger<AlarmEventGeneratorService> logger)
    {
        _db = db;
        _logger = logger;
    }

    /// <summary>
    /// 为指定设备生成告警事件
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>生成的事件数量</returns>
    public async Task<int> GenerateAlarmEventsAsync(long deviceId)
    {
        try
        {
            // 1. 获取设备信息
            var device = await GetDeviceAsync(deviceId);
            if (device == null)
            {
                _logger.LogWarning("设备不存在: DeviceId={DeviceId}", deviceId);
                return 0;
            }

            // 2. 获取设备参数
            var parameters = await GetDeviceParametersAsync(deviceId);
            if (parameters == null || parameters.Count == 0)
            {
                _logger.LogInformation("设备没有参数配置: DeviceId={DeviceId}", deviceId);
                return 0;
            }

            // 3. 获取现有的告警事件
            var existingEvents = await GetExistingEventsAsync(deviceId);

            // 4. 生成应该存在的告警事件
            var expectedEvents = new List<AlarmEventEntity>();

            // 4.1 生成通讯失败事件
            var commFailEvent = GenerateCommunicationFailureEvent(device);
            if (commFailEvent != null)
            {
                expectedEvents.Add(commFailEvent);
            }

            // 4.2 为每个参数生成告警事件
            foreach (var parameter in parameters)
            {
                var parameterEvents = GenerateParameterEvents(device, parameter);
                expectedEvents.AddRange(parameterEvents);
            }

            // 5. 对比现有事件和期望事件，进行增量更新
            var result = await UpdateEventsIncrementallyAsync(existingEvents, expectedEvents);

            _logger.LogInformation("设备告警事件同步完成: DeviceId={DeviceId}, 新增={NewCount}, 更新={UpdateCount}",
                deviceId, result.NewCount, result.UpdateCount);

            return result.NewCount + result.UpdateCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成告警事件时发生异常: DeviceId={DeviceId}", deviceId);
            throw;
        }
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    private async Task<DeviceEntity> GetDeviceAsync(long deviceId)
    {
        return await _db.Queryable<DeviceEntity>()
            .Where(d => d.Id == deviceId)
            .FirstAsync();
    }

    /// <summary>
    /// 获取设备参数列表
    /// </summary>
    private async Task<List<DeviceParaEntity>> GetDeviceParametersAsync(long deviceId)
    {
        return await _db.Queryable<DeviceParaEntity>()
            .Where(p => p.DeviceId == deviceId && p.MonitorStatus == 1) // 只获取启用监控的参数
            .ToListAsync();
    }

    /// <summary>
    /// 获取现有的告警事件
    /// </summary>
    private async Task<List<AlarmEventEntity>> GetExistingEventsAsync(long deviceId)
    {
        // 获取通讯失败事件
        var commFailEvents = await _db.Queryable<AlarmEventEntity>()
            .Where(e => e.Id == $"C_F_{deviceId}")
            .ToListAsync();

        // 获取参数相关事件
        var parameterIds = await _db.Queryable<DeviceParaEntity>()
            .Where(p => p.DeviceId == deviceId)
            .Select(p => p.ParameterId)
            .ToListAsync();

        var parameterEvents = new List<AlarmEventEntity>();
        foreach (var parameterId in parameterIds)
        {
            var events = await _db.Queryable<AlarmEventEntity>()
                .Where(e => e.Id.StartsWith($"U_L_{parameterId}") ||
                           e.Id.StartsWith($"L_L_{parameterId}") ||
                           e.Id.StartsWith($"U_W_{parameterId}") ||
                           e.Id.StartsWith($"L_W_{parameterId}") ||
                           e.Id.StartsWith($"S_A_{parameterId}"))
                .ToListAsync();
            parameterEvents.AddRange(events);
        }

        var allEvents = new List<AlarmEventEntity>();
        allEvents.AddRange(commFailEvents);
        allEvents.AddRange(parameterEvents);

        return allEvents;
    }

    /// <summary>
    /// 增量更新告警事件
    /// </summary>
    private async Task<EventUpdateResult> UpdateEventsIncrementallyAsync(List<AlarmEventEntity> existingEvents, List<AlarmEventEntity> expectedEvents)
    {
        var result = new EventUpdateResult();
        var existingEventDict = existingEvents.ToDictionary(e => e.Id, e => e);

        foreach (var expectedEvent in expectedEvents)
        {
            if (existingEventDict.TryGetValue(expectedEvent.Id, out var existingEvent))
            {
                // 检查是否需要更新
                if (IsEventNeedUpdate(existingEvent, expectedEvent))
                {
                    // 更新现有事件
                    existingEvent.Name = expectedEvent.Name;
                    existingEvent.Level = expectedEvent.Level;
                    existingEvent.AlarmType = expectedEvent.AlarmType;
                    existingEvent.MonitorStatus = expectedEvent.MonitorStatus;

                    await _db.Updateable(existingEvent).ExecuteCommandAsync();
                    result.UpdateCount++;

                    _logger.LogDebug("更新告警事件: EventId={EventId}, EventName={EventName}",
                        existingEvent.Id, existingEvent.Name);
                }
            }
            else
            {
                // 新增事件
                await _db.Insertable(expectedEvent).ExecuteCommandAsync();
                result.NewCount++;

                _logger.LogDebug("新增告警事件: EventId={EventId}, EventName={EventName}",
                    expectedEvent.Id, expectedEvent.Name);
            }
        }

        return result;
    }

    /// <summary>
    /// 检查事件是否需要更新
    /// </summary>
    private bool IsEventNeedUpdate(AlarmEventEntity existing, AlarmEventEntity expected)
    {
        return existing.Name != expected.Name ||
               existing.Level != expected.Level ||
               existing.AlarmType != expected.AlarmType ||
               existing.MonitorStatus != expected.MonitorStatus;
    }

    /// <summary>
    /// 生成通讯失败事件
    /// </summary>
    private AlarmEventEntity GenerateCommunicationFailureEvent(DeviceEntity device)
    {
        return new AlarmEventEntity
        {
            // 通讯失败事件ID格式：C_F_{DeviceId}
            Id = $"C_F_{device.Id}",
            Name = $"{device.DeviceName}通讯失败",
            Level = (int)AlarmLevelEnum.Emergency, // 通讯失败默认为紧急级别
            AlarmType = (int)AlarmTypeEnum.CommunicationFailure,
            MonitorStatus = 1 // 默认启用
        };
    }

    /// <summary>
    /// 为参数生成告警事件
    /// </summary>
    private List<AlarmEventEntity> GenerateParameterEvents(DeviceEntity device, DeviceParaEntity parameter)
    {
        var events = new List<AlarmEventEntity>();

        // 检查是否设置了告警等级（生成告警事件的前提）
        if (parameter.AlarmLevel <= 0 || parameter.AlarmLevel > 4)
        {
            _logger.LogDebug("参数未设置有效的告警等级，跳过事件生成: ParameterId={ParameterId}, AlarmLevel={AlarmLevel}", 
                parameter.ParameterId, parameter.AlarmLevel);
            return events;
        }

        // 根据数据类型生成不同的事件
        switch (parameter.DataType)
        {
            case 5: // 枚举类型
                events.AddRange(GenerateEnumParameterEvents(device, parameter));
                break;
            case 1: // decimal 模拟量
                events.AddRange(GenerateAnalogParameterEvents(device, parameter));
                break;
            default:
                // 其他数据类型暂不支持告警事件生成
                _logger.LogDebug("参数数据类型不支持告警事件生成: ParameterId={ParameterId}, DataType={DataType}", 
                    parameter.ParameterId, parameter.DataType);
                break;
        }

        return events;
    }

    /// <summary>
    /// 生成枚举参数的告警事件
    /// </summary>
    private List<AlarmEventEntity> GenerateEnumParameterEvents(DeviceEntity device, DeviceParaEntity parameter)
    {
        var events = new List<AlarmEventEntity>();

        // 枚举类型需要检查是否设置了上限或下限告警值
        if (!parameter.AlarmUpperLimit.HasValue && !parameter.AlarmLowerLimit.HasValue)
        {
            _logger.LogDebug("枚举参数未设置告警值，跳过事件生成: ParameterId={ParameterId}, ParameterName={ParameterName}",
                parameter.ParameterId, parameter.Name);
            return events;
        }

        // 枚举类型只生成一个状态异常事件
        var statusEvent = new AlarmEventEntity
        {
            // 状态异常事件ID格式：S_A_{ParameterId}
            Id = $"S_A_{parameter.ParameterId}",
            Name = $"{device.DeviceName}{parameter.Name}告警",
            Level = parameter.AlarmLevel,
            AlarmType = (int)AlarmTypeEnum.StatusAbnormal,
            MonitorStatus = 1 // 默认启用
        };

        events.Add(statusEvent);

        _logger.LogDebug("生成枚举参数告警事件: EventId={EventId}, EventName={EventName}",
            statusEvent.Id, statusEvent.Name);

        return events;
    }

    /// <summary>
    /// 生成模拟量参数的告警事件
    /// </summary>
    private List<AlarmEventEntity> GenerateAnalogParameterEvents(DeviceEntity device, DeviceParaEntity parameter)
    {
        var events = new List<AlarmEventEntity>();

        // 根据报警上限生成上限告警事件
        if (parameter.AlarmUpperLimit.HasValue)
        {
            var upperLimitEvent = new AlarmEventEntity
            {
                // 上限告警事件ID格式：U_L_{ParameterId}
                Id = $"U_L_{parameter.ParameterId}",
                Name = $"{device.DeviceName}{parameter.Name}上限告警",
                Level = parameter.AlarmLevel,
                AlarmType = (int)AlarmTypeEnum.ValueOverUpperLimit,
                MonitorStatus = 1 // 默认启用
            };

            events.Add(upperLimitEvent);
            
            _logger.LogDebug("生成模拟量参数上限告警事件: EventId={EventId}, EventName={EventName}", 
                upperLimitEvent.Id, upperLimitEvent.Name);
        }

        // 根据报警下限生成下限告警事件
        if (parameter.AlarmLowerLimit.HasValue)
        {
            var lowerLimitEvent = new AlarmEventEntity
            {
                // 下限告警事件ID格式：L_L_{ParameterId}
                Id = $"L_L_{parameter.ParameterId}",
                Name = $"{device.DeviceName}{parameter.Name}下限告警",
                Level = parameter.AlarmLevel,
                AlarmType = (int)AlarmTypeEnum.ValueOverLowerLimit,
                MonitorStatus = 1 // 默认启用
            };

            events.Add(lowerLimitEvent);

            _logger.LogDebug("生成模拟量参数下限告警事件: EventId={EventId}, EventName={EventName}",
                lowerLimitEvent.Id, lowerLimitEvent.Name);
        }

        // 根据预警上限生成上限预警事件
        if (parameter.WarningUpperLimit.HasValue)
        {
            var upperWarningEvent = new AlarmEventEntity
            {
                // 上限预警事件ID格式：U_W_{ParameterId}
                Id = $"U_W_{parameter.ParameterId}",
                Name = $"{device.DeviceName}{parameter.Name}上限预警",
                Level = (int)AlarmLevelEnum.Warning, // 预警等级默认为预警
                AlarmType = (int)AlarmTypeEnum.ValueOverUpperLimit,
                MonitorStatus = 1 // 默认启用
            };

            events.Add(upperWarningEvent);

            _logger.LogDebug("生成模拟量参数上限预警事件: EventId={EventId}, EventName={EventName}",
                upperWarningEvent.Id, upperWarningEvent.Name);
        }

        // 根据预警下限生成下限预警事件
        if (parameter.WarningLowerLimit.HasValue)
        {
            var lowerWarningEvent = new AlarmEventEntity
            {
                // 下限预警事件ID格式：L_W_{ParameterId}
                Id = $"L_W_{parameter.ParameterId}",
                Name = $"{device.DeviceName}{parameter.Name}下限预警",
                Level = (int)AlarmLevelEnum.Warning, // 预警等级默认为预警
                AlarmType = (int)AlarmTypeEnum.ValueOverLowerLimit,
                MonitorStatus = 1 // 默认启用
            };

            events.Add(lowerWarningEvent);

            _logger.LogDebug("生成模拟量参数下限预警事件: EventId={EventId}, EventName={EventName}",
                lowerWarningEvent.Id, lowerWarningEvent.Name);
        }

        // 如果既没有设置告警值也没有设置预警值，记录日志
        if (!parameter.AlarmUpperLimit.HasValue && !parameter.AlarmLowerLimit.HasValue &&
            !parameter.WarningUpperLimit.HasValue && !parameter.WarningLowerLimit.HasValue)
        {
            _logger.LogDebug("模拟量参数未设置告警值和预警值，跳过事件生成: ParameterId={ParameterId}, ParameterName={ParameterName}",
                parameter.ParameterId, parameter.Name);
        }

        return events;
    }

    /// <summary>
    /// 批量为多个设备生成告警事件
    /// </summary>
    /// <param name="deviceIds">设备ID列表</param>
    /// <returns>生成的事件总数</returns>
    public async Task<int> GenerateAlarmEventsForDevicesAsync(List<long> deviceIds)
    {
        int totalEventCount = 0;

        foreach (var deviceId in deviceIds)
        {
            try
            {
                var eventCount = await GenerateAlarmEventsAsync(deviceId);
                totalEventCount += eventCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为设备生成告警事件失败: DeviceId={DeviceId}", deviceId);
                // 继续处理其他设备
            }
        }

        _logger.LogInformation("批量生成告警事件完成: DeviceCount={DeviceCount}, TotalEventCount={TotalEventCount}", 
            deviceIds.Count, totalEventCount);

        return totalEventCount;
    }

    /// <summary>
    /// 为所有设备生成告警事件
    /// </summary>
    /// <returns>生成的事件总数</returns>
    public async Task<int> GenerateAlarmEventsForAllDevicesAsync()
    {
        var deviceIds = await _db.Queryable<DeviceEntity>()
            .Where(d => d.IsEnabled) // 只处理启用的设备
            .Select(d => d.Id)
            .ToListAsync();

        return await GenerateAlarmEventsForDevicesAsync(deviceIds);
    }
}

/// <summary>
/// 事件更新结果
/// </summary>
public class EventUpdateResult
{
    /// <summary>
    /// 新增事件数量
    /// </summary>
    public int NewCount { get; set; }

    /// <summary>
    /// 更新事件数量
    /// </summary>
    public int UpdateCount { get; set; }
}
