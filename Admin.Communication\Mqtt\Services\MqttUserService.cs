// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Mqtt.Abstractions;
using Admin.SqlSugar.Entity.Business.LOT;
using Admin.Core.DataEncryption.Encryptions;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Text.RegularExpressions;

namespace Admin.Communication.Mqtt.Services;

/// <summary>
/// MQTT用户服务实现
/// </summary>
public class MqttUserService : IMqttUserService
{
    private readonly ILogger<MqttUserService> _logger;
    private readonly ISqlSugarClient _db;

    public MqttUserService(ILogger<MqttUserService> logger, ISqlSugarClient db)
    {
        _logger = logger;
        _db = db;
    }

    /// <summary>
    /// 验证用户凭据
    /// </summary>
    public async Task<(bool IsAuthenticated, string Role, string Message)> ValidateCredentialsAsync(string username, string password, string clientId)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
            {
                return (false, string.Empty, "用户名不能为空");
            }

            var user = await _db.Queryable<MqttUserEntity>()
                .Where(u => u.Username == username)
                .FirstAsync();

            if (user == null)
            {
                _logger.LogWarning("MQTT用户验证失败: 用户 {Username} 不存在", username);
                return (false, string.Empty, $"用户 {username} 不存在");
            }

            if (!user.IsEnabled)
            {
                _logger.LogWarning("MQTT用户验证失败: 用户 {Username} 已禁用", username);
                return (false, string.Empty, $"用户 {username} 已禁用");
            }

            if (user.ExpireTime.HasValue && user.ExpireTime.Value < DateTime.Now)
            {
                _logger.LogWarning("MQTT用户验证失败: 用户 {Username} 已过期", username);
                return (false, string.Empty, $"用户 {username} 已过期");
            }

            if (user.Password != password)
            {
                _logger.LogWarning("MQTT用户验证失败: 用户 {Username} 密码错误", username);
                return (false, string.Empty, $"用户 {username} 密码错误");
            }

            // 验证客户端ID限制
            if (!string.IsNullOrEmpty(user.ClientIdLimit))
            {
                var allowedClientIds = user.ClientIdLimit.Split(',', StringSplitOptions.RemoveEmptyEntries);
                bool clientIdAllowed = false;

                foreach (var allowedClientId in allowedClientIds)
                {
                    if (IsClientIdMatch(clientId, allowedClientId.Trim()))
                    {
                        clientIdAllowed = true;
                        break;
                    }
                }

                if (!clientIdAllowed)
                {
                    _logger.LogWarning("MQTT用户验证失败: 客户端ID {ClientId} 不在允许列表中", clientId);
                    return (false, string.Empty, $"客户端ID {clientId} 不在允许列表中");
                }
            }

            // 所有MQTT用户都是普通用户角色
            string role = "user";
            _logger.LogInformation("MQTT用户 {Username} 验证成功, 角色: {Role}", username, role);
            return (true, role, "验证成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证MQTT用户凭据时发生错误: Username={Username}, ClientId={ClientId}", username, clientId);
            return (false, string.Empty, "验证过程中发生错误");
        }
    }

    /// <summary>
    /// 获取用户信息
    /// </summary>
    public async Task<MqttUserEntity> GetUserAsync(string username)
    {
        return await _db.Queryable<MqttUserEntity>()
            .Where(u => u.Username == username)
            .FirstAsync();
    }

    /// <summary>
    /// 获取匹配的ACL规则
    /// </summary>
    public async Task<List<MqttAclRuleEntity>> GetMatchingAclRulesAsync(string username, string clientId, string topic, int accessType)
    {
        try
        {
            // 查询激活的ACL规则，按优先级降序排序
            var query = _db.Queryable<MqttAclRuleEntity>()
                .Where(r => r.IsActive)
                .WhereIF(!string.IsNullOrEmpty(username), r => r.Username == username || r.Username == null || r.Username == string.Empty)
                .WhereIF(!string.IsNullOrEmpty(clientId), r => r.ClientId == clientId || r.ClientId == null || r.ClientId == string.Empty)
                .Where(r => r.AccessType == accessType || r.AccessType == 0) // 0表示所有访问类型
                .OrderByDescending(r => r.Priority);

            // 获取所有可能匹配的规则
            var rules = await query.ToListAsync();

            // 进一步过滤主题匹配的规则
            var matchingRules = rules.Where(r => IsTopicMatch(topic, r.Topic)).ToList();

            return matchingRules;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取匹配的ACL规则时发生错误: Username={Username}, ClientId={ClientId}, Topic={Topic}, AccessType={AccessType}",
                username, clientId, topic, accessType);
            return new List<MqttAclRuleEntity>();
        }
    }

    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    public async Task UpdateUserLastLoginAsync(string username, string ip)
    {
        try
        {
            await _db.Updateable<MqttUserEntity>()
                .SetColumns(u => new MqttUserEntity
                {
                    LastLoginTime = DateTime.Now,
                    LastLoginIp = ip
                })
                .Where(u => u.Username == username)
                .ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户最后登录信息时发生错误: Username={Username}, IP={IP}", username, ip);
        }
    }

    /// <summary>
    /// 判断主题是否匹配
    /// </summary>
    private bool IsTopicMatch(string topic, string pattern)
    {
        // 将MQTT通配符转换为正则表达式
        string regexPattern = "^" + Regex.Escape(pattern)
            .Replace(@"\+", "[^/]+")           // +匹配一个层级
            .Replace(@"\#", ".*")              // #匹配多个层级
            + "$";

        return Regex.IsMatch(topic, regexPattern);
    }

    /// <summary>
    /// 判断客户端ID是否匹配
    /// </summary>
    private bool IsClientIdMatch(string clientId, string pattern)
    {
        // 支持简单的通配符匹配
        if (pattern.Contains('*'))
        {
            string regexPattern = "^" + Regex.Escape(pattern).Replace("\\*", ".*") + "$";
            return Regex.IsMatch(clientId, regexPattern);
        }
        else
        {
            return clientId == pattern;
        }
    }

    /// <summary>
    /// 添加MQTT用户
    /// </summary>
    public async Task<bool> AddUserAsync(MqttUserEntity user)
    {
        try
        {
            // 检查用户是否已存在
            var existingUser = await _db.Queryable<MqttUserEntity>()
                .Where(u => u.Username == user.Username)
                .FirstAsync();

            if (existingUser != null)
            {
                _logger.LogWarning("添加MQTT用户失败: 用户 {Username} 已存在", user.Username);
                return false;
            }

            // CreateTime和CreateBy由框架AOP自动处理

            // 插入用户
            var result = await _db.Insertable(user).ExecuteCommandAsync();

            _logger.LogInformation("MQTT用户 {Username} 添加成功", user.Username);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加MQTT用户时发生错误: Username={Username}", user.Username);
            return false;
        }
    }

    /// <summary>
    /// 更新MQTT用户
    /// </summary>
    public async Task<bool> UpdateUserAsync(MqttUserEntity user)
    {
        try
        {
            // 设置更新时间
            user.UpdateTime = DateTime.Now;
            user.UpdateBy = 1; // 系统更新

            var result = await _db.Updateable(user).ExecuteCommandAsync();

            _logger.LogInformation("MQTT用户 {Username} 更新成功", user.Username);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新MQTT用户时发生错误: Username={Username}", user.Username);
            return false;
        }
    }

    /// <summary>
    /// 删除MQTT用户
    /// </summary>
    public async Task<bool> DeleteUserAsync(string username)
    {
        try
        {
            var result = await _db.Deleteable<MqttUserEntity>()
                .Where(u => u.Username == username)
                .ExecuteCommandAsync();

            if (result > 0)
            {
                _logger.LogInformation("MQTT用户 {Username} 删除成功", username);
                return true;
            }
            else
            {
                _logger.LogWarning("删除MQTT用户失败: 用户 {Username} 不存在", username);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除MQTT用户时发生错误: Username={Username}", username);
            return false;
        }
    }

    /// <summary>
    /// 分页查询用户
    /// </summary>
    public async Task<(List<MqttUserEntity> Users, int TotalCount)> QueryUsersAsync(int page = 1, int pageSize = 20, string? username = null, bool? isEnabled = null)
    {
        try
        {
            var query = _db.Queryable<MqttUserEntity>()
                .WhereIF(!string.IsNullOrEmpty(username), u => u.Username.Contains(username))
                .WhereIF(isEnabled.HasValue, u => u.IsEnabled == isEnabled.Value)
                .OrderByDescending(u => u.CreateTime);

            // 获取总数
            var totalCount = await query.CountAsync();

            // 分页查询
            var users = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (users, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询MQTT用户时发生错误");
            return (new List<MqttUserEntity>(), 0);
        }
    }

    /// <summary>
    /// 获取所有用户列表
    /// </summary>
    public async Task<List<MqttUserEntity>> GetAllUsersAsync()
    {
        try
        {
            return await _db.Queryable<MqttUserEntity>()
                .OrderByDescending(u => u.CreateTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有MQTT用户时发生错误");
            return new List<MqttUserEntity>();
        }
    }

    /// <summary>
    /// 检查用户是否存在
    /// </summary>
    public async Task<bool> UserExistsAsync(string username)
    {
        try
        {
            return await _db.Queryable<MqttUserEntity>()
                .Where(u => u.Username == username)
                .AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查MQTT用户是否存在时发生错误: Username={Username}", username);
            return false;
        }
    }

    /// <summary>
    /// 为设备创建MQTT用户
    /// </summary>
    public async Task<bool> CreateDeviceUserAsync(string deviceId, string deviceSecret, string? deviceName = null, DateTime? expireTime = null)
    {
        try
        {
            // 检查用户是否已存在
            if (await UserExistsAsync(deviceId))
            {
                _logger.LogWarning("创建设备MQTT用户失败: 设备 {DeviceId} 对应的用户已存在", deviceId);
                return false;
            }

            // 创建设备用户
            var entity = new MqttUserEntity
            {
                Username = deviceId,
                Password = MD5Encryption.Encrypt(deviceSecret, false, false),
                ClientIdLimit = deviceId, // 限制客户端ID为设备ID
                ExpireTime = expireTime,
                Description = $"设备用户: {deviceName ?? deviceId}",
                IsEnabled = true
            };

            var result = await _db.Insertable(entity).ExecuteCommandAsync();

            _logger.LogInformation("设备MQTT用户创建成功: DeviceId={DeviceId}", deviceId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建设备MQTT用户时发生错误: DeviceId={DeviceId}", deviceId);
            return false;
        }
    }

    /// <summary>
    /// 启用/禁用用户
    /// </summary>
    public async Task<bool> ToggleUserAsync(string username, bool isEnabled)
    {
        try
        {
            var result = await _db.Updateable<MqttUserEntity>()
                .SetColumns(u => new MqttUserEntity
                {
                    IsEnabled = isEnabled,
                    UpdateTime = DateTime.Now,
                    UpdateBy = 1
                })
                .Where(u => u.Username == username)
                .ExecuteCommandAsync();

            if (result > 0)
            {
                _logger.LogInformation("MQTT用户 {Username} 状态更新成功: {Status}", username, isEnabled ? "启用" : "禁用");
                return true;
            }
            else
            {
                _logger.LogWarning("更新MQTT用户状态失败: 用户 {Username} 不存在", username);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新MQTT用户状态时发生错误: Username={Username}", username);
            return false;
        }
    }

    /// <summary>
    /// 重置用户密码
    /// </summary>
    public async Task<bool> ResetPasswordAsync(string username, string newPassword)
    {
        try
        {
            var result = await _db.Updateable<MqttUserEntity>()
                .SetColumns(u => new MqttUserEntity
                {
                    Password = MD5Encryption.Encrypt(newPassword, false, false),
                    UpdateTime = DateTime.Now,
                    UpdateBy = 1
                })
                .Where(u => u.Username == username)
                .ExecuteCommandAsync();

            if (result > 0)
            {
                _logger.LogInformation("MQTT用户 {Username} 密码重置成功", username);
                return true;
            }
            else
            {
                _logger.LogWarning("重置MQTT用户密码失败: 用户 {Username} 不存在", username);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置MQTT用户密码时发生错误: Username={Username}", username);
            return false;
        }
    }
}