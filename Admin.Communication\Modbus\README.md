# Modbus RTU 通信功能

## 概述

本模块实现了基于MQTT的Modbus RTU通信功能，支持指令级别的精细化控制和调度。

## 架构设计

### 核心组件

1. **ModbusInstructionSchedulerService** - 指令调度服务
   - 负责按指令级别调度Modbus RTU指令的执行
   - 支持每条指令独立的时间参数：ReadInterval、ResponseTime、RetryCount
   - 运行在后台服务层，自动启动

2. **ModbusResponseHandler** - 响应处理器
   - 处理设备返回的Modbus RTU响应数据
   - 匹配等待响应的指令
   - 解析响应数据并映射到设备属性

3. **DeviceDataHandler** - 数据处理器扩展
   - 扩展现有的设备数据处理器
   - 支持Modbus响应主题：`/devices/{device_id}/modbus/command/up`

### 主题设计

- **指令发布主题**: `/devices/{device_id}/modbus/command/down`
- **响应接收主题**: `/devices/{device_id}/modbus/command/up`

## 功能特性

### 指令级精细控制

每条指令支持独立配置：
- **ReadInterval**: 相同指令的发送间隔（毫秒）
- **ResponseTime**: 单条指令的响应超时（毫秒）
- **RetryCount**: 单条指令的重试次数
- **IsEnabled**: 指令级别的启用/禁用控制

### 调度策略

- **并发设备**: 不同设备的指令可以并发执行
- **串行指令**: 同一设备内的指令串行执行，避免冲突
- **异步处理**: 指令执行不阻塞调度器主循环
- **智能重试**: 每条指令独立重试，不影响其他指令

### 可靠性保障

- **超时控制**: 每条指令有独立的响应超时时间
- **状态监控**: 详细记录每条指令的执行状态
- **异常隔离**: 单个设备异常不影响其他设备
- **自动恢复**: 设备重新上线后自动恢复调度

## 配置说明

### appsettings.json 配置

```json
{
  "Modbus": {
    "SchedulerInterval": 1000,        // 调度器扫描间隔(毫秒)
    "MaxConcurrentDevices": 10,       // 最大并发设备数
    "DeviceInstructionDelay": 100,    // 设备内指令间隔(毫秒)
    "DefaultTimeout": 5000,           // 默认响应超时(毫秒)
    "EnableRetry": true,              // 是否启用重试
    "MaxRetryCount": 3                // 最大重试次数
  }
}
```

## 使用方法

### 1. 设备配置

确保设备满足以下条件：
- 产品协议类型为 `Modbus` (ProtocolType = 2)
- 数据格式为 `HEX` (DataFormat = 2)
- 设备指令表中配置了相应的Modbus指令

### 2. 指令配置

在 `DeviceInstructionEntity` 表中配置指令：
```sql
INSERT INTO DEVICE_INSTRUCTION (
    DEVICE_ID, INSTRUCTION_NAME, SEND_STR, 
    READ_INTERVAL, RESPONSE_TIME, RETRY_COUNT, IS_ENABLED
) VALUES (
    1, '读取温湿度', '010300000002C40B',
    10000, 2000, 3, 1
);
```

### 3. API接口

#### 管理接口

- `GET /api/modbus/devices` - 获取Modbus设备列表
- `GET /api/modbus/devices/{deviceId}/instructions` - 获取设备指令列表
- `PUT /api/modbus/instructions` - 更新指令参数
- `POST /api/modbus/instructions/{id}/enable` - 启用/禁用指令

#### 测试接口

- `POST /api/modbus/test/send-command` - 手动发送测试指令
- `POST /api/modbus/test/simulate-response` - 模拟设备响应
- `POST /api/modbus/test/generate-command` - 生成标准Modbus指令

### 4. 监控和调试

#### 日志监控

系统会记录详细的执行日志：
- 指令发送日志
- 响应接收日志
- 超时和重试日志
- 错误和异常日志

#### 状态查询

可以通过API查询指令执行状态和统计信息。

## 示例

### 温湿度传感器示例

1. **设备配置**
   - 设备地址：1
   - 功能码：03 (读保持寄存器)
   - 起始地址：0
   - 读取数量：2

2. **指令生成**
   ```
   01 03 00 00 00 02 C4 0B
   ```

3. **响应解析**
   ```
   01 03 04 02 15 12 54 CRC16
   ```
   - 温度：0x0215 = 533 (53.3°C)
   - 湿度：0x1254 = 4692 (46.92%RH)

## 扩展开发

### 添加新的数据解析器

1. 实现 `IDataParser` 接口
2. 在 `DataParserFactory` 中注册
3. 配置对应的数据格式

### 自定义调度策略

1. 继承 `ModbusInstructionSchedulerService`
2. 重写调度逻辑
3. 在模块中替换注册

### 添加新的响应处理逻辑

1. 扩展 `ModbusResponseHandler`
2. 添加自定义解析规则
3. 支持更多的Modbus功能码

## 注意事项

1. **设备地址冲突**: 确保同一网络中设备地址唯一
2. **指令间隔**: 合理设置指令间隔，避免设备过载
3. **网络延迟**: 根据网络情况调整响应超时时间
4. **数据解析**: 根据设备文档正确配置寄存器地址和数据类型
5. **错误处理**: 监控错误日志，及时处理通信异常

## 故障排除

### 常见问题

1. **指令不执行**: 检查设备是否启用，指令是否启用
2. **响应超时**: 检查设备连接，调整超时时间
3. **数据解析错误**: 检查寄存器地址配置，验证数据格式
4. **重试失败**: 检查网络连接，增加重试次数

### 调试方法

1. 使用测试接口手动发送指令
2. 查看详细的执行日志
3. 模拟设备响应进行测试
4. 监控MQTT消息流量
