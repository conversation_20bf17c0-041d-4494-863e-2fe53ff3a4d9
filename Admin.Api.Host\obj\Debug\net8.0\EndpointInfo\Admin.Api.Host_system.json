{"openapi": "3.0.1", "info": {"title": "系统管理"}, "paths": {"/api/v1/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登录", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.LoginInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.LoginInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.LoginInput"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.LoginOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.LoginOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.LoginOutput"}}}}}}}, "/api/v1/auth/callback": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Auht2.0 回调服务", "parameters": [{"name": "Code", "in": "query", "description": "code", "required": true, "schema": {"type": "string"}}, {"name": "State", "in": "query", "description": "state", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/auth/bind-user": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "绑定用户", "requestBody": {"description": "input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.BindUserInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.BindUserInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.BindUserInput"}}}, "required": true}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/auth/register-user": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "注册用户", "requestBody": {"description": "input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.RegisterUserInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.RegisterUserInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.RegisterUserInput"}}}, "required": true}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/auth/vben-user-info": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取当前用户信息（vben）", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput"}}}}}}}, "/api/v1/auth/user-info": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取当前用户信息", "parameters": [{"name": "password", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetUserInfoOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetUserInfoOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetUserInfoOutput"}}}}}}, "put": {"tags": ["<PERSON><PERSON>"], "summary": "修改当前用户信息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.PutUserInfoInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.PutUserInfoInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.PutUserInfoInput"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/auth/functions": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取当前用户的功能", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/v1/auth/organization-tree": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取当前用户组织机构树", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput"}}}}}}}}, "/api/v1/auth/system-platform-info": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获得当前平台信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetSystemPlatformInfoOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetSystemPlatformInfoOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetSystemPlatformInfoOutput"}}}}}}}, "/api/v1/auth/unread-notice": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获得用户的通知", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Multiplex.Contracts.IAdminUser.Models.NoticeItemModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Multiplex.Contracts.IAdminUser.Models.NoticeItemModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Multiplex.Contracts.IAdminUser.Models.NoticeItemModel"}}}}}}}}, "/api/v1/dict-category/paged-list": {"get": {"tags": ["DictCategory"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "名称", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/dict-category/{id}": {"get": {"tags": ["DictCategory"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput"}}}}}}, "put": {"tags": ["DictCategory"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["DictCategory"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/dict-category": {"post": {"tags": ["DictCategory"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/dict-data/paged-list": {"get": {"tags": ["DictData"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "名称", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "description": "字典分类Id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DictDataServices.Dtos.DictDataOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DictDataServices.Dtos.DictDataOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.DictDataServices.Dtos.DictDataOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/dict-data": {"get": {"tags": ["DictData"], "summary": "查询分类下的所有数据", "parameters": [{"name": "categoryCode", "in": "query", "description": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.DictDataOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.DictDataOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.DictDataOutput"}}}}}}}, "post": {"tags": ["DictData"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.AddDictDataInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.AddDictDataInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.AddDictDataInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/dict-data/{id}": {"get": {"tags": ["DictData"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.DictDataOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.DictDataOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.DictDataOutput"}}}}}}, "put": {"tags": ["DictData"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.AddDictDataInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.AddDictDataInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.AddDictDataInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["DictData"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/function/paged-list": {"get": {"tags": ["Function"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "功能名称", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.FunctionServices.Dtos.FunctionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.FunctionServices.Dtos.FunctionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.FunctionServices.Dtos.FunctionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/function/{id}": {"get": {"tags": ["Function"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}}}}}}, "put": {"tags": ["Function"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AddFunctionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AddFunctionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AddFunctionInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Function"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/function": {"post": {"tags": ["Function"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AddFunctionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AddFunctionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AddFunctionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/function/tree": {"get": {"tags": ["Function"], "summary": "功能树查询", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}}}}}}}}, "/api/v1/function/{functionId}/interfaces": {"get": {"tags": ["Function"], "summary": "获取功能拥有的接口", "parameters": [{"name": "functionId", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput"}}}}}}}}, "/api/v1/function/assign-interface": {"post": {"tags": ["Function"], "summary": "给功能分配接口", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AssignInterfaceInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AssignInterfaceInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.AssignInterfaceInput"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/function/{id}/function-interface": {"delete": {"tags": ["Function"], "summary": "移除功能的接口", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/interface/paged-list": {"get": {"tags": ["Interface"], "summary": "分页查询", "parameters": [{"name": "GroupName", "in": "query", "schema": {"type": "string"}}, {"name": "Path", "in": "query", "description": "地址", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/interface/async-api": {"post": {"tags": ["Interface"], "summary": "同步接口", "responses": {"200": {"description": "Success"}}}}, "/api/v1/modbus-instruction-management/modbus-devices": {"get": {"tags": ["ModbusInstructionManagement"], "summary": "获取Modbus设备列表", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusDeviceOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusDeviceOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusDeviceOutput"}}}}}}}}, "/api/v1/modbus-instruction-management/{deviceId}/device-instructions": {"get": {"tags": ["ModbusInstructionManagement"], "summary": "获取设备的指令列表", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusInstructionOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusInstructionOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusInstructionOutput"}}}}}}}}, "/api/v1/modbus-instruction-management/instruction": {"put": {"tags": ["ModbusInstructionManagement"], "summary": "更新指令参数", "requestBody": {"description": "更新参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.UpdateModbusInstructionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.UpdateModbusInstructionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.UpdateModbusInstructionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusInstructionOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusInstructionOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ModbusServices.ModbusInstructionOutput"}}}}}}}, "/api/v1/modbus-instruction-management/{instructionId}/set-instruction-enabled": {"post": {"tags": ["ModbusInstructionManagement"], "summary": "启用/禁用指令", "parameters": [{"name": "instructionId", "in": "path", "description": "指令ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "enabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/modbus-instruction-management/{deviceId}/set-device-instructions-enabled": {"post": {"tags": ["ModbusInstructionManagement"], "summary": "批量启用/禁用设备的所有指令", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "enabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/notice/paged-list": {"get": {"tags": ["Notice"], "summary": "分页查询", "parameters": [{"name": "Title", "in": "query", "description": "标题", "schema": {"type": "string"}}, {"name": "NoticeType", "in": "query", "description": "类型", "schema": {"type": "integer", "format": "int64"}}, {"name": "Level", "in": "query", "description": "级别", "schema": {"type": "integer", "format": "int64"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.NoticeServices.Dtos.NoticeOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.NoticeServices.Dtos.NoticeOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.NoticeServices.Dtos.NoticeOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/notice/{id}": {"get": {"tags": ["Notice"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.NoticeOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.NoticeOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.NoticeOutput"}}}}}}, "put": {"tags": ["Notice"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.PutNoticeInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.PutNoticeInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.PutNoticeInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Notice"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/notice": {"post": {"tags": ["Notice"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.AddNoticeInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.AddNoticeInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.AddNoticeInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/notice/{id}/send": {"post": {"tags": ["Notice"], "summary": "发送通知", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/organization/paged-list": {"get": {"tags": ["Organization"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "组织机构名称", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.OrganizationServices.Dtos.OrganizationOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.OrganizationServices.Dtos.OrganizationOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.OrganizationServices.Dtos.OrganizationOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/organization/{id}": {"get": {"tags": ["Organization"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.OrganizationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.OrganizationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.OrganizationOutput"}}}}}}, "put": {"tags": ["Organization"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.AddOrganizationInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.AddOrganizationInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.AddOrganizationInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Organization"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/organization": {"post": {"tags": ["Organization"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.AddOrganizationInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.AddOrganizationInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.AddOrganizationInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/profile-system/paged-list": {"get": {"tags": ["ProfileSystem"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "配置名称", "schema": {"type": "string"}}, {"name": "Code", "in": "query", "description": "编码", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/profile-system/{id}": {"get": {"tags": ["ProfileSystem"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput"}}}}}}, "delete": {"tags": ["ProfileSystem"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/profile-system": {"post": {"tags": ["ProfileSystem"], "summary": "添加", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Code", "File", "Name"], "type": "object", "properties": {"Name": {"maxLength": 100, "type": "string", "description": "名称"}, "Code": {"maxLength": 100, "type": "string", "description": "编码"}, "File": {"type": "string", "description": "文件", "format": "binary"}, "Remark": {"maxLength": 1000, "type": "string", "description": "备注"}}}, "encoding": {"Name": {"style": "form"}, "Code": {"style": "form"}, "File": {"style": "form"}, "Remark": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/profile-system/{fileId}/download": {"get": {"tags": ["ProfileSystem"], "summary": "文件下载", "parameters": [{"name": "fileId", "in": "path", "description": "", "required": true, "schema": {"minimum": 1, "type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/request-log/paged-list": {"get": {"tags": ["RequestLog"], "summary": "分页查询", "parameters": [{"name": "RequestDate", "in": "query", "description": "日期", "required": true, "schema": {"type": "string"}}, {"name": "ControllerName", "in": "query", "description": "控制器名称", "schema": {"type": "string"}}, {"name": "ActionName", "in": "query", "description": "方法名", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.RequestLogServices.Dtos.RequestLogOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.RequestLogServices.Dtos.RequestLogOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.RequestLogServices.Dtos.RequestLogOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/request-log/request-log-chart": {"get": {"tags": ["RequestLog"], "summary": "按日期获取请求日志统计数", "parameters": [{"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Core.Echarts.ChartModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Core.Echarts.ChartModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Core.Echarts.ChartModel"}}}}}}}, "/api/v1/role/paged-list": {"get": {"tags": ["Role"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "角色名", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.RoleServices.Dtos.RoleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.RoleServices.Dtos.RoleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.RoleServices.Dtos.RoleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/role/roles": {"get": {"tags": ["Role"], "summary": "全量查询", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.RoleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.RoleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.RoleOutput"}}}}}}}}, "/api/v1/role/{id}": {"get": {"tags": ["Role"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.RoleOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.RoleOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.RoleOutput"}}}}}}, "put": {"tags": ["Role"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.AddRoleInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.AddRoleInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.AddRoleInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Role"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/role": {"post": {"tags": ["Role"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.AddRoleInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.AddRoleInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.AddRoleInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/role/{roleId}/assign-function": {"post": {"tags": ["Role"], "summary": "赋给角色功能", "parameters": [{"name": "roleId", "in": "path", "description": "角色Id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/role/{roleId}/functions": {"get": {"tags": ["Role"], "summary": "获取角色的功能", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.FunctionOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.FunctionOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.FunctionOutput"}}}}}}}}, "/api/v1/system-config/paged-list": {"get": {"tags": ["SystemConfig"], "summary": "分页查询", "parameters": [{"name": "Name", "in": "query", "description": "配置名称", "schema": {"type": "string"}}, {"name": "ConfigCode", "in": "query", "description": "配置编码", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/system-config/{id}": {"get": {"tags": ["SystemConfig"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput"}}}}}}, "put": {"tags": ["SystemConfig"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["SystemConfig"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/system-config": {"post": {"tags": ["SystemConfig"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/user/paged-list": {"get": {"tags": ["User"], "summary": "分页查询", "parameters": [{"name": "Account", "in": "query", "description": "账号", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "description": "用户名", "schema": {"type": "string"}}, {"name": "Telephone", "in": "query", "description": "电话", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "description": "邮箱", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "description": "账户状态", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.UserServices.Dtos.UserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.UserServices.Dtos.UserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.UserServices.Dtos.UserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/user/{id}": {"get": {"tags": ["User"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.UserOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.UserOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.UserOutput"}}}}}}, "put": {"tags": ["User"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.PutUserInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.PutUserInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.PutUserInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["User"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/user": {"post": {"tags": ["User"], "summary": "添加", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.AddUserInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.AddUserInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.AddUserInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/user/{id}/stop": {"post": {"tags": ["User"], "summary": "账户停用", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/user/{id}/normal": {"post": {"tags": ["User"], "summary": "账户恢复正常", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/user/{id}/reset-password": {"post": {"tags": ["User"], "summary": "重置密码", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}}, "components": {"schemas": {"Admin.Application.AuthServices.Dtos.BindUserInput": {"required": ["account", "connectionId", "oAuth2UserId", "password"], "type": "object", "properties": {"account": {"minLength": 3, "type": "string", "description": "账号"}, "password": {"minLength": 6, "type": "string", "description": "密码"}, "connectionId": {"minLength": 1, "type": "string", "description": "ConnectionId"}, "oAuth2UserId": {"type": "integer", "description": "OAuth2User表持久化Id", "format": "int64"}}, "additionalProperties": false}, "Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "parentId": {"type": "integer", "description": "父级Id", "format": "int64", "nullable": true}, "telephone": {"type": "string", "description": "联系电话", "nullable": true}, "leader": {"type": "string", "description": "负责人", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput"}, "description": "子集", "nullable": true}}, "additionalProperties": false, "description": "组织机构详情"}, "Admin.Application.AuthServices.Dtos.GetSystemPlatformInfoOutput": {"type": "object", "properties": {"frameworkDescription": {"type": "string", "nullable": true, "readOnly": true}, "osDescription": {"type": "string", "nullable": true, "readOnly": true}, "osVersion": {"type": "string", "nullable": true, "readOnly": true}, "osArchitecture": {"type": "string", "nullable": true, "readOnly": true}, "machineName": {"type": "string", "nullable": true, "readOnly": true}, "version": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false, "description": "系统信息"}, "Admin.Application.AuthServices.Dtos.GetUserInfoOutput": {"type": "object", "properties": {"password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "真实姓名", "nullable": true}, "telephone": {"type": "string", "description": "电话", "nullable": true}, "email": {"type": "string", "description": "邮箱", "nullable": true}}, "additionalProperties": false, "description": "用户信息输出"}, "Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput": {"type": "object", "properties": {"userId": {"type": "string", "description": "用户Id", "nullable": true}, "userName": {"type": "string", "description": "用户名（account）", "nullable": true}, "realName": {"type": "string", "description": "真实姓名", "nullable": true}, "avatar": {"type": "string", "description": "头像", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "description": "角色", "nullable": true}}, "additionalProperties": false, "description": "用户信息输出"}, "Admin.Application.AuthServices.Dtos.LoginInput": {"required": ["account", "password"], "type": "object", "properties": {"account": {"minLength": 3, "type": "string", "description": "账号"}, "password": {"minLength": 6, "type": "string", "description": "密码"}}, "additionalProperties": false, "description": "登录模型"}, "Admin.Application.AuthServices.Dtos.LoginOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户Id", "format": "int64"}, "name": {"type": "string", "description": "姓名", "nullable": true}}, "additionalProperties": false, "description": "登录模型"}, "Admin.Application.AuthServices.Dtos.PutUserInfoInput": {"type": "object", "properties": {"password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "真实姓名", "nullable": true}, "telephone": {"type": "string", "description": "电话", "nullable": true}, "email": {"type": "string", "description": "邮箱", "nullable": true}}, "additionalProperties": false, "description": "用户信息输出"}, "Admin.Application.AuthServices.Dtos.RegisterUserInput": {"required": ["account", "connectionId", "name", "oAuth2UserId", "password"], "type": "object", "properties": {"account": {"minLength": 3, "type": "string", "description": "账号"}, "password": {"minLength": 6, "type": "string", "description": "密码"}, "oAuth2UserId": {"type": "integer", "description": "OAuth2User表持久化Id", "format": "int64"}, "connectionId": {"minLength": 1, "type": "string", "description": "ConnectionId"}, "name": {"minLength": 1, "type": "string", "description": "用户名"}, "telephone": {"type": "string", "description": "电话", "nullable": true}, "email": {"type": "string", "description": "邮箱", "nullable": true}}, "additionalProperties": false, "description": "用户注册模型"}, "Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput": {"type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 20, "type": "string", "description": "分类名称", "nullable": true}, "code": {"maxLength": 20, "type": "string", "description": "分类编码", "nullable": true}}, "additionalProperties": false, "description": "字典分类添加"}, "Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "分类名称", "nullable": true}, "code": {"type": "string", "description": "分类编码", "nullable": true}}, "additionalProperties": false, "description": "字典分类详情"}, "Admin.Application.DictDataServices.Dtos.AddDictDataInput": {"required": ["categoryId", "sort"], "type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "categoryId": {"type": "integer", "description": "字典分类ID", "format": "int64"}, "name": {"maxLength": 20, "type": "string", "description": "字典名称", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32"}}, "additionalProperties": false, "description": "字典数据添加"}, "Admin.Application.DictDataServices.Dtos.DictDataOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "categoryId": {"type": "integer", "description": "字典分类ID", "format": "int64"}, "name": {"type": "string", "description": "字典名称", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32"}}, "additionalProperties": false, "description": "字典数据详情"}, "Admin.Application.FunctionServices.Dtos.AddFunctionInput": {"type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 20, "type": "string", "description": "名称", "nullable": true}, "code": {"maxLength": 40, "type": "string", "description": "编码", "nullable": true}, "parentId": {"type": "integer", "description": "父级Id", "format": "int64", "nullable": true}}, "additionalProperties": false, "description": "组织机构添加"}, "Admin.Application.FunctionServices.Dtos.AssignInterfaceInput": {"type": "object", "properties": {"functionId": {"type": "integer", "description": "功能Id", "format": "int64"}, "interfaceId": {"type": "integer", "description": "接口Id", "format": "int64"}}, "additionalProperties": false}, "Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id", "format": "int64"}, "name": {"type": "string", "description": "接口名称", "nullable": true}, "path": {"type": "string", "description": "接口地址", "nullable": true}, "interfaceId": {"type": "integer", "description": "接口Id", "format": "int64"}}, "additionalProperties": false}, "Admin.Application.FunctionServices.Dtos.FunctionOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "code": {"type": "string", "description": "编码", "nullable": true}, "parentId": {"type": "integer", "description": "父级Id", "format": "int64", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}, "description": "子集", "nullable": true}}, "additionalProperties": false, "description": "组织机构详情"}, "Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id", "format": "int64"}, "name": {"type": "string", "description": "名称", "nullable": true}, "code": {"type": "string", "description": "编码", "nullable": true}, "interfaces": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.InterfaceServices.Dtos.InterfaceOutput"}, "description": "接口详情", "nullable": true}}, "additionalProperties": false, "description": "接口组"}, "Admin.Application.InterfaceServices.Dtos.InterfaceOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "接口名称", "nullable": true}, "path": {"type": "string", "description": "接口地址", "nullable": true}, "requestMethod": {"type": "string", "description": "请求方法", "nullable": true}, "groupId": {"type": "integer", "description": "分组Id", "format": "int64"}}, "additionalProperties": false, "description": "接口详情"}, "Admin.Application.ModbusServices.ModbusDeviceOutput": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "deviceName": {"type": "string", "nullable": true}, "modbusAddr": {"type": "integer", "format": "int32"}, "deviceIdentityCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "modelName": {"type": "string", "nullable": true}, "protocolType": {"type": "integer", "format": "int32"}, "dataFormat": {"type": "integer", "format": "int32"}, "createTime": {"type": "string"}}, "additionalProperties": false, "description": "Modbus设备输出DTO"}, "Admin.Application.ModbusServices.ModbusInstructionOutput": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "deviceId": {"type": "integer", "format": "int64"}, "instructionName": {"type": "string", "nullable": true}, "send_str": {"type": "string", "nullable": true}, "encode": {"type": "integer", "format": "int32"}, "readInterval": {"type": "integer", "format": "int32"}, "responseTime": {"type": "integer", "format": "int32"}, "retryCount": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "createTime": {"type": "string"}}, "additionalProperties": false, "description": "Modbus指令输出DTO"}, "Admin.Application.ModbusServices.UpdateModbusInstructionInput": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "readInterval": {"type": "integer", "format": "int32", "nullable": true}, "responseTime": {"type": "integer", "format": "int32", "nullable": true}, "retryCount": {"type": "integer", "format": "int32", "nullable": true}, "isEnabled": {"type": "boolean", "nullable": true}}, "additionalProperties": false, "description": "更新Modbus指令输入DTO"}, "Admin.Application.NoticeServices.Dtos.AddNoticeInput": {"required": ["content", "title"], "type": "object", "properties": {"title": {"maxLength": 40, "minLength": 1, "type": "string", "description": "标题"}, "content": {"minLength": 1, "type": "string", "description": "内容"}, "noticeType": {"type": "integer", "description": "类型", "format": "int64"}, "level": {"type": "integer", "description": "级别", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false}, "Admin.Application.NoticeServices.Dtos.NoticeOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id", "format": "int64"}, "title": {"type": "string", "description": "标题", "nullable": true}, "content": {"type": "string", "description": "内容", "nullable": true}, "noticeType": {"type": "integer", "description": "类型", "format": "int64"}, "level": {"type": "integer", "description": "级别", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "noticeTypeString": {"type": "string", "description": "类型string", "nullable": true}, "levelString": {"type": "string", "description": "级别string", "nullable": true}}, "additionalProperties": false}, "Admin.Application.NoticeServices.Dtos.PutNoticeInput": {"required": ["content", "title"], "type": "object", "properties": {"title": {"maxLength": 40, "minLength": 1, "type": "string", "description": "标题"}, "content": {"minLength": 1, "type": "string", "description": "内容"}, "noticeType": {"type": "integer", "description": "类型", "format": "int64"}, "level": {"type": "integer", "description": "级别", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false}, "Admin.Application.OrganizationServices.Dtos.AddOrganizationInput": {"required": ["parentId", "sort"], "type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 100, "type": "string", "description": "名称", "nullable": true}, "parentId": {"type": "integer", "description": "父级Id", "format": "int64"}, "telephone": {"maxLength": 20, "type": "string", "description": "联系电话", "nullable": true}, "leader": {"maxLength": 20, "type": "string", "description": "负责人", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32"}}, "additionalProperties": false, "description": "组织机构添加"}, "Admin.Application.OrganizationServices.Dtos.OrganizationOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "parentId": {"type": "integer", "description": "父级Id", "format": "int64", "nullable": true}, "telephone": {"type": "string", "description": "联系电话", "nullable": true}, "leader": {"type": "string", "description": "负责人", "nullable": true}, "sort": {"type": "integer", "description": "排序", "format": "int32", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.OrganizationOutput"}, "description": "子集", "nullable": true}}, "additionalProperties": false, "description": "组织机构详情"}, "Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "code": {"type": "string", "description": "编码", "nullable": true}, "fileId": {"type": "integer", "description": "文件Id", "format": "int64"}, "fileName": {"type": "string", "description": "文件名称", "nullable": true}, "fileSize": {"type": "integer", "description": "文件大小", "format": "int32"}}, "additionalProperties": false, "description": "系统文件详情"}, "Admin.Application.RequestLogServices.Dtos.RequestLogOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id", "format": "int64"}, "controllerName": {"type": "string", "description": "控制器", "nullable": true}, "actionName": {"type": "string", "description": "方法名", "nullable": true}, "requestMethod": {"type": "string", "description": "请求类型", "nullable": true}, "environmentName": {"type": "string", "description": "服务器环境", "nullable": true}, "isSuccess": {"type": "boolean", "description": "完成情况"}, "elapsedTime": {"type": "integer", "description": "执行耗时", "format": "int64"}, "clientIp": {"type": "string", "description": "客户端IP", "nullable": true}, "createTime": {"type": "string", "description": "时间"}}, "additionalProperties": false}, "Admin.Application.RoleServices.Dtos.AddRoleInput": {"type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 20, "type": "string", "description": "角色名称", "nullable": true}, "description": {"maxLength": 200, "type": "string", "description": "角色描述", "nullable": true}, "securityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "权限Id集合", "nullable": true}}, "additionalProperties": false, "description": "角色添加"}, "Admin.Application.RoleServices.Dtos.FunctionOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "code": {"type": "string", "description": "编码", "nullable": true}, "parentId": {"type": "integer", "description": "父级Id", "format": "int64", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.FunctionOutput"}, "description": "子集", "nullable": true}}, "additionalProperties": false, "description": "组织机构详情"}, "Admin.Application.RoleServices.Dtos.RoleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "角色名称", "nullable": true}, "description": {"type": "string", "description": "角色描述", "nullable": true}, "websiteSecurityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "website权限", "nullable": true}, "interfaceSecurityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "interface权限", "nullable": true}}, "additionalProperties": false, "description": "角色详情"}, "Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput": {"type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 20, "type": "string", "description": "名称", "nullable": true}, "configCode": {"maxLength": 40, "type": "string", "description": "编码", "nullable": true}, "configValue": {"maxLength": 1000, "type": "string", "description": "值", "nullable": true}}, "additionalProperties": false, "description": "系统配置表添加"}, "Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "name": {"type": "string", "description": "名称", "nullable": true}, "configCode": {"type": "string", "description": "编码", "nullable": true}, "configValue": {"type": "string", "description": "值", "nullable": true}}, "additionalProperties": false, "description": "系统配置表详情"}, "Admin.Application.UserServices.Dtos.AddUserInput": {"required": ["account", "organizationId", "roleId"], "type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "account": {"maxLength": 36, "minLength": 1, "type": "string", "description": "账号"}, "password": {"maxLength": 36, "type": "string", "description": "密码", "nullable": true}, "name": {"maxLength": 20, "type": "string", "description": "用户名", "nullable": true}, "telephone": {"maxLength": 11, "type": "string", "description": "电话", "nullable": true}, "email": {"maxLength": 20, "type": "string", "description": "邮箱", "nullable": true}, "avatar": {"type": "string", "description": "头像", "format": "byte", "nullable": true}, "organizationId": {"type": "integer", "description": "组织机构Id", "format": "int64"}, "roleId": {"type": "integer", "description": "角色Id", "format": "int64"}}, "additionalProperties": false, "description": "用户添加"}, "Admin.Application.UserServices.Dtos.PutUserInput": {"required": ["organizationId", "roleId"], "type": "object", "properties": {"remark": {"maxLength": 1000, "type": "string", "description": "备注", "nullable": true}, "name": {"maxLength": 20, "type": "string", "description": "用户名", "nullable": true}, "telephone": {"maxLength": 11, "type": "string", "description": "电话", "nullable": true}, "email": {"maxLength": 20, "type": "string", "description": "邮箱", "nullable": true}, "avatar": {"type": "string", "description": "头像", "format": "byte", "nullable": true}, "organizationId": {"type": "integer", "description": "组织机构Id", "format": "int64"}, "roleId": {"type": "integer", "description": "角色Id", "format": "int64"}}, "additionalProperties": false, "description": "用户编辑"}, "Admin.Application.UserServices.Dtos.UserOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "remark": {"type": "string", "description": "备注", "nullable": true}, "account": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "真实姓名", "nullable": true}, "telephone": {"type": "string", "description": "电话", "nullable": true}, "email": {"type": "string", "description": "邮箱", "nullable": true}, "avatar": {"type": "string", "description": "头像", "format": "byte", "nullable": true}, "organizationId": {"type": "integer", "description": "组织机构Id", "format": "int64"}, "organizationName": {"type": "string", "description": "组织机构名称", "nullable": true}, "roleId": {"type": "integer", "description": "角色Id", "format": "int64"}, "roleName": {"type": "string", "description": "角色名称", "nullable": true}, "status": {"type": "integer", "description": "用户状态", "format": "int32"}}, "additionalProperties": false, "description": "用户详情"}, "Admin.Core.Echarts.ChartModel": {"type": "object", "properties": {"legend": {"$ref": "#/components/schemas/Admin.Core.Echarts.Legend"}, "xAxis": {"$ref": "#/components/schemas/Admin.Core.Echarts.XAxis"}, "series": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Core.Echarts.Series"}, "nullable": true}}, "additionalProperties": false}, "Admin.Core.Echarts.Legend": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Admin.Core.Echarts.Series": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"type": "number", "format": "double"}, "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Admin.Core.Echarts.XAxis": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Admin.Multiplex.Contracts.IAdminUser.Models.NoticeItemModel": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "dateTime": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "extra": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.DictDataServices.Dtos.DictDataOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.DictDataServices.Dtos.DictDataOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.FunctionServices.Dtos.FunctionOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.FunctionServices.Dtos.FunctionOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.NoticeServices.Dtos.NoticeOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.NoticeServices.Dtos.NoticeOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.OrganizationServices.Dtos.OrganizationOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.OrganizationServices.Dtos.OrganizationOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.RequestLogServices.Dtos.RequestLogOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RequestLogServices.Dtos.RequestLogOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.RoleServices.Dtos.RoleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.RoleServices.Dtos.RoleOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.UserServices.Dtos.UserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.UserServices.Dtos.UserOutput"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "User", "description": "用户服务"}, {"name": "SystemConfig", "description": "系统配置表服务"}, {"name": "Role", "description": "角色服务"}, {"name": "RequestLog", "description": "请求日志服务"}, {"name": "ProfileSystem", "description": "系统文件服务"}, {"name": "Organization", "description": "组织机构服务"}, {"name": "Notice", "description": "通知公告"}, {"name": "ModbusInstructionManagement", "description": "Modbus指令管理服务\r\n提供Modbus指令调度的管理和监控功能"}, {"name": "Interface", "description": "接口服务"}, {"name": "Function", "description": "功能服务"}, {"name": "DictData", "description": "字典数据服务"}, {"name": "DictCategory", "description": "字典分类服务"}, {"name": "<PERSON><PERSON>", "description": "用户授权服务"}, {"name": "User", "description": "用户服务"}, {"name": "SystemConfig", "description": "系统配置表服务"}, {"name": "Role", "description": "角色服务"}, {"name": "RequestLog", "description": "请求日志服务"}, {"name": "ProfileSystem", "description": "系统文件服务"}, {"name": "Organization", "description": "组织机构服务"}, {"name": "Notice", "description": "通知公告"}, {"name": "ModbusInstructionManagement", "description": "Modbus指令管理服务\r\n提供Modbus指令调度的管理和监控功能"}, {"name": "Interface", "description": "接口服务"}, {"name": "Function", "description": "功能服务"}, {"name": "DictData", "description": "字典数据服务"}, {"name": "DictCategory", "description": "字典分类服务"}, {"name": "<PERSON><PERSON>", "description": "用户授权服务"}, {"name": "User", "description": "用户服务"}, {"name": "SystemConfig", "description": "系统配置表服务"}, {"name": "Role", "description": "角色服务"}, {"name": "RequestLog", "description": "请求日志服务"}, {"name": "ProfileSystem", "description": "系统文件服务"}, {"name": "Organization", "description": "组织机构服务"}, {"name": "Notice", "description": "通知公告"}, {"name": "ModbusInstructionManagement", "description": "Modbus指令管理服务\r\n提供Modbus指令调度的管理和监控功能"}, {"name": "Interface", "description": "接口服务"}, {"name": "Function", "description": "功能服务"}, {"name": "DictData", "description": "字典数据服务"}, {"name": "DictCategory", "description": "字典分类服务"}, {"name": "<PERSON><PERSON>", "description": "用户授权服务"}]}