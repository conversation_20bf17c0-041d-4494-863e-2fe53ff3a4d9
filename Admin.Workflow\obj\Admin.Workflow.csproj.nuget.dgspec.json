{"format": 1, "restore": {"D:\\code projects\\purest-admin-main\\api\\Admin.Workflow\\Admin.Workflow.csproj": {}}, "projects": {"D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj", "projectName": "Admin.Core", "projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj": {"projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Flurl.Http": {"target": "Package", "version": "[4.0.0, )"}, "IP2Region.Net": {"target": "Package", "version": "[2.0.2, )"}, "Mapster": {"target": "Package", "version": "[7.4.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.7, )"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": {"target": "Package", "version": "[8.0.7, )"}, "Namotion.Reflection": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.2, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[2.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Volo.Abp.AspNetCore.SignalR": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.BackgroundJobs": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.BackgroundWorkers": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.BlobStoring.FileSystem": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.Swashbuckle": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\code projects\\purest-admin-main\\api\\Admin.Multiplex.Contracts\\Admin.Multiplex.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code projects\\purest-admin-main\\api\\Admin.Multiplex.Contracts\\Admin.Multiplex.Contracts.csproj", "projectName": "Admin.Multiplex.Contracts", "projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Multiplex.Contracts\\Admin.Multiplex.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Multiplex.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj": {"projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj", "projectName": "Admin.SqlSugar", "projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Serilog": {"target": "Package", "version": "[4.0.1, )"}, "SqlSugarCore": {"target": "Package", "version": "[5.1.4.166, )"}, "Volo.Abp": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.2.1, )"}, "Volo.Abp.Timing": {"target": "Package", "version": "[8.2.1, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\code projects\\purest-admin-main\\api\\Admin.Workflow\\Admin.Workflow.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code projects\\purest-admin-main\\api\\Admin.Workflow\\Admin.Workflow.csproj", "projectName": "Admin.Workflow", "projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Workflow\\Admin.Workflow.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Workflow\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj": {"projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Core\\Admin.Core.csproj"}, "D:\\code projects\\purest-admin-main\\api\\Admin.Multiplex.Contracts\\Admin.Multiplex.Contracts.csproj": {"projectPath": "D:\\code projects\\purest-admin-main\\api\\Admin.Multiplex.Contracts\\Admin.Multiplex.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"WorkflowCore": {"target": "Package", "version": "[3.10.0, )"}, "WorkflowCore.DSL": {"target": "Package", "version": "[3.10.0, )"}, "WorkflowCore.Persistence.MySQL": {"target": "Package", "version": "[3.10.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}