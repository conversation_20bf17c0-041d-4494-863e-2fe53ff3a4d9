using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Protocol;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.Handlers
{
    /// <summary>
    /// MQTT消息处理器基类
    /// </summary>
    public abstract class BaseMessageHandler : IMqttMessageHandler, IDisposable
    {
        protected readonly ILogger _logger;
        private bool _disposed = false;
        private readonly CircuitBreaker _circuitBreaker;

        /// <summary>
        /// 处理器名称
        /// </summary>
        public abstract string Name { get; }

        /// <summary>
        /// 处理器描述
        /// </summary>
        public abstract string Description { get; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public virtual bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 支持的主题模式（支持通配符）
        /// </summary>
        public virtual string[] SupportedTopicPatterns { get; protected set; } = new[] { "#" };

        /// <summary>
        /// 支持的消息类型
        /// </summary>
        public virtual Type[] SupportedMessageTypes { get; protected set; } = new[] { typeof(MqttMessage) };

        /// <summary>
        /// 处理器优先级（数值越小优先级越高）
        /// </summary>
        public virtual int Priority { get; protected set; } = 100;

        /// <summary>
        /// 处理统计信息
        /// </summary>
        public MqttMessageHandlerStatistics Statistics { get; private set; } = new MqttMessageHandlerStatistics();

        /// <summary>
        /// 熔断器配置
        /// </summary>
        protected virtual CircuitBreakerOptions CircuitBreakerOptions => new CircuitBreakerOptions
        {
            FailureThreshold = 5,
            TimeoutMilliseconds = 30000,
            RecoveryTimeMilliseconds = 60000
        };

        /// <summary>
        /// 重试配置
        /// </summary>
        protected virtual RetryOptions RetryOptions => new RetryOptions
        {
            MaxRetryAttempts = 3,
            DelayMilliseconds = 1000,
            BackoffMultiplier = 2.0
        };

        /// <summary>
        /// 初始化基础消息处理器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        protected BaseMessageHandler(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _circuitBreaker = new CircuitBreaker(CircuitBreakerOptions, _logger);
        }

        /// <summary>
        /// 判断是否可以处理指定的消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>如果可以处理返回true，否则返回false</returns>
        public virtual bool CanHandle(MqttMessage message, MqttMessageContext context)
        {
            if (!IsEnabled)
            {
                return false;
            }

            if (message == null || context == null)
            {
                return false;
            }

            // 检查消息类型
            if (!IsMessageTypeSupported(message))
            {
                return false;
            }

            // 检查主题模式（仅对发布消息检查主题）
            if (message is MqttPublishMessage publishMessage)
            {
                return IsTopicSupported(publishMessage.Topic);
            }

            return true;
        }

        /// <summary>
        /// 异步处理消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>处理结果</returns>
        public async Task<MqttMessageHandleResult> HandleAsync(MqttMessage message, MqttMessageContext context)
        {
            if (_disposed)
            {
                return MqttMessageHandleResult.Failure("处理器已被销毁");
            }

            // 检查熔断器状态
            if (!_circuitBreaker.CanExecute())
            {
                return MqttMessageHandleResult.Failure("处理器熔断器已打开，暂时无法处理消息");
            }

            var stopwatch = Stopwatch.StartNew();
            var result = new MqttMessageHandleResult();
            
            try
            {
                Statistics.IncrementProcessed();
                
                _logger.LogDebug("开始处理消息: {MessageType}, 处理器: {HandlerName}", 
                    message.GetType().Name, Name);

                // 使用重试机制执行处理逻辑
                result = await ExecuteWithRetryAsync(async () =>
                {
                    // 验证消息和上下文
                    var validationResult = await ValidateMessageAsync(message, context);
                    if (!validationResult.IsSuccess)
                    {
                        return validationResult;
                    }

                    // 预处理
                    var preProcessResult = await PreProcessAsync(message, context);
                    if (!preProcessResult.IsSuccess)
                    {
                        return preProcessResult;
                    }

                    // 核心处理逻辑
                    var processResult = await ProcessMessageAsync(message, context);
                    
                    // 后处理
                    if (processResult.IsSuccess)
                    {
                        var postProcessResult = await PostProcessAsync(message, context, processResult);
                        if (!postProcessResult.IsSuccess)
                        {
                            return postProcessResult;
                        }
                    }

                    return processResult;
                });

                if (result.IsSuccess)
                {
                    Statistics.IncrementSuccessful();
                    _circuitBreaker.RecordSuccess();
                    _logger.LogDebug("消息处理成功: {MessageType}, 处理器: {HandlerName}, 耗时: {ElapsedMs}ms", 
                        message.GetType().Name, Name, stopwatch.ElapsedMilliseconds);
                }
                else
                {
                    Statistics.IncrementFailed();
                    _circuitBreaker.RecordFailure();
                    _logger.LogWarning("消息处理失败: {MessageType}, 处理器: {HandlerName}, 错误: {Error}", 
                        message.GetType().Name, Name, result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                Statistics.IncrementFailed();
                _circuitBreaker.RecordFailure();
                _logger.LogError(ex, "处理消息时发生异常: {MessageType}, 处理器: {HandlerName}", 
                    message.GetType().Name, Name);
                
                result = MqttMessageHandleResult.Failure($"处理消息时发生异常: {ex.Message}", ex);
            }
            finally
            {
                stopwatch.Stop();
                result.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                Statistics.UpdateAverageProcessingTime(stopwatch.ElapsedMilliseconds);
            }

            return result;
        }

        /// <summary>
        /// 初始化处理器
        /// </summary>
        /// <returns>初始化任务</returns>
        public virtual async Task InitializeAsync()
        {
            _logger.LogInformation("初始化消息处理器: {HandlerName}", Name);
            
            try
            {
                await OnInitializeAsync();
                _logger.LogInformation("消息处理器初始化完成: {HandlerName}", Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化消息处理器失败: {HandlerName}", Name);
                throw;
            }
        }

        /// <summary>
        /// 销毁处理器
        /// </summary>
        /// <returns>销毁任务</returns>
        public virtual async Task DisposeAsync()
        {
            if (_disposed)
            {
                return;
            }

            _logger.LogInformation("销毁消息处理器: {HandlerName}", Name);
            
            try
            {
                await OnDisposeAsync();
                _disposed = true;
                _logger.LogInformation("消息处理器销毁完成: {HandlerName}", Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "销毁消息处理器失败: {HandlerName}", Name);
                throw;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                DisposeAsync().GetAwaiter().GetResult();
            }
        }

        #region 抽象和虚拟方法

        /// <summary>
        /// 处理消息的核心逻辑（子类必须实现）
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>处理结果</returns>
        protected abstract Task<MqttMessageHandleResult> ProcessMessageAsync(MqttMessage message, MqttMessageContext context);

        /// <summary>
        /// 初始化时的自定义逻辑
        /// </summary>
        /// <returns>初始化任务</returns>
        protected virtual Task OnInitializeAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 销毁时的自定义逻辑
        /// </summary>
        /// <returns>销毁任务</returns>
        protected virtual Task OnDisposeAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 验证消息和上下文
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>验证结果</returns>
        protected virtual Task<MqttMessageHandleResult> ValidateMessageAsync(MqttMessage message, MqttMessageContext context)
        {
            if (message == null)
            {
                return Task.FromResult(MqttMessageHandleResult.Failure("消息不能为空"));
            }

            if (context == null)
            {
                return Task.FromResult(MqttMessageHandleResult.Failure("消息上下文不能为空"));
            }

            return Task.FromResult(MqttMessageHandleResult.Success());
        }

        /// <summary>
        /// 预处理逻辑
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>预处理结果</returns>
        protected virtual Task<MqttMessageHandleResult> PreProcessAsync(MqttMessage message, MqttMessageContext context)
        {
            return Task.FromResult(MqttMessageHandleResult.Success());
        }

        /// <summary>
        /// 后处理逻辑
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <param name="result">处理结果</param>
        /// <returns>后处理结果</returns>
        protected virtual Task<MqttMessageHandleResult> PostProcessAsync(MqttMessage message, MqttMessageContext context, MqttMessageHandleResult result)
        {
            return Task.FromResult(MqttMessageHandleResult.Success());
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查消息类型是否受支持
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <returns>如果支持返回true，否则返回false</returns>
        protected virtual bool IsMessageTypeSupported(MqttMessage message)
        {
            if (SupportedMessageTypes == null || SupportedMessageTypes.Length == 0)
            {
                return true;
            }

            var messageType = message.GetType();
            return SupportedMessageTypes.Any(supportedType => 
                supportedType.IsAssignableFrom(messageType));
        }

        /// <summary>
        /// 检查主题是否受支持
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>如果支持返回true，否则返回false</returns>
        protected virtual bool IsTopicSupported(string topic)
        {
            if (string.IsNullOrEmpty(topic))
            {
                return false;
            }

            if (SupportedTopicPatterns == null || SupportedTopicPatterns.Length == 0)
            {
                return true;
            }

            return SupportedTopicPatterns.Any(pattern => MqttTopicMatcher.IsMatch(topic, pattern));
        }

        /// <summary>
        /// 创建响应消息
        /// </summary>
        /// <param name="originalMessage">原始消息</param>
        /// <param name="context">消息上下文</param>
        /// <param name="responsePayload">响应载荷</param>
        /// <param name="responseTopic">响应主题（可选，默认使用上下文中的响应主题）</param>
        /// <returns>响应消息</returns>
        protected virtual MqttPublishMessage CreateResponseMessage(MqttMessage originalMessage, MqttMessageContext context, byte[] responsePayload, string responseTopic = null)
        {
            var topic = responseTopic;
            if (string.IsNullOrEmpty(topic) && context.Properties.TryGetValue("ResponseTopic", out var responseTopicObj))
            {
                topic = responseTopicObj?.ToString();
            }
            
            if (string.IsNullOrEmpty(topic))
            {
                return null;
            }

            var responseMessage = new MqttPublishMessage
            {
                Topic = topic,
                Payload = responsePayload,
                QualityOfService = 0, // 响应消息通常使用QoS 0
                Retain = false,
                ProtocolVersion = originalMessage.ProtocolVersion
            };

            return responseMessage;
        }

        /// <summary>
        /// 记录处理统计信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="context">上下文</param>
        /// <param name="result">处理结果</param>
        protected virtual void LogProcessingStatistics(MqttMessage message, MqttMessageContext context, MqttMessageHandleResult result)
        {
            _logger.LogDebug("处理统计 - 处理器: {HandlerName}, 消息类型: {MessageType}, " +
                           "成功: {IsSuccess}, 耗时: {ElapsedMs}ms, " +
                           "总处理数: {TotalProcessed}, 成功率: {SuccessRate:P2}",
                Name, message.GetType().Name, result.IsSuccess, result.ElapsedMilliseconds,
                Statistics.TotalProcessed, Statistics.SuccessRate);
        }

        /// <summary>
        /// 使用重试机制执行操作
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <returns>操作结果</returns>
        protected virtual async Task<MqttMessageHandleResult> ExecuteWithRetryAsync(Func<Task<MqttMessageHandleResult>> operation)
        {
            var retryCount = 0;
            var delay = RetryOptions.DelayMilliseconds;

            while (retryCount <= RetryOptions.MaxRetryAttempts)
            {
                try
                {
                    var result = await operation();
                    
                    // 只有在失败且可重试的情况下才重试
                    if (result.IsSuccess || !IsRetryableError(result))
                    {
                        return result;
                    }

                    if (retryCount < RetryOptions.MaxRetryAttempts)
                    {
                        _logger.LogWarning("操作失败，将在 {Delay}ms 后重试 (第 {RetryCount}/{MaxRetries} 次): {Error}", 
                            delay, retryCount + 1, RetryOptions.MaxRetryAttempts, result.ErrorMessage);
                        
                        await Task.Delay(delay);
                        delay = (int)(delay * RetryOptions.BackoffMultiplier);
                        retryCount++;
                    }
                    else
                    {
                        _logger.LogError("操作失败，已达到最大重试次数: {Error}", result.ErrorMessage);
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    if (retryCount < RetryOptions.MaxRetryAttempts && IsRetryableException(ex))
                    {
                        _logger.LogWarning(ex, "操作异常，将在 {Delay}ms 后重试 (第 {RetryCount}/{MaxRetries} 次)", 
                            delay, retryCount + 1, RetryOptions.MaxRetryAttempts);
                        
                        await Task.Delay(delay);
                        delay = (int)(delay * RetryOptions.BackoffMultiplier);
                        retryCount++;
                    }
                    else
                    {
                        _logger.LogError(ex, "操作异常，已达到最大重试次数或异常不可重试");
                        return MqttMessageHandleResult.Failure($"操作异常: {ex.Message}", ex);
                    }
                }
            }

            return MqttMessageHandleResult.Failure("达到最大重试次数");
        }

        /// <summary>
        /// 判断错误是否可重试
        /// </summary>
        /// <param name="result">处理结果</param>
        /// <returns>是否可重试</returns>
        protected virtual bool IsRetryableError(MqttMessageHandleResult result)
        {
            // 默认情况下，只有临时性错误才重试
            // 子类可以重写此方法以自定义重试逻辑
            return result.Exception != null && IsRetryableException(result.Exception);
        }

        /// <summary>
        /// 判断异常是否可重试
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>是否可重试</returns>
        protected virtual bool IsRetryableException(Exception exception)
        {
            // 网络相关异常通常可以重试
            return exception is System.Net.NetworkInformation.NetworkInformationException ||
                   exception is System.Net.Sockets.SocketException ||
                   exception is TimeoutException ||
                   exception is TaskCanceledException;
        }

        #endregion
    }

    /// <summary>
    /// 消息处理器统计信息
    /// </summary>
    public class MqttMessageHandlerStatistics
    {
        private readonly object _lock = new object();
        private long _totalProcessed = 0;
        private long _totalSuccessful = 0;
        private long _totalFailed = 0;
        private long _totalProcessingTime = 0;

        /// <summary>
        /// 总处理数
        /// </summary>
        public long TotalProcessed => _totalProcessed;

        /// <summary>
        /// 总成功数
        /// </summary>
        public long TotalSuccessful => _totalSuccessful;

        /// <summary>
        /// 总失败数
        /// </summary>
        public long TotalFailed => _totalFailed;

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => _totalProcessed > 0 ? (double)_totalSuccessful / _totalProcessed : 0;

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTime => _totalProcessed > 0 ? (double)_totalProcessingTime / _totalProcessed : 0;

        /// <summary>
        /// 增加处理数
        /// </summary>
        public void IncrementProcessed()
        {
            lock (_lock)
            {
                _totalProcessed++;
            }
        }

        /// <summary>
        /// 增加成功数
        /// </summary>
        public void IncrementSuccessful()
        {
            lock (_lock)
            {
                _totalSuccessful++;
            }
        }

        /// <summary>
        /// 增加失败数
        /// </summary>
        public void IncrementFailed()
        {
            lock (_lock)
            {
                _totalFailed++;
            }
        }

        /// <summary>
        /// 更新平均处理时间
        /// </summary>
        /// <param name="processingTime">处理时间（毫秒）</param>
        public void UpdateAverageProcessingTime(long processingTime)
        {
            lock (_lock)
            {
                _totalProcessingTime += processingTime;
            }
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            lock (_lock)
            {
                _totalProcessed = 0;
                _totalSuccessful = 0;
                _totalFailed = 0;
                _totalProcessingTime = 0;
            }
        }
    }

    /// <summary>
    /// 熔断器配置选项
    /// </summary>
    public class CircuitBreakerOptions
    {
        /// <summary>
        /// 失败阈值
        /// </summary>
        public int FailureThreshold { get; set; } = 5;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int TimeoutMilliseconds { get; set; } = 30000;

        /// <summary>
        /// 恢复时间（毫秒）
        /// </summary>
        public int RecoveryTimeMilliseconds { get; set; } = 60000;
    }

    /// <summary>
    /// 重试配置选项
    /// </summary>
    public class RetryOptions
    {
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// 延迟时间（毫秒）
        /// </summary>
        public int DelayMilliseconds { get; set; } = 1000;

        /// <summary>
        /// 退避倍数
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;
    }

    /// <summary>
    /// 熔断器实现
    /// </summary>
    public class CircuitBreaker
    {
        private readonly CircuitBreakerOptions _options;
        private readonly ILogger _logger;
        private readonly object _lock = new object();
        
        private CircuitBreakerState _state = CircuitBreakerState.Closed;
        private int _failureCount = 0;
        private DateTime _lastFailureTime = DateTime.MinValue;
        private DateTime _nextAttemptTime = DateTime.MinValue;

        public CircuitBreaker(CircuitBreakerOptions options, ILogger logger)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool CanExecute()
        {
            lock (_lock)
            {
                switch (_state)
                {
                    case CircuitBreakerState.Closed:
                        return true;
                    
                    case CircuitBreakerState.Open:
                        if (DateTime.Now >= _nextAttemptTime)
                        {
                            _state = CircuitBreakerState.HalfOpen;
                            _logger.LogInformation("熔断器从打开状态转换为半开状态");
                            return true;
                        }
                        return false;
                    
                    case CircuitBreakerState.HalfOpen:
                        return true;
                    
                    default:
                        return false;
                }
            }
        }

        public void RecordSuccess()
        {
            lock (_lock)
            {
                if (_state == CircuitBreakerState.HalfOpen)
                {
                    _state = CircuitBreakerState.Closed;
                    _failureCount = 0;
                    _logger.LogInformation("熔断器从半开状态转换为关闭状态");
                }
                else if (_state == CircuitBreakerState.Closed)
                {
                    _failureCount = 0;
                }
            }
        }

        public void RecordFailure()
        {
            lock (_lock)
            {
                _failureCount++;
                _lastFailureTime = DateTime.Now;

                if (_state == CircuitBreakerState.HalfOpen)
                {
                    _state = CircuitBreakerState.Open;
                    _nextAttemptTime = DateTime.Now.AddMilliseconds(_options.RecoveryTimeMilliseconds);
                    _logger.LogWarning("熔断器从半开状态转换为打开状态");
                }
                else if (_state == CircuitBreakerState.Closed && _failureCount >= _options.FailureThreshold)
                {
                    _state = CircuitBreakerState.Open;
                    _nextAttemptTime = DateTime.Now.AddMilliseconds(_options.RecoveryTimeMilliseconds);
                    _logger.LogWarning("熔断器从关闭状态转换为打开状态，失败次数: {FailureCount}", _failureCount);
                }
            }
        }
    }

    /// <summary>
    /// 熔断器状态
    /// </summary>
    public enum CircuitBreakerState
    {
        /// <summary>
        /// 关闭状态（正常）
        /// </summary>
        Closed,
        
        /// <summary>
        /// 打开状态（熔断）
        /// </summary>
        Open,
        
        /// <summary>
        /// 半开状态（尝试恢复）
        /// </summary>
        HalfOpen
    }
} 