﻿using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.Communication.Modbus;

/// <summary>
/// Modbus通信计算函数工具类
/// 提供各种数据转换、校验和计算、CRC校验等功能
/// </summary>
public class CCalFunction
{
    /// <summary>
    /// 将ASCII字符数组转换为数值（APC格式）
    /// </summary>
    /// <param name="aSource">源字节数组</param>
    /// <param name="mvarStartPos">起始位置</param>
    /// <param name="mvarLen">长度</param>
    /// <returns>转换后的十进制数值</returns>
    public static decimal Apc_Asc2value_2(byte[] aSource, short mvarStartPos, short mvarLen)
    {
        string value = Conversions.ToString(Strings.Chr(aSource[0]));
        byte b = Conversions.ToByte(value);
        string value2 = Conversions.ToString(Strings.Chr(aSource[1]));
        byte b2 = Conversions.ToByte(value2);
        string value3 = Conversions.ToString(Strings.Chr(aSource[2]));
        byte b3 = Conversions.ToByte(value3);
        string value4 = Conversions.ToString(Strings.Chr(aSource[3]));
        byte b4 = Conversions.ToByte(value4);
        return new decimal(checked(b * 1000 + b2 * 100 + b3 * 10 + b4));
    }

    /// <summary>
    /// 将字节数组转换为十六进制字符串
    /// </summary>
    /// <param name="asource">源字节数组</param>
    /// <returns>十六进制字符串</returns>
    public static string arr2HexStr(byte[] asource)
    {
        string text = "";
        checked
        {
            short num = (short)asource.Length;
            if (num < 1)
            {
                return "";
            }
            text = "";
            short num2 = (short)(asource.Length - 1);
            for (num = 0; num <= num2; num = (short)unchecked(num + 1))
            {
                text = ((asource[num] <= 15) ? (text + "0" + Conversion.Hex(asource[num])) : (text + Conversion.Hex(asource[num])));
            }
            return text;
        }
    }

    /// <summary>
    /// 将ASCII十六进制字符串转换为二进制字符串
    /// </summary>
    /// <param name="sAsc">ASCII十六进制字符串</param>
    /// <returns>二进制字符串</returns>
    public static string asc2bin(string sAsc)
    {
        string text = "";
        checked
        {
            short num = (short)(sAsc.Length - 1);
            short num2 = num;
            for (short num3 = 0; num3 <= num2; num3 = (short)unchecked(num3 + 1))
            {
                switch (sAsc.Substring(num3, 1))
                {
                    case "0":
                        text += "0000";
                        break;
                    case "1":
                        text += "0001";
                        break;
                    case "2":
                        text += "0010";
                        break;
                    case "3":
                        text += "0011";
                        break;
                    case "4":
                        text += "0100";
                        break;
                    case "5":
                        text += "0101";
                        break;
                    case "6":
                        text += "0110";
                        break;
                    case "7":
                        text += "0111";
                        break;
                    case "8":
                        text += "1000";
                        break;
                    case "9":
                        text += "1001";
                        break;
                    case "A":
                        text += "1010";
                        break;
                    case "B":
                        text += "1011";
                        break;
                    case "C":
                        text += "1100";
                        break;
                    case "D":
                        text += "1101";
                        break;
                    case "E":
                        text += "1110";
                        break;
                    case "F":
                        text += "1111";
                        break;
                }
            }
            return text;
        }
    }

    /// <summary>
    /// 将ASCII十六进制字符串转换为字节数组
    /// </summary>
    /// <param name="sSource">ASCII十六进制字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] asc2byte(string sSource)
    {
        checked
        {
            short num = (short)sSource.Length;
            if (unchecked(num % 2) != 0)
            {
                num--;
            }
            if (num != 0)
            {
                byte[] array = new byte[(int)Math.Round((double)num / 2.0 - 1.0) + 1];
                num = (short)Math.Round((double)num / 2.0);
                short num2 = (short)(num - 1);
                for (short num3 = 0; num3 <= num2; num3 = (short)unchecked(num3 + 1))
                {
                    string s = sSource.Substring(2 * num3, 1);
                    string s2 = sSource.Substring(2 * num3 + 1, 1);
                    byte b = oneChar2Byte(s);
                    byte b2 = oneChar2Byte(s2);
                    short num4 = (short)(b * 16 + b2);
                    array[num3] = (byte)num4;
                }
                return array;
            }
            byte[] result = default(byte[]);
            return result;
        }
    }

    /// <summary>
    /// 将ASCII十六进制值字符串转换为ASCII字符串
    /// </summary>
    /// <param name="s">ASCII十六进制值字符串</param>
    /// <returns>ASCII字符串</returns>
    public static string AscHexValueStr2AscStr(string s)
    {
        string text = "";
        checked
        {
            int num = s.Length - 1;
            for (int i = 0; i <= num; i += 2)
            {
                byte charCode = TwoChar2Byte(s.Substring(i, 2));
                text += Conversions.ToString(Strings.Chr(charCode));
            }
            return text;
        }
    }

    /// <summary>
    /// 将二进制字符串转换为十进制整数
    /// </summary>
    /// <param name="Sbin">二进制字符串</param>
    /// <returns>十进制整数</returns>
    public static int BinToDe(string Sbin)
    {
        checked
        {
            short num = (short)Strings.Len(Sbin);
            int num2 = num;
            int num3 = default(int);
            for (int i = 1; i <= num2; i++)
            {
                if (Operators.CompareString(Sbin.Substring(num - i, 1), "1", TextCompare: false) == 0)
                {
                    num3 = (int)Math.Round((double)num3 + Math.Pow(2.0, i - 1));
                }
            }
            return num3;
        }
    }

    /// <summary>
    /// 将二进制字符串转换为长整型十进制数
    /// </summary>
    /// <param name="Bin">二进制字符串</param>
    /// <returns>长整型十进制数</returns>
    public static long BinToDecimal(string Bin)
    {
        long num = Strings.Len(Bin);
        checked
        {
            decimal num3 = default(decimal);
            for (long num2 = 1L; num2 <= num; num2++)
            {
                num3 = new decimal(Convert.ToDouble(decimal.Multiply(2m, num3)) + Conversion.Val(Strings.Mid(Bin, (int)num2, 1)));
            }
            return Convert.ToInt64(num3);
        }
    }

    /// <summary>
    /// 将二进制字符串转换为整型数值
    /// </summary>
    /// <param name="sbin">二进制字符串</param>
    /// <returns>整型数值</returns>
    public static int BinToInt(string sbin)
    {
        checked
        {
            short num = (short)Strings.Len(sbin);
            int num2 = num;
            int num3 = default(int);
            for (int i = 1; i <= num2; i++)
            {
                if (Operators.CompareString(sbin.Substring(num - i, 1), "1", TextCompare: false) == 0)
                {
                    num3 = (int)Math.Round((double)num3 + Math.Pow(2.0, i - 1));
                }
            }
            return num3;
        }
    }

    /// <summary>
    /// 将二进制字符串转换为无符号长整型数值
    /// </summary>
    /// <param name="sbin">二进制字符串</param>
    /// <returns>无符号长整型数值</returns>
    public static ulong BinTolong(string sbin)
    {
        decimal num = new decimal(Strings.Len(sbin));
        int num2 = Convert.ToInt32(num);
        checked
        {
            ulong num3 = default(ulong);
            for (int i = 1; i <= num2; i++)
            {
                if (Operators.CompareString(sbin.Substring(Convert.ToInt32(decimal.Subtract(num, new decimal(i))), 1), "1", TextCompare: false) == 0)
                {
                    num3 = (ulong)Math.Round((double)num3 + Math.Pow(2.0, i - 1));
                }
            }
            return num3;
        }
    }

    /// <summary>
    /// 将二进制字符串转换为八进制浮点数
    /// </summary>
    /// <param name="TmpBin">二进制字符串</param>
    /// <returns>八进制浮点数</returns>
    //public static double BinToOct(string TmpBin)
    //{
    //    int try0001_dispatch = -1;
    //    int num3 = default(int);
    //    int num2 = default(int);
    //    int num = default(int);
    //    string[] array = default(string[]);
    //    int num5 = default(int);
    //    double num6 = default(double);
    //    int num7 = default(int);
    //    int num8 = default(int);
    //    while (true)
    //    {
    //        try
    //        {
    //            /*Note: ILSpy has introduced the following switch to emulate a goto from catch-block to try-block*/
    //            ;
    //            checked
    //            {
    //                switch (try0001_dispatch)
    //                {
    //                    default:
    //                        ProjectData.ClearProjectError();
    //                        num3 = -2;
    //                        goto IL_000b;
    //                    case 313:
    //                        {
    //                            num2 = num;
    //                            switch ((num3 <= -2) ? 1 : num3)
    //                            {
    //                                case 1:
    //                                    break;
    //                                default:
    //                                    goto end_IL_0001;
    //                            }
    //                            int num4 = unchecked(num2 + 1);
    //                            num2 = 0;
    //                            switch (num4)
    //                            {
    //                                case 1:
    //                                    break;
    //                                case 2:
    //                                    goto IL_000b;
    //                                case 3:
    //                                    goto IL_001c;
    //                                case 4:
    //                                    goto IL_002e;
    //                                case 5:
    //                                    goto IL_0050;
    //                                case 6:
    //                                    goto IL_0070;
    //                                case 7:
    //                                    goto IL_007e;
    //                                case 8:
    //                                    goto IL_0091;
    //                                case 9:
    //                                    goto IL_00a3;
    //                                case 10:
    //                                    goto IL_00c6;
    //                                case 11:
    //                                    goto IL_00df;
    //                                default:
    //                                    goto end_IL_0001;
    //                                case 12:
    //                                case 13:
    //                                    goto end_IL_0001_2;
    //                            }
    //                            goto default;
    //                        }
    //                    IL_002e:
    //                        num = 4;
    //                        if (Operators.CompareString(Strings.Mid(array[0], num5, 1), "1", TextCompare: false) == 0)
    //                        {
    //                            goto IL_0050;
    //                        }
    //                        goto IL_0070;
    //                    IL_0050:
    //                        num = 5;
    //                        num6 += Math.Pow(2.0, Strings.Len(array[0]) - num5);
    //                        goto IL_0070;
    //                    IL_00df:
    //                        num = 11;
    //                        num5++;
    //                        goto IL_00e8;
    //                    IL_0070:
    //                        num = 6;
    //                        num5++;
    //                        goto IL_0078;
    //                    IL_000b:
    //                        num = 2;
    //                        array = Strings.Split(TmpBin, ".");
    //                        goto IL_001c;
    //                    IL_001c:
    //                        num = 3;
    //                        num7 = Strings.Len(array[0]);
    //                        num5 = 1;
    //                        goto IL_0078;
    //                    IL_0078:
    //                        if (num5 <= num7)
    //                        {
    //                            goto IL_002e;
    //                        }
    //                        goto IL_007e;
    //                    IL_007e:
    //                        num = 7;
    //                        if (Information.UBound(array) != 1)
    //                        {
    //                            goto end_IL_0001_2;
    //                        }
    //                        goto IL_0091;
    //                    IL_0091:
    //                        num = 8;
    //                        num8 = Strings.Len(array[1]);
    //                        num5 = 1;
    //                        goto IL_00e8;
    //                    IL_00e8:
    //                        if (num5 > num8)
    //                        {
    //                            goto end_IL_0001_2;
    //                        }
    //                        goto IL_00a3;
    //                    IL_00a3:
    //                        num = 9;
    //                        if (Operators.CompareString(Strings.Mid(array[1], num5, 1), "1", TextCompare: false) == 0)
    //                        {
    //                            goto IL_00c6;
    //                        }
    //                        goto IL_00df;
    //                    IL_00c6:
    //                        num = 10;
    //                        num6 += Math.Pow(2.0, -1 * num5);
    //                        goto IL_00df;
    //                    end_IL_0001:
    //                        break;
    //                }
    //            }
    //        }
    //        catch (object obj) when (obj is Exception && num3 != 0 && num2 == 0)
    //        {
    //            ProjectData.SetProjectError((Exception)obj);
    //            try0001_dispatch = 313;
    //            continue;
    //        }
    //        throw ProjectData.CreateProjectError(-2146828237);
    //        continue;
    //    end_IL_0001_2:
    //        break;
    //    }
    //    if (num2 != 0)
    //    {
    //        ProjectData.ClearProjectError();
    //    }
    //    return num6;
    //}

    /// <summary>
    /// 计算字节数组的位CRC校验值
    /// </summary>
    /// <param name="SFloat">待校验的字节数组</param>
    /// <returns>CRC校验结果（0表示校验通过，1表示校验失败）</returns>
    public static byte bitCRC(byte[] SFloat)
    {
        byte b = byte.MaxValue;
        byte b2 = byte.MaxValue;
        byte b3 = 160;
        byte b4 = 1;
        checked
        {
            short num = (short)(SFloat.Length - 1);
            for (short num2 = 0; num2 <= num; num2 = (short)unchecked(num2 + 1))
            {
                b = unchecked((byte)(b ^ SFloat[num2]));
                short num3 = 0;
                do
                {
                    byte b5 = b;
                    byte b6 = b2;
                    unchecked
                    {
                        b = (byte)((uint)b >> 1);
                        b2 = (byte)((uint)b2 >> 1);
                        if ((b6 & 1) == 1)
                        {
                            b = checked((byte)(b + 128));
                        }
                        if ((b5 & 1) == 1)
                        {
                            b ^= b4;
                            b2 ^= b3;
                        }
                    }
                    num3 = (short)unchecked(num3 + 1);
                }
                while (num3 <= 7);
            }
        }
        if (b == 0 && b2 == 0)
        {
            return 0;
        }
        return 1;
    }

    /// <summary>
    /// 计算字节数组的位CRC校验值（排除最后6个字节）
    /// </summary>
    /// <param name="SFloat">待校验的字节数组</param>
    /// <returns>CRC校验结果（0表示校验通过，1表示校验失败）</returns>
    public static byte bitCRC1(byte[] SFloat)
    {
        byte b = byte.MaxValue;
        byte b2 = byte.MaxValue;
        byte b3 = 160;
        byte b4 = 1;
        checked
        {
            short num = (short)(SFloat.Length - 6);
            for (short num2 = 0; num2 <= num; num2 = (short)unchecked(num2 + 1))
            {
                b = unchecked((byte)(b ^ SFloat[num2]));
                short num3 = 0;
                do
                {
                    byte b5 = b;
                    byte b6 = b2;
                    unchecked
                    {
                        b = (byte)((uint)b >> 1);
                        b2 = (byte)((uint)b2 >> 1);
                        if ((b6 & 1) == 1)
                        {
                            b = checked((byte)(b + 128));
                        }
                        if ((b5 & 1) == 1)
                        {
                            b ^= b4;
                            b2 ^= b3;
                        }
                    }
                    num3 = (short)unchecked(num3 + 1);
                }
                while (num3 <= 7);
            }
        }
        if (b == 0 && b2 == 0)
        {
            return 0;
        }
        return 1;
    }

    /// <summary>
    /// 计算十六进制字符串的字节和校验值
    /// </summary>
    /// <param name="sSource">十六进制字符串</param>
    /// <returns>校验和的十六进制字符串表示</returns>
    public static string byteSum(string sSource)
    {
        long num = 0L;
        byte[] array = asc2byte(sSource);
        checked
        {
            byte b = (byte)(array.Length - 1);
            byte b2 = 0;
            while (unchecked((uint)b2 <= (uint)b))
            {
                num += array[b2];
                b2 = (byte)unchecked((uint)(b2 + 1));
            }
        }
        num %= 256;
        string text = Conversion.Hex(num);
        if (text.Length == 1)
        {
            text = "0" + text;
        }
        return text;
    }

    /// <summary>
    /// 计算Canatal协议的校验和
    /// </summary>
    /// <param name="arrTemp">字节数组</param>
    /// <param name="sTemp">十六进制字符串</param>
    /// <returns>校验是否通过</returns>
    public static bool CalCheckSum_Canatal(ref byte[] arrTemp, string sTemp)
    {
        short num = 0;
        num = arrTemp[1];
        checked
        {
            num = (short)unchecked(num + arrTemp[2]);
            num = (short)unchecked(num + arrTemp[3]);
            sTemp = sTemp.Substring(4);
            sTemp = sTemp.Substring(0, sTemp.Length - 1);
            byte[] array = asc2byte(sTemp);
            short num2 = (short)(array.Length - 2);
            for (short num3 = 0; num3 <= num2; num3 = (short)unchecked(num3 + 1))
            {
                num = (short)unchecked(num + array[num3]);
                num &= 0xFF;
            }
            num &= 0xFF;
            short num4 = (short)unchecked(num % 256);
            num4 ^= 0xFF;
            num4++;
            num4 &= 0xF;
            array[array.Length - 1] = (byte)(array[array.Length - 1] & 0xF);
            if (num4 == array[array.Length - 1])
            {
                return true;
            }
            return false;
        }
    }

    /// <summary>
    /// 计算IEEE 754浮点数的逆向值
    /// </summary>
    /// <param name="sFloat">十六进制浮点数字符串</param>
    /// <returns>计算后的十进制值</returns>
    public static decimal CalFloatImv(string sFloat)
    {
        sFloat = asc2bin(sFloat);
        string text = sFloat.Substring(0, 8);
        string text2 = "";
        decimal num = default(decimal);
        checked
        {
            if (Operators.CompareString(text.Substring(0, 1), "1", TextCompare: false) == 0)
            {
                byte b = 1;
                do
                {
                    text2 = ((Operators.CompareString(text.Substring(b, 1), "1", TextCompare: false) != 0) ? (text2 + "1") : (text2 + "0"));
                    b = (byte)unchecked((uint)(b + 1));
                }
                while (unchecked((uint)b) <= 7u);
                b = 0;
                do
                {
                    byte value = Conversions.ToByte(text2.Substring(b, 1));
                    num = decimal.Add(decimal.Multiply(num, 2m), new decimal(value));
                    b = (byte)unchecked((uint)(b + 1));
                }
                while (unchecked((uint)b) <= 6u);
                num = decimal.Negate(decimal.Add(num, 1m));
            }
            else
            {
                byte b = 0;
                do
                {
                    byte value = Conversions.ToByte(text.Substring(b, 1));
                    num = decimal.Add(decimal.Multiply(num, 2m), new decimal(value));
                    b = (byte)unchecked((uint)(b + 1));
                }
                while (unchecked((uint)b) <= 7u);
            }
            if ((Math.Pow(2.0, Convert.ToDouble(num)) > 99999.0) | (Math.Pow(2.0, Convert.ToDouble(num)) < -99999.0))
            {
                return default(decimal);
            }
            num = new decimal(Math.Pow(2.0, Convert.ToDouble(num)));
            Debug.WriteLine(num);
            text = sFloat.Substring(8, 24);
            text2 = "";
            int num2 = 0;
            if (Operators.CompareString(text.Substring(0, 1), "1", TextCompare: false) == 0)
            {
                byte b = 1;
                do
                {
                    text2 = ((Operators.CompareString(text.Substring(b, 1), "1", TextCompare: false) != 0) ? (text2 + "1") : (text2 + "0"));
                    b = (byte)unchecked((uint)(b + 1));
                }
                while (unchecked((uint)b) <= 23u);
                b = 0;
                do
                {
                    byte value = Conversions.ToByte(text2.Substring(b, 1));
                    num2 = num2 * 2 + value;
                    b = (byte)unchecked((uint)(b + 1));
                }
                while (unchecked((uint)b) <= 22u);
                num2 = -(num2 + 1);
            }
            else
            {
                byte b = 0;
                do
                {
                    byte value = Conversions.ToByte(text.Substring(b, 1));
                    num2 = num2 * 2 + value;
                    b = (byte)unchecked((uint)(b + 1));
                }
                while (unchecked((uint)b) <= 23u);
            }
            return decimal.Multiply(num, new decimal(num2));
        }
    }

    /// <summary>
    /// 计算7E协议的校验和
    /// </summary>
    /// <param name="sSource">源字符串</param>
    /// <returns>4位十六进制校验和字符串</returns>
    public static string CalSum_7E(string sSource)
    {
        bool flag = false;
        long num = 0L;
        sSource = sSource.Substring(1);
        byte[] bytes = Encoding.ASCII.GetBytes(sSource);
        checked
        {
            short num2 = (short)(bytes.Length - 1);
            for (short num3 = 0; num3 <= num2; num3 = (short)unchecked(num3 + 1))
            {
                num += bytes[num3];
                num = unchecked(num % 65536);
            }
            num ^= 0xFFFF;
        }
        num = checked(num + 1) % 65536;
        string text = Conversion.Hex(num);
        return text.PadLeft(4, '0');
    }

    /// <summary>
    /// 计算字符串的ASCII值单字节校验和
    /// </summary>
    /// <param name="sFloat">输入字符串</param>
    /// <returns>4位十六进制校验和字符串</returns>
    public static string CharAscValueCheckSumOfOneByte(string sFloat)
    {
        short num = (short)((sFloat.Length == 1) ? checked((short)Strings.Asc(sFloat)) : 0);
        checked
        {
            int num2 = sFloat.Length - 1;
            for (int i = 0; i <= num2; i++)
            {
                num = (short)(num + Strings.Asc(sFloat.Substring(i, 1)));
            }
            num = unchecked((short)(~num));
            return Conversion.Hex(num + 1).Substring(4, 4);
        }
    }

    /// <summary>
    /// 检查7E协议代码格式是否正确
    /// </summary>
    /// <param name="sSource">源字符串</param>
    /// <returns>格式是否正确</returns>
    public static bool Check7ECode(string sSource)
    {
        if (Operators.CompareString(sSource.Substring(0, 1), "~", TextCompare: false) != 0)
        {
            return false;
        }
        checked
        {
            short num = (short)(sSource.Length - 2);
            for (short num2 = 1; num2 <= num; num2 = (short)unchecked(num2 + 1))
            {
                switch (sSource.Substring(num2, 1))
                {
                    default:
                        return false;
                    case "0":
                    case "1":
                    case "2":
                    case "3":
                    case "4":
                    case "5":
                    case "6":
                    case "7":
                    case "8":
                    case "9":
                    case "A":
                    case "B":
                    case "C":
                    case "D":
                    case "E":
                    case "F":
                    case " ":
                    case "\r":
                        break;
                }
            }
            return true;
        }
    }

    /// <summary>
    /// 验证7E协议的校验和
    /// </summary>
    /// <param name="sSource">源字符串</param>
    /// <returns>校验是否失败（true表示失败，false表示成功）</returns>
    public static bool CheckSum_7E(string sSource)
    {
        bool flag = false;
        long num = 0L;
        if (Operators.CompareString(sSource.Substring(0, 1), "~", TextCompare: false) != 0)
        {
            return true;
        }
        string right;
        checked
        {
            if ((Operators.CompareString(sSource.Substring(sSource.Length - 1, 1), " ", TextCompare: false) != 0) & (Operators.CompareString(sSource.Substring(sSource.Length - 1, 1), "\r", TextCompare: false) != 0))
            {
                sSource += "\r";
            }
            right = sSource.Substring(sSource.Length - 5, 4);
            sSource = sSource.Substring(0, sSource.Length - 5);
            sSource = sSource.Substring(1);
            byte[] bytes = Encoding.ASCII.GetBytes(sSource);
            short num2 = (short)(bytes.Length - 1);
            for (short num3 = 0; num3 <= num2; num3 = (short)unchecked(num3 + 1))
            {
                num += bytes[num3];
                num = unchecked(num % 65536);
            }
            num ^= 0xFFFF;
        }
        num = checked(num + 1) % 65536;
        string text = Conversion.Hex(num);
        text = text.PadLeft(4, '0');
        if (Operators.CompareString(text, right, TextCompare: false) == 0)
        {
            return false;
        }
        return true;
    }

    /// <summary>
    /// 验证字节数组的单字节校验和
    /// </summary>
    /// <param name="arr">字节数组</param>
    /// <returns>校验是否失败（true表示失败，false表示成功）</returns>
    public static bool CheckSumOfOneByte(byte[] arr)
    {
        long num = 0L;
        checked
        {
            byte b = (byte)(arr.Length - 2);
            byte b2 = 0;
            while (unchecked((uint)b2 <= (uint)b))
            {
                num += arr[b2];
                b2 = (byte)unchecked((uint)(b2 + 1));
            }
            num = unchecked(num % 256);
            if (num == arr[arr.Length - 1])
            {
                return false;
            }
            return true;
        }
    }

    /// <summary>
    /// 验证十六进制字符串的单字节校验和
    /// </summary>
    /// <param name="sSource">十六进制字符串</param>
    /// <returns>校验是否失败（true表示失败，false表示成功）</returns>
    public static bool CheckSumOfOneByte(string sSource)
    {
        long num = 0L;
        string right;
        checked
        {
            string sSource2 = sSource.Substring(0, sSource.Length - 2);
            right = sSource.Substring(sSource.Length - 2);
            byte[] array = HexChar2Byte(sSource2);
            byte b = (byte)(array.Length - 1);
            byte b2 = 0;
            while (unchecked((uint)b2 <= (uint)b))
            {
                num += array[b2];
                b2 = (byte)unchecked((uint)(b2 + 1));
            }
        }
        num %= 256;
        string text = Conversion.Hex(num);
        if (text.Length == 1)
        {
            text = "0" + text;
        }
        if (Operators.CompareString(text, right, TextCompare: false) == 0)
        {
            return false;
        }
        return true;
    }

    /// <summary>
    /// 计算CRC16校验码
    /// </summary>
    /// <param name="sSource">十六进制字符串</param>
    /// <returns>CRC16校验码的十六进制字符串</returns>
    public static string Crc16(string sSource)
    {
        byte b = byte.MaxValue;
        byte b2 = byte.MaxValue;
        byte b3 = 1;
        byte b4 = 160;
        byte[] array = HexChar2Byte(sSource);
        byte b5 = checked((byte)(array.Length - 1));
        byte b6 = 0;
        while ((uint)b6 <= (uint)b5)
        {
            b ^= array[b6];
            int num = 0;
            checked
            {
                do
                {
                    byte b7 = b2;
                    byte b8 = b;
                    b2 = (byte)unchecked(b2 / 2);
                    b = (byte)unchecked(b / 2);
                    if ((b7 & 1) == 1)
                    {
                        b |= 0x80;
                    }
                    unchecked
                    {
                        if ((b8 & 1) == 1)
                        {
                            b2 ^= b4;
                            b ^= b3;
                        }
                    }
                    num++;
                }
                while (num <= 7);
                b6 = (byte)unchecked((uint)(b6 + 1));
            }
        }
        string text = Conversion.Hex(b);
        string text2 = Conversion.Hex(b2);
        if (text.Length == 1)
        {
            text = "0" + text;
        }
        if (text2.Length == 1)
        {
            text2 = "0" + text2;
        }
        return text + text2;
    }

    /// <summary>
    /// 计算CRC16校验码（方法2）
    /// </summary>
    /// <param name="sourse">十六进制字符串</param>
    /// <returns>CRC16校验码的十六进制字符串</returns>
    public static string CRC162(string sourse)
    {
        byte[] array = HexChar2Byte(sourse);
        byte b = byte.MaxValue;
        byte b2 = byte.MaxValue;
        int num = Information.UBound(array);
        for (int i = 0; i <= num; i = checked(i + 1))
        {
            long ind = (uint)(b2 ^ array[i]);
            b2 = (byte)(b ^ GetCRCLo(ind));
            b = GetCRCHi(ind);
        }
        return arr2HexStr(new byte[2] { b, b2 });
    }

    /// <summary>
    /// 将4个字节转换为补码形式的十进制值（CYH格式）
    /// </summary>
    /// <param name="by1">第1个字节</param>
    /// <param name="by2">第2个字节</param>
    /// <param name="by3">第3个字节</param>
    /// <param name="by4">第4个字节</param>
    /// <returns>补码形式的十进制值</returns>
    public static decimal CyhDoubleWord2ValueOfComplement(byte by1, byte by2, byte by3, byte by4)
    {
        checked
        {
            decimal result;
            if (by3 > 127)
            {
                by3 ^= 0xFF;
                by4 ^= 0xFF;
                result = new decimal(-((by1 * 256 + by2) * 256 * 256 + by3 * 256 + by4 + 1));
            }
            else
            {
                result = new decimal((by1 * 256 + by2) * 256 * 256 + by3 * 256 + by4);
            }
            return result;
        }
    }

    /// <summary>
    /// 将十进制数转换为二进制字符串
    /// </summary>
    /// <param name="Dec">十进制数</param>
    /// <returns>二进制字符串</returns>
    public static string Decimal2bin(long Dec)
    {
        string text = "";
        while (Dec > 0)
        {
            text = Conversions.ToString(Dec % 2) + text;
            Dec /= 2;
        }
        return text;
    }

    /// <summary>
    /// 将十进制数转换为8位二进制字符串（左侧补0）
    /// </summary>
    /// <param name="Dec">十进制数</param>
    /// <returns>8位二进制字符串</returns>
    public static string Decimal2bin1(long Dec)
    {
        string text = "";
        while (Dec > 0)
        {
            text = Conversions.ToString(Dec % 2) + text;
            Dec /= 2;
        }
        return text.PadLeft(8, '0');
    }

    /// <summary>
    /// 将十进制数转换为特定格式的十进制数（减去51并调整十六进制偏移）
    /// </summary>
    /// <param name="Dec">输入的十进制数</param>
    /// <returns>转换后的十进制数</returns>
    public static decimal Decimal2Decimal1(long Dec)
    {
        checked
        {
            decimal num = new decimal(Dec - 51);
            return decimal.Subtract(num, new decimal(unchecked(Convert.ToInt64(num) / 16) * 6));
        }
    }

    /// <summary>
    /// 将4个字节转换为32位无符号十进制值
    /// </summary>
    /// <param name="by1">高位字节1</param>
    /// <param name="by2">高位字节2</param>
    /// <param name="by3">低位字节1</param>
    /// <param name="by4">低位字节2</param>
    /// <returns>32位无符号十进制值</returns>
    public static decimal DoubleWord2Value(byte by1, byte by2, byte by3, byte by4)
    {
        decimal num = default(decimal);
        num = new decimal(by1);
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by2));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by3));
        return decimal.Add(decimal.Multiply(num, 256m), new decimal(by4));
    }

    /// <summary>
    /// 将4个字节转换为32位有符号补码十进制值
    /// </summary>
    /// <param name="by1">高位字节1</param>
    /// <param name="by2">高位字节2</param>
    /// <param name="by3">低位字节1</param>
    /// <param name="by4">低位字节2</param>
    /// <returns>32位有符号补码十进制值</returns>
    public static decimal DoubleWord2ValueOfComplement(byte by1, byte by2, byte by3, byte by4)
    {
        checked
        {
            decimal result;
            if (by1 > 127)
            {
                by1 ^= 0xFF;
                by2 ^= 0xFF;
                by3 ^= 0xFF;
                by4 ^= 0xFF;
                result = new decimal(-((by1 * 256 + by2) * 256 * 256 + by3 * 256 + by4 + 1));
            }
            else
            {
                result = new decimal((by1 * 256 + by2) * 256 * 256 + by3 * 256 + by4);
            }
            return result;
        }
    }

    /// <summary>
    /// 将十六进制字符串转换为32位有符号补码十进制值
    /// </summary>
    /// <param name="s">十六进制字符串</param>
    /// <returns>32位有符号补码十进制值</returns>
    public static decimal DoubleWord2ValueOfComplement(string s)
    {
        string text = asc2bin(s);
        int num = 0;
        checked
        {
            int num2 = (int)Convert.ToInt64(text.Substring(0, 1), 2);
            decimal result;
            if (num2 == 1)
            {
                int num3 = text.Length - 1;
                for (int i = 1; i <= num3; i++)
                {
                    num = ((Operators.CompareString(text.Substring(i, 1), "0", TextCompare: false) != 0) ? (2 * num) : (2 * num + 1));
                }
                result = new decimal(-num - 1);
            }
            else
            {
                num = (int)Convert.ToInt64(text.Substring(1), 2);
                result = new decimal(num);
            }
            return result;
        }
    }

    /// <summary>
    /// 将3个字节转换为24位有符号补码十进制值
    /// </summary>
    /// <param name="by1">高位字节</param>
    /// <param name="by2">中位字节</param>
    /// <param name="by3">低位字节</param>
    /// <returns>24位有符号补码十进制值</returns>
    public static decimal DoubleWord3ValueOfComplement(byte by1, byte by2, byte by3)
    {
        checked
        {
            decimal result;
            if (by1 > 127)
            {
                by1 ^= 0xFF;
                by2 ^= 0xFF;
                by3 ^= 0xFF;
                result = new decimal(-((by1 * 256 + by2) * 256 * 256 + by3 * 256 + 1));
            }
            else
            {
                result = new decimal((by1 * 256 + by2) * 256 * 256 + by3 * 256);
            }
            return result;
        }
    }

    /// <summary>
    /// 将8个字节转换为64位无符号十进制值
    /// </summary>
    /// <param name="by1">字节1（最高位）</param>
    /// <param name="by2">字节2</param>
    /// <param name="by3">字节3</param>
    /// <param name="by4">字节4</param>
    /// <param name="by5">字节5</param>
    /// <param name="by6">字节6</param>
    /// <param name="by7">字节7</param>
    /// <param name="by8">字节8（最低位）</param>
    /// <returns>64位无符号十进制值</returns>
    public static decimal DoubleWord4Value(byte by1, byte by2, byte by3, byte by4, byte by5, byte by6, byte by7, byte by8)
    {
        decimal num = default(decimal);
        num = new decimal(by1);
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by2));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by3));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by4));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by5));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by6));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by7));
        return decimal.Add(decimal.Multiply(num, 256m), new decimal(by8));
    }

    /// <summary>
    /// 将8个字节转换为64位有符号补码十进制值
    /// </summary>
    /// <param name="by1">字节1（最高位）</param>
    /// <param name="by2">字节2</param>
    /// <param name="by3">字节3</param>
    /// <param name="by4">字节4</param>
    /// <param name="by5">字节5</param>
    /// <param name="by6">字节6</param>
    /// <param name="by7">字节7</param>
    /// <param name="by8">字节8（最低位）</param>
    /// <returns>64位有符号补码十进制值</returns>
    public static decimal DoubleWord4ValueOfComplement(byte by1, byte by2, byte by3, byte by4, byte by5, byte by6, byte by7, byte by8)
    {
        checked
        {
            decimal result;
            if (by1 > 127)
            {
                by1 ^= 0xFF;
                by2 ^= 0xFF;
                by3 ^= 0xFF;
                by4 ^= 0xFF;
                by5 ^= 0xFF;
                by6 ^= 0xFF;
                by7 ^= 0xFF;
                by8 ^= 0xFF;
                result = new decimal(-((by1 * 256 + by2) * 256 * 256 * 256 * 256 * 256 * 256 + (by3 * 256 + by4) * 256 * 256 * 256 * 256 + (by5 * 256 + by6) * 256 * 256 + by7 * 256 + by8));
            }
            else
            {
                result = new decimal((by1 * 256 + by2) * 256 * 256 * 256 * 256 * 256 * 256 + (by3 * 256 + by4) * 256 * 256 * 256 * 256 + (by5 * 256 + by6) * 256 * 256 + by7 * 256 + by8);
            }
            return result;
        }
    }

    /// <summary>
    /// 将4位十六进制字符串转换为十进制值
    /// </summary>
    /// <param name="s">4位十六进制字符串</param>
    /// <returns>十进制值</returns>
    public static decimal fourChar2Byte(string s)
    {
        if (s.Length >= 2)
        {
            decimal num = default(decimal);
            switch (s.Substring(0, 1))
            {
                case "0":
                    num = default(decimal);
                    break;
                case "1":
                    num = 1m;
                    break;
                case "2":
                    num = 2m;
                    break;
                case "3":
                    num = 3m;
                    break;
                case "4":
                    num = 4m;
                    break;
                case "5":
                    num = 5m;
                    break;
                case "6":
                    num = 6m;
                    break;
                case "7":
                    num = 7m;
                    break;
                case "8":
                    num = 8m;
                    break;
                case "9":
                    num = 9m;
                    break;
                case "A":
                    num = 10m;
                    break;
                case "B":
                    num = 11m;
                    break;
                case "C":
                    num = 12m;
                    break;
                case "D":
                    num = 13m;
                    break;
                case "E":
                    num = 14m;
                    break;
                case "F":
                    num = 15m;
                    break;
            }
            switch (s.Substring(1, 1))
            {
                case "0":
                    num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                    break;
                case "1":
                    num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                    break;
                case "2":
                    num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                    break;
                case "3":
                    num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                    break;
                case "4":
                    num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                    break;
                case "5":
                    num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                    break;
                case "6":
                    num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                    break;
                case "7":
                    num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                    break;
                case "8":
                    num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                    break;
                case "9":
                    num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                    break;
                case "A":
                    num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                    break;
                case "B":
                    num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                    break;
                case "C":
                    num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                    break;
                case "D":
                    num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                    break;
                case "E":
                    num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                    break;
                case "F":
                    num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                    break;
            }
            switch (s.Substring(2, 1))
            {
                case "0":
                    num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                    break;
                case "1":
                    num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                    break;
                case "2":
                    num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                    break;
                case "3":
                    num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                    break;
                case "4":
                    num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                    break;
                case "5":
                    num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                    break;
                case "6":
                    num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                    break;
                case "7":
                    num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                    break;
                case "8":
                    num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                    break;
                case "9":
                    num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                    break;
                case "A":
                    num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                    break;
                case "B":
                    num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                    break;
                case "C":
                    num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                    break;
                case "D":
                    num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                    break;
                case "E":
                    num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                    break;
                case "F":
                    num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                    break;
            }
            switch (s.Substring(3, 1))
            {
                case "0":
                    num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                    break;
                case "1":
                    num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                    break;
                case "2":
                    num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                    break;
                case "3":
                    num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                    break;
                case "4":
                    num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                    break;
                case "5":
                    num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                    break;
                case "6":
                    num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                    break;
                case "7":
                    num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                    break;
                case "8":
                    num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                    break;
                case "9":
                    num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                    break;
                case "A":
                    num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                    break;
                case "B":
                    num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                    break;
                case "C":
                    num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                    break;
                case "D":
                    num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                    break;
                case "E":
                    num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                    break;
                case "F":
                    num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                    break;
            }
            return num;
        }
        decimal result = default(decimal);
        return result;
    }

    /// <summary>
    /// 将8个字节转换为64位无符号十进制值（fourWord格式）
    /// </summary>
    /// <param name="by1">字节1（最高位）</param>
    /// <param name="by2">字节2</param>
    /// <param name="by3">字节3</param>
    /// <param name="by4">字节4</param>
    /// <param name="by5">字节5</param>
    /// <param name="by6">字节6</param>
    /// <param name="by7">字节7</param>
    /// <param name="by8">字节8（最低位）</param>
    /// <returns>64位无符号十进制值</returns>
    public static decimal fourWord2Value(byte by1, byte by2, byte by3, byte by4, byte by5, byte by6, byte by7, byte by8)
    {
        decimal num = default(decimal);
        num = new decimal(by1);
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by2));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by3));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by4));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by5));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by6));
        num = decimal.Add(decimal.Multiply(num, 256m), new decimal(by7));
        return decimal.Add(decimal.Multiply(num, 256m), new decimal(by8));
    }

    /// <summary>
    /// 将8个字节转换为64位有符号补码十进制值（FourWord格式）
    /// </summary>
    /// <param name="by1">字节1（最高位）</param>
    /// <param name="by2">字节2</param>
    /// <param name="by3">字节3</param>
    /// <param name="by4">字节4</param>
    /// <param name="by5">字节5</param>
    /// <param name="by6">字节6</param>
    /// <param name="by7">字节7</param>
    /// <param name="by8">字节8（最低位）</param>
    /// <returns>64位有符号补码十进制值</returns>
    public static decimal FourWord2ValueOfComplement(byte by1, byte by2, byte by3, byte by4, byte by5, byte by6, byte by7, byte by8)
    {
        checked
        {
            double num;
            if (by1 > 127)
            {
                by1 ^= 0xFF;
                by2 ^= 0xFF;
                by3 ^= 0xFF;
                by4 ^= 0xFF;
                by5 ^= 0xFF;
                by6 ^= 0xFF;
                by7 ^= 0xFF;
                by8 ^= 0xFF;
                num = by1 * 256 + by2;
                num = num * 256.0 * 256.0 + (double)(by3 * 256) + (double)unchecked((int)by4);
                num = num * 256.0 * 256.0;
                num = num * 256.0 * 256.0;
                double num2 = by5 * 256 + by6;
                num2 = num2 * 256.0 * 256.0 + (double)(by7 * 256) + (double)unchecked((int)by8) + 1.0;
                num = 0.0 - (num + num2);
            }
            else
            {
                num = by1 * 256 + by2;
                num = num * 256.0 * 256.0 + (double)(by3 * 256) + (double)unchecked((int)by4);
                num = num * 256.0 * 256.0;
                num = num * 256.0 * 256.0;
                double num2 = by5 * 256 + by6;
                num2 = num2 * 256.0 * 256.0;
                num2 = num2 + (double)(by7 * 256) + (double)unchecked((int)by8);
                num += num2;
            }
            double value = num;
            return new decimal(value);
        }
    }

    /// <summary>
    /// 获取CRC校验的高位字节
    /// </summary>
    /// <param name="Ind">索引值</param>
    /// <returns>CRC高位字节</returns>
    public static byte GetCRCHi(long Ind)
    {
        int[] array = new int[256]
        {
        0, 192, 193, 1, 195, 3, 2, 194, 198, 6,
        7, 199, 5, 197, 196, 4, 204, 12, 13, 205,
        15, 207, 206, 14, 10, 202, 203, 11, 201, 9,
        8, 200, 216, 24, 25, 217, 27, 219, 218, 26,
        30, 222, 223, 31, 221, 29, 28, 220, 20, 212,
        213, 21, 215, 23, 22, 214, 210, 18, 19, 211,
        17, 209, 208, 16, 240, 48, 49, 241, 51, 243,
        242, 50, 54, 246, 247, 55, 245, 53, 52, 244,
        60, 252, 253, 61, 255, 63, 62, 254, 250, 58,
        59, 251, 57, 249, 248, 56, 40, 232, 233, 41,
        235, 43, 42, 234, 238, 46, 47, 239, 45, 237,
        236, 44, 228, 36, 37, 229, 39, 231, 230, 38,
        34, 226, 227, 35, 225, 33, 32, 224, 160, 96,
        97, 161, 99, 163, 162, 98, 102, 166, 167, 103,
        165, 101, 100, 164, 108, 172, 173, 109, 175, 111,
        110, 174, 170, 106, 107, 171, 105, 169, 168, 104,
        120, 184, 185, 121, 187, 123, 122, 186, 190, 126,
        127, 191, 125, 189, 188, 124, 180, 116, 117, 181,
        119, 183, 182, 118, 114, 178, 179, 115, 177, 113,
        112, 176, 80, 144, 145, 81, 147, 83, 82, 146,
        150, 86, 87, 151, 85, 149, 148, 84, 156, 92,
        93, 157, 95, 159, 158, 94, 90, 154, 155, 91,
        153, 89, 88, 152, 136, 72, 73, 137, 75, 139,
        138, 74, 78, 142, 143, 79, 141, 77, 76, 140,
        68, 132, 133, 69, 135, 71, 70, 134, 130, 66,
        67, 131, 65, 129, 128, 64
        };
        return checked((byte)array[(int)(Ind + 1)]);
    }

    /// <summary>
    /// 获取CRC校验的低位字节
    /// </summary>
    /// <param name="Ind">索引值</param>
    /// <returns>CRC低位字节</returns>
    public static byte GetCRCLo(long Ind)
    {
        int[] array = new int[256]
        {
        0, 193, 129, 64, 1, 192, 128, 65, 1, 192,
        128, 65, 0, 193, 129, 64, 1, 192, 128, 65,
        0, 193, 129, 64, 0, 193, 129, 64, 1, 192,
        128, 65, 1, 192, 128, 65, 0, 193, 129, 64,
        0, 193, 129, 64, 1, 192, 128, 65, 0, 193,
        129, 64, 1, 192, 128, 65, 1, 192, 128, 65,
        0, 193, 129, 64, 1, 192, 128, 65, 0, 193,
        129, 64, 0, 193, 129, 64, 1, 192, 128, 65,
        0, 193, 129, 64, 1, 192, 128, 65, 1, 192,
        128, 65, 0, 193, 129, 64, 0, 193, 129, 64,
        1, 192, 128, 65, 1, 192, 128, 65, 0, 193,
        129, 64, 1, 192, 128, 65, 0, 193, 129, 64,
        0, 193, 129, 64, 1, 192, 128, 65, 1, 192,
        128, 65, 0, 193, 129, 64, 0, 193, 129, 64,
        1, 192, 128, 65, 0, 193, 129, 64, 1, 192,
        128, 65, 1, 192, 128, 65, 0, 193, 129, 64,
        0, 193, 129, 64, 1, 192, 128, 65, 1, 192,
        128, 65, 0, 193, 129, 64, 1, 192, 128, 65,
        0, 193, 129, 64, 0, 193, 129, 64, 1, 192,
        128, 65, 0, 193, 129, 64, 1, 192, 128, 65,
        1, 192, 128, 65, 0, 193, 129, 64, 1, 192,
        128, 65, 0, 193, 129, 64, 0, 193, 129, 64,
        1, 192, 128, 65, 1, 192, 128, 65, 0, 193,
        129, 64, 0, 193, 129, 64, 1, 192, 128, 65,
        0, 193, 129, 64, 1, 192, 128, 65, 1, 192,
        128, 65, 0, 193, 129, 64
        };
        return checked((byte)array[(int)(Ind + 1)]);
    }

    /// <summary>
    /// 从十六进制字符串中提取数据
    /// </summary>
    /// <param name="TmpHex">十六进制字符串</param>
    /// <returns>提取的数据字符串</returns>
    //public static string GetData(string TmpHex)
    //{
    //    int try0001_dispatch = -1;
    //    int num3 = default(int);
    //    int num2 = default(int);
    //    int num = default(int);
    //    int num5 = default(int);
    //    string str = default(string);
    //    string text = default(string);
    //    while (true)
    //    {
    //        try
    //        {
    //            /*Note: ILSpy has introduced the following switch to emulate a goto from catch-block to try-block*/
    //            ;
    //            switch (try0001_dispatch)
    //            {
    //                default:
    //                    ProjectData.ClearProjectError();
    //                    num3 = -2;
    //                    goto IL_000b;
    //                case 203:
    //                    {
    //                        num2 = num;
    //                        switch ((num3 <= -2) ? 1 : num3)
    //                        {
    //                            case 1:
    //                                break;
    //                            default:
    //                                goto end_IL_0001;
    //                        }
    //                        int num4 = num2 + 1;
    //                        num2 = 0;
    //                        switch (num4)
    //                        {
    //                            case 1:
    //                                break;
    //                            case 2:
    //                                goto IL_000b;
    //                            case 3:
    //                                goto IL_0015;
    //                            case 4:
    //                                goto IL_0037;
    //                            case 5:
    //                                goto IL_0071;
    //                            case 6:
    //                                goto end_IL_0001_2;
    //                            default:
    //                                goto end_IL_0001;
    //                            case 7:
    //                                goto end_IL_0001_3;
    //                        }
    //                        goto default;
    //                    }
    //                IL_0015:
    //                    num = 3;
    //                    num5 = checked((int)Math.Round(BinToOct(Strings.Mid(str, 2, 8)) - 127.0));
    //                    goto IL_0037;
    //                IL_0037:
    //                    num = 4;
    //                    text = Conversions.ToString(Math.Round(BinToOct("1." + Strings.Mid(str, 10, 23)) * Math.Pow(2.0, num5), 6));
    //                    goto IL_0071;
    //                IL_000b:
    //                    num = 2;
    //                    str = HexToBin(TmpHex);
    //                    goto IL_0015;
    //                IL_0071:
    //                    num = 5;
    //                    if (Operators.CompareString(Strings.Left(str, 1), "1", TextCompare: false) != 0)
    //                    {
    //                        goto end_IL_0001_3;
    //                    }
    //                    break;
    //                end_IL_0001_2:
    //                    break;
    //            }
    //            num = 6;
    //            text = "-" + text;
    //            break;
    //        end_IL_0001:;
    //        }
    //        catch (object obj) when (obj is Exception && num3 != 0 && num2 == 0)
    //        {
    //            ProjectData.SetProjectError((Exception)obj);
    //            try0001_dispatch = 203;
    //            continue;
    //        }
    //        throw ProjectData.CreateProjectError(-2146828237);
    //        continue;
    //    end_IL_0001_3:
    //        break;
    //    }
    //    if (num2 != 0)
    //    {
    //        ProjectData.ClearProjectError();
    //    }
    //    return text;
    //}

    /// <summary>
    /// 将字节数组转换为ASCII十六进制字符串
    /// </summary>
    /// <param name="arrHex">字节数组</param>
    /// <returns>ASCII十六进制字符串</returns>
    public static string HEX2ASC(byte[] arrHex)
    {
        string text = "";
        string text2 = "";
        checked
        {
            short num = (short)(arrHex.Length - 1);
            for (short num2 = 0; num2 <= num; num2 = (short)unchecked(num2 + 1))
            {
                text2 = Conversion.Hex(arrHex[num2]);
                if (text2.Length < 2)
                {
                    text2 = "0" + text2;
                }
                text += text2;
            }
            return text;
        }
    }

    /// <summary>
    /// 将两个字节的十六进制转换为3位BCD码十进制值
    /// </summary>
    /// <param name="byBcd1">第一个BCD字节</param>
    /// <param name="byBcd2">第二个BCD字节</param>
    /// <returns>3位十进制值</returns>
    public static decimal Hex2Bcd_3(byte byBcd1, byte byBcd2)
    {
        byte b = 0;
        byte b2 = 0;
        byte b3 = 0;
        string text = Conversion.Hex(byBcd1);
        if (text.Length == 2)
        {
            text = text.Substring(1, 1);
        }
        b = Conversions.ToByte(text);
        text = Conversion.Hex(byBcd2);
        if (text.Length == 2)
        {
            b2 = Conversions.ToByte(text.Substring(0, 1));
            b3 = Conversions.ToByte(text.Substring(1, 1));
        }
        else
        {
            b2 = 0;
            b3 = Conversions.ToByte(text);
        }
        return new decimal(checked(b * 100 + b2 * 10 + b3));
    }

    /// <summary>
    /// 将两个字节的十六进制转换为4位BCD码十进制值
    /// </summary>
    /// <param name="byBcd1">第一个BCD字节</param>
    /// <param name="byBcd2">第二个BCD字节</param>
    /// <returns>4位十进制值</returns>
    public static decimal Hex2Bcd_4(byte byBcd1, byte byBcd2)
    {
        byte b = 0;
        byte b2 = 0;
        byte b3 = 0;
        byte b4 = 0;
        string text = Conversion.Hex(byBcd1);
        if (text.Length == 2)
        {
            b = Conversions.ToByte(text.Substring(0, 1));
            b2 = Conversions.ToByte(text.Substring(1, 1));
        }
        else
        {
            b = 0;
            b2 = Conversions.ToByte(text);
        }
        text = Conversion.Hex(byBcd2);
        if (text.Length == 2)
        {
            b3 = Conversions.ToByte(text.Substring(0, 1));
            b4 = Conversions.ToByte(text.Substring(1, 1));
        }
        else
        {
            b3 = 0;
            b4 = Conversions.ToByte(text);
        }
        return new decimal(checked(b * 1000 + b2 * 100 + b3 * 10 + b4));
    }

    /// <summary>
    /// 将十六进制字符串转换为十进制数
    /// </summary>
    /// <param name="s">十六进制字符串</param>
    /// <returns>十进制数值</returns>
    public static decimal HEX2Decimal(string s)
    {
        decimal num = default(decimal);
        char[] array = s.ToCharArray();
        checked
        {
            int num2 = s.Length - 1;
            for (int i = 0; i <= num2; i++)
            {
                switch (array[i].ToString().ToUpper())
                {
                    case "0":
                        num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                        break;
                    case "1":
                        num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                        break;
                    case "2":
                        num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                        break;
                    case "3":
                        num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                        break;
                    case "4":
                        num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                        break;
                    case "5":
                        num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                        break;
                    case "6":
                        num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                        break;
                    case "7":
                        num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                        break;
                    case "8":
                        num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                        break;
                    case "9":
                        num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                        break;
                    case "A":
                        num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                        break;
                    case "B":
                        num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                        break;
                    case "C":
                        num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                        break;
                    case "D":
                        num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                        break;
                    case "E":
                        num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                        break;
                    case "F":
                        num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                        break;
                }
            }
            return num;
        }
    }

    /// <summary>
    /// 将ASCII十六进制字符串转换为二进制字符串（支持特殊字符映射）
    /// </summary>
    /// <param name="sAsc">ASCII十六进制字符串</param>
    /// <returns>二进制字符串</returns>
    public static string HexAsc2bin(string sAsc)
    {
        string text = "";
        checked
        {
            short num = (short)(sAsc.Length - 1);
            short num2 = num;
            for (short num3 = 0; num3 <= num2; num3 = (short)unchecked(num3 + 1))
            {
                switch (sAsc.Substring(num3, 1))
                {
                    case "0":
                        text += "0000";
                        break;
                    case "1":
                        text += "0001";
                        break;
                    case "2":
                        text += "0010";
                        break;
                    case "3":
                        text += "0011";
                        break;
                    case "4":
                        text += "0100";
                        break;
                    case "5":
                        text += "0101";
                        break;
                    case "6":
                        text += "0110";
                        break;
                    case "7":
                        text += "0111";
                        break;
                    case "8":
                        text += "1000";
                        break;
                    case "9":
                        text += "1001";
                        break;
                    case ":":
                        text += "1010";
                        break;
                    case ";":
                        text += "1011";
                        break;
                    case "<":
                        text += "1100";
                        break;
                    case "=":
                        text += "1101";
                        break;
                    case ">":
                        text += "1110";
                        break;
                    case "?":
                        text += "1111";
                        break;
                }
            }
            return text;
        }
    }

    /// <summary>
    /// 将十六进制字符串转换为字节数组
    /// </summary>
    /// <param name="sSource">十六进制字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] HexChar2Byte(string sSource)
    {
        checked
        {
            short num = (short)sSource.Length;
            if (unchecked(num % 2) != 0)
            {
                num--;
            }
            if (num == 0)
            {
                return null;
            }
            byte[] array = new byte[(int)Math.Round((double)num / 2.0 - 1.0) + 1];
            num = (short)Math.Round((double)num / 2.0);
            short num2 = (short)(num - 1);
            for (short num3 = 0; num3 <= num2; num3 = (short)unchecked(num3 + 1))
            {
                string s = sSource.Substring(2 * num3, 1);
                string s2 = sSource.Substring(2 * num3 + 1, 1);
                byte b = Convert.ToByte(MulChar2Decimal(s));
                byte b2 = Convert.ToByte(MulChar2Decimal(s2));
                short num4 = (short)(b * 16 + b2);
                array[num3] = (byte)num4;
            }
            return array;
        }
    }

    /// <summary>
    /// 将十六进制字符串转换为十进制数（支持特殊字符映射）
    /// </summary>
    /// <param name="s">十六进制字符串</param>
    /// <returns>十进制数值</returns>
    public static decimal HexChar2Decimal(string s)
    {
        decimal num = default(decimal);
        checked
        {
            int num2 = s.Length - 1;
            for (int i = 0; i <= num2; i++)
            {
                switch (s.Substring(i, 1))
                {
                    case "0":
                        num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                        break;
                    case "1":
                        num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                        break;
                    case "2":
                        num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                        break;
                    case "3":
                        num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                        break;
                    case "4":
                        num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                        break;
                    case "5":
                        num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                        break;
                    case "6":
                        num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                        break;
                    case "7":
                        num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                        break;
                    case "8":
                        num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                        break;
                    case "9":
                        num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                        break;
                    case ":":
                        num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                        break;
                    case ";":
                        num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                        break;
                    case "<":
                        num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                        break;
                    case "=":
                        num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                        break;
                    case ">":
                        num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                        break;
                    case "?":
                        num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                        break;
                }
            }
            return num;
        }
    }

    /// <summary>
    /// 将十六进制字符串转换为二进制字符串
    /// </summary>
    /// <param name="TmpHex">十六进制字符串</param>
    /// <returns>二进制字符串</returns>
    //public static string HexToBin(string TmpHex)
    //{
    //    int try0001_dispatch = -1;
    //    int num3 = default(int);
    //    int num2 = default(int);
    //    int num = default(int);
    //    int num5 = default(int);
    //    string text = default(string);
    //    int num6 = default(int);
    //    string text2 = default(string);
    //    int num7 = default(int);
    //    while (true)
    //    {
    //        try
    //        {
    //            /*Note: ILSpy has introduced the following switch to emulate a goto from catch-block to try-block*/
    //            ;
    //            switch (try0001_dispatch)
    //            {
    //                default:
    //                    ProjectData.ClearProjectError();
    //                    num3 = -2;
    //                    goto IL_000b;
    //                case 228:
    //                    {
    //                        num2 = num;
    //                        switch ((num3 <= -2) ? 1 : num3)
    //                        {
    //                            case 1:
    //                                break;
    //                            default:
    //                                goto end_IL_0001;
    //                        }
    //                        int num4 = num2 + 1;
    //                        num2 = 0;
    //                        switch (num4)
    //                        {
    //                            case 1:
    //                                break;
    //                            case 2:
    //                                goto IL_000b;
    //                            case 3:
    //                                goto IL_001a;
    //                            case 4:
    //                                goto IL_003c;
    //                            case 7:
    //                                goto IL_0047;
    //                            case 8:
    //                                goto IL_005b;
    //                            case 5:
    //                            case 6:
    //                            case 9:
    //                                goto IL_0064;
    //                            case 10:
    //                                goto IL_0071;
    //                            case 11:
    //                                goto IL_008d;
    //                            default:
    //                                goto end_IL_0001;
    //                            case 12:
    //                                goto end_IL_0001_2;
    //                        }
    //                        goto default;
    //                    }
    //                IL_008d:
    //                    num = 11;
    //                    num5 = checked(num5 + 1);
    //                    goto IL_0096;
    //                IL_0047:
    //                    num = 7;
    //                    text = Conversions.ToString(num6 % 2) + text;
    //                    goto IL_005b;
    //                IL_0071:
    //                    num = 10;
    //                    text2 += Strings.Right("0000" + text, 4);
    //                    goto IL_008d;
    //                IL_005b:
    //                    num = 8;
    //                    num6 /= 2;
    //                    goto IL_0064;
    //                IL_000b:
    //                    num = 2;
    //                    num7 = Strings.Len(TmpHex);
    //                    num5 = 1;
    //                    goto IL_0096;
    //                IL_0096:
    //                    if (num5 > num7)
    //                    {
    //                        goto end_IL_0001_2;
    //                    }
    //                    goto IL_001a;
    //                IL_001a:
    //                    num = 3;
    //                    num6 = checked((int)Math.Round(Conversion.Val("&H" + Strings.Mid(TmpHex, num5, 1))));
    //                    goto IL_003c;
    //                IL_003c:
    //                    num = 4;
    //                    text = "";
    //                    goto IL_0064;
    //                IL_0064:
    //                    num = 6;
    //                    if (num6 > 0)
    //                    {
    //                        goto IL_0047;
    //                    }
    //                    goto IL_0071;
    //                end_IL_0001:
    //                    break;
    //            }
    //        }
    //        catch (object obj) when (obj is Exception && num3 != 0 && num2 == 0)
    //        {
    //            ProjectData.SetProjectError((Exception)obj);
    //            try0001_dispatch = 228;
    //            continue;
    //        }
    //        throw ProjectData.CreateProjectError(-2146828237);
    //        continue;
    //    end_IL_0001_2:
    //        break;
    //    }
    //    if (num2 != 0)
    //    {
    //        ProjectData.ClearProjectError();
    //    }
    //    return text2;
    //}

    /// <summary>
    /// 解析IEEE 754单精度浮点数
    /// </summary>
    /// <param name="sFloat">十六进制浮点数字符串</param>
    /// <returns>解析后的双精度浮点数</returns>
    public static double Ieee754(string sFloat)
    {
        string text = asc2bin(sFloat);
        int num = Conversions.ToInteger(text.Substring(0, 1));
        int num2 = BinToInt(text.Substring(1, 8));
        int num3 = BinToInt(text.Substring(9, 23));
        double value = Math.Pow(-1.0, num) * (1.0 + (double)num3 / 8388608.0) * Math.Pow(2.0, checked(num2 - 127));
        value = Math.Round(value, 2);
        if (value > 99999.0 || value < -99999.0)
        {
            return 0.0;
        }
        return value;
    }

    /// <summary>
    /// 解析IEEE 754双精度浮点数
    /// </summary>
    /// <param name="sFloat">十六进制浮点数字符串</param>
    /// <returns>解析后的十进制数</returns>
    public static decimal Ieee754_double(string sFloat)
    {
        string text = asc2bin(sFloat);
        ulong num = Conversions.ToULong(text.Substring(0, 1));
        ulong value = checked((ulong)BinToInt(text.Substring(1, 11)));
        ulong num2 = BinTolong(text.Substring(12, 52));
        decimal d = new decimal((double)num2 / 4503599627370496.0);
        decimal d2 = new decimal(Math.Pow(-1.0, num) * Convert.ToDouble(decimal.Add(1m, d)) * Math.Pow(2.0, Convert.ToDouble(decimal.Subtract(new decimal(value), 1023m))));
        d2 = Math.Round(d2, 2);
        if ((decimal.Compare(d2, 99999m) > 0) | (decimal.Compare(d2, -99999m) < 0))
        {
            return default(decimal);
        }
        return d2;
    }

    /// <summary>
    /// 解析IEEE 754双精度浮点数（T版本，不限制范围）
    /// </summary>
    /// <param name="sFloat">十六进制浮点数字符串</param>
    /// <returns>解析后的十进制数</returns>
    public static decimal Ieee754_doubleT(string sFloat)
    {
        string text = asc2bin(sFloat);
        ulong num = Conversions.ToULong(text.Substring(0, 1));
        ulong value = checked((ulong)BinToInt(text.Substring(1, 11)));
        ulong num2 = BinTolong(text.Substring(12, 52));
        decimal d = new decimal((double)num2 / 4503599627370496.0);
        decimal d2 = new decimal(Math.Pow(-1.0, num) * Convert.ToDouble(decimal.Add(1m, d)) * Math.Pow(2.0, Convert.ToDouble(decimal.Subtract(new decimal(value), 1023m))));
        return Math.Round(d2, 2);
    }

    /// <summary>
    /// 解析IEEE 754单精度浮点数（字节序反转）
    /// </summary>
    /// <param name="sFloat">十六进制浮点数字符串</param>
    /// <returns>解析后的双精度浮点数</returns>
    public static double Ieee754_invert(string sFloat)
    {
        string sAsc = sFloat.Substring(6, 2) + sFloat.Substring(4, 2) + sFloat.Substring(2, 2) + sFloat.Substring(0, 2);
        sAsc = asc2bin(sAsc);
        int num = Conversions.ToInteger(sAsc.Substring(0, 1));
        int num2 = BinToInt(sAsc.Substring(1, 8));
        int num3 = BinToInt(sAsc.Substring(9, 23));
        double value = Math.Pow(-1.0, num) * (1.0 + (double)num3 / 8388608.0) * Math.Pow(2.0, checked(num2 - 127));
        value = Math.Round(value, 2);
        if (value > 99999.0 || value < -99999.0)
        {
            return 0.0;
        }
        return value;
    }

    /// <summary>
    /// 解析IEEE 754单精度浮点数（字节序反转，T版本）
    /// </summary>
    /// <param name="sFloat">十六进制浮点数字符串</param>
    /// <returns>解析后的双精度浮点数</returns>
    public static double Ieee754_invertT(string sFloat)
    {
        string sAsc = sFloat.Substring(6, 2) + sFloat.Substring(4, 2) + sFloat.Substring(2, 2) + sFloat.Substring(0, 2);
        sAsc = asc2bin(sAsc);
        int num = Conversions.ToInteger(sAsc.Substring(0, 1));
        int num2 = BinToInt(sAsc.Substring(1, 8));
        int num3 = BinToInt(sAsc.Substring(9, 23));
        double value = Math.Pow(-1.0, num) * (1.0 + (double)num3 / 8388608.0) * Math.Pow(2.0, checked(num2 - 127));
        return Math.Round(value, 2);
    }

    /// <summary>
    /// 解析IEEE 754单精度浮点数（T版本，不限制范围）
    /// </summary>
    /// <param name="sFloat">十六进制浮点数字符串</param>
    /// <returns>解析后的双精度浮点数</returns>
    public static double Ieee754_T(string sFloat)
    {
        string text = asc2bin(sFloat);
        int num = Conversions.ToInteger(text.Substring(0, 1));
        int num2 = BinToInt(text.Substring(1, 8));
        int num3 = BinToInt(text.Substring(9, 23));
        double value = Math.Pow(-1.0, num) * (1.0 + (double)num3 / 8388608.0) * Math.Pow(2.0, checked(num2 - 127));
        return Math.Round(value, 2);
    }

    /// <summary>
    /// 解析IEEE 754单精度浮点数（ODY格式，字节序反转）
    /// </summary>
    /// <param name="SFloat">十六进制浮点数字符串</param>
    /// <returns>解析后的双精度浮点数</returns>
    public static double Ieee754ody(string SFloat)
    {
        string sAsc = SFloat.Substring(6, 2) + SFloat.Substring(4, 2) + SFloat.Substring(2, 2) + SFloat.Substring(0, 2);
        sAsc = asc2bin(sAsc);
        int num = Conversions.ToInteger(sAsc.Substring(0, 1));
        int num2 = BinToDe(sAsc.Substring(1, 8));
        int num3 = BinToDe(sAsc.Substring(9, 23));
        double value = Math.Pow(-1.0, num) * (1.0 + (double)num3 / 8388608.0) * Math.Pow(2.0, checked(num2 - 127));
        return Math.Round(value, 2);
    }

    /// <summary>
    /// 执行IMV异或校验
    /// </summary>
    /// <param name="arr">字节数组</param>
    /// <returns>校验是否通过</returns>
    public static bool ImvXorCheck(ref byte[] arr)
    {
        checked
        {
            byte b = (byte)(arr.Length - 3);
            byte b2 = 0;
            byte b3 = default(byte);
            while (unchecked((uint)b2 <= (uint)b))
            {
                b3 = unchecked((byte)((b2 != 0) ? (b3 ^ arr[b2]) : arr[b2]));
                b2 = (byte)unchecked((uint)(b2 + 1));
            }
            b3 = (byte)unchecked(b3 % 256);
            if (b3 < 32)
            {
                b3 ^= 0xFF;
            }
            if (b3 == arr[arr.Length - 2])
            {
                return true;
            }
            return false;
        }
    }


    /// <summary>
    /// 将多字符十六进制字符串转换为十进制数
    /// </summary>
    /// <param name="s">十六进制字符串</param>
    /// <returns>十进制数值</returns>
    public static decimal MulChar2Decimal(string s)
    {
        decimal num = default(decimal);
        checked
        {
            int num2 = s.Length - 1;
            for (int i = 0; i <= num2; i++)
            {
                switch (s.Substring(i, 1))
                {
                    case "0":
                        num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                        break;
                    case "1":
                        num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                        break;
                    case "2":
                        num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                        break;
                    case "3":
                        num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                        break;
                    case "4":
                        num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                        break;
                    case "5":
                        num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                        break;
                    case "6":
                        num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                        break;
                    case "7":
                        num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                        break;
                    case "8":
                        num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                        break;
                    case "9":
                        num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                        break;
                    case "A":
                        num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                        break;
                    case "B":
                        num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                        break;
                    case "C":
                        num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                        break;
                    case "D":
                        num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                        break;
                    case "E":
                        num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                        break;
                    case "F":
                        num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                        break;
                }
            }
            return num;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="s"></param>
    /// <returns></returns>
    /// <summary>
    /// 将单个十六进制字符转换为字节值
    /// </summary>
    /// <param name="s">单个十六进制字符</param>
    /// <returns>对应的字节值</returns>
    public static byte oneChar2Byte(string s)
    {
        if (s.Length >= 1)
        {
            byte result = default(byte);
            switch (s)
            {
                case "0":
                    result = 0;
                    break;
                case "1":
                    result = 1;
                    break;
                case "2":
                    result = 2;
                    break;
                case "3":
                    result = 3;
                    break;
                case "4":
                    result = 4;
                    break;
                case "5":
                    result = 5;
                    break;
                case "6":
                    result = 6;
                    break;
                case "7":
                    result = 7;
                    break;
                case "8":
                    result = 8;
                    break;
                case "9":
                    result = 9;
                    break;
                case "A":
                    result = 10;
                    break;
                case "B":
                    result = 11;
                    break;
                case "C":
                    result = 12;
                    break;
                case "D":
                    result = 13;
                    break;
                case "E":
                    result = 14;
                    break;
                case "F":
                    result = 15;
                    break;
            }
            return result;
        }
        byte result2 = default(byte);
        return result2;
    }

    /// <summary>
    /// 将单字符字符串补齐为2位（左侧补0）
    /// </summary>
    /// <param name="SFloat">输入字符串</param>
    /// <returns>补齐后的2位字符串</returns>
    public static string ONETO2(string SFloat)
    {
        if (Strings.Len(SFloat) == 1)
        {
            return "0" + SFloat;
        }
        return SFloat;
    }

    /// <summary>
    /// 将字符串补齐为4位（左侧补0）
    /// </summary>
    /// <param name="SFloat">输入字符串</param>
    /// <returns>补齐后的4位字符串</returns>
    public static string ONETO4(string SFloat)
    {
        string text = default(string);
        return SFloat.Length switch
        {
            1 => "000" + SFloat,
            2 => "00" + SFloat,
            3 => "0" + SFloat,
            4 => SFloat,
            _ => text,
        };
    }

    /// <summary>
    /// 设置Modbus时间格式
    /// </summary>
    /// <param name="addr">地址字符串</param>
    /// <returns>格式化后的时间字符串</returns>
    public static string setmodtime(string addr)
    {
        string text = "";
        string text2 = "2014";
        string text3 = "6";
        string text4 = "18";
        string text5 = "16";
        string text6 = "39";
        string text7 = "59";
        string text8 = "";
        text = Conversions.ToString(DateTime.Now);
        int num = text.IndexOf("/");
        checked
        {
            if (num > 0)
            {
                int length = text.Trim().Length;
                text2 = text.Substring(0, num);
                text = text.Substring(num + 1, length - num - 1);
                text2 = Conversion.Hex(Conversions.ToInteger(text2)).PadLeft(4, '0');
            }
            num = text.IndexOf("/");
            if (num > 0)
            {
                int length = text.Trim().Length;
                text3 = text.Substring(0, num);
                text = text.Substring(num + 1, length - num - 1);
                text3 = Conversion.Hex(Conversions.ToInteger(text3)).PadLeft(2, '0');
            }
            num = text.IndexOf(" ");
            if (num > 0)
            {
                int length = text.Trim().Length;
                text4 = text.Substring(0, num);
                text = text.Substring(num + 1, length - num - 1);
                text4 = Conversion.Hex(Conversions.ToInteger(text4)).PadLeft(2, '0');
            }
            num = text.IndexOf(":");
            if (num > 0)
            {
                int length = text.Trim().Length;
                text5 = text.Substring(0, num);
                text = text.Substring(num + 1, length - num - 1);
                text5 = Conversion.Hex(Conversions.ToInteger(text5)).PadLeft(2, '0');
            }
            num = text.IndexOf(":");
            if (num > 0)
            {
                int length = text.Trim().Length;
                text6 = text.Substring(0, num);
                text = text.Substring(num + 1, length - num - 1);
                text6 = Conversion.Hex(Conversions.ToInteger(text6)).PadLeft(2, '0');
            }
            text7 = text;
            text7 = Conversion.Hex(Conversions.ToInteger(text7)).PadLeft(2, '0');
            string text9 = "~21" + Conversion.Hex(Conversions.ToInteger(addr)).PadLeft(2, '0') + "604E200E" + text2 + text3 + text4 + text5 + text6 + text7;
            return text9 + CalSum_7E(text9);
        }
    }

    /// <summary>
    /// 字符串替换函数
    /// </summary>
    /// <param name="sSource">源字符串</param>
    /// <param name="sOld">要替换的旧字符串</param>
    /// <param name="sNew">替换为的新字符串</param>
    /// <returns>替换后的字符串</returns>
    public static string strReplace(string sSource, string sOld, string sNew)
    {
        if (sOld.Length == 0)
        {
            return sSource;
        }
        checked
        {
            short num = (short)sOld.Length;
            short num2 = (short)sSource.IndexOf(sOld);
            string text = "";
            while (num2 >= 0)
            {
                text = text + sSource.Substring(0, num2) + sNew;
                sSource = sSource.Substring((short)unchecked(num2 + num));
                num2 = (short)sSource.IndexOf(sOld);
            }
            return text + sSource;
        }
    }

    /// <summary>
    /// 将ASCII字符串转换为字长度格式的二进制字符串
    /// </summary>
    /// <param name="sAsc">ASCII字符串</param>
    /// <returns>二进制字符串</returns>
    public static string Towordlegth(string sAsc)
    {
        checked
        {
            short num = (short)Strings.Len(sAsc);
            string text = default(string);
            if (num < 4)
            {
                short num2 = (short)(4 - num);
                for (short num3 = 1; num3 <= num2; num3 = (short)unchecked(num3 + 1))
                {
                    text += "0";
                }
                text += sAsc;
            }
            else
            {
                text = sAsc;
            }
            string text2 = "";
            short num4 = 0;
            do
            {
                switch (text.Substring(num4, 1))
                {
                    case "0":
                        text2 += "0000";
                        break;
                    case "1":
                        text2 += "0001";
                        break;
                    case "2":
                        text2 += "0010";
                        break;
                    case "3":
                        text2 += "0011";
                        break;
                    case "4":
                        text2 += "0100";
                        break;
                    case "5":
                        text2 += "0101";
                        break;
                    case "6":
                        text2 += "0110";
                        break;
                    case "7":
                        text2 += "0111";
                        break;
                    case "8":
                        text2 += "1000";
                        break;
                    case "9":
                        text2 += "1001";
                        break;
                    case "A":
                        text2 += "1010";
                        break;
                    case "B":
                        text2 += "1011";
                        break;
                    case "C":
                        text2 += "1100";
                        break;
                    case "D":
                        text2 += "1101";
                        break;
                    case "E":
                        text2 += "1110";
                        break;
                    case "F":
                        text2 += "1111";
                        break;
                }
                num4 = (short)unchecked(num4 + 1);
            }
            while (num4 <= 3);
            return text2;
        }
    }

    /// <summary>
    /// 将两个十六进制字符转换为字节值
    /// </summary>
    /// <param name="s">两个十六进制字符组成的字符串</param>
    /// <returns>对应的字节值</returns>
    public static byte TwoChar2Byte(string s)
    {
        decimal num = default(decimal);
        int num2 = 0;
        do
        {
            switch (s.Substring(num2, 1))
            {
                case "0":
                    num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                    break;
                case "1":
                    num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                    break;
                case "2":
                    num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                    break;
                case "3":
                    num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                    break;
                case "4":
                    num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                    break;
                case "5":
                    num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                    break;
                case "6":
                    num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                    break;
                case "7":
                    num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                    break;
                case "8":
                    num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                    break;
                case "9":
                    num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                    break;
                case "A":
                    num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                    break;
                case "B":
                    num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                    break;
                case "C":
                    num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                    break;
                case "D":
                    num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                    break;
                case "E":
                    num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                    break;
                case "F":
                    num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                    break;
            }
            num2 = checked(num2 + 1);
        }
        while (num2 <= 1);
        return Convert.ToByte(num);
    }

    /// <summary>
    /// 将两个十六进制字符转换为十进制数值（版本1）
    /// </summary>
    /// <param name="s">两个十六进制字符组成的字符串</param>
    /// <returns>对应的十进制数值</returns>
    public static decimal twoChar2Byte_1(string s)
    {
        if (s.Length >= 2)
        {
            decimal num = default(decimal);
            switch (s.Substring(0, 1))
            {
                case "0":
                    num = default(decimal);
                    break;
                case "1":
                    num = 1m;
                    break;
                case "2":
                    num = 2m;
                    break;
                case "3":
                    num = 3m;
                    break;
                case "4":
                    num = 4m;
                    break;
                case "5":
                    num = 5m;
                    break;
                case "6":
                    num = 6m;
                    break;
                case "7":
                    num = 7m;
                    break;
                case "8":
                    num = 8m;
                    break;
                case "9":
                    num = 9m;
                    break;
                case "A":
                    num = 10m;
                    break;
                case "B":
                    num = 11m;
                    break;
                case "C":
                    num = 12m;
                    break;
                case "D":
                    num = 13m;
                    break;
                case "E":
                    num = 14m;
                    break;
                case "F":
                    num = 15m;
                    break;
            }
            switch (s.Substring(1, 1))
            {
                case "0":
                    num = decimal.Add(decimal.Multiply(num, 16m), 0m);
                    break;
                case "1":
                    num = decimal.Add(decimal.Multiply(num, 16m), 1m);
                    break;
                case "2":
                    num = decimal.Add(decimal.Multiply(num, 16m), 2m);
                    break;
                case "3":
                    num = decimal.Add(decimal.Multiply(num, 16m), 3m);
                    break;
                case "4":
                    num = decimal.Add(decimal.Multiply(num, 16m), 4m);
                    break;
                case "5":
                    num = decimal.Add(decimal.Multiply(num, 16m), 5m);
                    break;
                case "6":
                    num = decimal.Add(decimal.Multiply(num, 16m), 6m);
                    break;
                case "7":
                    num = decimal.Add(decimal.Multiply(num, 16m), 7m);
                    break;
                case "8":
                    num = decimal.Add(decimal.Multiply(num, 16m), 8m);
                    break;
                case "9":
                    num = decimal.Add(decimal.Multiply(num, 16m), 9m);
                    break;
                case "A":
                    num = decimal.Add(decimal.Multiply(num, 16m), 10m);
                    break;
                case "B":
                    num = decimal.Add(decimal.Multiply(num, 16m), 11m);
                    break;
                case "C":
                    num = decimal.Add(decimal.Multiply(num, 16m), 12m);
                    break;
                case "D":
                    num = decimal.Add(decimal.Multiply(num, 16m), 13m);
                    break;
                case "E":
                    num = decimal.Add(decimal.Multiply(num, 16m), 14m);
                    break;
                case "F":
                    num = decimal.Add(decimal.Multiply(num, 16m), 15m);
                    break;
            }
            return num;
        }
        decimal result = default(decimal);
        return result;
    }

    /// <summary>
    /// 将两个字节转换为16位无符号十进制值
    /// </summary>
    /// <param name="by1">高位字节</param>
    /// <param name="by2">低位字节</param>
    /// <returns>16位无符号十进制值</returns>
    public static decimal Word2Value(byte by1, byte by2)
    {
        decimal num = default(decimal);
        num = new decimal(by1);
        return decimal.Add(decimal.Multiply(num, 256m), new decimal(by2));
    }

    /// <summary>
    /// 将两个字节转换为16位有符号补码十进制值
    /// </summary>
    /// <param name="by1">高位字节</param>
    /// <param name="by2">低位字节</param>
    /// <returns>16位有符号补码十进制值</returns>
    public static decimal Word2ValueOfComplement(byte by1, byte by2)
    {
        checked
        {
            decimal result;
            if (by1 > 127)
            {
                by1 ^= 0xFF;
                by2 ^= 0xFF;
                result = new decimal(-(by1 * 256 + by2 + 1));
            }
            else
            {
                result = new decimal(by1 * 256 + by2);
            }
            return result;
        }
    }

    /// <summary>
    /// 将十六进制字符串转换为16位有符号补码整数值
    /// </summary>
    /// <param name="s">十六进制字符串</param>
    /// <returns>16位有符号补码整数值</returns>
    public static int Word2ValueOfComplement(string s)
    {
        string text = asc2bin(s);
        int num = 0;
        int num2 = Convert.ToInt32(text.Substring(0, 1), 2);
        checked
        {
            if (num2 == 1)
            {
                int num3 = text.Length - 1;
                for (int i = 1; i <= num3; i++)
                {
                    num = ((Operators.CompareString(text.Substring(i, 1), "0", TextCompare: false) != 0) ? (2 * num) : (2 * num + 1));
                }
                return -num - 1;
            }
            return Convert.ToInt32(text.Substring(1), 2);
        }
    }
}
