[16:05:42] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1194
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[23:15:04] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理配置无效: 启用TLS/SSL时必须提供证书路径

[23:15:04] [ERR] Admin.Application.MqttBrokerServices.MqttBrokerManagementService 
启动MQTT代理服务时发生错误
System.InvalidOperationException: MQTT代理配置无效: 启用TLS/SSL时必须提供证书路径
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 118
   at Admin.Application.MqttBrokerServices.MqttBrokerManagementService.StartAsync(StartBrokerRequest request, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Application\MqttBrokerServices\MqttBrokerManagementService.cs:line 94

