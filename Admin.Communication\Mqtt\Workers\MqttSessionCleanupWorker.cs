// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Configuration;
using Microsoft.Extensions.Options;

using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Admin.Communication.Mqtt.Workers;

/// <summary>
/// MQTT会话清理后台工作器
/// </summary>
public class MqttSessionCleanupWorker : AsyncPeriodicBackgroundWorkerBase
{
    public MqttSessionCleanupWorker(AbpAsyncTimer timer, IServiceScopeFactory serviceScopeFactory) 
        : base(timer, serviceScopeFactory)
    {
        // 每5分钟执行一次清理任务
        Timer.Period = 1000 * 60 * 5; // 5分钟
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        using var scope = ServiceScopeFactory.CreateScope();
        var logger = scope.ServiceProvider.GetService<ILogger<MqttSessionCleanupWorker>>();
        
        try
        {
            // 获取MQTT连接管理器
            var connectionManager = scope.ServiceProvider.GetService<IMqttConnectionManager>();
            if (connectionManager == null)
            {
                logger?.LogDebug("MQTT连接管理器未注册，跳过会话清理");
                return;
            }

            // 获取MQTT配置
            var mqttOptions = scope.ServiceProvider.GetService<IOptions<MqttBrokerOptions>>();
            if (mqttOptions?.Value?.PersistSessions != true)
            {
                logger?.LogDebug("MQTT会话持久化未启用，跳过会话清理");
                return;
            }

            logger?.LogDebug("开始执行MQTT会话清理任务");

            // 清理超时连接
            await connectionManager.CleanupTimeoutConnectionsAsync(workerContext.CancellationToken);

            // 清理过期会话（内存和数据库）
            await connectionManager.CleanupExpiredSessionsAsync();

            // 清理过期的待发送消息
            await connectionManager.CleanupExpiredPendingMessagesAsync();

            logger?.LogDebug("MQTT会话清理任务执行完成");
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "执行MQTT会话清理任务时发生错误");
        }
    }
}
