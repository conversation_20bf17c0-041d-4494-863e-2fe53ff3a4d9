// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// MQTT会话
/// </summary>
[SugarTable("mqtt_sessions")]
public partial class MqttSessionEntity
{
    /// <summary>
    /// 主键ID（雪花ID）
    /// </summary>
    [SugarColumn(ColumnName = "Id", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [SugarColumn(ColumnName = "ClientId")]
    public string ClientId { get; set; }

    /// <summary>
    /// 是否为清理会话(0-持久会话, 1-清理会话)
    /// </summary>
    [SugarColumn(ColumnName = "CleanSession")]
    public bool CleanSession { get; set; }

    /// <summary>
    /// 会话创建时间
    /// </summary>
    [SugarColumn(ColumnName = "CreatedTime")]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    [SugarColumn(ColumnName = "LastActivityTime")]
    public DateTime LastActivityTime { get; set; }

    /// <summary>
    /// 会话过期时间
    /// </summary>
    [SugarColumn(ColumnName = "ExpiryTime")]
    public DateTime? ExpiryTime { get; set; }

    /// <summary>
    /// 会话状态(1-活跃, 2-暂停, 3-过期)
    /// </summary>
    [SugarColumn(ColumnName = "State")]
    public int State { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    [SugarColumn(ColumnName = "Username")]
    public string Username { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    [SugarColumn(ColumnName = "IpAddress")]
    public string IpAddress { get; set; }

    /// <summary>
    /// 保持连接时间(秒)
    /// </summary>
    [SugarColumn(ColumnName = "KeepAlive")]
    public int KeepAlive { get; set; }

    /// <summary>
    /// MQTT协议版本
    /// </summary>
    [SugarColumn(ColumnName = "ProtocolVersion")]
    public string ProtocolVersion { get; set; }
} 