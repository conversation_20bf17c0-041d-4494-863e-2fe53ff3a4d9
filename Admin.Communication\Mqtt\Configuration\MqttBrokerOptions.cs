using System;
using System.Collections.Generic;
using System.Net;

namespace Admin.Communication.Mqtt.Configuration
{
    /// <summary>
    /// MQTT代理服务器配置选项
    /// </summary>
    public class MqttBrokerOptions
    {
        /// <summary>
        /// 监听端口，默认为1883
        /// </summary>
        public int Port { get; set; } = 1883;

        /// <summary>
        /// 是否启用TLS/SSL，默认为false
        /// </summary>
        public bool UseTls { get; set; } = false;

        /// <summary>
        /// TLS/SSL证书路径，仅当UseTls为true时有效
        /// </summary>
        public string CertificatePath { get; set; }

        /// <summary>
        /// TLS/SSL证书密码，仅当UseTls为true时有效
        /// </summary>
        public string CertificatePassword { get; set; }

        /// <summary>
        /// 最大并发连接数，默认为10000
        /// </summary>
        public int MaxConnections { get; set; } = 10000;

        /// <summary>
        /// 单个IP地址的最大连接数，默认为100
        /// </summary>
        public int MaxConnectionsPerIp { get; set; } = 100;

        /// <summary>
        /// 连接超时时间(秒)，默认为15秒
        /// </summary>
        public int ConnectionTimeout { get; set; } = 15;

        /// <summary>
        /// 是否允许匿名连接，默认为true
        /// </summary>
        public bool AllowAnonymousAccess { get; set; } = true;

        /// <summary>
        /// 默认用户名，当禁用匿名访问且未配置用户列表时使用
        /// </summary>
        public string Username { get; set; } = "admin";

        /// <summary>
        /// 默认密码，当禁用匿名访问且未配置用户列表时使用
        /// </summary>
        public string Password { get; set; } = "password";

        /// <summary>
        /// 是否启用保留消息功能，默认为true
        /// </summary>
        public bool EnableRetainedMessages { get; set; } = true;

        /// <summary>
        /// 是否启用通配符订阅，默认为true
        /// </summary>
        public bool EnableWildcardSubscriptions { get; set; } = true;

        /// <summary>
        /// 最大消息大小(字节)，默认为8192字节(8KB)
        /// </summary>
        public int MaxMessageSize { get; set; } = 8192;

        /// <summary>
        /// 最大主题长度，默认为256字符
        /// </summary>
        public int MaxTopicLength { get; set; } = 256;

        /// <summary>
        /// 最大QoS级别，默认为2
        /// </summary>
        public int MaxQosLevel { get; set; } = 2;

        /// <summary>
        /// 是否持久化会话，默认为true
        /// </summary>
        public bool PersistSessions { get; set; } = true;

        /// <summary>
        /// 是否启用统计功能，默认为true
        /// </summary>
        public bool EnableStatistics { get; set; } = true;

        /// <summary>
        /// 统计数据更新间隔(秒)，默认为60秒
        /// </summary>
        public int StatisticsUpdateInterval { get; set; } = 60;

        /// <summary>
        /// 监听的IP地址，默认为所有地址
        /// </summary>
        public IPAddress BindAddress { get; set; } = IPAddress.Any;

        /// <summary>
        /// 是否启用WebSocket支持，默认为false
        /// </summary>
        public bool EnableWebSockets { get; set; } = false;

        /// <summary>
        /// WebSocket监听端口，默认为8083
        /// </summary>
        public int WebSocketPort { get; set; } = 8083;

        /// <summary>
        /// WebSocket路径，默认为"/mqtt"
        /// </summary>
        public string WebSocketPath { get; set; } = "/mqtt";

        /// <summary>
        /// 认证用户列表，键为用户名，值为密码
        /// </summary>
        public Dictionary<string, string> Users { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 访问控制列表，定义用户对主题的访问权限
        /// </summary>
        public List<MqttAclEntry> AclRules { get; set; } = new List<MqttAclEntry>();

        /// <summary>
        /// 验证配置选项是否有效
        /// </summary>
        /// <returns>验证结果，如果有错误则包含错误信息</returns>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (Port <= 0 || Port > 65535)
            {
                return (false, $"无效的端口号: {Port}，端口号必须在1-65535之间");
            }

            if (UseTls && string.IsNullOrEmpty(CertificatePath))
            {
                return (false, "启用TLS/SSL时必须提供证书路径");
            }

            if (MaxConnections <= 0)
            {
                return (false, $"无效的最大连接数: {MaxConnections}，最大连接数必须大于0");
            }

            if (ConnectionTimeout <= 0)
            {
                return (false, $"无效的连接超时时间: {ConnectionTimeout}，连接超时时间必须大于0");
            }

            if (MaxMessageSize <= 0)
            {
                return (false, $"无效的最大消息大小: {MaxMessageSize}，最大消息大小必须大于0");
            }

            if (MaxTopicLength <= 0)
            {
                return (false, $"无效的最大主题长度: {MaxTopicLength}，最大主题长度必须大于0");
            }

            if (MaxQosLevel < 0 || MaxQosLevel > 2)
            {
                return (false, $"无效的最大QoS级别: {MaxQosLevel}，最大QoS级别必须在0-2之间");
            }

            if (StatisticsUpdateInterval <= 0)
            {
                return (false, $"无效的统计数据更新间隔: {StatisticsUpdateInterval}，统计数据更新间隔必须大于0");
            }

            if (EnableWebSockets)
            {
                if (WebSocketPort <= 0 || WebSocketPort > 65535)
                {
                    return (false, $"无效的WebSocket端口号: {WebSocketPort}，端口号必须在1-65535之间");
                }

                if (string.IsNullOrEmpty(WebSocketPath))
                {
                    return (false, "WebSocket路径不能为空");
                }
            }

            return (true, null);
        }
    }

    /// <summary>
    /// MQTT访问控制列表条目
    /// </summary>
    public class MqttAclEntry
    {
        /// <summary>
        /// 用户名，为null或空表示适用于所有用户
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 客户端ID，为null或空表示适用于所有客户端
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 主题，支持通配符
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// 访问类型
        /// </summary>
        public MqttAccessType AccessType { get; set; }

        /// <summary>
        /// 是否允许访问
        /// </summary>
        public bool Allow { get; set; }
    }

    /// <summary>
    /// MQTT访问类型
    /// </summary>
    public enum MqttAccessType
    {
        /// <summary>
        /// 订阅
        /// </summary>
        Subscribe,

        /// <summary>
        /// 发布
        /// </summary>
        Publish,

        /// <summary>
        /// 订阅和发布
        /// </summary>
        All
    }
} 