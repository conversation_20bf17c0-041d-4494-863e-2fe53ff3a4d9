[11:30:40] [ERR]  
SELECT `Id`,`MessageId`,`SessionId`,`ClientId`,`Topic`,`Payload`,`Qos`,`Retain`,`Duplicate`,`PacketId`,`MessageType`,`Priority`,`CreatedTime`,`ScheduleTime`,`ExpiryTime`,`RetryCount`,`MaxRetryCount`,`LastRetryTime`,`Status`,`ErrorMessage`,`PublisherId`,`AckStatus`,`AckTime` FROM `mqtt_pending_messages`  WHERE (( `ClientId` = @ClientId0 ) AND ( `AckStatus` = @AckStatus1 )) 

[11:30:40] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
获取QoS 2临时消息时发生错误: ClientId=gateWay
MySqlConnector.MySqlException (0x80004005): Unknown column 'AckStatus' in 'field list'
   at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.GetQos2ReceivedMessagesAsync(String clientId) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 2049

[11:39:18] [ERR]  
SELECT `Id`,`MessageId`,`SessionId`,`ClientId`,`Topic`,`Payload`,`Qos`,`Retain`,`Duplicate`,`PacketId`,`MessageType`,`Priority`,`CreatedTime`,`ScheduleTime`,`ExpiryTime`,`RetryCount`,`MaxRetryCount`,`LastRetryTime`,`Status`,`ErrorMessage`,`PublisherId`,`AckStatus`,`AckTime` FROM `mqtt_pending_messages`  WHERE (( `ClientId` = @ClientId0 ) AND ( `AckStatus` = @AckStatus1 )) 

[11:39:18] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
获取QoS 2临时消息时发生错误: ClientId=mqttx_a978efd2_1751513958000
MySqlConnector.MySqlException (0x80004005): Unknown column 'AckStatus' in 'field list'
   at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.GetQos2ReceivedMessagesAsync(String clientId) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 2049

[11:39:18] [ERR]  
INSERT INTO `mqtt_subscriptions`  
           (`Id`,`SessionId`,`ClientId`,`TopicFilter`,`Qos`,`SubscribedTime`,`LastMatchTime`,`MatchCount`,`IsWildcard`,`Status`)
     VALUES
           (@Id,@SessionId,@ClientId,@TopicFilter,@Qos,@SubscribedTime,@LastMatchTime,@MatchCount,@IsWildcard,@Status) ;

[11:39:18] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存会话订阅信息时发生错误: ClientId=mqttx_a978efd2_1751513958000
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0' for key 'PRIMARY'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveSessionSubscriptionsAsync(MqttSession session) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 1003

[14:27:03] [ERR]  
INSERT INTO `mqtt_subscriptions`  
           (`Id`,`SessionId`,`ClientId`,`TopicFilter`,`Qos`,`SubscribedTime`,`LastMatchTime`,`MatchCount`,`IsWildcard`,`Status`)
     VALUES
           (@Id,@SessionId,@ClientId,@TopicFilter,@Qos,@SubscribedTime,@LastMatchTime,@MatchCount,@IsWildcard,@Status) ;

[14:27:03] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存会话订阅信息时发生错误: ClientId=mqttx_a978efd2_1751524023000
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0' for key 'PRIMARY'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveSessionSubscriptionsAsync(MqttSession session) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 1003

[14:51:58] [ERR]  
INSERT INTO `mqtt_subscriptions`  
           (`Id`,`SessionId`,`ClientId`,`TopicFilter`,`Qos`,`SubscribedTime`,`LastMatchTime`,`MatchCount`,`IsWildcard`,`Status`)
     VALUES
           (@Id,@SessionId,@ClientId,@TopicFilter,@Qos,@SubscribedTime,@LastMatchTime,@MatchCount,@IsWildcard,@Status) ;

[14:51:58] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存会话订阅信息时发生错误: ClientId=mqttx_a978efd2_1751525518000
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0-gateway/up' for key 'uk_session_topic'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveSessionSubscriptionsAsync(MqttSession session) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 1004

[14:52:15] [ERR]  
INSERT INTO `mqtt_subscriptions`  
           (`Id`,`SessionId`,`ClientId`,`TopicFilter`,`Qos`,`SubscribedTime`,`LastMatchTime`,`MatchCount`,`IsWildcard`,`Status`)
     VALUES
           (@Id,@SessionId,@ClientId,@TopicFilter,@Qos,@SubscribedTime,@LastMatchTime,@MatchCount,@IsWildcard,@Status) ;

[14:52:15] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存会话订阅信息时发生错误: ClientId=mqttx_a978efd2_1751525535000
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0-gateway/up' for key 'uk_session_topic'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveSessionSubscriptionsAsync(MqttSession session) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 1004

[14:52:29] [ERR]  
INSERT INTO `mqtt_subscriptions`  
           (`Id`,`SessionId`,`ClientId`,`TopicFilter`,`Qos`,`SubscribedTime`,`LastMatchTime`,`MatchCount`,`IsWildcard`,`Status`)
     VALUES
           (@Id,@SessionId,@ClientId,@TopicFilter,@Qos,@SubscribedTime,@LastMatchTime,@MatchCount,@IsWildcard,@Status) ;

[14:52:29] [ERR] Admin.Communication.Mqtt.Services.MqttConnectionManager 
保存会话订阅信息时发生错误: ClientId=mqttx_a978efd2_1751525549000
MySqlConnector.MySqlException (0x80004005): Duplicate entry '0-gateway/up' for key 'uk_session_topic'
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at Admin.Communication.Mqtt.Services.MqttConnectionManager.SaveSessionSubscriptionsAsync(MqttSession session) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttConnectionManager.cs:line 1004

