[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[14:27:17] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[14:27:18] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[14:27:18] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[14:27:18] [INF] Admin.Communication.DeviceOnline.Workers.DeviceOnlineDetectionWorker 
设备在线检测工作器初始化完成: CheckInterval=30s

[14:27:18] [ERR] Admin.Communication.AdminCommunicationModule 
注册MQTT连接事件处理器时发生异常
Autofac.Core.DependencyResolutionException: An exception was thrown while activating Admin.Communication.Mqtt.Services.MqttConnectionManager.
 ---> Autofac.Core.DependencyResolutionException: None of the constructors found on type 'Admin.Communication.Mqtt.Services.MqttConnectionManager' can be invoked with the available services and parameters:
Cannot resolve parameter 'Volo.Abp.BackgroundJobs.IBackgroundJobManager backgroundJobManager' of constructor 'Void .ctor(Microsoft.Extensions.Logging.ILogger`1[Admin.Communication.Mqtt.Services.MqttConnectionManager], Microsoft.Extensions.Options.IOptions`1[Admin.Communication.Mqtt.Configuration.MqttBrokerOptions], SqlSugar.ISqlSugarClient, Volo.Abp.BackgroundJobs.IBackgroundJobManager, Admin.Communication.Mqtt.Abstractions.IMqttUserService)'.

See https://autofac.rtfd.io/help/no-constructors-bindable for more info.
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass14_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Builder.RegistrationBuilder`3.<>c__DisplayClass41_0.<PropertiesAutowired>b__0(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Builder.RegistrationBuilder`3.<>c__DisplayClass39_0.<OnActivated>b__0(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Middleware.CoreEventMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Extensions.DependencyInjection.KeyedServiceMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext context)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest& request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest& request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest& request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest& request)
   at Autofac.Core.Lifetime.LifetimeScope.Autofac.IComponentContext.ResolveComponent(ResolveRequest& request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.Resolve(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.Resolve(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetRequiredService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Admin.Communication.AdminCommunicationModule.RegisterMqttConnectionEventHandlers(ApplicationInitializationContext context) in D:\code projects\purest-admin-main\api\Admin.Communication\AdminCommunicationModule.cs:line 166

[14:27:18] [INF]  
项目当前环境为：Development

[14:27:18] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[14:27:19] [INF] WorkflowCore.Services.WorkflowHost 
Stopping background tasks

[14:27:19] [INF] WorkflowCore.Services.WorkflowHost 
Worker tasks stopped

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[14:30:04] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[14:30:04] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[14:30:05] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[14:30:05] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[14:30:05] [INF] Admin.Communication.DeviceOnline.Workers.DeviceOnlineDetectionWorker 
设备在线检测工作器初始化完成: CheckInterval=30s

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[14:30:05] [INF] Admin.Communication.AdminCommunicationModule 
MQTT连接事件处理器注册成功

[14:30:05] [INF]  
项目当前环境为：Development

[14:30:05] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[14:30:05] [INF] Admin.Communication.Mqtt.Services.ControlResponseHandler 
控制响应处理器已启动，定时清理间隔：1分钟

[14:30:05] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[14:30:05] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[14:30:05] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[14:30:05] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[14:30:05] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[14:30:05] [INF] Admin.Communication.Mqtt.Processors.ControlResponseMessageProcessor 
控制响应消息处理器已初始化

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: ControlResponseMessageProcessor (控制响应消息处理器 - 处理设备控制指令的响应消息), 优先级: 100

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 2 个处理器

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:46633

[14:30:05] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=123123

[14:30:05] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[14:30:28] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[14:30:28] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[14:30:29] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[14:30:29] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[14:30:29] [INF] Admin.Communication.DeviceOnline.Workers.DeviceOnlineDetectionWorker 
设备在线检测工作器初始化完成: CheckInterval=30s

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[14:30:29] [INF] Admin.Communication.AdminCommunicationModule 
MQTT连接事件处理器注册成功

[14:30:29] [INF]  
项目当前环境为：Development

[14:30:29] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[14:30:29] [INF] Admin.Communication.Mqtt.Services.ControlResponseHandler 
控制响应处理器已启动，定时清理间隔：1分钟

[14:30:29] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[14:30:29] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[14:30:29] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report, /devices/+/modbus/command/up

[14:30:29] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[14:30:29] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[14:30:29] [INF] Admin.Communication.Mqtt.Processors.ControlResponseMessageProcessor 
控制响应消息处理器已初始化

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: ControlResponseMessageProcessor (控制响应消息处理器 - 处理设备控制指令的响应消息), 优先级: 100

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 2 个处理器

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: 123123, IP: *************:46642

[14:30:29] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=123123

[14:30:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=123123, IP=*************, SessionPresent=false

[14:30:46] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:31:00] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:31:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[14:31:00] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[14:31:00] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[14:31:00] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[14:31:12] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:31:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[14:31:12] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[14:31:12] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[14:31:12] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

[14:31:26] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[14:31:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端 QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2 已存在连接，替换旧连接

[14:31:26] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[14:31:26] [INF] Admin.Communication.AdminCommunicationModule 
设备连接状态变化: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[14:31:26] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="ReplacedByNewConnection"

