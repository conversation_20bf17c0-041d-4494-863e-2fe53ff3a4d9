[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[14:27:17] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[14:27:17] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[14:27:18] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[14:27:18] [INF] Admin.Communication.Modbus.Workers.ModbusInstructionSchedulerService 
Modbus指令调度服务初始化完成: Interval=1000ms, MaxDevices=10

[14:27:18] [INF] Admin.Communication.DeviceOnline.Workers.DeviceOnlineDetectionWorker 
设备在线检测工作器初始化完成: CheckInterval=30s

[14:27:18] [ERR] Admin.Communication.AdminCommunicationModule 
注册MQTT连接事件处理器时发生异常
Autofac.Core.DependencyResolutionException: An exception was thrown while activating Admin.Communication.Mqtt.Services.MqttConnectionManager.
 ---> Autofac.Core.DependencyResolutionException: None of the constructors found on type 'Admin.Communication.Mqtt.Services.MqttConnectionManager' can be invoked with the available services and parameters:
Cannot resolve parameter 'Volo.Abp.BackgroundJobs.IBackgroundJobManager backgroundJobManager' of constructor 'Void .ctor(Microsoft.Extensions.Logging.ILogger`1[Admin.Communication.Mqtt.Services.MqttConnectionManager], Microsoft.Extensions.Options.IOptions`1[Admin.Communication.Mqtt.Configuration.MqttBrokerOptions], SqlSugar.ISqlSugarClient, Volo.Abp.BackgroundJobs.IBackgroundJobManager, Admin.Communication.Mqtt.Abstractions.IMqttUserService)'.

See https://autofac.rtfd.io/help/no-constructors-bindable for more info.
   at Autofac.Core.Activators.Reflection.ReflectionActivator.<>c__DisplayClass14_0.<UseSingleConstructorActivation>b__0(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.DisposalTrackingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Builder.RegistrationBuilder`3.<>c__DisplayClass41_0.<PropertiesAutowired>b__0(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Middleware.DelegateMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Builder.RegistrationBuilder`3.<>c__DisplayClass39_0.<OnActivated>b__0(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Middleware.CoreEventMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   --- End of inner exception stack trace ---
   at Autofac.Core.Resolving.Middleware.ActivatorErrorHandlingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.RegistrationPipelineInvokeMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.<>c__DisplayClass5_0.<Execute>b__0()
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid id, Func`1 creator)
   at Autofac.Core.Lifetime.LifetimeScope.CreateSharedInstance(Guid primaryId, Nullable`1 qualifyingId, Func`1 creator)
   at Autofac.Core.Resolving.Middleware.SharingMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Resolving.Middleware.CircularDependencyDetectorMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Extensions.DependencyInjection.KeyedServiceMiddleware.Execute(ResolveRequestContext context, Action`1 next)
   at Autofac.Core.Resolving.Pipeline.ResolvePipelineBuilder.<>c__DisplayClass14_0.<BuildPipeline>b__1(ResolveRequestContext context)
   at Autofac.Core.Pipeline.ResolvePipeline.Invoke(ResolveRequestContext context)
   at Autofac.Core.Resolving.ResolveOperation.GetOrCreateInstance(ISharingLifetimeScope currentOperationScope, ResolveRequest& request)
   at Autofac.Core.Resolving.ResolveOperation.ExecuteOperation(ResolveRequest& request)
   at Autofac.Core.Resolving.ResolveOperation.Execute(ResolveRequest& request)
   at Autofac.Core.Lifetime.LifetimeScope.ResolveComponent(ResolveRequest& request)
   at Autofac.Core.Lifetime.LifetimeScope.Autofac.IComponentContext.ResolveComponent(ResolveRequest& request)
   at Autofac.ResolutionExtensions.TryResolveService(IComponentContext context, Service service, IEnumerable`1 parameters, Object& instance)
   at Autofac.ResolutionExtensions.ResolveService(IComponentContext context, Service service, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.Resolve(IComponentContext context, Type serviceType, IEnumerable`1 parameters)
   at Autofac.ResolutionExtensions.Resolve(IComponentContext context, Type serviceType)
   at Autofac.Extensions.DependencyInjection.AutofacServiceProvider.GetRequiredService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Admin.Communication.AdminCommunicationModule.RegisterMqttConnectionEventHandlers(ApplicationInitializationContext context) in D:\code projects\purest-admin-main\api\Admin.Communication\AdminCommunicationModule.cs:line 166

[14:27:18] [INF]  
项目当前环境为：Development

[14:27:18] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[14:27:19] [INF] WorkflowCore.Services.WorkflowHost 
Stopping background tasks

[14:27:19] [INF] WorkflowCore.Services.WorkflowHost 
Worker tasks stopped

