// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System.Security.Cryptography;
using System.Text;

namespace Admin.Core.DataEncryption.Encryptions;

/// <summary>
/// 密钥生成工具类
/// </summary>
public static class SecretKeyGeneration
{
    /// <summary>
    /// 生成设备密钥
    /// </summary>
    /// <param name="length">密钥长度，默认48位</param>
    /// <returns>设备密钥</returns>
    public static string GenerateDeviceSecret(int length = 48)
    {
        if (length <= 0)
            throw new ArgumentException("密钥长度必须大于0", nameof(length));

        // 使用两个GUID生成48位随机字符串
        if (length <= 32)
        {
            return Guid.NewGuid().ToString("N")[..length];
        }
        else if (length <= 64)
        {
            var guid1 = Guid.NewGuid().ToString("N");
            var guid2 = Guid.NewGuid().ToString("N");
            return (guid1 + guid2)[..length];
        }
        else
        {
            // 对于更长的密钥，使用加密安全的随机数生成器
            return GenerateSecureRandomString(length);
        }
    }

    /// <summary>
    /// 生成API密钥
    /// </summary>
    /// <param name="length">密钥长度，默认32位</param>
    /// <returns>API密钥</returns>
    public static string GenerateApiKey(int length = 32)
    {
        return GenerateSecureRandomString(length, includeSpecialChars: false);
    }

    /// <summary>
    /// 生成随机密码
    /// </summary>
    /// <param name="length">密码长度，默认12位</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机密码</returns>
    public static string GenerateRandomPassword(int length = 12, bool includeSpecialChars = true)
    {
        return GenerateSecureRandomString(length, includeSpecialChars);
    }

    /// <summary>
    /// 生成数字验证码
    /// </summary>
    /// <param name="length">验证码长度，默认6位</param>
    /// <returns>数字验证码</returns>
    public static string GenerateNumericCode(int length = 6)
    {
        if (length <= 0)
            throw new ArgumentException("验证码长度必须大于0", nameof(length));

        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);

        var result = new StringBuilder(length);
        for (int i = 0; i < length; i++)
        {
            result.Append(bytes[i] % 10);
        }

        return result.ToString();
    }

    /// <summary>
    /// 生成加密安全的随机字符串
    /// </summary>
    /// <param name="length">字符串长度</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机字符串</returns>
    private static string GenerateSecureRandomString(int length, bool includeSpecialChars = false)
    {
        if (length <= 0)
            throw new ArgumentException("字符串长度必须大于0", nameof(length));

        const string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string lowerCase = "abcdefghijklmnopqrstuvwxyz";
        const string digits = "0123456789";
        const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        var chars = upperCase + lowerCase + digits;
        if (includeSpecialChars)
        {
            chars += specialChars;
        }

        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);

        var result = new StringBuilder(length);
        for (int i = 0; i < length; i++)
        {
            result.Append(chars[bytes[i] % chars.Length]);
        }

        return result.ToString();
    }

    /// <summary>
    /// 生成UUID格式的密钥
    /// </summary>
    /// <param name="format">UUID格式 (N, D, B, P)</param>
    /// <returns>UUID密钥</returns>
    public static string GenerateUuidKey(string format = "N")
    {
        return Guid.NewGuid().ToString(format);
    }

    /// <summary>
    /// 生成带前缀的密钥
    /// </summary>
    /// <param name="prefix">前缀</param>
    /// <param name="length">密钥部分长度，默认32位</param>
    /// <param name="separator">分隔符，默认下划线</param>
    /// <returns>带前缀的密钥</returns>
    public static string GeneratePrefixedKey(string prefix, int length = 32, string separator = "_")
    {
        if (string.IsNullOrWhiteSpace(prefix))
            throw new ArgumentException("前缀不能为空", nameof(prefix));

        var key = GenerateSecureRandomString(length);
        return $"{prefix}{separator}{key}";
    }
}
