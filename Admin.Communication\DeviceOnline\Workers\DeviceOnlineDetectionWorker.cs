// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.DeviceOnline.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Admin.Communication.DeviceOnline.Workers;

/// <summary>
/// 设备在线检测后台工作器
/// 负责定期检测直连设备和网关设备的在线状态，并处理相关告警和状态同步
/// </summary>
public class DeviceOnlineDetectionWorker : AsyncPeriodicBackgroundWorkerBase
{
    private readonly ILogger<DeviceOnlineDetectionWorker> _logger;
    private readonly IConfiguration _configuration;
    private readonly DeviceOnlineDetectionService.DetectionConfig _detectionConfig;

    public DeviceOnlineDetectionWorker(
        AbpAsyncTimer timer,
        IServiceScopeFactory serviceScopeFactory,
        IConfiguration configuration,
        ILogger<DeviceOnlineDetectionWorker> logger)
        : base(timer, serviceScopeFactory)
    {
        _logger = logger;
        _configuration = configuration;

        // 从配置文件读取检测参数
        _detectionConfig = new DeviceOnlineDetectionService.DetectionConfig
        {
            CheckIntervalSeconds = _configuration.GetValue<int>("DeviceOnlineDetection:CheckInterval", 30)
        };

        // 设置工作器执行间隔
        Timer.Period = _detectionConfig.CheckIntervalSeconds * 1000; // 转换为毫秒

        _logger.LogInformation("设备在线检测工作器初始化完成: CheckInterval={CheckInterval}s",
            _detectionConfig.CheckIntervalSeconds);
    }

    /// <summary>
    /// 执行设备在线检测任务
    /// </summary>
    /// <param name="workerContext">工作器上下文</param>
    /// <returns>执行任务</returns>
    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        try
        {
            using var scope = workerContext.ServiceProvider.CreateScope();
            var detectionService = scope.ServiceProvider.GetRequiredService<DeviceOnlineDetectionService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<DeviceOnlineDetectionWorker>>();

            logger.LogDebug("开始执行设备在线检测任务");

            // 1. 执行设备在线检测
            var detectionResults = await detectionService.ExecuteDetectionAsync(
                _detectionConfig, 
                workerContext.CancellationToken);

            if (detectionResults.Count == 0)
            {
                logger.LogDebug("没有需要检测的设备");
                return;
            }

            // 2. 处理检测结果 - 更新设备状态和处理告警
            await detectionService.ProcessDetectionResultsAsync(
                detectionResults,
                workerContext.CancellationToken);

            logger.LogDebug("设备在线检测任务执行完成，共处理 {Count} 个设备", detectionResults.Count);

            logger.LogDebug("设备在线检测任务执行完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行设备在线检测任务时发生异常");
        }
    }
}
