using Admin.Communication.Mqtt.Abstractions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Admin.Communication.Mqtt.Services
{
    /// <summary>
    /// MQTT代理托管服务，用于自动启动和停止MQTT代理
    /// </summary>
    public class MqttBrokerHostedService : IHostedService
    {
        private readonly IMqttBroker _mqttBroker;
        private readonly ILogger<MqttBrokerHostedService> _logger;

        /// <summary>
        /// 初始化MQTT代理托管服务
        /// </summary>
        /// <param name="mqttBroker">MQTT代理</param>
        /// <param name="logger">日志记录器</param>
        public MqttBrokerHostedService(IMqttBroker mqttBroker, ILogger<MqttBrokerHostedService> logger)
        {
            _mqttBroker = mqttBroker ?? throw new ArgumentNullException(nameof(mqttBroker));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("正在启动MQTT代理服务...");
            
            try
            {
                await _mqttBroker.StartAsync(cancellationToken: cancellationToken);
                _logger.LogInformation("MQTT代理服务已成功启动，监听端口: {Port}", _mqttBroker.Port);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动MQTT代理服务时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("正在停止MQTT代理服务...");
            
            try
            {
                await _mqttBroker.StopAsync(cancellationToken);
                _logger.LogInformation("MQTT代理服务已成功停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止MQTT代理服务时发生错误");
            }
        }
    }
} 