using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Configuration;
using Admin.SqlSugar.Entity.Business.LOT;
using Admin.Multiplex.Contracts.Enums.Mqtt;
using Admin.Communication.Mqtt.Models.Results;
using Admin.Communication.Mqtt.Models.Statistics;
using Admin.Communication.Mqtt.Events;

namespace Admin.Communication.Mqtt.Abstractions
{
    /// <summary>
    /// MQTT连接管理器接口
    /// </summary>
    public interface IMqttConnectionManager : IDisposable
    {
        #region 连接生命周期管理

        /// <summary>
        /// 添加新的客户端连接
        /// </summary>
        /// <param name="connection">客户端连接</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接添加结果</returns>
        Task<ConnectionResult> AddConnectionAsync(MqttClientConnection connection, CancellationToken cancellationToken = default);

        /// <summary>
        /// 移除客户端连接
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="reason">断开原因</param>
        /// <param name="sendDisconnect">是否发送断开消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>移除任务</returns>
        Task RemoveConnectionAsync(string clientId, DisconnectReasonEnum reason = DisconnectReasonEnum.NormalDisconnection, bool sendDisconnect = true, CancellationToken cancellationToken = default);

        /// <summary>
        /// 更新连接活动时间
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        void UpdateConnectionActivity(string clientId);

        /// <summary>
        /// 检查并清理超时连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>清理任务</returns>
        Task CleanupTimeoutConnectionsAsync(CancellationToken cancellationToken = default);

        #endregion

        #region 连接池管理

        /// <summary>
        /// 获取客户端连接
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>客户端连接，如果不存在则返回null</returns>
        MqttClientConnection GetConnection(string clientId);

        /// <summary>
        /// 检查客户端是否已连接
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>是否已连接</returns>
        bool IsConnected(string clientId);

        /// <summary>
        /// 获取所有活跃连接
        /// </summary>
        /// <returns>活跃连接列表</returns>
        IReadOnlyList<MqttClientConnection> GetAllConnections();

        /// <summary>
        /// 获取连接数量
        /// </summary>
        /// <returns>连接数量</returns>
        int GetConnectionCount();

        #endregion

        #region 连接限制和控制

        /// <summary>
        /// 检查是否可以建立新连接
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>连接检查结果</returns>
        ConnectionValidationResult ValidateNewConnection(string clientId, string ipAddress);

        /// <summary>
        /// 设置最大连接数
        /// </summary>
        /// <param name="maxConnections">最大连接数</param>
        void SetMaxConnections(int maxConnections);

        /// <summary>
        /// 设置单个IP最大连接数
        /// </summary>
        /// <param name="maxConnectionsPerIp">单个IP最大连接数</param>
        void SetMaxConnectionsPerIp(int maxConnectionsPerIp);

        /// <summary>
        /// 添加客户端ID到黑名单
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        void AddToBlacklist(string clientId);

        /// <summary>
        /// 从黑名单移除客户端ID
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        void RemoveFromBlacklist(string clientId);

        #endregion

        #region 连接认证和授权

        /// <summary>
        /// 异步验证客户端连接凭据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>认证结果</returns>
        Task<AuthenticationResult> ValidateCredentialsAsync(string clientId, string username, string password, string ipAddress);

        /// <summary>
        /// 检查客户端是否有连接权限
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>授权结果</returns>
        AuthorizationResult CheckConnectionPermission(string clientId, string ipAddress);

        /// <summary>
        /// 检查客户端是否有发布主题的权限
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="username">用户名</param>
        /// <param name="topic">主题</param>
        /// <returns>授权结果</returns>
        AuthorizationResult CheckPublishPermission(string clientId, string username, string topic);

        /// <summary>
        /// 检查客户端是否有订阅主题的权限
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="username">用户名</param>
        /// <param name="topic">主题</param>
        /// <returns>授权结果</returns>
        AuthorizationResult CheckSubscribePermission(string clientId, string username, string topic);

        /// <summary>
        /// 检查客户端对主题的访问权限
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="username">用户名</param>
        /// <param name="topic">主题</param>
        /// <param name="accessType">访问类型</param>
        /// <returns>授权结果</returns>
        AuthorizationResult CheckTopicPermission(string clientId, string username, string topic, MqttAccessTypeEnum accessType);

        #endregion

        #region 连接监控和统计

        /// <summary>
        /// 获取连接统计信息
        /// </summary>
        /// <returns>连接统计</returns>
        ConnectionStatistics GetConnectionStatistics();

        /// <summary>
        /// 获取指定IP的连接数
        /// </summary>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>连接数</returns>
        int GetConnectionCountByIp(string ipAddress);

        /// <summary>
        /// 获取客户端详细信息
        /// </summary>
        /// <returns>客户端信息列表</returns>
        IReadOnlyList<MqttClientInfo> GetClientInfos();

        #endregion

        #region 连接事件管理

        /// <summary>
        /// 客户端连接事件
        /// </summary>
        event EventHandler<MqttClientConnectedEventArgs> ClientConnected;

        /// <summary>
        /// 客户端断开连接事件
        /// </summary>
        event EventHandler<MqttClientDisconnectedEventArgs> ClientDisconnected;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 连接超时事件
        /// </summary>
        event EventHandler<ConnectionTimeoutEventArgs> ConnectionTimeout;

        #endregion

        #region 会话管理

        /// <summary>
        /// 创建或获取客户端会话
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="cleanSession">是否清除会话</param>
        /// <returns>会话信息</returns>
        Task<MqttSession> GetOrCreateSessionAsync(string clientId, bool cleanSession);

        /// <summary>
        /// 清理客户端会话
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>清理任务</returns>
        Task CleanupSessionAsync(string clientId);

        /// <summary>
        /// 检查客户端是否有持久会话
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>是否有持久会话</returns>
        bool HasPersistentSession(string clientId);

        /// <summary>
        /// 保存会话状态
        /// </summary>
        /// <param name="session">会话信息</param>
        /// <returns>保存任务</returns>
        Task SaveSessionAsync(MqttSession session);

        /// <summary>
        /// 保存会话的待发送消息
        /// </summary>
        /// <param name="session">会话信息</param>
        /// <returns>保存任务</returns>
        Task SaveSessionPendingMessagesAsync(MqttSession session);

        /// <summary>
        /// 从数据库加载持久会话
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>会话信息，如果不存在则返回null</returns>
        Task<MqttSession> LoadSessionFromDatabaseAsync(string clientId);

        /// <summary>
        /// 清理过期会话（内存和数据库）
        /// 供后台服务调用
        /// </summary>
        Task CleanupExpiredSessionsAsync();

        #endregion

        #region 待发送消息管理

        /// <summary>
        /// 更新待发送消息状态
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="status">新状态</param>
        /// <param name="errorMessage">错误信息（可选）</param>
        Task UpdatePendingMessageStatusAsync(string messageId, int status, string? errorMessage = null);

        /// <summary>
        /// 增加消息重试次数
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="errorMessage">错误信息</param>
        Task IncrementMessageRetryCountAsync(string messageId, string? errorMessage = null);

        /// <summary>
        /// 更新消息确认状态
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="packetId">包ID</param>
        /// <param name="ackStatus">确认状态</param>
        /// <param name="messageStatus">消息状态（可选）</param>
        Task UpdateMessageAckStatusAsync(string clientId, ushort packetId, MqttMessageAckStatusEnum ackStatus, MqttMessageStatusEnum? messageStatus = null);

        /// <summary>
        /// 清理过期的待发送消息
        /// </summary>
        Task CleanupExpiredPendingMessagesAsync();

        /// <summary>
        /// 获取客户端的待发送消息统计
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>消息统计信息</returns>
        Task<PendingMessageStatistics> GetPendingMessageStatisticsAsync(string clientId);

        #endregion

        #region QoS 2消息管理

        /// <summary>
        /// 保存QoS 2临时存储的消息到数据库
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="qos2Messages">QoS 2消息字典</param>
        Task SaveQos2ReceivedMessagesAsync(string clientId, IReadOnlyDictionary<ushort, MqttMessage> qos2Messages);

        /// <summary>
        /// 从数据库加载QoS 2临时消息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>QoS 2消息字典</returns>
        Task<Dictionary<ushort, MqttMessage>> LoadQos2ReceivedMessagesAsync(string clientId);

        /// <summary>
        /// 清理QoS 2临时消息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="messageId">消息ID（可选，如果不指定则清理所有）</param>
        Task CleanupQos2ReceivedMessagesAsync(string clientId, ushort? messageId = null);

        /// <summary>
        /// 获取QoS 2临时消息（用于恢复）
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>QoS 2消息列表</returns>
        Task<List<MqttPublishMessage>> GetQos2ReceivedMessagesAsync(string clientId);

        #endregion
    }
} 