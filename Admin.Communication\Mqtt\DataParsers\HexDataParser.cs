using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Admin.Multiplex.Contracts.Enums;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.DataParsers
{
    /// <summary>
    /// HEX数据解析器
    /// </summary>
    public class HexDataParser : IDataParser
    {
        private readonly ILogger<HexDataParser> _logger;

        /// <summary>
        /// 支持的数据格式
        /// </summary>
        public DataFormatEnum SupportedFormat => DataFormatEnum.Hex;

        /// <summary>
        /// 解析器名称
        /// </summary>
        public string Name => "HexDataParser";

        /// <summary>
        /// 解析器描述
        /// </summary>
        public string Description => "HEX格式数据解析器，支持十六进制字符串和二进制数据解析，包括Modbus RTU协议";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public HexDataParser(ILogger<HexDataParser> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 是否可以解析指定的载荷数据
        /// </summary>
        /// <param name="payload">载荷数据</param>
        /// <param name="context">解析上下文</param>
        /// <returns>是否可以解析</returns>
        public bool CanParse(byte[] payload, DeviceParseContext context)
        {
            if (payload == null || payload.Length == 0)
                return false;

            // 检查是否为二进制数据或HEX字符串
            try
            {
                // 尝试作为HEX字符串解析
                var hexString = Encoding.UTF8.GetString(payload);
                if (IsValidHexString(hexString))
                    return true;

                // 直接作为二进制数据处理
                return payload.Length >= 4; // 至少需要4字节的数据
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查HEX格式时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 异步解析设备数据
        /// </summary>
        /// <param name="payload">载荷数据</param>
        /// <param name="context">解析上下文</param>
        /// <returns>解析结果</returns>
        public async Task<DeviceDataParseResult> ParseAsync(byte[] payload, DeviceParseContext context)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogDebug("开始解析HEX数据: 设备={DeviceId}, 载荷大小={PayloadSize}字节",
                    context.Device.DeviceId, payload?.Length ?? 0);

                if (!CanParse(payload, context))
                {
                    return DeviceDataParseResult.Failure("载荷数据不是有效的HEX格式");
                }

                byte[] binaryData;
                string rawData;

                // 判断是HEX字符串还是二进制数据
                var payloadString = Encoding.UTF8.GetString(payload!);
                if (IsValidHexString(payloadString))
                {
                    // HEX字符串，转换为二进制
                    binaryData = ConvertHexStringToBytes(payloadString);
                    rawData = payloadString;
                    _logger.LogDebug("检测到HEX字符串格式: {HexString}", payloadString);
                }
                else
                {
                    // 直接是二进制数据
                    binaryData = payload;
                    rawData = Convert.ToHexString(payload);
                    _logger.LogDebug("检测到二进制数据格式，转换为HEX: {HexString}", rawData);
                }

                // 根据协议类型选择解析方式
                List<DeviceDataItem> dataItems;
                if (IsModbusRtuData(binaryData))
                {
                    dataItems = await ParseModbusRtuDataAsync(binaryData, context);
                }
                else
                {
                    dataItems = await ParseGenericHexDataAsync(binaryData, context);
                }

                stopwatch.Stop();

                _logger.LogInformation("HEX数据解析完成: 设备={DeviceId}, 解析出{Count}个属性, 耗时={ElapsedMs}ms",
                    context.Device.DeviceId, dataItems.Count, stopwatch.ElapsedMilliseconds);

                return DeviceDataParseResult.Success(dataItems, rawData);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "解析HEX数据时发生异常: 设备={DeviceId}", context.Device.DeviceId);
                return DeviceDataParseResult.Failure($"解析异常: {ex.Message}", ex);
            }
            finally
            {
                if (stopwatch.IsRunning)
                    stopwatch.Stop();
            }
        }

        /// <summary>
        /// 解析Modbus RTU数据
        /// </summary>
        private async Task<List<DeviceDataItem>> ParseModbusRtuDataAsync(byte[] data, DeviceParseContext context)
        {
            _logger.LogDebug("解析Modbus RTU数据: 设备={DeviceId}, 数据长度={Length}",
                context.Device.DeviceId, data.Length);

            var dataItems = new List<DeviceDataItem>();

            try
            {
                // Modbus RTU响应格式: [设备地址][功能码][数据长度][数据...][CRC16]
                if (data.Length < 5) // 最小长度：地址(1) + 功能码(1) + 数据长度(1) + 数据(至少1) + CRC(2)
                {
                    _logger.LogWarning("Modbus RTU数据长度不足: {Length}", data.Length);
                    return dataItems;
                }

                var deviceAddress = data[0];
                var functionCode = data[1];
                var dataLength = data[2];

                _logger.LogDebug("Modbus RTU解析: 设备地址={Address}, 功能码={FunctionCode}, 数据长度={DataLength}",
                    deviceAddress, functionCode, dataLength);

                // 验证数据长度
                if (data.Length < 3 + dataLength + 2)
                {
                    _logger.LogWarning("Modbus RTU数据长度不匹配: 期望={Expected}, 实际={Actual}",
                        3 + dataLength + 2, data.Length);
                    return dataItems;
                }

                // 提取数据部分（跳过地址、功能码、长度，去掉CRC）
                var registerData = new byte[dataLength];
                Array.Copy(data, 3, registerData, 0, dataLength);

                // 根据模型属性配置解析寄存器数据
                dataItems = await ParseModbusRegistersAsync(registerData, context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析Modbus RTU数据时发生异常");
            }

            await Task.CompletedTask;
            return dataItems;
        }

        /// <summary>
        /// 解析Modbus寄存器数据
        /// </summary>
        private async Task<List<DeviceDataItem>> ParseModbusRegistersAsync(byte[] registerData, DeviceParseContext context)
        {
            var dataItems = new List<DeviceDataItem>();

            // 简化的Modbus解析：按顺序解析寄存器数据
            var registerIndex = 0;
            foreach (var modelProperty in context.ModelProperties.OrderBy(p => p.Sort))
            {
                try
                {
                    // 每个属性占用2字节（1个寄存器）
                    var offset = registerIndex * 2;
                    var length = 2;

                    if (offset + length > registerData.Length)
                    {
                        _logger.LogWarning("寄存器数据不足: 属性={PropertyName}, 需要偏移={Offset}, 数据长度={Length}",
                            modelProperty.Name, offset, registerData.Length);
                        break;
                    }

                    // 提取寄存器值
                    var value = ExtractRegisterValue(registerData, offset, length, modelProperty.DataType);

                    var dataItem = new DeviceDataItem
                    {
                        PropertyKey = modelProperty.JsonKey,
                        PropertyName = modelProperty.Name,
                        PropertyId = modelProperty.Id,
                        Value = value,
                        DataType = modelProperty.DataType,
                        Unit = modelProperty.Unit ?? string.Empty,
                        IsDefined = true,
                        DataTime = DateTime.Now
                    };

                    dataItems.Add(dataItem);

                    _logger.LogDebug("✓ Modbus属性: {PropertyName} = {Value} [寄存器索引={Index}]",
                        modelProperty.Name, value, registerIndex);

                    registerIndex++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析Modbus属性失败: {PropertyName}", modelProperty.Name);
                    registerIndex++;
                }
            }

            await Task.CompletedTask;
            return dataItems;
        }

        /// <summary>
        /// 解析通用HEX数据
        /// </summary>
        private async Task<List<DeviceDataItem>> ParseGenericHexDataAsync(byte[] data, DeviceParseContext context)
        {
            _logger.LogDebug("解析通用HEX数据: 设备={DeviceId}, 数据长度={Length}",
                context.Device.DeviceId, data.Length);

            var dataItems = new List<DeviceDataItem>();

            // 简单的通用解析：将整个数据作为一个属性
            var hexString = Convert.ToHexString(data);
            
            var dataItem = new DeviceDataItem
            {
                PropertyKey = "hex_data",
                PropertyName = "HEX数据",
                Value = hexString,
                DataType = 4, // 字符串类型
                Unit = string.Empty,
                IsDefined = false,
                DataTime = DateTime.Now
            };

            dataItems.Add(dataItem);

            _logger.LogDebug("✓ 通用HEX数据: {HexString}", hexString);

            await Task.CompletedTask;
            return dataItems;
        }

        /// <summary>
        /// 提取寄存器值
        /// </summary>
        private object? ExtractRegisterValue(byte[] data, int offset, int length, int dataType)
        {
            try
            {
                return dataType switch
                {
                    1 => BitConverter.ToInt16(data, offset),        // int整形 (16位)
                    2 => BitConverter.ToInt32(data, offset),        // long长整型 (32位)
                    3 => BitConverter.ToSingle(data, offset),       // decimal小数 (32位浮点)
                    4 => Encoding.UTF8.GetString(data, offset, length), // string字符串
                    8 => data[offset] != 0,                         // boolean布尔
                    _ => BitConverter.ToInt16(data, offset)         // 默认16位整数
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取寄存器值失败，数据类型: {DataType}, 偏移: {Offset}, 长度: {Length}",
                    dataType, offset, length);
                return 0;
            }
        }

        /// <summary>
        /// 检查是否为有效的HEX字符串
        /// </summary>
        private bool IsValidHexString(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;

            // 移除空格和常见分隔符
            var cleanInput = input.Replace(" ", "").Replace("-", "").Replace(":", "");

            // 检查长度是否为偶数
            if (cleanInput.Length % 2 != 0)
                return false;

            // 检查是否只包含十六进制字符
            return cleanInput.All(c => "0123456789ABCDEFabcdef".Contains(c));
        }

        /// <summary>
        /// 将HEX字符串转换为字节数组
        /// </summary>
        private byte[] ConvertHexStringToBytes(string hexString)
        {
            var cleanHex = hexString.Replace(" ", "").Replace("-", "").Replace(":", "");
            var bytes = new byte[cleanHex.Length / 2];

            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
            }

            return bytes;
        }

        /// <summary>
        /// 检查是否为Modbus RTU数据
        /// </summary>
        private bool IsModbusRtuData(byte[] data)
        {
            if (data.Length < 5)
                return false;

            // 简单的Modbus RTU检测：检查功能码是否在有效范围内
            var functionCode = data[1];
            return functionCode >= 1 && functionCode <= 127;
        }
    }
}
