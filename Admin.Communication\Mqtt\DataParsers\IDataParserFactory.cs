using System;
using System.Collections.Generic;
using System.Linq;
using Admin.Multiplex.Contracts.Enums;

namespace Admin.Communication.Mqtt.DataParsers
{
    /// <summary>
    /// 数据解析器工厂接口
    /// </summary>
    public interface IDataParserFactory
    {
        /// <summary>
        /// 根据数据格式获取解析器
        /// </summary>
        /// <param name="dataFormat">数据格式</param>
        /// <returns>数据解析器</returns>
        IDataParser? GetParser(DataFormatEnum dataFormat);

        /// <summary>
        /// 注册解析器
        /// </summary>
        /// <param name="parser">解析器实例</param>
        void RegisterParser(IDataParser parser);

        /// <summary>
        /// 获取所有已注册的解析器
        /// </summary>
        /// <returns>解析器列表</returns>
        IEnumerable<IDataParser> GetAllParsers();

        /// <summary>
        /// 检查是否支持指定的数据格式
        /// </summary>
        /// <param name="dataFormat">数据格式</param>
        /// <returns>是否支持</returns>
        bool IsSupported(DataFormatEnum dataFormat);
    }

    /// <summary>
    /// 数据解析器工厂实现
    /// </summary>
    public class DataParserFactory : IDataParserFactory
    {
        private readonly Dictionary<DataFormatEnum, IDataParser> _parsers = new();
        private readonly object _lock = new object();

        /// <summary>
        /// 根据数据格式获取解析器
        /// </summary>
        /// <param name="dataFormat">数据格式</param>
        /// <returns>数据解析器</returns>
        public IDataParser? GetParser(DataFormatEnum dataFormat)
        {
            lock (_lock)
            {
                return _parsers.TryGetValue(dataFormat, out var parser) ? parser : null;
            }
        }

        /// <summary>
        /// 注册解析器
        /// </summary>
        /// <param name="parser">解析器实例</param>
        public void RegisterParser(IDataParser parser)
        {
            if (parser == null)
                throw new ArgumentNullException(nameof(parser));

            lock (_lock)
            {
                _parsers[parser.SupportedFormat] = parser;
            }
        }

        /// <summary>
        /// 获取所有已注册的解析器
        /// </summary>
        /// <returns>解析器列表</returns>
        public IEnumerable<IDataParser> GetAllParsers()
        {
            lock (_lock)
            {
                return _parsers.Values.ToArray();
            }
        }

        /// <summary>
        /// 检查是否支持指定的数据格式
        /// </summary>
        /// <param name="dataFormat">数据格式</param>
        /// <returns>是否支持</returns>
        public bool IsSupported(DataFormatEnum dataFormat)
        {
            lock (_lock)
            {
                return _parsers.ContainsKey(dataFormat);
            }
        }
    }
}
