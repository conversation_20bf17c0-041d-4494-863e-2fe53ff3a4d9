[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
Loaded ABP modules:

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
- Admin.Api.Host.AdminHostModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Swashbuckle.AbpSwashbuckleModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.VirtualFileSystem.AbpVirtualFileSystemModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.AbpAspNetCoreModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Auditing.AbpAuditingModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Data.AbpDataModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.ObjectExtending.AbpObjectExtendingModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Localization.AbpLocalizationAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Validation.AbpValidationAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Uow.AbpUnitOfWorkModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.EventBus.Abstractions.AbpEventBusAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Json.AbpJsonModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Json.SystemTextJson.AbpJsonSystemTextJsonModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Json.AbpJsonAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
              - Volo.Abp.Timing.AbpTimingModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
                - Volo.Abp.Localization.AbpLocalizationModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Settings.AbpSettingsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
                    - Volo.Abp.Security.AbpSecurityModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
                  - Volo.Abp.Threading.AbpThreadingModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.MultiTenancy.AbpMultiTenancyModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.MultiTenancy.AbpMultiTenancyAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Auditing.AbpAuditingContractsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Http.AbpHttpModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Http.AbpHttpAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Minify.AbpMinifyModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Authorization.AbpAuthorizationModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Authorization.AbpAuthorizationAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Validation.AbpValidationModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.ExceptionHandling.AbpExceptionHandlingModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.AspNetCore.AbpAspNetCoreAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.ApiVersioning.AbpApiVersioningAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.AspNetCore.Mvc.AbpAspNetCoreMvcContractsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Application.AbpDddApplicationContractsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.UI.Navigation.AbpUiNavigationModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.UI.AbpUiModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.GlobalFeatures.AbpGlobalFeaturesModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.Application.AbpDddApplicationModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Domain.AbpDddDomainModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.EventBus.AbpEventBusModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Guids.AbpGuidsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.BackgroundWorkers.AbpBackgroundWorkersModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.DistributedLocking.AbpDistributedLockingAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.ObjectMapping.AbpObjectMappingModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Specifications.AbpSpecificationsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Caching.AbpCachingModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
            - Volo.Abp.Serialization.AbpSerializationModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
          - Volo.Abp.Domain.AbpDddDomainSharedModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.Features.AbpFeaturesModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
  - Volo.Abp.Autofac.AbpAutofacModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.Castle.AbpCastleCoreModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Core.AdminCoreModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.SqlSugar.AdminSqlSugarModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.AspNetCore.SignalR.AbpAspNetCoreSignalRModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Volo.Abp.BlobStoring.FileSystem.AbpBlobStoringFileSystemModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BlobStoring.AbpBlobStoringModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Application.AdminApplicationModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.BackgroundService.AdminBackgroundModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
      - Volo.Abp.BackgroundJobs.AbpBackgroundJobsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
        - Volo.Abp.BackgroundJobs.AbpBackgroundJobsAbstractionsModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
    - Admin.Multiplex.AdminMultiplexModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Workflow.AdminWorkflowModule

[01:43:12] [INF] Volo.Abp.AbpApplicationBase 
  - Admin.Communication.AdminCommunicationModule

[01:43:12] [INF] WorkflowCore.Services.WorkflowHost 
Starting background tasks

[01:43:13] [INF] Admin.Communication.AdminCommunicationModule 
Admin Communication Module initialized

[01:43:13] [INF]  
项目当前环境为：Development

[01:43:13] [INF] Volo.Abp.Modularity.ModuleManager 
Initialized all ABP modules.

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
MQTT连接管理器已初始化，最大连接数: 10000, 单IP最大连接数: 100

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
正在启动MQTT代理服务...

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
正在启动MQTT代理服务，监听地址: 0.0.0.0:1883

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
正在初始化MQTT消息分发器...

[01:43:13] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
初始化消息处理器: DeviceDataHandler

[01:43:13] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器正在初始化...

[01:43:13] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
支持的主题模式: /devices/+/sys/properties/report, /devices/+/sys/gateway/sub_devices/properties/report

[01:43:13] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备数据处理器初始化完成

[01:43:13] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
消息处理器初始化完成: DeviceDataHandler

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
成功注册消息处理器: DeviceDataHandler (设备数据处理器，专门处理设备上报的数据消息), 优先级: 10

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttMessageDispatcher 
MQTT消息分发器初始化完成，已注册 1 个处理器

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
开始接受客户端连接

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务已启动

[01:43:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
MQTT代理服务已成功启动，监听端口: 1883

[01:43:32] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[01:43:32] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:49677

[01:43:32] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
开始处理设备消息: 主题=/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report, 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到设备: ID=696462312706117, 名称=1号温湿度, 模型ID=696308782780485

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品信息: ID=696308296450117, 名称=测试, 数据格式=JSON

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
模型属性数量: 2

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 开始解析设备数据 ===

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备信息: ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 名称=1号温湿度, 分组Key=

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
原始JSON数据: {
  "wendu": 20.4,
  "shidu": 60.1
}

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
--- 解析直连设备数据 ---

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备: 1号温湿度(QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2)

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 温度(wendu) = 20.4 [decimal小数] C

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 湿度(shidu) = 60.1 [decimal小数] %

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 批量保存历史数据: 2 条

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
直连设备数据处理完成: 成功保存 2 个属性

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 设备数据解析完成 ===

[01:43:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备消息处理完成: 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
开始处理设备消息: 主题=/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report, 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到设备: ID=696462312706117, 名称=1号温湿度, 模型ID=696308782780485

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品信息: ID=696308296450117, 名称=测试, 数据格式=JSON

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
模型属性数量: 2

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 开始解析设备数据 ===

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备信息: ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 名称=1号温湿度, 分组Key=

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
原始JSON数据: {
  "wendu": 21.4,
  "shidu": 51.1
}

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
--- 解析直连设备数据 ---

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备: 1号温湿度(QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2)

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 温度(wendu) = 21.4 [decimal小数] C

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 湿度(shidu) = 51.1 [decimal小数] %

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 批量保存历史数据: 2 条

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
直连设备数据处理完成: 成功保存 2 个属性

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 设备数据解析完成 ===

[01:45:20] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备消息处理完成: 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[01:58:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T01:56:38.4640003+08:00"

[01:58:15] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[01:58:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[01:58:15] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[01:58:19] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[01:58:19] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:49943

[01:58:19] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
开始处理设备消息: 主题=/devices/QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2/sys/properties/report, 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到设备: ID=696462312706117, 名称=1号温湿度, 模型ID=696308782780485

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
找到产品信息: ID=696308296450117, 名称=测试, 数据格式=JSON

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
模型属性数量: 2

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 开始解析设备数据 ===

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备类型: 直连设备

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备信息: ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 名称=1号温湿度, 分组Key=

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
产品模型: ID=696308782780485, 名称=温湿度模型, 设备类型=1, 分组Key=

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
原始JSON数据: {
  "wendu": 21.4,
  "shidu": 51.1
}

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
--- 解析直连设备数据 ---

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备: 1号温湿度(QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2)

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 温度(wendu) = 21.4 [decimal小数] C

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 湿度(shidu) = 51.1 [decimal小数] %

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
    ✓ 批量保存历史数据: 2 条

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
直连设备数据处理完成: 成功保存 2 个属性

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
=== 设备数据解析完成 ===

[02:01:35] [INF] Admin.Communication.Mqtt.Handlers.DeviceDataHandler 
设备消息处理完成: 设备ID=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2

[02:13:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T02:11:38.4717997+08:00"

[02:13:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[02:13:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[02:13:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[02:13:34] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[02:13:34] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:50218

[02:13:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[03:24:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T03:22:38.4599322+08:00"

[03:24:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[03:24:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[03:24:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[03:24:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[03:24:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:51232

[03:24:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[03:34:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T03:32:38.4636951+08:00"

[03:34:13] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[03:34:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[03:34:13] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[03:34:18] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[03:34:18] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:51389

[03:34:18] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[06:10:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T06:08:38.4613704+08:00"

[06:10:29] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[06:10:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[06:10:29] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[06:10:34] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[06:10:34] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:51724

[06:10:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[06:25:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T06:23:38.4571072+08:00"

[06:25:30] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[06:25:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[06:25:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[06:25:34] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[06:25:34] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:51916

[06:25:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[06:35:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T06:33:38.4603880+08:00"

[06:35:30] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[06:35:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[06:35:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[06:35:34] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[06:35:34] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:52033

[06:35:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[06:55:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理超时连接: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, LastActivity="2025-07-12T06:53:38.4625365+08:00"

[06:55:30] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: false

[06:55:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="Timeout"

[06:55:30] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
清理了 1 个超时连接

[06:55:34] [INF] Admin.Communication.Mqtt.Services.MqttUserService 
MQTT用户 admin 验证成功, 角色: user

[06:55:34] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP: 127.0.0.1:52278

[06:55:34] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接成功: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, SessionPresent=true

[14:43:38] [INF] Admin.Communication.Mqtt.Services.MqttBrokerService 
客户端已通过连接管理器断开连接: QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, 是否正常断开: true

[14:43:38] [INF] Admin.Communication.Mqtt.Services.MqttConnectionManager 
客户端连接已移除: ClientId=QH5LqzsdgZLLYJ5Qq82lkURQaYuVIg49A-KkOa-IMGSNZnhaL2, IP=127.0.0.1, Reason="NormalDisconnection"

