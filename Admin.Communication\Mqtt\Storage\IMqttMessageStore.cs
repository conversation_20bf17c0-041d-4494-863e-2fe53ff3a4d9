using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Models;

namespace Admin.Communication.Mqtt.Storage
{
    /// <summary>
    /// MQTT消息存储接口，提供消息的持久化存储和检索功能
    /// </summary>
    public interface IMqttMessageStore : IDisposable
    {
        /// <summary>
        /// 存储名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 存储描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 是否支持过期处理
        /// </summary>
        bool SupportsExpiration { get; }

        /// <summary>
        /// 是否支持事务
        /// </summary>
        bool SupportsTransaction { get; }

        /// <summary>
        /// 当前存储的消息数量
        /// </summary>
        long MessageCount { get; }

        /// <summary>
        /// 存储的最大容量
        /// </summary>
        long MaxCapacity { get; }

        /// <summary>
        /// 初始化存储
        /// </summary>
        /// <returns>初始化任务</returns>
        Task InitializeAsync();

        /// <summary>
        /// 存储消息
        /// </summary>
        /// <param name="message">要存储的消息</param>
        /// <param name="options">存储选项</param>
        /// <returns>存储结果</returns>
        Task<MessageStoreResult> StoreMessageAsync(MqttMessage message, MessageStoreOptions options = null);

        /// <summary>
        /// 批量存储消息
        /// </summary>
        /// <param name="messages">要存储的消息列表</param>
        /// <param name="options">存储选项</param>
        /// <returns>存储结果</returns>
        Task<MessageStoreResult> StoreMessagesAsync(IEnumerable<MqttMessage> messages, MessageStoreOptions options = null);

        /// <summary>
        /// 根据消息ID获取消息
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <returns>消息对象，如果不存在返回null</returns>
        Task<MqttMessage> GetMessageAsync(string messageId);

        /// <summary>
        /// 根据主题获取消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="maxCount">最大返回数量</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>消息列表</returns>
        Task<IEnumerable<MqttMessage>> GetMessagesByTopicAsync(string topic, int maxCount = 100, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 根据客户端ID获取消息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="maxCount">最大返回数量</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>消息列表</returns>
        Task<IEnumerable<MqttMessage>> GetMessagesByClientAsync(string clientId, int maxCount = 100, DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 获取保留消息
        /// </summary>
        /// <param name="topicFilter">主题过滤器</param>
        /// <returns>保留消息列表</returns>
        Task<IEnumerable<MqttMessage>> GetRetainedMessagesAsync(string topicFilter = null);

        /// <summary>
        /// 存储保留消息
        /// </summary>
        /// <param name="message">保留消息</param>
        /// <returns>存储结果</returns>
        Task<MessageStoreResult> StoreRetainedMessageAsync(MqttMessage message);

        /// <summary>
        /// 删除保留消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>删除结果</returns>
        Task<MessageStoreResult> DeleteRetainedMessageAsync(string topic);

        /// <summary>
        /// 删除消息
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <returns>删除结果</returns>
        Task<MessageStoreResult> DeleteMessageAsync(string messageId);

        /// <summary>
        /// 批量删除消息
        /// </summary>
        /// <param name="messageIds">消息ID列表</param>
        /// <returns>删除结果</returns>
        Task<MessageStoreResult> DeleteMessagesAsync(IEnumerable<string> messageIds);

        /// <summary>
        /// 清理过期消息
        /// </summary>
        /// <param name="expirationTime">过期时间</param>
        /// <returns>清理结果</returns>
        Task<MessageStoreResult> CleanupExpiredMessagesAsync(DateTime? expirationTime = null);

        /// <summary>
        /// 清空所有消息
        /// </summary>
        /// <returns>清空结果</returns>
        Task<MessageStoreResult> ClearAllMessagesAsync();

        /// <summary>
        /// 获取存储统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<MessageStoreStatistics> GetStatisticsAsync();

        /// <summary>
        /// 执行存储维护操作
        /// </summary>
        /// <returns>维护结果</returns>
        Task<MessageStoreResult> PerformMaintenanceAsync();

        /// <summary>
        /// 检查存储健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        Task<MessageStoreHealthStatus> CheckHealthAsync();

        /// <summary>
        /// 开始事务（如果支持）
        /// </summary>
        /// <returns>事务对象</returns>
        Task<IMessageStoreTransaction> BeginTransactionAsync();
    }

    /// <summary>
    /// 消息存储选项
    /// </summary>
    public class MessageStoreOptions
    {
        /// <summary>
        /// 消息过期时间（相对于存储时间）
        /// </summary>
        public TimeSpan? ExpirationTime { get; set; }

        /// <summary>
        /// 是否覆盖已存在的消息
        /// </summary>
        public bool OverwriteExisting { get; set; } = false;

        /// <summary>
        /// 是否为保留消息
        /// </summary>
        public bool IsRetained { get; set; } = false;

        /// <summary>
        /// 存储优先级
        /// </summary>
        public MessageStorePriority Priority { get; set; } = MessageStorePriority.Normal;

        /// <summary>
        /// 自定义标签
        /// </summary>
        public Dictionary<string, string> Tags { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool EnableCompression { get; set; } = false;

        /// <summary>
        /// 是否启用加密
        /// </summary>
        public bool EnableEncryption { get; set; } = false;
    }

    /// <summary>
    /// 消息存储优先级
    /// </summary>
    public enum MessageStorePriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 1,

        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal = 2,

        /// <summary>
        /// 高优先级
        /// </summary>
        High = 3,

        /// <summary>
        /// 关键优先级
        /// </summary>
        Critical = 4
    }

    /// <summary>
    /// 消息存储结果
    /// </summary>
    public class MessageStoreResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 操作耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary>
        /// 影响的消息数量
        /// </summary>
        public int AffectedMessageCount { get; set; }

        /// <summary>
        /// 操作详细信息
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="affectedCount">影响的消息数量</param>
        /// <param name="elapsedMs">耗时</param>
        /// <returns>成功结果</returns>
        public static MessageStoreResult Success(int affectedCount = 1, long elapsedMs = 0)
        {
            return new MessageStoreResult
            {
                IsSuccess = true,
                AffectedMessageCount = affectedCount,
                ElapsedMilliseconds = elapsedMs
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="exception">异常</param>
        /// <returns>失败结果</returns>
        public static MessageStoreResult Failure(string errorMessage, Exception exception = null)
        {
            return new MessageStoreResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Exception = exception,
                AffectedMessageCount = 0
            };
        }
    }

    /// <summary>
    /// 消息存储统计信息
    /// </summary>
    public class MessageStoreStatistics
    {
        /// <summary>
        /// 总消息数量
        /// </summary>
        public long TotalMessageCount { get; set; }

        /// <summary>
        /// 保留消息数量
        /// </summary>
        public long RetainedMessageCount { get; set; }

        /// <summary>
        /// 过期消息数量
        /// </summary>
        public long ExpiredMessageCount { get; set; }

        /// <summary>
        /// 存储大小（字节）
        /// </summary>
        public long StorageSize { get; set; }

        /// <summary>
        /// 平均消息大小（字节）
        /// </summary>
        public double AverageMessageSize { get; set; }

        /// <summary>
        /// 最早消息时间
        /// </summary>
        public DateTime? EarliestMessageTime { get; set; }

        /// <summary>
        /// 最新消息时间
        /// </summary>
        public DateTime? LatestMessageTime { get; set; }

        /// <summary>
        /// 按主题分组的消息数量
        /// </summary>
        public Dictionary<string, long> MessageCountByTopic { get; set; } = new Dictionary<string, long>();

        /// <summary>
        /// 按客户端分组的消息数量
        /// </summary>
        public Dictionary<string, long> MessageCountByClient { get; set; } = new Dictionary<string, long>();

        /// <summary>
        /// 按优先级分组的消息数量
        /// </summary>
        public Dictionary<MessageStorePriority, long> MessageCountByPriority { get; set; } = new Dictionary<MessageStorePriority, long>();
    }

    /// <summary>
    /// 消息存储健康状态
    /// </summary>
    public class MessageStoreHealthStatus
    {
        /// <summary>
        /// 是否健康
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// 健康状态描述
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 最后检查时间
        /// </summary>
        public DateTime LastCheckTime { get; set; }

        /// <summary>
        /// 存储可用性百分比
        /// </summary>
        public double AvailabilityPercentage { get; set; }

        /// <summary>
        /// 平均响应时间（毫秒）
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// 错误详情
        /// </summary>
        public List<string> ErrorDetails { get; set; } = new List<string>();

        /// <summary>
        /// 警告信息
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 消息存储事务接口
    /// </summary>
    public interface IMessageStoreTransaction : IDisposable
    {
        /// <summary>
        /// 事务ID
        /// </summary>
        string TransactionId { get; }

        /// <summary>
        /// 事务状态
        /// </summary>
        MessageStoreTransactionStatus Status { get; }

        /// <summary>
        /// 提交事务
        /// </summary>
        /// <returns>提交结果</returns>
        Task<MessageStoreResult> CommitAsync();

        /// <summary>
        /// 回滚事务
        /// </summary>
        /// <returns>回滚结果</returns>
        Task<MessageStoreResult> RollbackAsync();
    }

    /// <summary>
    /// 消息存储事务状态
    /// </summary>
    public enum MessageStoreTransactionStatus
    {
        /// <summary>
        /// 活跃状态
        /// </summary>
        Active,

        /// <summary>
        /// 已提交
        /// </summary>
        Committed,

        /// <summary>
        /// 已回滚
        /// </summary>
        RolledBack,

        /// <summary>
        /// 已终止
        /// </summary>
        Aborted
    }
} 