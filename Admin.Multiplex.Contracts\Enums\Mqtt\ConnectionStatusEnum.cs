namespace Admin.Multiplex.Contracts.Enums.Mqtt
{
    /// <summary>
    /// 连接状态
    /// </summary>
    public enum ConnectionStatusEnum
    {
        /// <summary>
        /// 已连接
        /// </summary>
        Connected,

        /// <summary>
        /// 已断开
        /// </summary>
        Disconnected,

        /// <summary>
        /// 连接中
        /// </summary>
        Connecting,

        /// <summary>
        /// 断开中
        /// </summary>
        Disconnecting,

        /// <summary>
        /// 已暂停
        /// </summary>
        Suspended,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error
    }
} 