﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
   
namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 设备指令表
/// </summary>
[SugarTable("DEVICE_INSTRUCTION")]
public partial class DeviceInstructionEntity : BaseEntity
{
    /// <summary>
    /// 设备id
    /// </summary> 
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    [SugarColumn(ColumnName = "INSTRUCTION_NAME")]
    public string InstructionName { get; set; }

    /// <summary>
    /// 发送指令
    /// 例如 010300000002C40B
    /// </summary>
    [SugarColumn(ColumnName = "SEND_STR")]
    public string send_str { get; set; }

    /// <summary>
    /// 编码 1:HEX 2:ASCII
    /// </summary>
    [SugarColumn(ColumnName = "ENCODE")]
    public int Encode { get; set; } = 1; // 默认16进制编码

    /// <summary>
    /// 指令顺序
    /// </summary>
    [SugarColumn(ColumnName = "ORDER")]
    public int InstructionOrder { get; set; } = 1; // 默认1，表示第一个指令

    /// <summary>
    /// 读取间隔(毫秒)
    /// </summary>
    [SugarColumn(ColumnName = "READ_INTERVAL")]
    public int ReadInterval { get; set; } = 10000;

    /// <summary>
    /// 响应时间(毫秒)
    /// </summary>
    [SugarColumn(ColumnName = "RESPONSE_TIME")]
    public int ResponseTime { get; set; } = 2000;

    /// <summary>
    /// 重试次数
    /// </summary>
    [SugarColumn(ColumnName = "RETRY_COUNT")]
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnName = "IS_ENABLED")]
    public bool IsEnabled { get; set; } = true;
}
