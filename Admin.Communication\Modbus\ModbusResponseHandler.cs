// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Modbus.Models;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Collections.Concurrent;

namespace Admin.Communication.Modbus;

/// <summary>
/// Modbus响应处理器实现
/// 负责处理设备返回的Modbus RTU响应数据，匹配等待响应的指令
/// </summary>
public class ModbusResponseHandler(
    ISqlSugarClient db,
    ILogger<ModbusResponseHandler> logger) : IModbusResponseHandler
{
    private readonly ISqlSugarClient _db = db;
    private readonly ILogger<ModbusResponseHandler> _logger = logger;

    // 存储等待响应的指令 (DeviceId -> InstructionScheduleState)
    private static readonly ConcurrentDictionary<string, InstructionScheduleState> _pendingInstructions = new();

    /// <summary>
    /// 注册等待响应的指令
    /// </summary>
    /// <param name="instruction">指令状态</param>
    public static void RegisterPendingInstruction(InstructionScheduleState instruction)
    {
        var key = $"{instruction.DeviceId}";
        _pendingInstructions.AddOrUpdate(key, instruction, (k, v) => instruction);
    }

    /// <summary>
    /// 移除等待响应的指令
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    public static void RemovePendingInstruction(string deviceId)
    {
        _pendingInstructions.TryRemove(deviceId, out _);
    }

    /// <summary>
    /// 处理Modbus响应数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="responseData">响应数据</param>
    /// <returns>处理任务</returns>
    public async Task HandleModbusResponseAsync(string deviceId, byte[] responseData)
    {
        try
        {
            _logger.LogDebug("收到Modbus响应: DeviceId={DeviceId}, DataSize={DataSize}", 
                deviceId, responseData.Length);

            // 查找等待响应的指令
            if (!_pendingInstructions.TryGetValue(deviceId, out var waitingInstruction))
            {
                _logger.LogWarning("未找到等待响应的指令: DeviceId={DeviceId}", deviceId);
                return;
            }

            if (waitingInstruction.PendingResponse == null)
            {
                _logger.LogWarning("指令响应任务为空: DeviceId={DeviceId}", deviceId);
                return;
            }

            // 解析Modbus响应
            var response = await ParseModbusResponseAsync(waitingInstruction, responseData);

            // 完成等待任务
            waitingInstruction.PendingResponse.SetResult(response);

            _logger.LogDebug("Modbus响应处理完成: DeviceId={DeviceId}, Success={Success}", 
                deviceId, response.IsSuccess);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理Modbus响应时发生错误: DeviceId={DeviceId}", deviceId);

            // 如果有等待的任务，设置异常
            if (_pendingInstructions.TryGetValue(deviceId, out var instruction) && 
                instruction.PendingResponse != null)
            {
                instruction.PendingResponse.SetException(ex);
            }
        }
    }

    /// <summary>
    /// 检查是否有等待响应的指令
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>是否有等待响应的指令</returns>
    public bool HasPendingInstruction(string deviceId)
    {
        return _pendingInstructions.ContainsKey(deviceId);
    }

    /// <summary>
    /// 解析Modbus响应数据
    /// </summary>
    /// <param name="instruction">指令状态</param>
    /// <param name="responseData">响应数据</param>
    /// <returns>解析结果</returns>
    private async Task<ModbusResponse> ParseModbusResponseAsync(InstructionScheduleState instruction, byte[] responseData)
    {
        try
        {
            // 1. 基础响应验证
            var basicValidation = ValidateBasicResponse(responseData);
            if (!basicValidation.IsValid)
            {
                return new ModbusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = basicValidation.ErrorMessage,
                    RawData = responseData,
                    DeviceId = instruction.DeviceId,
                    InstructionId = instruction.DeviceInstructionId
                };
            }

            // 2. CRC校验
            if (!ValidateCRC(responseData))
            {
                return new ModbusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "CRC校验失败",
                    RawData = responseData,
                    DeviceId = instruction.DeviceId,
                    InstructionId = instruction.DeviceInstructionId
                };
            }

            // 3. 获取指令信息（需要起始地址等信息）
            var instructionInfo = await GetInstructionInfoAsync(instruction.DeviceInstructionId);
            if (instructionInfo == null)
            {
                return new ModbusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "无法获取指令信息",
                    RawData = responseData,
                    DeviceId = instruction.DeviceId,
                    InstructionId = instruction.DeviceInstructionId
                };
            }

            // 4. 查询设备参数配置（使用设备参数实体）
            var deviceParameters = await _db.Queryable<DeviceParaEntity>()
                .Where(p => p.DeviceId == instruction.DeviceId && p.InstructionId == instruction.DeviceInstructionId)
                .OrderBy( p => p.Sort)
                .ToListAsync();

            // 5. 解析设备参数数据
            var parameterValues = new Dictionary<int, object>();
            foreach (var parameter in deviceParameters)
            {
                var value = ExtractParameterValue(responseData, parameter.StartAddress, parameter.StartCount,
                    parameter.DataType, parameter.DivisionFactor,
                    parameter.CorrectionScale, parameter.CorrectionAmplitude);
                if (value != null)
                {
                    parameterValues[parameter.StartAddress] = value;
                }
            }

            return new ModbusResponse
            {
                IsSuccess = true,
                RawData = responseData,
                RegisterValues = parameterValues,
                DeviceId = instruction.DeviceId,
                InstructionId = instruction.DeviceInstructionId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析Modbus响应时发生错误: DeviceId={DeviceId}", instruction.DeviceId);

            return new ModbusResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                RawData = responseData,
                DeviceId = instruction.DeviceId,
                InstructionId = instruction.DeviceInstructionId
            };
        }
    }

    /// <summary>
    /// 基础响应验证
    /// </summary>
    private (bool IsValid, string ErrorMessage) ValidateBasicResponse(byte[] responseData)
    {
        if (responseData.Length < 5)
            return (false, "响应数据长度不足，最小长度为5字节");

        var slaveAddress = responseData[0];
        var functionCode = responseData[1];
        var dataLength = responseData[2];

        // 检查功能码是否为错误响应
        if ((functionCode & 0x80) != 0)
        {
            var errorCode = responseData[2];
            return (false, $"设备返回错误，功能码: {functionCode:X2}, 错误码: {errorCode:X2}");
        }

        // 检查数据长度是否合理
        if (responseData.Length < 3 + dataLength + 2)
            return (false, "响应数据长度与声明的数据长度不符");

        return (true, string.Empty);
    }

    /// <summary>
    /// CRC校验
    /// </summary>
    private bool ValidateCRC(byte[] responseData)
    {
        if (responseData.Length < 3) return false;

        var dataWithoutCrc = new byte[responseData.Length - 2];
        Array.Copy(responseData, 0, dataWithoutCrc, 0, dataWithoutCrc.Length);

        var calculatedCrc = CalculateCRC16(dataWithoutCrc);
        var receivedCrc = (ushort)(responseData[responseData.Length - 2] |
                                  (responseData[responseData.Length - 1] << 8));

        return calculatedCrc == receivedCrc;
    }

    /// <summary>
    /// 获取指令信息
    /// </summary>
    private async Task<ModbusInstructionInfo?> GetInstructionInfoAsync(long deviceInstructionId)
    {
        try
        {
            // 先获取设备指令信息
            var deviceInstruction = await _db.Queryable<DeviceInstructionEntity>()
                .Where(di => di.Id == deviceInstructionId)
                .FirstAsync();

            if (deviceInstruction == null)
                return null;

            // 通过设备获取模型指令信息
            var device = await _db.Queryable<DeviceEntity>()
                .Where(d => d.Id == deviceInstruction.DeviceId)
                .FirstAsync();

            if (device == null)
                return null;

            // 获取模型指令信息（通过指令名称匹配）
            var modelInstruction = await _db.Queryable<ModelInstructionEntity>()
                .Where(mi => mi.ModelId == device.ModelId && mi.InstructionName == deviceInstruction.InstructionName)
                .FirstAsync();

            if (modelInstruction == null)
                return null;

            var instruction = new ModbusInstructionInfo
            {
                DeviceInstructionId = deviceInstruction.Id,
                FunctionCode = modelInstruction.FunctionCode,
                StartAddress = modelInstruction.StartAddress,
                ReadCount = modelInstruction.ReadCount
            };

            return instruction;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取指令信息失败: DeviceInstructionId={DeviceInstructionId}", deviceInstructionId);
            return null;
        }
    }

    /// <summary>
    /// 从响应数据中提取参数值
    /// </summary>
    /// <param name="responseData">响应数据</param>
    /// <param name="startAddress">字节起始地址（在响应数据中的位置）</param>
    /// <param name="startCount">字节读取数量</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="divisionFactor">倍率</param>
    /// <param name="correctionScale">校正比例</param>
    /// <param name="correctionAmplitude">校正幅度</param>
    /// <returns>解析后的参数值</returns>
    private object? ExtractParameterValue(byte[] responseData, int startAddress, int startCount,
        int dataType, int divisionFactor, decimal correctionScale, decimal correctionAmplitude)
    {
        try
        {
            // Modbus RTU响应格式：地址(1) + 功能码(1) + 数据长度(1) + 数据(N) + CRC(2)
            if (responseData.Length < 5)
                return null;

            var dataLength = responseData[2];
            var dataStartIndex = 3; // 数据部分的起始索引

            // 检查起始地址和读取数量是否在有效范围内
            if (startAddress < 0 || startCount <= 0)
            {
                _logger.LogWarning("无效的参数配置: StartAddress={StartAddress}, StartCount={StartCount}",
                    startAddress, startCount);
                return null;
            }

            // 检查是否超出响应数据范围
            if (dataStartIndex + startAddress + startCount > responseData.Length - 2) // 减去CRC的2字节
            {
                _logger.LogWarning("参数地址超出响应数据范围: StartAddress={StartAddress}, StartCount={StartCount}, DataLength={DataLength}",
                    startAddress, startCount, dataLength);
                return null;
            }

            // 提取指定字节范围的数据
            var parameterBytes = new byte[startCount];
            Array.Copy(responseData, dataStartIndex + startAddress, parameterBytes, 0, startCount);

            // 根据字节数量和数据类型解析值
            var rawValue = ParseBytesToValue(parameterBytes, dataType);
            if (rawValue == null)
                return null;

            // 应用倍率和校正
            var processedValue = ApplyDataProcessing(rawValue, dataType, divisionFactor, correctionScale, correctionAmplitude);

            _logger.LogDebug("参数值解析: StartAddress={StartAddress}, StartCount={StartCount}, Raw={Raw}, Processed={Processed}",
                startAddress, startCount, rawValue, processedValue);

            return processedValue;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "提取参数值时发生错误: StartAddress={StartAddress}, StartCount={StartCount}",
                startAddress, startCount);
            return null;
        }
    }

    /// <summary>
    /// 将字节数组解析为原始值
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="dataType">数据类型</param>
    /// <returns>原始值</returns>
    private object? ParseBytesToValue(byte[] bytes, int dataType)
    {
        try
        {
            switch (bytes.Length)
            {
                case 1:
                    // 单字节值
                    return bytes[0];

                case 2:
                    // 16位值（大端序）
                    return (ushort)((bytes[0] << 8) | bytes[1]);

                case 4:
                    // 32位值（大端序）
                    if (dataType == 1) // decimal类型，可能是浮点数
                    {
                        // IEEE 754 单精度浮点数
                        var intValue = (uint)((bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3]);
                        return BitConverter.ToSingle(BitConverter.GetBytes(intValue), 0);
                    }
                    else
                    {
                        // 32位整数
                        return (uint)((bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3]);
                    }

                case 8:
                    // 64位值（大端序）
                    var longValue = 0UL;
                    for (int i = 0; i < 8; i++)
                    {
                        longValue = (longValue << 8) | bytes[i];
                    }
                    return longValue;

                default:
                    // 多字节数据，根据数据类型处理
                    if (dataType == 2) // string类型
                    {
                        // 移除尾部的0字节
                        var endIndex = Array.IndexOf(bytes, (byte)0);
                        if (endIndex >= 0)
                        {
                            var trimmedBytes = new byte[endIndex];
                            Array.Copy(bytes, trimmedBytes, endIndex);
                            return System.Text.Encoding.UTF8.GetString(trimmedBytes);
                        }
                        return System.Text.Encoding.UTF8.GetString(bytes);
                    }
                    else
                    {
                        // 默认返回16位值（取前两个字节）
                        if (bytes.Length >= 2)
                            return (ushort)((bytes[0] << 8) | bytes[1]);
                        else
                            return bytes[0];
                    }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "解析字节数组时发生错误: Length={Length}, DataType={DataType}",
                bytes.Length, dataType);
            return null;
        }
    }

    /// <summary>
    /// 应用数据处理（倍率、校正等）
    /// </summary>
    private object ApplyDataProcessing(object rawValue, int dataType, int divisionFactor,
        decimal correctionScale, decimal correctionAmplitude)
    {
        // 根据数据类型进行基础转换
        object baseValue = dataType switch
        {
            1 => Convert.ToDecimal(rawValue), // decimal模拟量
            2 => rawValue.ToString()!, // string字符串
            3 => rawValue is long longValue ? DateTime.FromBinary(longValue) : DateTime.Now, // datetime时间
            4 => $"{{\"value\":{rawValue}}}", // json
            5 => rawValue, // enum枚举
            _ => rawValue
        };

        // 对数值类型应用倍率和校正
        if (dataType == 1 && baseValue is decimal decimalValue)
        {
            // 应用倍率
            if (divisionFactor > 0)
                decimalValue = decimalValue / divisionFactor;

            // 应用校正：解析值 * 校正比例 + 校正幅度
            decimalValue = decimalValue * correctionScale + correctionAmplitude;

            return decimalValue;
        }

        return baseValue;
    }

    /// <summary>
    /// 计算CRC16校验码
    /// </summary>
    private ushort CalculateCRC16(byte[] data)
    {
        ushort crc = 0xFFFF;
        for (int i = 0; i < data.Length; i++)
        {
            crc ^= data[i];
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= 0xA001;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }
        return crc;
    }
}

/// <summary>
/// Modbus指令信息
/// </summary>
public class ModbusInstructionInfo
{
    public long DeviceInstructionId { get; set; }
    public int FunctionCode { get; set; }
    public int StartAddress { get; set; }
    public int ReadCount { get; set; }
}
