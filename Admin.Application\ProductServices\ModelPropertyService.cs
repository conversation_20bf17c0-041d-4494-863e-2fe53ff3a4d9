// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ProductServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ProductServices;

/// <summary>
/// 模型属性服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class ModelPropertyService(ISqlSugarClient db, Repository<ModelPropertyEntity> modelPropertyRepository) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<ModelPropertyEntity> _modelPropertyRepository = modelPropertyRepository;

    /// <summary>
    /// 获取模型属性分页列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页列表</returns>
    public async Task<PagedList<ModelPropertyOutput>> GetPagedListAsync(ModelPropertyQueryInput input)
    {
        var query = _db.Queryable<ModelPropertyEntity>()
            .WhereIF(input.ModelId.HasValue, mp => mp.ModelId == input.ModelId.Value)
            .WhereIF(input.InstructionId.HasValue, mp => mp.InstructionId == input.InstructionId.Value)
            .WhereIF(!string.IsNullOrEmpty(input.Key), mp => mp.JsonKey.Contains(input.Key))
            .WhereIF(input.DataType.HasValue, mp => mp.DataType == input.DataType.Value)
            .WhereIF(input.MonitorStatus.HasValue, mp => mp.MonitorStatus == input.MonitorStatus.Value)
            .WhereIF(input.IsSave.HasValue, mp => mp.IsSave == input.IsSave.Value)
            .OrderBy(mp => mp.Sort)
            .OrderBy(mp => mp.Id);

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        return pagedList.Adapt<PagedList<ModelPropertyOutput>>();
    }



    /// <summary>
    /// 根据ID获取模型属性详情
    /// </summary>
    /// <param name="id">属性ID</param>
    /// <returns>属性详情</returns>
    public async Task<ModelPropertyOutput> GetByIdAsync(long id)
    {
        var entity = await _modelPropertyRepository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("模型属性不存在");

        return entity.Adapt<ModelPropertyOutput>();
    }

    /// <summary>
    /// 根据ID获取产品属性详情 (向后兼容)
    /// </summary>
    /// <param name="id">属性ID</param>
    /// <returns>属性详情</returns>
    public async Task<ProductPropertyOutput> GetProductPropertyByIdAsync(long id)
    {
        var entity = await _modelPropertyRepository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("模型属性不存在");

        return entity.Adapt<ProductPropertyOutput>();
    }

    /// <summary>
    /// 添加模型属性
    /// </summary>
    /// <param name="input">属性信息</param>
    /// <returns>属性ID</returns>
    public async Task<long> AddAsync(ModelPropertyInput input)
    {
        // 验证模型是否存在
        var model = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.Id == input.ModelId)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        // 验证指令是否存在
        if (input.InstructionId > 0)
        {
            var instruction = await _db.Queryable<ModelInstructionEntity>()
                .Where(mi => mi.Id == input.InstructionId && mi.ModelId == input.ModelId)
                .FirstAsync() ??
                throw PersistdValidateException.Message("模型指令不存在");
        }

        // 验证属性标识符是否重复（同一模型下）
        var exists = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == input.ModelId && mp.JsonKey == input.Key)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("该模型下已存在相同的属性标识符");

        // 验证属性名称是否重复（同一模型下）
        var nameExists = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == input.ModelId && mp.Name == input.Name)
            .AnyAsync();
        if (nameExists)
            throw PersistdValidateException.Message("该模型下已存在相同的属性名称");

        var entity = input.Adapt<ModelPropertyEntity>();
        return await _modelPropertyRepository.InsertReturnSnowflakeIdAsync(entity);
    }

    /// <summary>
    /// 更新模型属性
    /// </summary>
    /// <param name="input">属性信息</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateAsync(UpdateModelPropertyInput input)
    {
        var entity = await _modelPropertyRepository.GetByIdAsync(input.Id) ??
            throw PersistdValidateException.Message("模型属性不存在");

        // 验证模型是否存在
        var model = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.Id == input.ModelId)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        // 验证指令是否存在
        if (input.InstructionId > 0)
        {
            var instruction = await _db.Queryable<ModelInstructionEntity>()
                .Where(mi => mi.Id == input.InstructionId && mi.ModelId == input.ModelId)
                .FirstAsync() ??
                throw PersistdValidateException.Message("模型指令不存在");
        }

        // 验证属性标识符是否重复（同一模型下，排除自己）
        var exists = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == input.ModelId && mp.JsonKey == input.Key && mp.Id != input.Id)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("该模型下已存在相同的属性标识符");

        // 验证属性名称是否重复（同一模型下，排除自己）
        var nameExists = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == input.ModelId && mp.Name == input.Name && mp.Id != input.Id)
            .AnyAsync();
        if (nameExists)
            throw PersistdValidateException.Message("该模型下已存在相同的属性名称");

        input.Adapt(entity);
        return await _modelPropertyRepository.UpdateAsync(entity);
    }



    /// <summary>
    /// 删除模型属性
    /// </summary>
    /// <param name="id">属性ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteAsync(long id)
    {
        var entity = await _modelPropertyRepository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("模型属性不存在");

        return await _modelPropertyRepository.DeleteAsync(entity);
    }

    /// <summary>
    /// 批量删除模型属性
    /// </summary>
    /// <param name="ids">属性ID集合</param>
    /// <returns>是否成功</returns>
    public async Task<bool> BatchDeleteAsync(List<long> ids)
    {
        if (ids == null || ids.Count == 0)
            throw PersistdValidateException.Message("请选择要删除的属性");

        return await _modelPropertyRepository.DeleteAsync(mp => ids.Contains(mp.Id));
    }

    /// <summary>
    /// 根据模型ID获取属性列表
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>属性列表</returns>
    public async Task<List<ModelPropertyOutput>> GetByModelIdAsync(long modelId)
    {
        var properties = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == modelId)
            .OrderBy(mp => mp.Sort)
            .OrderBy(mp => mp.Id)
            .ToListAsync();

        return properties.Adapt<List<ModelPropertyOutput>>();
    }

    /// <summary>
    /// 根据模型ID获取属性列表 (向后兼容)
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>属性列表</returns>
    public async Task<List<ProductPropertyOutput>> GetProductPropertiesByModelIdAsync(long modelId)
    {
        var result = await GetByModelIdAsync(modelId);
        return result.Adapt<List<ProductPropertyOutput>>();
    }

    /// <summary>
    /// 根据模型ID获取属性简单列表
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>属性简单列表</returns>
    public async Task<List<ModelPropertySimpleOutput>> GetSimpleByModelIdAsync(long modelId)
    {
        var properties = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == modelId)
            .Select(mp => new ModelPropertySimpleOutput
            {
                Id = mp.Id,
                Name = mp.Name,
                Key = mp.JsonKey,
                DataType = mp.DataType,
                Unit = mp.Unit
            })
            .OrderBy(mp => mp.Id)
            .ToListAsync();

        return properties.Adapt<List<ModelPropertySimpleOutput>>();
    }

    /// <summary>
    /// 根据模型ID获取属性简单列表 (向后兼容)
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>属性简单列表</returns>
    public async Task<List<ProductPropertySimpleOutput>> GetProductPropertySimpleByModelIdAsync(long modelId)
    {
        var result = await GetSimpleByModelIdAsync(modelId);
        return result.Adapt<List<ProductPropertySimpleOutput>>();
    }

    /// <summary>
    /// 批量添加模型属性
    /// </summary>
    /// <param name="input">批量添加信息</param>
    /// <returns>是否成功</returns>
    [UnitOfWork]
    public async Task<bool> BatchAddAsync(BatchAddModelPropertyInput input)
    {
        // 验证模型是否存在
        var model = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.Id == input.ModelId)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        if (input.Properties == null || input.Properties.Count == 0)
            throw PersistdValidateException.Message("属性列表不能为空");

        // 验证属性标识符是否重复
        var propertyKeys = input.Properties.Select(p => p.Key).ToList();
        var duplicateKeys = propertyKeys.GroupBy(k => k).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
        if (duplicateKeys.Any())
            throw PersistdValidateException.Message($"存在重复的属性标识符: {string.Join(", ", duplicateKeys)}");

        // 验证属性名称是否重复
        var propertyNames = input.Properties.Select(p => p.Name).ToList();
        var duplicateNames = propertyNames.GroupBy(n => n).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
        if (duplicateNames.Any())
            throw PersistdValidateException.Message($"存在重复的属性名称: {string.Join(", ", duplicateNames)}");

        // 验证是否与现有属性冲突
        var existingProperties = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.ModelId == input.ModelId)
            .Select(mp => new { mp.JsonKey, mp.Name })
            .ToListAsync();

        var existingKeys = existingProperties.Select(p => p.JsonKey).ToList();
        var existingNames = existingProperties.Select(p => p.Name).ToList();

        var conflictKeys = propertyKeys.Intersect(existingKeys).ToList();
        if (conflictKeys.Any())
            throw PersistdValidateException.Message($"属性标识符已存在: {string.Join(", ", conflictKeys)}");

        var conflictNames = propertyNames.Intersect(existingNames).ToList();
        if (conflictNames.Any())
            throw PersistdValidateException.Message($"属性名称已存在: {string.Join(", ", conflictNames)}");

        // 批量插入
        var entities = input.Properties.Adapt<List<ModelPropertyEntity>>();
        return await _modelPropertyRepository.InsertRangeAsync(entities);
    }



    /// <summary>
    /// 根据模型ID删除所有属性
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteByModelIdAsync(long modelId)
    {
        return await _modelPropertyRepository.DeleteAsync(mp => mp.ModelId == modelId);
    }

    /// <summary>
    /// 获取属性简单列表 (用于下拉选择)
    /// </summary>
    /// <returns>属性简单列表</returns>
    public async Task<List<ModelPropertySimpleOutput>> GetSimpleListAsync()
    {
        var properties = await _db.Queryable<ModelPropertyEntity>()
            .Select(mp => new ModelPropertySimpleOutput
            {
                Id = mp.Id,
                Name = mp.Name,
                Key = mp.JsonKey,
                DataType = mp.DataType,
                Unit = mp.Unit
            })
            .OrderBy(mp => mp.Id)
            .ToListAsync();

        return properties.Adapt<List<ModelPropertySimpleOutput>>();
    }

    /// <summary>
    /// 获取属性简单列表 (用于下拉选择) (向后兼容)
    /// </summary>
    /// <returns>属性简单列表</returns>
    public async Task<List<ProductPropertySimpleOutput>> GetProductPropertySimpleListAsync()
    {
        var result = await GetSimpleListAsync();
        return result.Adapt<List<ProductPropertySimpleOutput>>();
    }

    /// <summary>
    /// 根据指令ID获取属性列表
    /// </summary>
    /// <param name="instructionId">指令ID</param>
    /// <returns>属性列表</returns>
    public async Task<List<ModelPropertyOutput>> GetByInstructionIdAsync(long instructionId)
    {
        var properties = await _db.Queryable<ModelPropertyEntity>()
            .Where(mp => mp.InstructionId == instructionId)
            .OrderBy(mp => mp.Sort)
            .OrderBy(mp => mp.Id)
            .ToListAsync();

        return properties.Adapt<List<ModelPropertyOutput>>();
    }

    /// <summary>
    /// 根据指令ID删除所有属性
    /// </summary>
    /// <param name="instructionId">指令ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteByInstructionIdAsync(long instructionId)
    {
        return await _modelPropertyRepository.DeleteAsync(mp => mp.InstructionId == instructionId);
    }
}