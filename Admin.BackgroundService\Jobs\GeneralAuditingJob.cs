﻿// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.SqlSugar.Entity;
using Mapster;
using Admin.Multiplex.Contracts.BackgroundArgs;
using Admin.Multiplex.Contracts.Consts;
using Admin.Multiplex.Contracts.Enums;

using SqlSugar;

using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;

using WorkflowCore.Interface;

namespace Admin.BackgroundService.Jobs;
/// <summary>
/// 通用审批Job
/// </summary>
public class GeneralAuditingJob(ISqlSugarClient db, IWorkflowHost workflowHost) : AsyncBackgroundJob<GeneralAuditingArgs>, ITransientDependency
{
    private readonly ISqlSugarClient _db = db;
    private readonly IWorkflowHost _workflowHost = workflowHost;

    public override async Task ExecuteAsync(GeneralAuditingArgs args)
    {
        var auditingRecord = args.Adapt<WfAuditingRecordEntity>();
        await _db.Insertable(auditingRecord).ExecuteReturnSnowflakeIdAsync();
        var executionPointer = await _db.Queryable<WfExecutionPointerEntity>().Includes(x => x.ExtensionAttributes).FirstAsync(x => x.PersistenceId == args.ExecutionPointerId);
        var stepType = executionPointer.ExtensionAttributes.First(x => x.AttributeKey == GeneralAuditingConst.AUDITINGSTEPTYPE).AttributeValue;
        if (stepType == ((int)GeneralAuditingStepTypeEnum.Parallel).ToString() || !args.IsAgree)
        {
            var i = await _db.Deleteable<WfWaitingPointerEntity>().Where(x => x.PointerId == executionPointer.Id).ExecuteCommandAsync();
            await _workflowHost.PublishEvent(executionPointer.EventName, executionPointer.EventKey, args.IsAgree);
            return;
        }
        if (stepType == ((int)GeneralAuditingStepTypeEnum.Serial).ToString())
        {
            var count = await _db.Queryable<WfWaitingPointerEntity>().Where(x => x.PointerId == executionPointer.Id).CountAsync();
            if (count == 0)
                await _workflowHost.PublishEvent(executionPointer.EventName, executionPointer.EventKey, args.IsAgree);
        }
    }
}
