using System.ComponentModel.DataAnnotations;

namespace Admin.Application.DeviceServices.Dto;

/// <summary>
/// 设备查询输入DTO
/// </summary>
public class DeviceQueryInput : PaginationParams
{
    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 设备状态 (0:离线 1:在线)
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 产品ID
    /// </summary>
    public long? ProductId { get; set; }

    /// <summary>
    /// 父设备ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 分组标识
    /// </summary>
    public string? GroupKey { get; set; }
    
    /// <summary>
    /// 设备标识码
    /// </summary>
    public string? DeviceIdentityCode { get; set; }

    /// <summary>
    /// 设备协议类型
    /// </summary>
    public int? ProtocolType { get; set; }

    /// <summary>
    /// 设备数据格式
    /// </summary>
    public int? DataFormat { get; set; }

    /// <summary>
    /// 设备属性类型
    /// </summary>
    public int? DeviceType { get; set; }
}

/// <summary>
/// 添加设备输入DTO
/// </summary>
public class AddDeviceInput
{
    /// <summary>
    /// 模型ID
    /// </summary>
    [Required(ErrorMessage = "模型ID不能为空")]
    public long ModelId { get; set; }

    /// <summary>
    /// 父设备ID (0表示无父设备)
    /// </summary>
    public long ParentId { get; set; }

    /// <summary>
    /// 分组标识 (网关类型产品使用，对应JSON中的分组key，如"IO"、"wenshidu")
    /// 直连设备此字段为空
    /// </summary>
    [MaxLength(50, ErrorMessage = "分组标识最大长度为50个字符")]
    public string? GroupKey { get; set; }

    /// <summary>
    /// 设备ID，用于唯一标识一个设备。如果填写该参数，平台将设备ID设置为该参数值；
    /// 设备ID长度为4至128个字符，只允许字母、数字、下划线（_）、连接符（-）的组合。
    /// 如果不填写该参数，设备ID由物联网平台分配获得，生成规则为guid + _ + IdentityCode拼接而成。
    /// 网关子设备为空字符串
    /// </summary>
    [RegularExpression(@"^[a-zA-Z0-9_-]{4,128}$", ErrorMessage = "设备ID长度为4至128个字符,只允许字母、数字、下划线(_)、连接符(-)的组合")]
    public string? DeviceId { get; set; }

    /// <summary>
    /// 设备标识码 (通常使用IMEI、MAC地址或Serial No作为设备标识码)
    /// 设备标识码长度为4至64个字符，只允许字母、数字、下划线（_）、连接符（-）的组合
    /// 网关子设备为空字符串
    /// </summary>
    [RegularExpression(@"^[a-zA-Z0-9_-]{4,64}$", ErrorMessage = "设备标识码长度为4至64个字符,只允许字母、数字、下划线(_)、连接符(-)的组合")]
    public string? DeviceIdentityCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能为空")]
    [MaxLength(100, ErrorMessage = "设备名称最大长度为100个字符")]
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "设备描述最大长度为500个字符")]
    public string? DeviceDescription { get; set; }

    /// <summary>
    /// 设备IP地址
    /// </summary>
    [RegularExpression(@"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", ErrorMessage = "请输入有效的IP地址")]
    public string? IpAddress { get; set; }

    /// <summary>
    /// 设备串口地址 (Modbus设备使用)
    /// </summary>
    [Range(1, 247, ErrorMessage = "串口地址范围为1-247")]
    public int ModbusAddr { get; set; }
}

/// <summary>
/// 更新设备输入DTO
/// </summary>
public class UpdateDeviceInput
{
    /// <summary>
    /// 产品ID
    /// </summary>
    [Required(ErrorMessage = "产品ID不能为空")]
    public long ProductId { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    [Required(ErrorMessage = "模型ID不能为空")]
    public long ModelId { get; set; }

    /// <summary>
    /// 设备协议 (1:MQTT 2:Modbus)
    /// </summary>
    [Required(ErrorMessage = "设备协议不能为空")]
    [Range(1, 2, ErrorMessage = "设备协议值必须在1-2之间")]
    public int ProtocolType { get; set; }

    /// <summary>
    /// 设备数据格式 (1:JSON 2:HEX)
    /// </summary>
    [Required(ErrorMessage = "设备数据格式不能为空")]
    [Range(1, 2, ErrorMessage = "设备数据格式值必须在1-2之间")]
    public int DataFormat { get; set; }

    /// <summary>
    /// 设备属性 (1:直连设备 2:网关设备 3:网关子设备)
    /// </summary>
    [Required(ErrorMessage = "设备属性不能为空")]
    [Range(1, 3, ErrorMessage = "设备属性值必须在1-3之间")]
    public int DeviceType { get; set; }

    /// <summary>
    /// 父设备ID (0表示无父设备)
    /// </summary>
    public long ParentId { get; set; }

    /// <summary>
    /// 分组标识 (网关类型产品使用，对应JSON中的分组key，如"IO"、"wenshidu")
    /// 直连设备此字段为空
    /// </summary>
    [MaxLength(50, ErrorMessage = "分组标识最大长度为50个字符")]
    public string? GroupKey { get; set; }

    /// <summary>
    /// 设备ID，用于唯一标识一个设备。
    /// 设备ID长度为4至128个字符，只允许字母、数字、下划线（_）、连接符（-）的组合。
    /// 网关子设备为空字符串
    /// </summary>
    [RegularExpression(@"^[a-zA-Z0-9_-]{4,128}$", ErrorMessage = "设备ID长度为4至128个字符，只允许字母、数字、下划线（_）、连接符（-）的组合")]
    public string? DeviceId { get; set; }

    /// <summary>
    /// 设备标识码 (通常使用IMEI、MAC地址或Serial No作为设备标识码)
    /// 设备标识码长度为4至64个字符，只允许字母、数字、下划线（_）、连接符（-）的组合
    /// 网关子设备为空字符串
    /// </summary>
    [RegularExpression(@"^[a-zA-Z0-9_-]{4,64}$", ErrorMessage = "设备标识码长度为4至64个字符，只允许字母、数字、下划线（_）、连接符（-）的组合")]
    public string? DeviceIdentityCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能为空")]
    [MaxLength(100, ErrorMessage = "设备名称最大长度为100个字符")]
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "设备描述最大长度为500个字符")]
    public string? DeviceDescription { get; set; }

    /// <summary>
    /// 设备IP地址
    /// </summary>
    [RegularExpression(@"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", ErrorMessage = "请输入有效的IP地址")]
    public string? IpAddress { get; set; }

    /// <summary>
    /// 设备串口地址 (Modbus设备使用)
    /// </summary>
    [Range(1, 247, ErrorMessage = "串口地址范围为1-247")]
    public int ModbusAddr { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 设备输出DTO
/// </summary>
public class DeviceOutput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 产品ID
    /// </summary>
    public long ProductId { get; set; }
    
    /// <summary>
    /// 模型ID
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    /// 设备协议 (1:MQTT 2:Modbus)
    /// </summary>
    public int ProtocolType { get; set; }

    /// <summary>
    /// 设备数据格式 (1:JSON 2:二进制码流)
    /// </summary>
    public int DataFormat { get; set; }

    /// <summary>
    /// 设备属性 (1:直连设备 2:网关设备 3:网关子设备)
    /// </summary>
    public int DeviceType { get; set; }

    /// <summary>
    /// 父设备ID
    /// </summary>
    public long ParentId { get; set; }
    
    /// <summary>
    /// 分组标识
    /// </summary>
    public string? GroupKey { get; set; }
    
    /// <summary>
    /// 设备ID，用于唯一标识一个设备
    /// 网关子设备为空字符串
    /// </summary>
    public string DeviceId { get; set; }

    /// <summary>
    /// 设备标识码 (网关子设备为空字符串)
    /// </summary>
    public string? DeviceIdentityCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }
    
    /// <summary>
    /// 设备描述
    /// </summary>
    public string? DeviceDescription { get; set; }
    
    /// <summary>
    /// 设备状态 (0:离线 1:在线)
    /// </summary>
    public int Status { get; set; }
    
    /// <summary>
    /// 设备IP地址
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// 设备串口地址 (Modbus设备使用)
    /// </summary>
    public int ModbusAddr { get; set; }

    /// <summary>
    /// 设备密钥 (网关子设备为null)
    /// </summary>
    public string? DeviceSecret { get; set; }

    /// <summary>
    /// 最后数据上报时间
    /// </summary>
    public DateTime? LastDataTime { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public long CreateBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 修改人
    /// </summary>
    public long? UpdateBy { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}