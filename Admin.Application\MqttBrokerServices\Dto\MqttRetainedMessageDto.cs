using Admin.Communication.Mqtt.Protocol;
using Admin.Multiplex.Contracts;
using System.ComponentModel.DataAnnotations;

namespace Admin.Application.MqttBrokerServices.Dto
{
    /// <summary>
    /// 获取保留消息分页列表输入
    /// </summary>
    public class GetRetainedMessagesInput : PaginationParams
    {
        /// <summary>
        /// 主题模式（支持模糊搜索）
        /// </summary>
        public string TopicPattern { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 发布者客户端ID
        /// </summary>
        public string PublisherId { get; set; }

        /// <summary>
        /// QoS等级
        /// </summary>
        [Range(0, 2, ErrorMessage = "QoS等级必须在0-2之间")]
        public int? Qos { get; set; }
    }

    /// <summary>
    /// 保留消息输出
    /// </summary>
    public class RetainedMessageOutput
    {
        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// 消息负载
        /// </summary>
        public string Payload { get; set; }

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int Qos { get; set; }

        /// <summary>
        /// 消息保留时间
        /// </summary>
        public DateTime RetainTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 负载大小（字节）
        /// </summary>
        public int PayloadSize { get; set; }

        /// <summary>
        /// 负载编码方式
        /// </summary>
        public PayloadEncoding Encoding { get; set; }

        /// <summary>
        /// 发布者客户端ID
        /// </summary>
        public string PublisherId { get; set; }

        /// <summary>
        /// 消息ID
        /// </summary>
        public long Id { get; set; }
    }

    /// <summary>
    /// 清除保留消息输入
    /// </summary>
    public class ClearRetainedMessageInput
    {
        /// <summary>
        /// 要清除的主题
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        [StringLength(256, ErrorMessage = "主题长度不能超过256个字符")]
        public string Topic { get; set; }
    }

    /// <summary>
    /// 清除保留消息输出
    /// </summary>
    public class ClearRetainedMessageOutput
    {
        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// 清除时间
        /// </summary>
        public DateTime ClearTime { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 清除的负载大小
        /// </summary>
        public int ClearedPayloadSize { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string ErrorMessage { get; set; }
    }
} 