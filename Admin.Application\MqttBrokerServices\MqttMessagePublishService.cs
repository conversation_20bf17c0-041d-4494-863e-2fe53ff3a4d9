using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text;
using Admin.Application.MqttBrokerServices.Dto;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Protocol;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.MqttBrokerServices
{
    /// <summary>
    /// MQTT消息发布管理服务接口
    /// </summary>
    public interface IMqttMessagePublishService
    {
        /// <summary>
        /// 发布消息
        /// </summary>
        Task<MessagePublishResult> PublishMessageAsync(PublishMessageRequest request);

        /// <summary>
        /// 批量发布消息
        /// </summary>
        Task<BatchMessagePublishResult> BatchPublishAsync(BatchPublishRequest request);

        /// <summary>
        /// 获取保留消息列表
        /// </summary>
        Task<PagedList<RetainedMessageOutput>> GetRetainedMessagesAsync(GetRetainedMessagesInput input);

        /// <summary>
        /// 清除指定主题的保留消息
        /// </summary>
        Task<ClearRetainedMessageOutput> ClearRetainedMessageAsync(ClearRetainedMessageInput input);
    }

    /// <summary>
    /// MQTT消息发布管理服务
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.MQTT)]
    public class MqttMessagePublishService(
        IMqttBroker mqttBroker,
        IMqttConnectionManager connectionManager,
        ISqlSugarClient db,
        ILogger<MqttMessagePublishService> logger) : ApplicationService, IMqttMessagePublishService
    {
        private readonly IMqttBroker _mqttBroker = mqttBroker;
        private readonly IMqttConnectionManager _connectionManager = connectionManager;
        private readonly ISqlSugarClient _db = db;
        private readonly ILogger<MqttMessagePublishService> _logger = logger;
        
        // 发布统计（保留内存统计以提高性能）
        private static readonly ConcurrentDictionary<string, long> _publishStatistics = new();
        
        // 配置参数
        private const int MaxTopicLength = 256;
        private const int MaxPayloadSize = 1048576; // 1MB
        private const int MaxBatchSize = 100;

        /// <summary>
        /// 发布消息
        /// </summary>
        public async Task<MessagePublishResult> PublishMessageAsync(PublishMessageRequest request)
        {
            try
            {
                _logger.LogInformation("发布消息: Topic={Topic}, QoS={Qos}, Retain={Retain}", 
                    request.Topic, request.Qos, request.Retain);

                // 验证请求
                var (isValid, errorMessage) = ValidatePublishRequest(request);
                if (!isValid)
                {
                    return MessagePublishResult.Error(errorMessage);
                }

                // 解码负载
                var payloadBytes = DecodePayload(request.Payload, request.Encoding);
                if (payloadBytes == null)
                {
                    return MessagePublishResult.Error("负载解码失败");
                }

                // 发布消息
                await _mqttBroker.PublishAsync(request.Topic, payloadBytes, request.Qos, request.Retain);

                // 如果是保留消息，更新数据库存储
                if (request.Retain)
                {
                    await UpdateRetainedMessageAsync(request.Topic, request.Payload, request.Qos, 
                        request.Encoding, null, payloadBytes.Length);
                }

                // 更新统计
                UpdatePublishStatistics(request.Topic);

                // 获取订阅者数量
                var subscriberCount = GetTopicSubscriberCount(request.Topic);

                var response = new PublishMessageResponse
                {
                    MessageId = GenerateMessageId(),
                    Topic = request.Topic,
                    Qos = request.Qos,
                    Retain = request.Retain,
                    PublishTime = DateTime.UtcNow,
                    SubscriberCount = subscriberCount,
                    PayloadSize = payloadBytes.Length
                };

                _logger.LogInformation("消息发布成功: MessageId={MessageId}, SubscriberCount={SubscriberCount}", 
                    response.MessageId, subscriberCount);

                return MessagePublishResult.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布消息失败: Topic={Topic}", request.Topic);
                return MessagePublishResult.Error($"发布消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量发布消息
        /// </summary>
        public async Task<BatchMessagePublishResult> BatchPublishAsync(BatchPublishRequest request)
        {
            try
            {
                _logger.LogInformation("批量发布 {Count} 条消息", request.Messages.Count);

                if (request.Messages.Count > MaxBatchSize)
                {
                    return BatchMessagePublishResult.Error($"批量消息数量不能超过 {MaxBatchSize} 条");
                }

                var results = new List<SinglePublishResult>();
                var successCount = 0;
                var failureCount = 0;
                var stopwatch = Stopwatch.StartNew();

                foreach (var message in request.Messages)
                {
                    try
                    {
                        // 验证单条消息
                        var (isValid, errorMessage) = ValidateSingleMessage(message);
                        if (!isValid)
                        {
                            results.Add(new SinglePublishResult
                            {
                                Topic = message.Topic,
                                Success = false,
                                Error = errorMessage
                            });
                            failureCount++;
                            continue;
                        }

                        // 解码负载
                        var payloadBytes = DecodePayload(message.Payload, message.Encoding);
                        if (payloadBytes == null)
                        {
                            results.Add(new SinglePublishResult
                            {
                                Topic = message.Topic,
                                Success = false,
                                Error = "负载解码失败"
                            });
                            failureCount++;
                            continue;
                        }

                        // 发布消息
                        await _mqttBroker.PublishAsync(message.Topic, payloadBytes, message.Qos, message.Retain);

                        // 如果是保留消息，更新数据库存储
                        if (message.Retain)
                        {
                            await UpdateRetainedMessageAsync(message.Topic, message.Payload, message.Qos, 
                                message.Encoding, null, payloadBytes.Length);
                        }

                        // 更新统计
                        UpdatePublishStatistics(message.Topic);

                        // 获取订阅者数量
                        var subscriberCount = GetTopicSubscriberCount(message.Topic);

                        results.Add(new SinglePublishResult
                        {
                            Topic = message.Topic,
                            Success = true,
                            MessageId = GenerateMessageId(),
                            PublishTime = DateTime.UtcNow,
                            SubscriberCount = subscriberCount
                        });
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量发布中单条消息失败: Topic={Topic}", message.Topic);
                        results.Add(new SinglePublishResult
                        {
                            Topic = message.Topic,
                            Success = false,
                            Error = ex.Message
                        });
                        failureCount++;
                    }
                }

                stopwatch.Stop();

                var statistics = new BatchPublishStatistics
                {
                    TotalSubscribers = 0,
                    TotalPayloadSize = 0,
                    AveragePayloadSize = 0,
                    ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                    MessagesPerSecond = stopwatch.ElapsedMilliseconds > 0 ? (double)request.Messages.Count / stopwatch.ElapsedMilliseconds * 1000 : 0
                };

                var response = new BatchPublishResponse
                {
                    TotalMessages = request.Messages.Count,
                    SuccessfulPublishes = successCount,
                    FailedPublishes = failureCount,
                    PublishTime = DateTime.UtcNow,
                    Results = results,
                    Statistics = statistics
                };

                _logger.LogInformation("批量发布完成: 总数={Total}, 成功={Success}, 失败={Failed}, 耗时={Duration}ms", 
                    request.Messages.Count, successCount, failureCount, stopwatch.ElapsedMilliseconds);

                return BatchMessagePublishResult.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量发布消息失败");
                return BatchMessagePublishResult.Error($"批量发布消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取保留消息列表
        /// </summary>
        public async Task<PagedList<RetainedMessageOutput>> GetRetainedMessagesAsync(GetRetainedMessagesInput input)
        {
            try
            {
                _logger.LogInformation("获取保留消息列表: PageIndex={PageIndex}, PageSize={PageSize}", 
                    input.PageIndex, input.PageSize);

                var query = _db.Queryable<MqttRetainedMessage>();

                // 应用过滤器
                if (!string.IsNullOrEmpty(input.TopicPattern))
                {
                    query = query.Where(m => m.Topic.Contains(input.TopicPattern));
                }

                if (!string.IsNullOrEmpty(input.PublisherId))
                {
                    query = query.Where(m => m.PublisherId != null && m.PublisherId.Contains(input.PublisherId));
                }

                if (input.Qos.HasValue)
                {
                    query = query.Where(m => m.Qos == input.Qos);
                }

                if (input.StartTime.HasValue)
                {
                    query = query.Where(m => m.RetainTime >= input.StartTime);
                }

                if (input.EndTime.HasValue)
                {
                    query = query.Where(m => m.RetainTime <= input.EndTime);
                }

                // 排序
                query = query.OrderByDescending(m => m.RetainTime);

                // 分页查询
                var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);

                // 转换为DTO
                var result = pagedList.Adapt<PagedList<RetainedMessageOutput>>();

                _logger.LogInformation("获取保留消息列表成功: 总数={Total}, 当前页={PageIndex}", 
                    pagedList.Total, input.PageIndex);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取保留消息列表失败");
                throw;
            }
        }

        /// <summary>
        /// 清除指定主题的保留消息
        /// </summary>
        public async Task<ClearRetainedMessageOutput> ClearRetainedMessageAsync(ClearRetainedMessageInput input)
        {
            try
            {
                _logger.LogInformation("清除保留消息: Topic={Topic}", input.Topic);

                // 查找要清除的保留消息
                var retainedMessage = await _db.Queryable<MqttRetainedMessage>()
                    .FirstAsync(m => m.Topic == input.Topic);

                var clearedPayloadSize = 0;
                var success = false;
                string errorMessage = null;

                if (retainedMessage != null)
                {
                    clearedPayloadSize = retainedMessage.PayloadSize;
                    
                    // 从数据库中删除
                    await _db.Deleteable<MqttRetainedMessage>()
                        .Where(m => m.Topic == input.Topic)
                        .ExecuteCommandAsync();
                    
                    // 通过发布空消息来清除MQTT代理中的保留消息
                    await _mqttBroker.PublishAsync(input.Topic, Array.Empty<byte>(), 0, true);
                    
                    success = true;
                    _logger.LogInformation("保留消息已清除: Topic={Topic}, PayloadSize={PayloadSize}", 
                        input.Topic, clearedPayloadSize);
                }
                else
                {
                    errorMessage = "未找到保留消息";
                    _logger.LogWarning("未找到要清除的保留消息: Topic={Topic}", input.Topic);
                }

                return new ClearRetainedMessageOutput
                {
                    Topic = input.Topic,
                    ClearTime = DateTime.UtcNow,
                    Success = success,
                    ClearedPayloadSize = clearedPayloadSize,
                    ErrorMessage = errorMessage
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除保留消息失败: Topic={Topic}", input.Topic);
                throw;
            }
        }

        /// <summary>
        /// 获取主题的订阅者数量
        /// </summary>
        public int GetTopicSubscriberCount(string topic)
        {
            try
            {
                return _mqttBroker.GetSubscriptionCount(topic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主题订阅者数量失败: Topic={Topic}", topic);
                return 0;
            }
        }

        /// <summary>
        /// 验证主题格式
        /// </summary>
        private bool ValidateTopic(string topic)
        {
            if (string.IsNullOrEmpty(topic))
                return false;

            if (topic.Length > MaxTopicLength)
                return false;

            return MqttTopicMatcher.IsValidTopic(topic);
        }

        /// <summary>
        /// 验证负载大小
        /// </summary>
        private bool ValidatePayloadSize(string payload)
        {
            if (string.IsNullOrEmpty(payload))
                return true;

            return Encoding.UTF8.GetByteCount(payload) <= MaxPayloadSize;
        }

        #region 私有方法

        private (bool IsValid, string ErrorMessage) ValidatePublishRequest(PublishMessageRequest request)
        {
            if (string.IsNullOrEmpty(request.Topic))
                return (false, "主题不能为空");

            if (!ValidateTopic(request.Topic))
                return (false, "主题格式无效或超过最大长度");

            if (string.IsNullOrEmpty(request.Payload))
                return (false, "消息负载不能为空");

            if (!ValidatePayloadSize(request.Payload))
                return (false, $"消息负载超过最大大小限制 ({MaxPayloadSize} 字节)");

            if (request.Qos < 0 || request.Qos > 2)
                return (false, "QoS必须在0-2之间");

            return (true, string.Empty);
        }

        private (bool IsValid, string ErrorMessage) ValidateSingleMessage(SinglePublishMessage message)
        {
            if (string.IsNullOrEmpty(message.Topic))
                return (false, "主题不能为空");

            if (!ValidateTopic(message.Topic))
                return (false, "主题格式无效或超过最大长度");

            if (string.IsNullOrEmpty(message.Payload))
                return (false, "消息负载不能为空");

            if (!ValidatePayloadSize(message.Payload))
                return (false, $"消息负载超过最大大小限制 ({MaxPayloadSize} 字节)");

            if (message.Qos < 0 || message.Qos > 2)
                return (false, "QoS必须在0-2之间");

            return (true, string.Empty);
        }

        private byte[]? DecodePayload(string payload, PayloadEncoding encoding)
        {
            try
            {
                return encoding switch
                {
                    PayloadEncoding.Utf8 => Encoding.UTF8.GetBytes(payload),
                    PayloadEncoding.Base64 => Convert.FromBase64String(payload),
                    PayloadEncoding.Hex => ConvertHexStringToByteArray(payload),
                    _ => null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解码负载失败: Encoding={Encoding}", encoding);
                return null;
            }
        }

        private byte[] ConvertHexStringToByteArray(string hex)
        {
            if (hex.Length % 2 != 0)
                throw new ArgumentException("十六进制字符串长度必须是偶数");

            var bytes = new byte[hex.Length / 2];
            for (int i = 0; i < hex.Length; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            }
            return bytes;
        }

        private string GenerateMessageId()
        {
            return $"msg_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid():N}";
        }

        /// <summary>
        /// 更新保留消息到数据库
        /// </summary>
        private async Task UpdateRetainedMessageAsync(string topic, string payload, int qos, PayloadEncoding encoding, string? publisherId, int payloadSize)
        {
            try
            {
                var now = DateTime.UtcNow;
                
                // 检查是否已存在该主题的保留消息
                var existingMessage = await _db.Queryable<MqttRetainedMessage>()
                    .FirstAsync(m => m.Topic == topic);

                if (existingMessage != null)
                {
                    // 更新现有保留消息
                    existingMessage.Payload = payload;
                    existingMessage.Qos = qos;
                    existingMessage.Encoding = encoding.ToString();
                    existingMessage.PublisherId = publisherId;
                    existingMessage.PayloadSize = payloadSize;
                    existingMessage.LastUpdateTime = now;

                    await _db.Updateable(existingMessage).ExecuteCommandAsync();
                }
                else
                {
                    // 创建新的保留消息
                    var retainedMessage = new MqttRetainedMessage
                    {
                        Topic = topic,
                        Payload = payload,
                        Qos = qos,
                        Encoding = encoding.ToString(),
                        PublisherId = publisherId,
                        PayloadSize = payloadSize,
                        RetainTime = now,
                        LastUpdateTime = now
                    };

                    await _db.Insertable(retainedMessage).ExecuteReturnSnowflakeIdAsync();
                }

                _logger.LogDebug("保留消息已存储到数据库: Topic={Topic}", topic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "存储保留消息到数据库失败: Topic={Topic}", topic);
                throw;
            }
        }

        private void UpdatePublishStatistics(string topic)
        {
            _publishStatistics.AddOrUpdate(topic, 1, (key, oldValue) => oldValue + 1);
        }

        #endregion
    }
} 