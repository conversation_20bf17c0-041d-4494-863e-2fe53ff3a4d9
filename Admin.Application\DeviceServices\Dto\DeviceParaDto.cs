using System.ComponentModel.DataAnnotations;

namespace Admin.Application.DeviceServices.Dto;

/// <summary>
/// 设备参数查询输入DTO
/// </summary>
public class DeviceParaQueryInput : PaginationParams
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long? DeviceId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    public int? DataType { get; set; }

    /// <summary>
    /// 监控状态（0: 不启用 1：启用）
    /// </summary>
    public int? MonitorStatus { get; set; }

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool? IsSave { get; set; }
}

/// <summary>
/// 添加设备参数输入DTO
/// </summary>
public class AddDeviceParaInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令ID
    /// </summary>
    [Required(ErrorMessage = "指令ID不能为空")]
    public long InstructionId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    [Required(ErrorMessage = "参数名称不能为空")]
    [MaxLength(100, ErrorMessage = "参数名称最大长度为100个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 参数序号
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "参数序号必须大于等于0")]
    public int Sort { get; set; }

    /// <summary>
    /// 参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
    /// </summary>
    [Required(ErrorMessage = "参数标识符不能为空")]
    [MaxLength(200, ErrorMessage = "参数标识符最大长度为200个字符")]
    public string Key { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    [Required(ErrorMessage = "数据类型不能为空")]
    [Range(1, 5, ErrorMessage = "数据类型值必须在1-5之间")]
    public int DataType { get; set; }

    /// <summary>
    /// 枚举说明
    /// </summary>
    [MaxLength(500, ErrorMessage = "枚举说明最大长度为500个字符")]
    public string? EnumDescription { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(50, ErrorMessage = "单位最大长度为50个字符")]
    public string? Unit { get; set; }

    /// <summary>
    /// 倍率
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "倍率必须大于0")]
    public int DivisionFactor { get; set; } = 1;

    /// <summary>
    /// 小数位数
    /// </summary>
    [Range(0, 10, ErrorMessage = "小数位数必须在0-10之间")]
    public int DecimalPlaces { get; set; } = 0;

    /// <summary>
    /// 校正比例（默认为1）
    /// </summary>
    [Range(0.001, 1000, ErrorMessage = "校正比例必须在0.001-1000之间")]
    public decimal CorrectionScale { get; set; } = 1;

    /// <summary>
    /// 校正幅度（默认为0）
    /// </summary>
    public decimal CorrectionAmplitude { get; set; } = 0;

    /// <summary>
    /// 报警上限
    /// </summary>
    public decimal AlarmUpperLimit { get; set; } = 0;

    /// <summary>
    /// 报警上限解除值
    /// </summary>
    public decimal AlarmUpperLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 报警下限
    /// </summary>
    public decimal AlarmLowerLimit { get; set; } = 0;

    /// <summary>
    /// 报警下限解除值
    /// </summary>
    public decimal AlarmLowerLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 预警上限
    /// </summary>
    public decimal WarningUpperLimit { get; set; } = 0;

    /// <summary>
    /// 预警上限解除值
    /// </summary>
    public decimal WarningUpperLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 预警下限
    /// </summary>
    public decimal WarningLowerLimit { get; set; } = 0;

    /// <summary>
    /// 预警下限解除值
    /// </summary>
    public decimal WarningLowerLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool IsSave { get; set; } = true;

    /// <summary>
    /// 保存幅度
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "保存幅度必须大于等于0")]
    public decimal SaveAmplitude { get; set; } = 0;

    /// <summary>
    /// 保存幅度类型（0: 数值 1：百分比）
    /// </summary>
    [Range(0, 1, ErrorMessage = "保存幅度类型值必须在0-1之间")]
    public int SaveAmplitudeType { get; set; } = 0;

    /// <summary>
    /// 保存间隔(毫秒)
    /// </summary>
    [Range(1000, int.MaxValue, ErrorMessage = "保存间隔必须大于等于1000毫秒")]
    public int SaveInterval { get; set; } = 5000;

    /// <summary>
    /// 监控状态（0: 不启用 1：启用）
    /// </summary>
    [Range(0, 1, ErrorMessage = "监控状态值必须在0-1之间")]
    public int MonitorStatus { get; set; } = 1;

    /// <summary>
    /// 参数描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "参数描述最大长度为500个字符")]
    public string? Description { get; set; }
}

/// <summary>
/// 更新设备参数输入DTO
/// </summary>
public class UpdateDeviceParaInput
{
    /// <summary>
    /// 参数ID
    /// </summary>
    [Required(ErrorMessage = "参数ID不能为空")]
    public long ParameterId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令ID
    /// </summary>
    [Required(ErrorMessage = "指令ID不能为空")]
    public long InstructionId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    [Required(ErrorMessage = "参数名称不能为空")]
    [MaxLength(100, ErrorMessage = "参数名称最大长度为100个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 参数序号
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "参数序号必须大于等于0")]
    public int Sort { get; set; }

    /// <summary>
    /// 参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
    /// </summary>
    [Required(ErrorMessage = "参数标识符不能为空")]
    [MaxLength(200, ErrorMessage = "参数标识符最大长度为200个字符")]
    public string Key { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    [Required(ErrorMessage = "数据类型不能为空")]
    [Range(1, 5, ErrorMessage = "数据类型值必须在1-5之间")]
    public int DataType { get; set; }

    /// <summary>
    /// 枚举说明
    /// </summary>
    [MaxLength(500, ErrorMessage = "枚举说明最大长度为500个字符")]
    public string? EnumDescription { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(50, ErrorMessage = "单位最大长度为50个字符")]
    public string? Unit { get; set; }

    /// <summary>
    /// 倍率
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "倍率必须大于0")]
    public int DivisionFactor { get; set; } = 1;

    /// <summary>
    /// 小数位数
    /// </summary>
    [Range(0, 10, ErrorMessage = "小数位数必须在0-10之间")]
    public int DecimalPlaces { get; set; } = 0;

    /// <summary>
    /// 校正比例（默认为1）
    /// </summary>
    [Range(0.001, 1000, ErrorMessage = "校正比例必须在0.001-1000之间")]
    public decimal CorrectionScale { get; set; } = 1;

    /// <summary>
    /// 校正幅度（默认为0）
    /// </summary>
    public decimal CorrectionAmplitude { get; set; } = 0;

    /// <summary>
    /// 报警上限
    /// </summary>
    public decimal AlarmUpperLimit { get; set; } = 0;

    /// <summary>
    /// 报警上限解除值
    /// </summary>
    public decimal AlarmUpperLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 报警下限
    /// </summary>
    public decimal AlarmLowerLimit { get; set; } = 0;

    /// <summary>
    /// 报警下限解除值
    /// </summary>
    public decimal AlarmLowerLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 预警上限
    /// </summary>
    public decimal WarningUpperLimit { get; set; } = 0;

    /// <summary>
    /// 预警上限解除值
    /// </summary>
    public decimal WarningUpperLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 预警下限
    /// </summary>
    public decimal WarningLowerLimit { get; set; } = 0;

    /// <summary>
    /// 预警下限解除值
    /// </summary>
    public decimal WarningLowerLimitClearValue { get; set; } = 0;

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool IsSave { get; set; } = true;

    /// <summary>
    /// 保存幅度
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "保存幅度必须大于等于0")]
    public decimal SaveAmplitude { get; set; } = 0;

    /// <summary>
    /// 保存幅度类型（0: 数值 1：百分比）
    /// </summary>
    [Range(0, 1, ErrorMessage = "保存幅度类型值必须在0-1之间")]
    public int SaveAmplitudeType { get; set; } = 0;

    /// <summary>
    /// 保存间隔(毫秒)
    /// </summary>
    [Range(1000, int.MaxValue, ErrorMessage = "保存间隔必须大于等于1000毫秒")]
    public int SaveInterval { get; set; } = 5000;

    /// <summary>
    /// 监控状态（0: 不启用 1：启用）
    /// </summary>
    [Range(0, 1, ErrorMessage = "监控状态值必须在0-1之间")]
    public int MonitorStatus { get; set; } = 1;

    /// <summary>
    /// 参数描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "参数描述最大长度为500个字符")]
    public string? Description { get; set; }
}

/// <summary>
/// 设备参数输出DTO
/// </summary>
public class DeviceParaOutput
{
    /// <summary>
    /// 参数ID
    /// </summary>
    public long ParameterId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令ID
    /// </summary>
    public long InstructionId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 参数序号
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 枚举说明
    /// </summary>
    public string? EnumDescription { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 倍率
    /// </summary>
    public int DivisionFactor { get; set; }

    /// <summary>
    /// 小数位数
    /// </summary>
    public int DecimalPlaces { get; set; }

    /// <summary>
    /// 校正比例
    /// </summary>
    public decimal CorrectionScale { get; set; }

    /// <summary>
    /// 校正幅度
    /// </summary>
    public decimal CorrectionAmplitude { get; set; }

    /// <summary>
    /// 报警上限
    /// </summary>
    public decimal AlarmUpperLimit { get; set; }

    /// <summary>
    /// 报警上限解除值
    /// </summary>
    public decimal AlarmUpperLimitClearValue { get; set; }

    /// <summary>
    /// 报警下限
    /// </summary>
    public decimal AlarmLowerLimit { get; set; }

    /// <summary>
    /// 报警下限解除值
    /// </summary>
    public decimal AlarmLowerLimitClearValue { get; set; }

    /// <summary>
    /// 预警上限
    /// </summary>
    public decimal WarningUpperLimit { get; set; }

    /// <summary>
    /// 预警上限解除值
    /// </summary>
    public decimal WarningUpperLimitClearValue { get; set; }

    /// <summary>
    /// 预警下限
    /// </summary>
    public decimal WarningLowerLimit { get; set; }

    /// <summary>
    /// 预警下限解除值
    /// </summary>
    public decimal WarningLowerLimitClearValue { get; set; }

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool IsSave { get; set; }

    /// <summary>
    /// 保存幅度
    /// </summary>
    public decimal SaveAmplitude { get; set; }

    /// <summary>
    /// 保存幅度类型（0: 数值 1：百分比）
    /// </summary>
    public int SaveAmplitudeType { get; set; }

    /// <summary>
    /// 保存间隔(毫秒)
    /// </summary>
    public int SaveInterval { get; set; }

    /// <summary>
    /// 监控状态（0: 不启用 1：启用）
    /// </summary>
    public int MonitorStatus { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 设备参数简单输出DTO（用于下拉选择等场景）
/// </summary>
public class DeviceParaSimpleOutput
{
    /// <summary>
    /// 参数ID
    /// </summary>
    public long ParameterId { get; set; }

    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 参数标识符
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 监控状态（0: 不启用 1：启用）
    /// </summary>
    public int MonitorStatus { get; set; }
}

/// <summary>
/// 批量删除设备参数输入DTO
/// </summary>
public class BatchDeleteDeviceParaInput
{
    /// <summary>
    /// 参数ID列表
    /// </summary>
    [Required(ErrorMessage = "参数ID列表不能为空")]
    [MinLength(1, ErrorMessage = "至少选择一个参数")]
    public List<long> ParameterIds { get; set; }
}

/// <summary>
/// 设置设备参数监控状态输入DTO
/// </summary>
public class SetDeviceParaMonitorStatusInput
{
    /// <summary>
    /// 参数ID列表
    /// </summary>
    [Required(ErrorMessage = "参数ID列表不能为空")]
    [MinLength(1, ErrorMessage = "至少选择一个参数")]
    public List<long> ParameterIds { get; set; }

    /// <summary>
    /// 监控状态（0: 不启用 1：启用）
    /// </summary>
    [Required(ErrorMessage = "监控状态不能为空")]
    [Range(0, 1, ErrorMessage = "监控状态值必须在0-1之间")]
    public int MonitorStatus { get; set; }
}


