<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PurestAdmin.Application</name>
    </assembly>
    <members>
        <member name="T:PurestAdmin.Application.AuthServices.AuthService">
            <summary>
            用户授权服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.#ctor(PurestAdmin.Multiplex.Contracts.IAdminUser.OAuth2.IOAuth2UserManager,Microsoft.AspNetCore.SignalR.IHubContext{PurestAdmin.Multiplex.AdminUser.AuthorizationHub,PurestAdmin.Multiplex.Contracts.IAdminUser.OAuth2.IAuthorizationClient},Microsoft.Extensions.Configuration.IConfiguration,PurestAdmin.Multiplex.Contracts.IAdminUser.IAdminToken,Microsoft.AspNetCore.Http.IHttpContextAccessor,SqlSugar.ISqlSugarClient,PurestAdmin.Multiplex.Contracts.IAdminUser.ICurrentUser)">
            <summary>
            用户授权服务
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.AuthServices.AuthService._oAuth2UserManager">
            <summary>
            oAuth2UserManager
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.AuthServices.AuthService._hubContext">
            <summary>
            hubContext
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.AuthServices.AuthService._configuration">
            <summary>
            configuration
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.AuthServices.AuthService._adminToken">
            <summary>
            IAdminToken
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.AuthServices.AuthService._db">
            <summary>
            db
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.AuthServices.AuthService._httpContextAccessor">
            <summary>
            IHttpContextAccessor
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.AuthServices.AuthService._currentUser">
            <summary>
            当前用户
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.LoginAsync(PurestAdmin.Application.AuthServices.Dtos.LoginInput)">
            <summary>
            用户登录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetCallbackAsync(PurestAdmin.Application.AuthServices.Dtos.GetCallbackInput)">
            <summary>
            Auht2.0 回调服务
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.BindUserAsync(PurestAdmin.Application.AuthServices.Dtos.BindUserInput)">
            <summary>
            绑定用户
            </summary>
            <param name="input">input</param>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.RegisterUserAsync(PurestAdmin.Application.AuthServices.Dtos.RegisterUserInput)">
            <summary>
            注册用户
            </summary>
            <param name="input">input</param>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetVbenUserInfoAsync">
            <summary>
            获取当前用户信息（vben）
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetUserInfoAsync(System.String)">
            <summary>
            获取当前用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetFunctionsAsync">
            <summary>
            获取当前用户的功能
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.PutUserInfoAsync(PurestAdmin.Application.AuthServices.Dtos.PutUserInfoInput)">
            <summary>
            修改当前用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetOrganizationTreeAsync">
            <summary>
            获取当前用户组织机构树
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetSystemPlatformInfoAsync">
            <summary>
            获得当前平台信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetUnreadNoticeAsync">
            <summary>
            获得用户的通知
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.AuthServices.AuthService.GetTokenAndUserInfoAsync(System.Int64)">
            <summary>
            获取TokenAndUserInfo
            </summary>
            <param name="oAuth2UserId"></param>
            <returns></returns>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.BindUserInput.ConnectionId">
            <summary>
            ConnectionId
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.BindUserInput.OAuth2UserId">
            <summary>
            OAuth2User表持久化Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetCallbackInput.Code">
            <summary>
            code
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetCallbackInput.State">
            <summary>
            state
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Telephone">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Leader">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.GetSystemPlatformInfoOutput">
            <summary>
            系统信息
            </summary>
            <remarks>引用来源https://gitee.com/whuanle/reflection_and_properties/blob/master/%E5%8F%8D%E5%B0%84%E7%89%B9%E6%80%A7%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF1.cs</remarks>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.GetUserInfoOutput">
            <summary>
            用户信息输出
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetUserInfoOutput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetUserInfoOutput.Name">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetUserInfoOutput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetUserInfoOutput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.GetVbenUserInfoOutput">
            <summary>
            用户信息输出
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.UserName">
            <summary>
            用户名（account）
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.Roles">
            <summary>
            角色
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.LoginInput">
            <summary>
            登录模型
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.LoginInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.LoginInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.LoginOutput">
            <summary>
            登录模型
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.LoginOutput.Id">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.LoginOutput.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.PutUserInfoInput">
            <summary>
            用户信息输出
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.PutUserInfoInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.PutUserInfoInput.Name">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.PutUserInfoInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.PutUserInfoInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.RegisterUserInput">
            <summary>
            用户注册模型
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.RegisterUserInput.OAuth2UserId">
            <summary>
            OAuth2User表持久化Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.RegisterUserInput.ConnectionId">
            <summary>
            ConnectionId
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.RegisterUserInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.RegisterUserInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.AuthServices.Dtos.RegisterUserInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.AuthServices.Dtos.RegisterUserInputToEntity">
            <summary>
            注册用户映射
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.DictCategoryServices.DictCategoryService">
            <summary>
            字典分类服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.DictCategoryServices.DictCategoryService.#ctor(SqlSugar.ISqlSugarClient,PurestAdmin.SqlSugar.Repository{PurestAdmin.SqlSugar.Entity.DictCategoryEntity})">
            <summary>
            字典分类服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.DictCategoryServices.DictCategoryService.GetPagedListAsync(PurestAdmin.Application.DictCategoryServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictCategoryServices.DictCategoryService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictCategoryServices.DictCategoryService.AddAsync(PurestAdmin.Application.DictCategoryServices.Dtos.AddDictCategoryInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictCategoryServices.DictCategoryService.PutAsync(System.Int64,PurestAdmin.Application.DictCategoryServices.Dtos.AddDictCategoryInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictCategoryServices.DictCategoryService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PurestAdmin.Application.DictCategoryServices.Dtos.AddDictCategoryInput">
            <summary>
            字典分类添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.AddDictCategoryInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.AddDictCategoryInput.Name">
            <summary>
            分类名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.AddDictCategoryInput.Code">
            <summary>
            分类编码
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.DictCategoryServices.Dtos.DictCategoryOutput">
            <summary>
            字典分类详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Name">
            <summary>
            分类名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Code">
            <summary>
            分类编码
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.DictCategoryServices.Dtos.GetPagedListInput">
            <summary>
            字典分类查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictCategoryServices.Dtos.GetPagedListInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.DictDataServices.DictDataService">
            <summary>
            字典数据服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.DictDataServices.DictDataService.#ctor(SqlSugar.ISqlSugarClient,PurestAdmin.SqlSugar.Repository{PurestAdmin.SqlSugar.Entity.DictDataEntity})">
            <summary>
            字典数据服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.DictDataServices.DictDataService.GetPagedListAsync(PurestAdmin.Application.DictDataServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictDataServices.DictDataService.GetListAsync(System.String)">
            <summary>
            查询分类下的所有数据
            </summary>
            <param name="categoryCode"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictDataServices.DictDataService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictDataServices.DictDataService.AddAsync(PurestAdmin.Application.DictDataServices.Dtos.AddDictDataInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictDataServices.DictDataService.PutAsync(System.Int64,PurestAdmin.Application.DictDataServices.Dtos.AddDictDataInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.DictDataServices.DictDataService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PurestAdmin.Application.DictDataServices.Dtos.AddDictDataInput">
            <summary>
            字典数据添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.AddDictDataInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.AddDictDataInput.CategoryId">
            <summary>
            字典分类ID
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.AddDictDataInput.Name">
            <summary>
            字典名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.AddDictDataInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.DictDataServices.Dtos.DictDataOutput">
            <summary>
            字典数据详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.DictDataOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.DictDataOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.DictDataOutput.CategoryId">
            <summary>
            字典分类ID
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.DictDataOutput.Name">
            <summary>
            字典名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.DictDataOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.DictDataServices.Dtos.GetPagedListInput">
            <summary>
            字典数据查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.GetPagedListInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.DictDataServices.Dtos.GetPagedListInput.CategoryId">
            <summary>
            字典分类Id
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.FunctionServices.Dtos.AddFunctionInput">
            <summary>
            组织机构添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.AddFunctionInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.AddFunctionInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.AddFunctionInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.AddFunctionInput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.AssignInterfaceInput.FunctionId">
            <summary>
            功能Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.AssignInterfaceInput.InterfaceId">
            <summary>
            接口Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.BindedInterfaceOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.BindedInterfaceOutput.Name">
            <summary>
            接口名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.BindedInterfaceOutput.Path">
            <summary>
            接口地址
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.BindedInterfaceOutput.InterfaceId">
            <summary>
            接口Id 
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.FunctionServices.Dtos.FunctionOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.FunctionOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.FunctionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.FunctionOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.FunctionOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.FunctionOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.FunctionOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.FunctionServices.Dtos.GetPagedListInput">
            <summary>
            组织机构查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.FunctionServices.Dtos.GetPagedListInput.Name">
            <summary>
            功能名称
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.FunctionServices.FunctionService">
            <summary>
            功能服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.#ctor(SqlSugar.ISqlSugarClient,PurestAdmin.SqlSugar.Repository{PurestAdmin.SqlSugar.Entity.FunctionEntity},PurestAdmin.Core.Cache.IAdminCache)">
            <summary>
            功能服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.GetPagedListAsync(PurestAdmin.Application.FunctionServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.AddAsync(PurestAdmin.Application.FunctionServices.Dtos.AddFunctionInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.PutAsync(System.Int64,PurestAdmin.Application.FunctionServices.Dtos.AddFunctionInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.GetTreeAsync">
            <summary>
            功能树查询
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.GetInterfacesAsync(System.Int64)">
            <summary>
            获取功能拥有的接口
            </summary>
            <param name="functionId"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.AssignInterfaceAsync(PurestAdmin.Application.FunctionServices.Dtos.AssignInterfaceInput)">
            <summary>
            给功能分配接口
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:PurestAdmin.Application.FunctionServices.FunctionService.DeleteFunctionInterfaceAsync(System.Int64)">
            <summary>
            移除功能的接口
            </summary>
            <param name="id"></param>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.GetPagedListInput.Path">
            <summary>
            地址
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceGroupOutput">
            <summary>
            接口组
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Interfaces">
            <summary>
            接口详情
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceOutput">
            <summary>
            接口详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceOutput.Id">
            <summary>
            id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceOutput.Name">
            <summary>
            接口名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceOutput.Path">
            <summary>
            接口地址
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceOutput.RequestMethod">
            <summary>
            请求方法
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.InterfaceServices.Dtos.InterfaceOutput.GroupId">
            <summary>
            分组Id
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.InterfaceServices.InterfaceService">
            <summary>
            接口服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.InterfaceServices.InterfaceService.#ctor(SqlSugar.ISqlSugarClient,Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionGroupCollectionProvider)">
            <summary>
            接口服务
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.InterfaceServices.InterfaceService._db">
            <summary>
            db
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.InterfaceServices.InterfaceService._apiDescriptionGroupCollectionProvider">
            <summary>
            apiDescriptionGroupCollectionProvider
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.InterfaceServices.InterfaceService.GetPagedListAsync(PurestAdmin.Application.InterfaceServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.InterfaceServices.InterfaceService.AsyncApi">
            <summary>
            同步接口
            </summary>
            <returns></returns>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.AddNoticeInput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.AddNoticeInput.Content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.AddNoticeInput.NoticeType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.AddNoticeInput.Level">
            <summary>
            级别
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.AddNoticeInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.GetPagedListInput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.GetPagedListInput.NoticeType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.GetPagedListInput.Level">
            <summary>
            级别
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.Content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.NoticeType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.Level">
            <summary>
            级别
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.NoticeTypeString">
            <summary>
            类型string
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.NoticeServices.Dtos.NoticeOutput.LevelString">
            <summary>
            级别string
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.NoticeServices.NoticeService">
            <summary>
            通知公告
            </summary>
            <param name="db"></param>
            <param name="backgroundJobManager"></param>
        </member>
        <member name="M:PurestAdmin.Application.NoticeServices.NoticeService.#ctor(SqlSugar.ISqlSugarClient,Volo.Abp.BackgroundJobs.IBackgroundJobManager)">
            <summary>
            通知公告
            </summary>
            <param name="db"></param>
            <param name="backgroundJobManager"></param>
        </member>
        <member name="F:PurestAdmin.Application.NoticeServices.NoticeService._db">
            <summary>
            db
            </summary>
        </member>
        <member name="F:PurestAdmin.Application.NoticeServices.NoticeService._backgroundJobManager">
            <summary>
            IBackgroundJobManager
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.NoticeServices.NoticeService.GetPagedListAsync(PurestAdmin.Application.NoticeServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.NoticeServices.NoticeService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.NoticeServices.NoticeService.AddAsync(PurestAdmin.Application.NoticeServices.Dtos.AddNoticeInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.NoticeServices.NoticeService.PutAsync(System.Int64,PurestAdmin.Application.NoticeServices.Dtos.PutNoticeInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.NoticeServices.NoticeService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.NoticeServices.NoticeService.SendAsync(System.Int64,System.Int64[])">
            <summary>
            发送通知
            </summary>
            <param name="userIds"></param>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput">
            <summary>
            组织机构添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput.Telephone">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput.Leader">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.OrganizationServices.Dtos.GetPagedListInput">
            <summary>
            组织机构查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.GetPagedListInput.Name">
            <summary>
            组织机构名称
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.Telephone">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.Leader">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.OrganizationServices.Dtos.OrganizationOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.OrganizationServices.OrganizationService">
            <summary>
            组织机构服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.OrganizationServices.OrganizationService.#ctor(PurestAdmin.SqlSugar.Repository{PurestAdmin.SqlSugar.Entity.OrganizationEntity},PurestAdmin.Multiplex.Contracts.IAdminUser.ICurrentUser)">
            <summary>
            组织机构服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.OrganizationServices.OrganizationService.GetPagedListAsync(PurestAdmin.Application.OrganizationServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.OrganizationServices.OrganizationService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.OrganizationServices.OrganizationService.AddAsync(PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.OrganizationServices.OrganizationService.PutAsync(System.Int64,PurestAdmin.Application.OrganizationServices.Dtos.AddOrganizationInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.OrganizationServices.OrganizationService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PurestAdmin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput">
            <summary>
            系统文件添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.File">
            <summary>
            文件
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.ProfileSystemServices.Dtos.GetPagedListInput">
            <summary>
            系统配置表查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.GetPagedListInput.Name">
            <summary>
            配置名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.GetPagedListInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput">
            <summary>
            系统文件详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.FileId">
            <summary>
            文件Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.FileName">
            <summary>
            文件名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService">
            <summary>
            系统文件服务
            </summary>
            <param name="db"></param>
            <param name="fileCommand"></param>
            <param name="httpContextAccessor"></param>
            <param name="objectValidator"></param>
        </member>
        <member name="M:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService.#ctor(SqlSugar.ISqlSugarClient,PurestAdmin.Core.File.IFileCommand{PurestAdmin.Core.File.Containers.ProfileSystemContainer},Microsoft.AspNetCore.Http.IHttpContextAccessor,Volo.Abp.Validation.IObjectValidator)">
            <summary>
            系统文件服务
            </summary>
            <param name="db"></param>
            <param name="fileCommand"></param>
            <param name="httpContextAccessor"></param>
            <param name="objectValidator"></param>
        </member>
        <member name="M:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService.GetPagedListAsync(PurestAdmin.Application.ProfileSystemServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService.AddByStreamAsync">
            <summary>
            添加（文件流式上传）
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService.AddAsync(PurestAdmin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput)">
            <summary>
            添加
            </summary>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.ProfileSystemServices.ProfileSystemService.DownloadAsync(System.Int64)">
            <summary>
            文件下载
            </summary>
            <param name="fileId"></param>
            <returns>FileStreamResult</returns>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.GetPagedListInput.RequestDate">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.GetPagedListInput.ControllerName">
            <summary>
            控制器名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.GetPagedListInput.ActionName">
            <summary>
            方法名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.GetRequestLogChartInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.GetRequestLogChartInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.Id">
             <summary>
             Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.ControllerName">
            <summary>
            控制器
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.ActionName">
            <summary>
            方法名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.RequestMethod">
            <summary>
            请求类型
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.EnvironmentName">
            <summary>
            服务器环境
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.IsSuccess">
            <summary>
            完成情况
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.ElapsedTime">
            <summary>
            执行耗时
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.ClientIp">
            <summary>
            客户端IP
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RequestLogServices.Dtos.RequestLogOutput.CreateTime">
            <summary>
            时间
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.RequestLogServices.RequestLogService">
            <summary>
            请求日志服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.RequestLogServices.RequestLogService.#ctor(SqlSugar.ISqlSugarClient)">
            <summary>
            请求日志服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.RequestLogServices.RequestLogService.GetPagedListAsync(PurestAdmin.Application.RequestLogServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RequestLogServices.RequestLogService.GetRequestLogChartAsync(PurestAdmin.Application.RequestLogServices.Dtos.GetRequestLogChartInput)">
            <summary>
            按日期获取请求日志统计数
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:PurestAdmin.Application.RoleServices.Dtos.AddRoleInput">
            <summary>
            角色添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.AddRoleInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.AddRoleInput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.AddRoleInput.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.AddRoleInput.SecurityIds">
            <summary>
            权限Id集合
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.RoleServices.Dtos.FunctionOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.FunctionOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.FunctionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.FunctionOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.FunctionOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.FunctionOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.FunctionOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.RoleServices.Dtos.GetPagedListInput">
            <summary>
            角色查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.GetPagedListInput.Name">
            <summary>
            角色名
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.RoleServices.Dtos.RoleOutput">
            <summary>
            角色详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.RoleOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.RoleOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.RoleOutput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.RoleOutput.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.RoleOutput.WebsiteSecurityIds">
            <summary>
            website权限
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.RoleServices.Dtos.RoleOutput.InterfaceSecurityIds">
            <summary>
            interface权限
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.RoleServices.RoleService">
            <summary>
            角色服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.#ctor(SqlSugar.ISqlSugarClient,PurestAdmin.SqlSugar.Repository{PurestAdmin.SqlSugar.Entity.RoleEntity},PurestAdmin.Core.Cache.IAdminCache)">
            <summary>
            角色服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.GetPagedListAsync(PurestAdmin.Application.RoleServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.GetRolesAsync(System.String)">
            <summary>
            全量查询
            </summary>
            <param name="roleName"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.AddAsync(PurestAdmin.Application.RoleServices.Dtos.AddRoleInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.PutAsync(System.Int64,PurestAdmin.Application.RoleServices.Dtos.AddRoleInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.AssignFunctionAsync(System.Int64,System.Int64[])">
            <summary>
            赋给角色功能
            </summary>
            <param name="roleId">角色Id</param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.RoleServices.RoleService.GetFunctionsAsync(System.Int64)">
            <summary>
            获取角色的功能
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.SystemConfigServices.Dtos.AddSystemConfigInput">
            <summary>
            系统配置表添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.ConfigCode">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.ConfigValue">
            <summary>
            值
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.SystemConfigServices.Dtos.GetPagedListInput">
            <summary>
            系统配置表查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.GetPagedListInput.Name">
            <summary>
            配置名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.GetPagedListInput.ConfigCode">
            <summary>
            配置编码
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.SystemConfigServices.Dtos.SystemConfigOutput">
            <summary>
            系统配置表详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.SystemConfigOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.SystemConfigOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.SystemConfigOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.SystemConfigOutput.ConfigCode">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.SystemConfigServices.Dtos.SystemConfigOutput.ConfigValue">
            <summary>
            值
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.SystemConfigServices.SystemConfigService">
            <summary>
            系统配置表服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.SystemConfigServices.SystemConfigService.#ctor(SqlSugar.ISqlSugarClient,PurestAdmin.SqlSugar.Repository{PurestAdmin.SqlSugar.Entity.SystemConfigEntity})">
            <summary>
            系统配置表服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.SystemConfigServices.SystemConfigService.GetPagedListAsync(PurestAdmin.Application.SystemConfigServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.SystemConfigServices.SystemConfigService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.SystemConfigServices.SystemConfigService.AddAsync(PurestAdmin.Application.SystemConfigServices.Dtos.AddSystemConfigInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.SystemConfigServices.SystemConfigService.PutAsync(System.Int64,PurestAdmin.Application.SystemConfigServices.Dtos.AddSystemConfigInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.SystemConfigServices.SystemConfigService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:PurestAdmin.Application.UserServices.Dtos.AddUserInput">
            <summary>
            用户添加
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.OrganizationId">
            <summary>
            组织机构Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.AddUserInput.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.UserServices.Dtos.GetPagedListInput">
            <summary>
            用户查询
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.GetPagedListInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.GetPagedListInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.GetPagedListInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.GetPagedListInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.GetPagedListInput.Status">
            <summary>
            账户状态
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.UserServices.Dtos.PutUserInput">
            <summary>
            用户编辑
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.PutUserInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.PutUserInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.PutUserInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.PutUserInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.PutUserInput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.PutUserInput.OrganizationId">
            <summary>
            组织机构Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.PutUserInput.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.UserServices.Dtos.UserOutput">
            <summary>
            用户详情
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Account">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Name">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.OrganizationId">
            <summary>
            组织机构Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.OrganizationName">
            <summary>
            组织机构名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:PurestAdmin.Application.UserServices.Dtos.UserOutput.Status">
            <summary>
            用户状态
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.UserServices.Mapper.AddUserInputToEntity">
            <summary>
            新增用户映射
            </summary>
        </member>
        <member name="T:PurestAdmin.Application.UserServices.UserService">
            <summary>
            用户服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.#ctor(SqlSugar.ISqlSugarClient,PurestAdmin.SqlSugar.Repository{PurestAdmin.SqlSugar.Entity.UserEntity},Microsoft.AspNetCore.SignalR.IHubContext{PurestAdmin.Multiplex.AdminUser.OnlineUserHub,PurestAdmin.Multiplex.Contracts.IAdminUser.IOnlineUserClient},PurestAdmin.Multiplex.Contracts.IAdminUser.ICacheOnlineUser)">
            <summary>
            用户服务
            </summary>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.GetPagedListAsync(PurestAdmin.Application.UserServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.AddAsync(PurestAdmin.Application.UserServices.Dtos.AddUserInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.PutAsync(System.Int64,PurestAdmin.Application.UserServices.Dtos.PutUserInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.StopAsync(System.Int64)">
            <summary>
            账户停用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.NormalAsync(System.Int64)">
            <summary>
            账户恢复正常
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:PurestAdmin.Application.UserServices.UserService.ResetPasswordAsync(System.Int64)">
            <summary>
            重置密码
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
    </members>
</doc>
