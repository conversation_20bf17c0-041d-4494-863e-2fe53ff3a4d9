// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Application.ProductServices.Dto;

/// <summary>
/// 产品输入DTO
/// </summary>
public class ProductInput
{
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 协议类型 (1:MQTT 2:Modbus)
    /// </summary>
    public int ProtocolType { get; set; }

    /// <summary>
    /// 产品类型 (1:直连设备 2:网关设备 3:网关子设备)
    /// </summary>
    public int ProductType { get; set; } = 1;

    /// <summary>
    /// 数据格式 (1:JSON 2:HEX)
    /// </summary>
    public int DataFormat { get; set; }

    /// <summary>
    /// 产品描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 产品更新输入DTO
/// </summary>
public class UpdateProductInput : ProductInput
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
/// 产品输出DTO
/// </summary>
public class ProductOutput
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 协议类型
    /// </summary>
    public int ProtocolType { get; set; }

    /// <summary>
    /// 协议类型名称
    /// </summary>
    public string ProtocolTypeName { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public int ProductType { get; set; }

    /// <summary>
    /// 产品类型名称
    /// </summary>
    public string ProductTypeName { get; set; }

    /// <summary>
    /// 数据格式
    /// </summary>
    public int DataFormat { get; set; }

    /// <summary>
    /// 数据格式名称
    /// </summary>
    public string DataFormatName { get; set; }

    /// <summary>
    /// 产品描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public long CreateBy { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public long? UpdateBy { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// 产品查询输入DTO
/// </summary>
public class ProductQueryInput : PaginationParams
{
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 协议类型
    /// </summary>
    public int? ProtocolType { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public int? ProductType { get; set; }

    /// <summary>
    /// 数据格式
    /// </summary>
    public int? DataFormat { get; set; }
}

/// <summary>
/// 产品简单输出DTO (用于下拉选择等场景)
/// </summary>
public class ProductSimpleOutput
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 协议类型
    /// </summary>
    public int ProtocolType { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public int ProductType { get; set; }

    /// <summary>
    /// 数据格式
    /// </summary>
    public int DataFormat { get; set; }
} 