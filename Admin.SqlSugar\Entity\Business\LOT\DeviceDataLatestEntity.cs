// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 设备最新数据
/// </summary>
[SugarTable("DEVICE_DATA_LATEST")]
public partial class DeviceDataLatestEntity : BaseEntity
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [SugarColumn(ColumnName = "DEVICE_ID")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    [SugarColumn(ColumnName = "MODEL_ID")]
    public long ModelId { get; set; }

    /// <summary>
    /// 属性ID
    /// </summary>
    [SugarColumn(ColumnName = "PROPERTY_ID")]
    public long PropertyId { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    [SugarColumn(ColumnName = "PROPERTY_NAME")]
    public string PropertyName { get; set; }

    /// <summary>
    /// 数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
    /// </summary>
    [SugarColumn(ColumnName = "DATA_TYPE")]
    public int DataType { get; set; }

    /// <summary>
    /// 数据值
    /// </summary>
    [SugarColumn(ColumnName = "DATA_VALUE")]
    public string DataValue { get; set; }

    /// <summary>
    /// 属性单位
    /// </summary>
    [SugarColumn(ColumnName = "UNIT")]
    public string Unit { get; set; }

    /// <summary>
    /// 数据状态 (1:正常 2:异常)
    /// </summary>
    [SugarColumn(ColumnName = "DATA_STATUS")]
    public int DataStatus { get; set; } = 1;

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(ColumnName = "UPDATE_TIME")]
    public DateTime UpdateTime { get; set; }
}

