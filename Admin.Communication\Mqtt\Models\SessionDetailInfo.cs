// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System.Collections.Generic;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Communication.Mqtt.Models;

/// <summary>
/// 会话详细信息
/// </summary>
public class SessionDetailInfo
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 会话信息
    /// </summary>
    public MqttSessionEntity Session { get; set; }

    /// <summary>
    /// 订阅信息列表
    /// </summary>
    public List<MqttSubscriptionEntity> Subscriptions { get; set; } = [];

    /// <summary>
    /// 消息统计信息
    /// </summary>
    public PendingMessageStatistics MessageStatistics { get; set; } = new();
}
