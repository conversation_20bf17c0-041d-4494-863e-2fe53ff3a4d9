// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Alarm.Services;
using Admin.Multiplex.Contracts.Enums;
using Admin.SqlSugar.Entity;
using Admin.SqlSugar.Entity.Business.LOT;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Volo.Abp.DependencyInjection;

namespace Admin.Communication.DeviceOnline.Services;

/// <summary>
/// 设备在线检测服务
/// </summary>
public class DeviceOnlineDetectionService(
    ISqlSugarClient db,
    IMqttConnectionManager connectionManager,
    AlarmService alarmService,
    ILogger<DeviceOnlineDetectionService> logger) : ITransientDependency
{
    private readonly ISqlSugarClient _db = db;
    private readonly IMqttConnectionManager _connectionManager = connectionManager;
    private readonly AlarmService _alarmService = alarmService;
    private readonly ILogger<DeviceOnlineDetectionService> _logger = logger;

    /// <summary>
    /// 检测设备在线状态配置
    /// </summary>
    public class DetectionConfig
    {
        /// <summary>
        /// 检测间隔(秒)，默认30秒
        /// </summary>
        public int CheckIntervalSeconds { get; set; } = 30;
    }

    /// <summary>
    /// 设备连接状态枚举
    /// </summary>
    public enum DeviceConnectionStatus
    {
        /// <summary>
        /// 在线 - MQTT连接正常
        /// </summary>
        Online,

        /// <summary>
        /// 离线 - MQTT连接断开
        /// </summary>
        Offline
    }

    /// <summary>
    /// 设备在线检测结果
    /// </summary>
    public class DeviceOnlineDetectionResult
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public long DeviceId { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string DeviceCode { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 连接状态
        /// </summary>
        public DeviceConnectionStatus Status { get; set; }

        /// <summary>
        /// MQTT是否连接
        /// </summary>
        public bool MqttConnected { get; set; }

        /// <summary>
        /// 最后Keep-Alive时间
        /// </summary>
        public DateTime? LastKeepAlive { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public int DeviceType { get; set; }

        /// <summary>
        /// 原始设备状态
        /// </summary>
        public int OriginalStatus { get; set; }

        /// <summary>
        /// 是否需要更新状态
        /// </summary>
        public bool NeedStatusUpdate { get; set; }

        /// <summary>
        /// 是否需要告警处理
        /// </summary>
        public bool NeedAlarmProcessing { get; set; }
    }

    /// <summary>
    /// 执行设备在线检测
    /// </summary>
    /// <param name="config">检测配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>检测结果列表</returns>
    public async Task<List<DeviceOnlineDetectionResult>> ExecuteDetectionAsync(
        DetectionConfig? config = null,
        CancellationToken cancellationToken = default)
    {
        config ??= new DetectionConfig();
        var results = new List<DeviceOnlineDetectionResult>();

        try
        {
            _logger.LogDebug("开始执行设备在线检测");

            // 1. 获取所有直连设备和网关设备
            var devices = await GetTargetDevicesAsync();
            _logger.LogDebug("获取到 {Count} 个需要检测的设备", devices.Count);

            // 2. 逐个检测设备状态
            foreach (var device in devices)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var result = DetectSingleDevice(device);
                results.Add(result);
            }

            _logger.LogDebug("设备在线检测完成，共检测 {Count} 个设备", results.Count);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行设备在线检测时发生异常");
            return results;
        }
    }

    /// <summary>
    /// 处理检测结果 - 更新设备状态和处理告警
    /// </summary>
    /// <param name="results">检测结果列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task ProcessDetectionResultsAsync(
        List<DeviceOnlineDetectionResult> results, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statusUpdateCount = 0;
            var alarmProcessCount = 0;

            foreach (var result in results)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                // 1. 更新设备状态
                if (result.NeedStatusUpdate)
                {
                    await UpdateDeviceStatusAsync(result);
                    statusUpdateCount++;
                }

                // 2. 处理通讯告警
                if (result.NeedAlarmProcessing)
                {
                    await ProcessCommunicationAlarmAsync(result);
                    alarmProcessCount++;
                }
            }

            _logger.LogInformation("设备状态处理完成: 状态更新={StatusCount}, 告警处理={AlarmCount}", 
                statusUpdateCount, alarmProcessCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备检测结果时发生异常");
        }
    }

    /// <summary>
    /// 获取需要检测的目标设备（直连设备和网关设备）
    /// </summary>
    /// <returns>设备列表</returns>
    private async Task<List<DeviceEntity>> GetTargetDevicesAsync()
    {
        try
        {
            // 查询直连设备和网关设备
            var devices = await _db.Queryable<DeviceEntity>()
                .LeftJoin<ProductEntity>((d, p) => d.ModelId == p.Id)
                .Where((d, p) => d.IsEnabled &&
                               (p.ProductType == (int)DevicePropertyTypeEnum.DirectDevice ||
                                p.ProductType == (int)DevicePropertyTypeEnum.GatewayDevice) &&
                               !string.IsNullOrEmpty(d.DeviceId))
                .Select((d, p) => new DeviceEntity
                {
                    Id = d.Id,
                    DeviceId = d.DeviceId,
                    DeviceName = d.DeviceName,
                    Status = d.Status,
                    ModelId = d.ModelId,
                    IsEnabled = d.IsEnabled
                })
                .ToListAsync();

            return devices ?? new List<DeviceEntity>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取目标设备列表时发生异常");
            return new List<DeviceEntity>();
        }
    }

    /// <summary>
    /// 检测单个设备状态
    /// </summary>
    /// <param name="device">设备实体</param>
    /// <returns>检测结果</returns>
    private DeviceOnlineDetectionResult DetectSingleDevice(DeviceEntity device)
    {
        var result = new DeviceOnlineDetectionResult
        {
            DeviceId = device.Id,
            DeviceCode = device.DeviceId,
            DeviceName = device.DeviceName,
            OriginalStatus = device.Status,
            Status = DeviceConnectionStatus.Offline
        };

        try
        {
            // 检查MQTT连接状态
            result.MqttConnected = _connectionManager.IsConnected(device.DeviceId);
            if (result.MqttConnected)
            {
                var connection = _connectionManager.GetConnection(device.DeviceId);
                result.LastKeepAlive = connection?.LastActivityTime;
                result.Status = DeviceConnectionStatus.Online;
                result.StatusDescription = "设备在线，MQTT连接正常";
            }
            else
            {
                result.Status = DeviceConnectionStatus.Offline;
                result.StatusDescription = "设备离线，MQTT连接断开";
            }

            // 判断是否需要状态更新和告警处理
            var newDeviceStatus = result.Status == DeviceConnectionStatus.Online ?
                (int)DeviceStatusEnum.Online : (int)DeviceStatusEnum.Offline;

            result.NeedStatusUpdate = result.OriginalStatus != newDeviceStatus;
            result.NeedAlarmProcessing = result.Status == DeviceConnectionStatus.Offline;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检测设备状态时发生异常: DeviceId={DeviceId}", device.Id);
            result.StatusDescription = $"检测异常: {ex.Message}";
            return result;
        }
    }



    /// <summary>
    /// 更新设备状态
    /// </summary>
    /// <param name="result">检测结果</param>
    /// <returns>更新任务</returns>
    private async Task UpdateDeviceStatusAsync(DeviceOnlineDetectionResult result)
    {
        try
        {
            var newStatus = result.Status == DeviceConnectionStatus.Online ?
                (int)DeviceStatusEnum.Online : (int)DeviceStatusEnum.Offline;

            var updateCount = await _db.Updateable<DeviceEntity>()
                .SetColumns(d => d.Status == newStatus)
                .Where(d => d.Id == result.DeviceId)
                .ExecuteCommandAsync();

            if (updateCount > 0)
            {
                _logger.LogDebug("设备状态更新成功: DeviceId={DeviceId}, DeviceName={DeviceName}, " +
                    "OldStatus={OldStatus}, NewStatus={NewStatus}",
                    result.DeviceId, result.DeviceName, result.OriginalStatus, newStatus);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备状态时发生异常: DeviceId={DeviceId}", result.DeviceId);
        }
    }

    /// <summary>
    /// 处理通讯告警
    /// </summary>
    /// <param name="result">检测结果</param>
    /// <returns>处理任务</returns>
    private async Task ProcessCommunicationAlarmAsync(DeviceOnlineDetectionResult result)
    {
        try
        {
            // 获取设备实体
            var device = await _db.Queryable<DeviceEntity>()
                .Where(d => d.Id == result.DeviceId)
                .FirstAsync();

            if (device == null)
            {
                _logger.LogWarning("未找到设备实体: DeviceId={DeviceId}", result.DeviceId);
                return;
            }

            // 根据状态处理告警
            switch (result.Status)
            {
                case DeviceConnectionStatus.Offline:
                    // 处理通讯失败告警
                    await _alarmService.ProcessCommunicationFailureAlarmAsync(device);
                    _logger.LogDebug("处理通讯失败告警: DeviceId={DeviceId}, Status={Status}",
                        result.DeviceId, result.Status);
                    break;

                case DeviceConnectionStatus.Online:
                    // 检查是否需要清除通讯失败告警
                    await _alarmService.CheckCommunicationAlarmClearAsync(device.Id);
                    _logger.LogDebug("检查通讯告警清除: DeviceId={DeviceId}", result.DeviceId);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理通讯告警时发生异常: DeviceId={DeviceId}", result.DeviceId);
        }
    }
}
