using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using Admin.Application.MqttBrokerServices.Dto;
using System.Text;

namespace Admin.Application.MqttBrokerServices
{
    /// <summary>
    /// MQTT会话管理服务接口
    /// </summary>
    public interface IMqttSessionManagementService
    {
        /// <summary>
        /// 获取所有会话
        /// </summary>
        Task<SessionManagementResult> GetSessionsAsync(GetSessionsRequest request);

        /// <summary>
        /// 获取单个会话详情
        /// </summary>
        Task<SessionManagementResult> GetSessionDetailAsync(string sessionId);

        /// <summary>
        /// 根据客户端ID获取会话详情
        /// </summary>
        Task<SessionManagementResult> GetSessionByClientIdAsync(string clientId);

        /// <summary>
        /// 更新会话状态
        /// </summary>
        Task<SessionManagementResult> UpdateSessionStateAsync(string sessionId, UpdateSessionStateRequest request);

        /// <summary>
        /// 清理会话
        /// </summary>
        Task<SessionManagementResult> CleanupSessionAsync(string sessionId);

        /// <summary>
        /// 批量会话操作
        /// </summary>
        Task<SessionManagementResult> BatchSessionOperationAsync(BatchSessionOperationRequest request);

        /// <summary>
        /// 清理过期会话
        /// </summary>
        Task<SessionManagementResult> CleanupExpiredSessionsAsync();
    }

    /// <summary>
    /// MQTT会话管理服务实现
    /// </summary>
    [ApiExplorerSettings(GroupName = ApiExplorerGroupConst.MQTT)]
    public class MqttSessionManagementService(
        IMqttBroker mqttBroker,
        IMqttConnectionManager connectionManager,
        ILogger<MqttSessionManagementService> logger) : ApplicationService, IMqttSessionManagementService
    {
        private readonly IMqttBroker _mqttBroker = mqttBroker ?? throw new ArgumentNullException(nameof(mqttBroker));
        private readonly IMqttConnectionManager _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        private readonly ILogger<MqttSessionManagementService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// 获取所有会话
        /// </summary>
        public async Task<SessionManagementResult> GetSessionsAsync(GetSessionsRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return SessionManagementResult.Error("MQTT代理服务未运行");
                }

                // 获取所有会话
                var allSessions = await GetAllSessionsAsync();
                
                // 应用过滤条件
                var filteredSessions = allSessions.AsEnumerable();

                if (!string.IsNullOrEmpty(request.ClientId))
                {
                    filteredSessions = filteredSessions.Where(s => 
                        s.ClientId.Contains(request.ClientId, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(request.State))
                {
                    filteredSessions = filteredSessions.Where(s => 
                        s.State.ToString().Equals(request.State, StringComparison.OrdinalIgnoreCase));
                }

                if (request.Persistent.HasValue)
                {
                    filteredSessions = filteredSessions.Where(s => s.IsPersistent == request.Persistent.Value);
                }

                if (request.Expired.HasValue)
                {
                    filteredSessions = filteredSessions.Where(s => s.IsExpired() == request.Expired.Value);
                }

                var totalCount = filteredSessions.Count();

                // 应用分页
                var pagedSessions = filteredSessions
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // 转换为DTO
                var sessionDtos = pagedSessions.Select(MapToSessionInfo).ToList();

                var response = new GetSessionsResponse(sessionDtos, totalCount, request.Page, request.PageSize);

                _logger.LogDebug("获取会话列表成功，总数: {TotalCount}, 当前页: {Page}, 每页大小: {PageSize}", 
                    totalCount, request.Page, request.PageSize);

                return SessionManagementResult.Success("获取会话列表成功", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话列表时发生错误");
                return SessionManagementResult.Error($"获取会话列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取单个会话详情
        /// </summary>
        public async Task<SessionManagementResult> GetSessionDetailAsync(string sessionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId))
                {
                    return SessionManagementResult.Error("会话ID不能为空");
                }

                if (!_mqttBroker.IsRunning)
                {
                    return SessionManagementResult.Error("MQTT代理服务未运行");
                }

                var session = await FindSessionByIdAsync(sessionId);
                if (session == null)
                {
                    return SessionManagementResult.Error($"未找到会话 {sessionId}");
                }

                var detail = MapToSessionDetail(session);

                _logger.LogDebug("获取会话 {SessionId} 详情成功", sessionId);

                return SessionManagementResult.Success("获取会话详情成功", detail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取会话 {SessionId} 详情时发生错误", sessionId);
                return SessionManagementResult.Error($"获取会话详情失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据客户端ID获取会话详情
        /// </summary>
        public async Task<SessionManagementResult> GetSessionByClientIdAsync(string clientId)
        {
            try
            {
                if (string.IsNullOrEmpty(clientId))
                {
                    return SessionManagementResult.Error("客户端ID不能为空");
                }

                if (!_mqttBroker.IsRunning)
                {
                    return SessionManagementResult.Error("MQTT代理服务未运行");
                }

                var session = await FindSessionByClientIdAsync(clientId);
                if (session == null)
                {
                    return SessionManagementResult.Error($"未找到客户端 {clientId} 的会话");
                }

                var detail = MapToSessionDetail(session);

                _logger.LogDebug("获取客户端 {ClientId} 的会话详情成功", clientId);

                return SessionManagementResult.Success("获取会话详情成功", detail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户端 {ClientId} 的会话详情时发生错误", clientId);
                return SessionManagementResult.Error($"获取会话详情失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新会话状态
        /// </summary>
        public async Task<SessionManagementResult> UpdateSessionStateAsync(string sessionId, UpdateSessionStateRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId))
                {
                    return SessionManagementResult.Error("会话ID不能为空");
                }

                if (!_mqttBroker.IsRunning)
                {
                    return SessionManagementResult.Error("MQTT代理服务未运行");
                }

                var session = await FindSessionByIdAsync(sessionId);
                if (session == null)
                {
                    return SessionManagementResult.Error($"未找到会话 {sessionId}");
                }

                var oldState = session.State;
                var newState = oldState;

                switch (request.Action.ToLower())
                {
                    case "suspend":
                        session.Suspend();
                        newState = session.State;
                        break;
                    case "resume":
                    case "activate":
                        session.Activate();
                        newState = session.State;
                        break;
                    case "terminate":
                        session.Terminate();
                        newState = session.State;
                        break;
                    default:
                        return SessionManagementResult.Error($"不支持的操作: {request.Action}");
                }

                // 保存会话状态
                await _connectionManager.SaveSessionAsync(session);

                var response = new UpdateSessionStateResponse
                {
                    SessionId = sessionId,
                    ClientId = session.ClientId,
                    OldState = oldState.ToString(),
                    NewState = newState.ToString(),
                    UpdateTime = DateTime.UtcNow,
                    Reason = request.Reason
                };

                _logger.LogInformation("更新会话 {SessionId} 状态成功: {OldState} -> {NewState}", 
                    sessionId, oldState, newState);

                return SessionManagementResult.Success("会话状态已更新", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新会话 {SessionId} 状态时发生错误", sessionId);
                return SessionManagementResult.Error($"更新会话状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理会话
        /// </summary>
        public async Task<SessionManagementResult> CleanupSessionAsync(string sessionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId))
                {
                    return SessionManagementResult.Error("会话ID不能为空");
                }

                if (!_mqttBroker.IsRunning)
                {
                    return SessionManagementResult.Error("MQTT代理服务未运行");
                }

                var session = await FindSessionByIdAsync(sessionId);
                if (session == null)
                {
                    return SessionManagementResult.Error($"未找到会话 {sessionId}");
                }

                // 收集清理前的统计信息
                var subscriptionCount = session.GetSubscriptions().Count;
                var pendingMessageCount = session.GetPendingMessageCount();
                var awaitingAckCount = GetAwaitingAckMessageCount(session);
                var awaitingCompCount = GetAwaitingCompMessageCount(session);

                // 执行清理
                await _connectionManager.CleanupSessionAsync(session.ClientId);

                var response = new CleanupSessionResponse
                {
                    SessionId = sessionId,
                    ClientId = session.ClientId,
                    CleanupTime = DateTime.UtcNow,
                    DeletedSubscriptions = subscriptionCount,
                    DeletedPendingMessages = pendingMessageCount,
                    DeletedAwaitingAckMessages = awaitingAckCount,
                    DeletedAwaitingCompMessages = awaitingCompCount
                };

                _logger.LogInformation("清理会话 {SessionId} 成功，删除订阅: {Subscriptions}, 待发送消息: {PendingMessages}", 
                    sessionId, subscriptionCount, pendingMessageCount);

                return SessionManagementResult.Success("会话已清理", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理会话 {SessionId} 时发生错误", sessionId);
                return SessionManagementResult.Error($"清理会话失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量会话操作
        /// </summary>
        public async Task<SessionManagementResult> BatchSessionOperationAsync(BatchSessionOperationRequest request)
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return SessionManagementResult.Error("MQTT代理服务未运行");
                }

                var results = new List<SessionOperationResult>();
                int successCount = 0;
                int failCount = 0;

                foreach (var sessionId in request.SessionIds)
                {
                    var result = new SessionOperationResult
                    {
                        SessionId = sessionId
                    };

                    try
                    {
                        var session = await FindSessionByIdAsync(sessionId);
                        if (session == null)
                        {
                            result.Success = false;
                            result.Error = $"未找到会话 {sessionId}";
                            failCount++;
                        }
                        else
                        {
                            result.ClientId = session.ClientId;

                            switch (request.Operation.ToLower())
                            {
                                case "cleanup":
                                    await _connectionManager.CleanupSessionAsync(session.ClientId);
                                    break;
                                case "suspend":
                                    session.Suspend();
                                    await _connectionManager.SaveSessionAsync(session);
                                    break;
                                case "activate":
                                case "resume":
                                    session.Activate();
                                    await _connectionManager.SaveSessionAsync(session);
                                    break;
                                case "terminate":
                                    session.Terminate();
                                    await _connectionManager.SaveSessionAsync(session);
                                    break;
                                default:
                                    result.Success = false;
                                    result.Error = $"不支持的操作: {request.Operation}";
                                    failCount++;
                                    continue;
                            }

                            result.Success = true;
                            result.OperationTime = DateTime.UtcNow;
                            successCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Success = false;
                        result.Error = ex.Message;
                        failCount++;
                        _logger.LogError(ex, "批量操作会话 {SessionId} 时发生错误", sessionId);
                    }

                    results.Add(result);
                }

                var response = new BatchSessionOperationResponse
                {
                    TotalRequested = request.SessionIds.Count,
                    SuccessfulOperations = successCount,
                    FailedOperations = failCount,
                    Results = results
                };

                _logger.LogInformation("批量会话操作完成，总数: {Total}, 成功: {Success}, 失败: {Failed}", 
                    request.SessionIds.Count, successCount, failCount);

                return SessionManagementResult.Success("批量会话操作完成", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量会话操作时发生错误");
                return SessionManagementResult.Error($"批量会话操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理过期会话
        /// </summary>
        public async Task<SessionManagementResult> CleanupExpiredSessionsAsync()
        {
            try
            {
                if (!_mqttBroker.IsRunning)
                {
                    return SessionManagementResult.Error("MQTT代理服务未运行");
                }

                var allSessions = await GetAllSessionsAsync();
                var expiredSessions = allSessions.Where(s => s.IsExpired()).ToList();

                var cleanupResults = new List<SessionOperationResult>();
                int successCount = 0;
                int failCount = 0;

                foreach (var session in expiredSessions)
                {
                    var result = new SessionOperationResult
                    {
                        SessionId = session.SessionId,
                        ClientId = session.ClientId
                    };

                    try
                    {
                        await _connectionManager.CleanupSessionAsync(session.ClientId);
                        result.Success = true;
                        result.OperationTime = DateTime.UtcNow;
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        result.Success = false;
                        result.Error = ex.Message;
                        failCount++;
                        _logger.LogError(ex, "清理过期会话 {SessionId} 时发生错误", session.SessionId);
                    }

                    cleanupResults.Add(result);
                }

                var response = new BatchSessionOperationResponse
                {
                    TotalRequested = expiredSessions.Count,
                    SuccessfulOperations = successCount,
                    FailedOperations = failCount,
                    Results = cleanupResults
                };

                _logger.LogInformation("清理过期会话完成，总数: {Total}, 成功: {Success}, 失败: {Failed}", 
                    expiredSessions.Count, successCount, failCount);

                return SessionManagementResult.Success("过期会话清理完成", response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期会话时发生错误");
                return SessionManagementResult.Error($"清理过期会话失败: {ex.Message}");
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取所有会话
        /// </summary>
        private async Task<List<MqttSession>> GetAllSessionsAsync()
        {
            // 由于当前的连接管理器没有直接暴露获取所有会话的方法，
            // 我们需要通过连接的客户端信息来获取会话
            var sessions = new List<MqttSession>();
            
            try
            {
                var connectedClients = _mqttBroker.GetConnectedClients();
                
                foreach (var clientInfo in connectedClients)
                {
                    var session = await _connectionManager.GetOrCreateSessionAsync(clientInfo.ClientId, false);
                    if (session != null)
                    {
                        sessions.Add(session);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有会话时发生错误");
            }

            return sessions;
        }

        /// <summary>
        /// 根据会话ID查找会话
        /// </summary>
        private async Task<MqttSession?> FindSessionByIdAsync(string sessionId)
        {
            var allSessions = await GetAllSessionsAsync();
            return allSessions.FirstOrDefault(s => s.SessionId == sessionId);
        }

        /// <summary>
        /// 根据客户端ID查找会话
        /// </summary>
        private async Task<MqttSession?> FindSessionByClientIdAsync(string clientId)
        {
            try
            {
                return await _connectionManager.GetOrCreateSessionAsync(clientId, false);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取待确认消息数量（通过反射或其他方式）
        /// </summary>
        private int GetAwaitingAckMessageCount(MqttSession session)
        {
            // 由于没有直接的方法，返回会话摘要中的数量
            return session.GetSummary().AwaitingAckCount;
        }

        /// <summary>
        /// 获取待完成消息数量（通过反射或其他方式）
        /// </summary>
        private int GetAwaitingCompMessageCount(MqttSession session)
        {
            // 由于没有直接的方法，返回会话摘要中的数量
            return session.GetSummary().AwaitingCompCount;
        }

        /// <summary>
        /// 映射会话信息到DTO
        /// </summary>
        private SessionInfoDto MapToSessionInfo(MqttSession session)
        {
            var summary = session.GetSummary();
            
            return new SessionInfoDto
            {
                SessionId = session.SessionId,
                ClientId = session.ClientId,
                CleanSession = session.CleanSession,
                State = session.State.ToString(),
                CreatedTime = session.CreatedTime,
                LastActivityTime = session.LastActivityTime,
                ExpiryTime = session.ExpiryTime,
                SubscriptionCount = summary.SubscriptionCount,
                PendingMessageCount = summary.PendingMessageCount,
                AwaitingAckCount = summary.AwaitingAckCount,
                AwaitingCompCount = summary.AwaitingCompCount,
                Duration = FormatDuration(summary.Duration),
                IsExpired = summary.IsExpired,
                IsPersistent = session.IsPersistent
            };
        }

        /// <summary>
        /// 映射会话详情到DTO
        /// </summary>
        private SessionDetailDto MapToSessionDetail(MqttSession session)
        {
            var sessionInfo = MapToSessionInfo(session);
            
            return new SessionDetailDto
            {
                SessionId = sessionInfo.SessionId,
                ClientId = sessionInfo.ClientId,
                CleanSession = sessionInfo.CleanSession,
                State = sessionInfo.State,
                CreatedTime = sessionInfo.CreatedTime,
                LastActivityTime = sessionInfo.LastActivityTime,
                ExpiryTime = sessionInfo.ExpiryTime,
                SubscriptionCount = sessionInfo.SubscriptionCount,
                PendingMessageCount = sessionInfo.PendingMessageCount,
                AwaitingAckCount = sessionInfo.AwaitingAckCount,
                AwaitingCompCount = sessionInfo.AwaitingCompCount,
                Duration = sessionInfo.Duration,
                IsExpired = sessionInfo.IsExpired,
                IsPersistent = sessionInfo.IsPersistent,
                Subscriptions = MapSubscriptions(session),
                PendingMessages = MapPendingMessages(session),
                Statistics = MapStatistics(session)
            };
        }

        /// <summary>
        /// 映射订阅信息
        /// </summary>
        private List<SessionSubscriptionDto> MapSubscriptions(MqttSession session)
        {
            return session.GetSubscriptions().Select(sub => new SessionSubscriptionDto
            {
                TopicFilter = sub.TopicFilter,
                Qos = sub.QualityOfService,
                SubscribedTime = DateTime.Now // 由于MqttSubscription没有SubscribedTime属性，使用当前时间
            }).ToList();
        }

        /// <summary>
        /// 映射待发送消息
        /// </summary>
        private List<PendingMessageDto> MapPendingMessages(MqttSession session)
        {
            // 由于MqttSession没有直接获取待发送消息列表的方法，
            // 这里创建一个空列表或模拟数据用于演示
            // 在实际实现中，可能需要修改MqttSession类来提供这个功能
            var pendingMessages = new List<PendingMessageDto>();
            
            // 模拟一些待发送消息数据（实际应该从session中获取）
            var pendingCount = session.GetPendingMessageCount();
            for (int i = 0; i < Math.Min(pendingCount, 10); i++) // 限制显示前10条
            {
                pendingMessages.Add(new PendingMessageDto
                {
                    MessageId = i + 1,
                    Topic = $"topic/example/{i}",
                    Qos = 1,
                    Payload = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($"Pending message {i}")),
                    EnqueuedTime = DateTime.Now.AddMinutes(-i),
                    PayloadSize = 50 + i * 10
                });
            }
            
            return pendingMessages;
        }

        /// <summary>
        /// 映射统计信息
        /// </summary>
        private SessionStatisticsDto MapStatistics(MqttSession session)
        {
            return new SessionStatisticsDto
            {
                SubscriptionCount = session.Statistics.SubscriptionCount,
                PendingMessageCount = session.Statistics.PendingMessageCount,
                SentMessageCount = session.Statistics.SentMessageCount,
                ReceivedMessageCount = session.Statistics.ReceivedMessageCount,
                FailedMessageCount = session.Statistics.FailedMessageCount,
                LastSentMessageTime = session.Statistics.LastSentMessageTime,
                LastReceivedMessageTime = session.Statistics.LastReceivedMessageTime
            };
        }

        /// <summary>
        /// 格式化持续时间
        /// </summary>
        private string FormatDuration(TimeSpan duration)
        {
            if (duration.TotalDays >= 1)
            {
                return $"{(int)duration.TotalDays}天 {duration.Hours}小时 {duration.Minutes}分钟";
            }
            else if (duration.TotalHours >= 1)
            {
                return $"{duration.Hours}小时 {duration.Minutes}分钟";
            }
            else if (duration.TotalMinutes >= 1)
            {
                return $"{duration.Minutes}分钟 {duration.Seconds}秒";
            }
            else
            {
                return $"{duration.Seconds}秒";
            }
        }

        #endregion
    }
} 