// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.DeviceServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.DeviceServices;

/// <summary>
/// 设备数据服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class DeviceDataService(ISqlSugarClient db) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;

    /// <summary>
    /// 获取设备最新数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备最新数据列表</returns>
    //public async Task<List<DeviceDataLatestOutput>> GetLatestDataAsync(long deviceId)
    //{
    //    var query = _db.Queryable<DeviceDataLatestEntity>()
    //        .LeftJoin<DeviceEntity>((dl, d) => dl.DeviceId == d.Id)
    //        .LeftJoin<ProductEntity>((dl, d, p) => d.ProductId == p.Id)
    //        .LeftJoin<ProductModelEntity>((dl, d, p, pm) => dl.ModelId == pm.Id)
    //        .Where((dl, d, p, pm) => dl.DeviceId == deviceId)
    //        .Select((dl, d, p, pm) => new DeviceDataLatestOutput
    //        {
    //            Id = dl.Id,
    //            DeviceId = dl.DeviceId,
    //            DeviceName = d.DeviceName,
    //            DeviceCode = d.DeviceId,
    //            ProductId = d.ProductId,
    //            ProductName = p.ProductName,
    //            ModelId = dl.ModelId,
    //            ModelName = pm.ModelName,
    //            PropertyId = dl.PropertyId,
    //            PropertyName = dl.PropertyName,
    //            DataType = dl.DataType,
    //            DataValue = dl.DataValue,
    //            Unit = dl.Unit,
    //            DataStatus = dl.DataStatus,
    //            UpdateTime = dl.UpdateTime,
    //            DeviceStatus = d.Status,
    //            IsOnline = SqlFunc.DateDiff(DateType.Minute, dl.UpdateTime, DateTime.Now) <= 5,
    //            OfflineMinutes = SqlFunc.DateDiff(DateType.Minute, dl.UpdateTime, DateTime.Now)
    //        });

    //    var result = await query.ToListAsync();
    //    return result;
    //}

    /// <summary>
    /// 批量获取设备最新数据
    /// </summary>
    /// <param name="input">批量获取输入</param>
    /// <returns>设备最新数据列表</returns>
    //public async Task<List<DeviceDataLatestOutput>> GetLatestDataBatchAsync(BatchGetLatestDataInput input)
    //{
    //    if (!input.DeviceIds.Any()) return new List<DeviceDataLatestOutput>();

    //    var query = _db.Queryable<DeviceDataLatestEntity>()
    //        .LeftJoin<DeviceEntity>((dl, d) => dl.DeviceId == d.Id)
    //        .LeftJoin<ProductEntity>((dl, d, p) => d.ProductId == p.Id)
    //        .LeftJoin<ProductModelEntity>((dl, d, p, pm) => dl.ModelId == pm.Id)
    //        .Where((dl, d, p, pm) => input.DeviceIds.Contains(dl.DeviceId));

    //    // 是否包含离线设备
    //    if (!input.IncludeOffline)
    //    {
    //        query = query.Where((dl, d, p, pm) => SqlFunc.DateDiff(DateType.Minute, dl.UpdateTime, DateTime.Now) <= 5);
    //    }

    //    var result = await query.Select((dl, d, p, pm) => new DeviceDataLatestOutput
    //    {
    //        Id = dl.Id,
    //        DeviceId = dl.DeviceId,
    //        DeviceName = d.DeviceName,
    //        DeviceCode = d.DeviceId,
    //        ProductId = d.ProductId,
    //        ProductName = p.ProductName,
    //        ModelId = dl.ModelId,
    //        ModelName = pm.ModelName,
    //        PropertyId = dl.PropertyId,
    //        PropertyName = dl.PropertyName,
    //        DataType = dl.DataType,
    //        DataValue = dl.DataValue,
    //        Unit = dl.Unit,
    //        DataStatus = dl.DataStatus,
    //        UpdateTime = dl.UpdateTime,
    //        DeviceStatus = d.Status,
    //        IsOnline = SqlFunc.DateDiff(DateType.Minute, dl.UpdateTime, DateTime.Now) <= 5,
    //        OfflineMinutes = SqlFunc.DateDiff(DateType.Minute, dl.UpdateTime, DateTime.Now)
    //    }).ToListAsync();

    //    return result;
    //}

    /// <summary>
    /// 获取设备历史数据
    /// </summary>
    /// <param name="input">查询输入</param>
    /// <returns>分页的历史数据</returns>
    public async Task<PagedList<DeviceDataHistoryOutput>> GetHistoryDataAsync(GetDeviceHistoryInput input)
    {
        var query = _db.Queryable<DeviceDataHistoryEntity>()
            .LeftJoin<DeviceEntity>((dh, d) => dh.DeviceId == d.Id)
            .Where((dh, d) => dh.DeviceId == input.DeviceId);

        // 时间范围筛选
        if (input.StartTime.HasValue)
            query = query.Where((dh, d) => dh.DataTime >= input.StartTime.Value);

        if (input.EndTime.HasValue)
            query = query.Where((dh, d) => dh.DataTime <= input.EndTime.Value);

        // 排序
        query = query.OrderByDescending((dh, d) => dh.DataTime);

        // 分页查询
        var pagedList = await query.Select((dh, d) => new DeviceDataHistoryOutput
        {
            Id = dh.Id,
            DeviceId = dh.DeviceId,
            DeviceName = d.DeviceName,
            PropertyName = dh.PropertyName,
            PropertyValue = dh.PropertyValue,
            DataTime = dh.DataTime
        }).ToPurestPagedListAsync(input.PageIndex, input.PageSize);

        return pagedList;
    }


    /// <summary>
    /// 检查设备是否在线
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="timeoutMinutes">超时时间（分钟）</param>
    /// <returns>是否在线</returns>
    public async Task<bool> IsDeviceOnlineAsync(long deviceId, int timeoutMinutes = 5)
    {
        var latestData = await _db.Queryable<DeviceDataLatestEntity>()
            .Where(x => x.DeviceId == deviceId)
            .OrderByDescending(x => x.UpdateTime)
            .FirstAsync();

        if (latestData == null) return false;

        var offlineMinutes = (DateTime.Now - latestData.UpdateTime).TotalMinutes;
        return offlineMinutes <= timeoutMinutes;
    }

    /// <summary>
    /// 获取离线设备列表
    /// </summary>
    /// <param name="timeoutMinutes">超时时间（分钟）</param>
    /// <returns>离线设备列表</returns>
    public async Task<List<DeviceStatusOverviewOutput>> GetOfflineDevicesAsync(int timeoutMinutes = 10)
    {
        // 获取每个设备的最新数据时间
        var deviceLatestTimes = await _db.Queryable<DeviceDataLatestEntity>()
            .GroupBy(x => x.DeviceId)
            .Select(x => new { DeviceId = x.DeviceId, LastUpdateTime = SqlFunc.AggregateMax(x.UpdateTime) })
            .ToListAsync();

        var offlineDeviceIds = deviceLatestTimes
            .Where(x => (DateTime.Now - x.LastUpdateTime).TotalMinutes > timeoutMinutes)
            .Select(x => x.DeviceId)
            .ToList();

        if (!offlineDeviceIds.Any()) return new List<DeviceStatusOverviewOutput>();

        var query = _db.Queryable<DeviceEntity>()
            .Where(d => offlineDeviceIds.Contains(d.Id))
            .Select(d => new DeviceStatusOverviewOutput
            {
                DeviceId = d.Id,
                DeviceName = d.DeviceName,
                DeviceCode = d.DeviceId,
                DeviceStatus = d.Status,
                LastDataTime = deviceLatestTimes.FirstOrDefault(x => x.DeviceId == d.Id).LastUpdateTime,
                IsOnline = false,
                OfflineMinutes = (int)(DateTime.Now - deviceLatestTimes.FirstOrDefault(x => x.DeviceId == d.Id).LastUpdateTime).TotalMinutes,
                DataJson = string.Empty
            });

        return await query.ToListAsync();
    }

    /// <summary>
    /// 删除设备历史数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="beforeDate">删除此时间之前的数据</param>
    /// <returns>删除的记录数</returns>
    public async Task<int> DeleteHistoryDataAsync(long deviceId, DateTime beforeDate)
    {
        var deletedCount = await _db.Deleteable<DeviceDataHistoryEntity>()
            .Where(x => x.DeviceId == deviceId && x.DataTime < beforeDate)
            .ExecuteCommandAsync();

        return deletedCount;
    }

    /// <summary>
    /// 批量删除历史数据
    /// </summary>
    /// <param name="beforeDate">删除此时间之前的数据</param>
    /// <returns>删除的记录数</returns>
    public async Task<int> BatchDeleteHistoryDataAsync(DateTime beforeDate)
    {
        var deletedCount = await _db.Deleteable<DeviceDataHistoryEntity>()
            .Where(x => x.DataTime < beforeDate)
            .ExecuteCommandAsync();

        return deletedCount;
    }

    /// <summary>
    /// 获取设备指定属性的最新数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="propertyId">属性ID</param>
    /// <returns>属性最新数据</returns>
    //public async Task<DeviceDataLatestOutput?> GetLatestPropertyDataAsync(long deviceId, long propertyId)
    //{
    //    var query = _db.Queryable<DeviceDataLatestEntity>()
    //        .LeftJoin<DeviceEntity>((dl, d) => dl.DeviceId == d.Id)
    //        .LeftJoin<ProductEntity>((dl, d, p) => d.ProductId == p.Id)
    //        .LeftJoin<ProductModelEntity>((dl, d, p, pm) => dl.ModelId == pm.Id)
    //        .Where((dl, d, p, pm) => dl.DeviceId == deviceId && dl.PropertyId == propertyId)
    //        .Select((dl, d, p, pm) => new DeviceDataLatestOutput
    //        {
    //            Id = dl.Id,
    //            DeviceId = dl.DeviceId,
    //            DeviceName = d.DeviceName,
    //            DeviceCode = d.DeviceId,
    //            ProductId = d.ProductId,
    //            ProductName = p.ProductName,
    //            ModelId = dl.ModelId,
    //            ModelName = pm.ModelName,
    //            PropertyId = dl.PropertyId,
    //            PropertyName = dl.PropertyName,
    //            DataType = dl.DataType,
    //            DataValue = dl.DataValue,
    //            Unit = dl.Unit,
    //            DataStatus = dl.DataStatus,
    //            UpdateTime = dl.UpdateTime,
    //            DeviceStatus = d.Status,
    //            IsOnline = SqlFunc.DateDiff(DateType.Minute, dl.UpdateTime, DateTime.Now) <= 5,
    //            OfflineMinutes = SqlFunc.DateDiff(DateType.Minute, dl.UpdateTime, DateTime.Now)
    //        });

    //    var result = await query.FirstAsync();
    //    return result;
    //}

    
    /// <summary>
    /// 获取设备属性最新数据（使用DTO）
    /// </summary>
    /// <param name="input">查询输入</param>
    /// <returns>属性最新数据</returns>
    //public async Task<DeviceDataLatestOutput?> GetLatestPropertyDataAsync(GetLatestPropertyDataInput input)
    //{
    //    return await GetLatestPropertyDataAsync(input.DeviceId, input.PropertyId);
    //}

    /// <summary>
    /// 删除设备所有最新数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>删除的记录数</returns>
    public async Task<int> DeleteDeviceLatestDataAsync(long deviceId)
    {
        var deletedCount = await _db.Deleteable<DeviceDataLatestEntity>()
            .Where(x => x.DeviceId == deviceId)
            .ExecuteCommandAsync();

        return deletedCount;
    }
}