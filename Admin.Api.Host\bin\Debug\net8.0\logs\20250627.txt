[00:00:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[00:01:24] [WRN] Admin.Application.MqttBrokerServices.MqttBrokerManagementService 
MQTT代理服务已在运行中

[00:01:47] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理配置无效: 启用TLS/SSL时必须提供证书路径

[00:01:47] [ERR] Admin.Application.MqttBrokerServices.MqttBrokerManagementService 
启动MQTT代理服务时发生错误
System.InvalidOperationException: MQTT代理配置无效: 启用TLS/SSL时必须提供证书路径
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 118
   at Admin.Application.MqttBrokerServices.MqttBrokerManagementService.StartAsync(StartBrokerRequest request, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Application\MqttBrokerServices\MqttBrokerManagementService.cs:line 96

[00:35:37] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[10:08:08] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1097
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[10:36:35] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1113
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[10:37:35] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1219
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[10:46:49] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1057
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[11:24:28] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1048
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[11:28:39] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1204
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[12:45:20] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1184
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[14:08:29] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1204
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[14:22:40] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端连接时发生错误: 192.168.1.2:1086
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.WriteAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.SendConnAckAsync(MqttClientConnection connection, ConnectReturnCode returnCode, Boolean sessionPresent, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 478
   at Admin.Communication.Mqtt.Services.MqttBrokerService.HandleClientConnectionAsync(TcpClient tcpClient, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 408

[17:00:26] [ERR] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware 
An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The converter specified on 'Admin.Application.MqttBrokerServices.Dto.AclRuleDto.CreatedTime' is not compatible with the type 'System.DateTime'.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_SerializationConverterOnAttributeNotCompatible(Type classTypeAttributeIsOn, MemberInfo memberInfo, Type typeToConvert)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetConverterFromAttribute(JsonConverterAttribute converterAttribute, Type typeToConvert, MemberInfo memberInfo, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreatePropertyInfo(JsonTypeInfo typeInfo, Type typeToConvert, MemberInfo memberInfo, JsonSerializerOptions options, Boolean shouldCheckForRequiredKeyword, Boolean hasJsonIncludeAttribute)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.AddMembersDeclaredBySuperType(JsonTypeInfo typeInfo, Type currentType, Boolean constructorHasSetsRequiredMembersAttribute, PropertyHierarchyResolutionState& state)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.PopulateProperties(JsonTypeInfo typeInfo)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.CreateTypeInfoCore(Type type, JsonConverter converter, JsonSerializerOptions options)
   at System.Text.Json.Serialization.Metadata.DefaultJsonTypeInfoResolver.GetTypeInfo(Type type, JsonSerializerOptions options)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoNoCaching(Type type)
   at System.Text.Json.JsonSerializerOptions.CachingContext.CreateCacheEntry(Type type, CachingContext context)
--- End of stack trace from previous location ---
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.<EnsureConfigured>g__ConfigureSynchronized|172_0()
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.ConfigureProperties()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.Configure()
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo.<EnsureConfigured>g__ConfigureSynchronized|172_0()
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializer.GetTypeInfo(JsonSerializerOptions options, Type inputType)
   at System.Text.Json.JsonSerializer.Serialize(Utf8JsonWriter writer, Object value, Type inputType, JsonSerializerOptions options)
   at Volo.Abp.Json.SystemTextJson.JsonConverters.ObjectToInferredTypesConverter.Write(Utf8JsonWriter writer, Object objectToWrite, JsonSerializerOptions options)
   at System.Text.Json.Serialization.JsonConverter`1.TryWrite(Utf8JsonWriter writer, T& value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.Metadata.JsonPropertyInfo`1.GetMemberAndWriteJson(Object obj, WriteStack& state, Utf8JsonWriter writer)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryWrite(Utf8JsonWriter writer, T value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.JsonConverter`1.TryWrite(Utf8JsonWriter writer, T& value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.JsonConverter`1.WriteCore(Utf8JsonWriter writer, T& value, JsonSerializerOptions options, WriteStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.SerializeAsync(Stream utf8Json, T rootValue, CancellationToken cancellationToken, Object rootValueBoxed)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.SerializeAsync(Stream utf8Json, T rootValue, CancellationToken cancellationToken, Object rootValueBoxed)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.SerializeAsync(Stream utf8Json, T rootValue, CancellationToken cancellationToken, Object rootValueBoxed)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Admin.Api.Host.Options.AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult) in D:\code projects\purest-admin-main\api\Admin.Api.Host\Options\AuthorizationMiddlewareResultHandler.cs:line 22
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)

