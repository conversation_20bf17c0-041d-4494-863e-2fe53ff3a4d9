// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 控制计划
/// </summary>
[SugarTable("CONTROL_PLAN")]
public partial class ControlPlanEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    [SugarColumn(ColumnName = "NAME")]
    public string Name { get; set; }

    /// <summary>
    /// 触发方式
    /// 1:重复执行 2:条件执行
    /// </summary>
    [SugarColumn(ColumnName = "TRIGGER_TYPE")]
    public int TriggerType { get; set; }

    /// <summary>
    /// 执行方式
    /// 重复执行
    /// 1:时间点 2:间隔
    /// </summary>
    [SugarColumn(ColumnName = "EXECUTE_TYPE")]
    public int ExecuteType { get; set; }

    /// <summary>
    /// 时间点
    /// HH:mm，多个用逗号分隔
    /// </summary>
    [SugarColumn(ColumnName = "TIME_POINT")]
    public string TimePoint { get; set; }

    /// <summary>
    /// 间隔
    /// </summary>
    [SugarColumn(ColumnName = "INTERVAL")]
    public int Interval { get; set; }

    /// <summary>
    /// 间隔单位
    /// 1:分钟 2:小时
    /// </summary>
    [SugarColumn(ColumnName = "INTERVAL_UNIT")]
    public int IntervalUnit { get; set; }


    /// <summary>
    /// 方法
    /// 1:大于参考值 2:小于参考值 3:等于参考值
    /// 条件执行
    /// </summary>
    [SugarColumn(ColumnName = "METHOD")]
    public int Method { get; set; }

    /// <summary>
    /// 关联参数
    /// 条件执行
    /// </summary>
    [SugarColumn(ColumnName = "PARAMETER_ID")]
    public long ParameterId { get; set; }

    /// <summary>
    /// 参考值
    /// 条件执行
    /// </summary>
    [SugarColumn(ColumnName = "REFERENCE_VALUE")]
    public decimal ReferenceValue { get; set; }


    /// <summary>
    /// 指令id集
    /// </summary>
    [SugarColumn(ColumnName = "CMD_IDS")]
    public string CmdIds { get; set; }

    /// <summary>
    /// 指令间隔
    /// 单位：毫秒
    /// </summary>
    [SugarColumn(ColumnName = "CMD_INTERVAL")]
    public int CmdInterval { get; set; } = 1000;

    /// <summary>
    /// 监控状态
    /// 0: 不启用 1：启用
    /// </summary>
    [SugarColumn(ColumnName = "IS_APP_CONTROL")]
    public int IsAppControl { get; set; } = 1;
}
