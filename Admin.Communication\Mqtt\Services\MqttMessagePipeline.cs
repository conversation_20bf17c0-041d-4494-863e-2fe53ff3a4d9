using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Admin.Communication.Mqtt.Models.Results;
using Admin.Core.ExceptionExtensions;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.Services
{
    /// <summary>
    /// MQTT消息处理管道实现
    /// </summary>
    public class MqttMessagePipeline : IMqttMessagePipeline
    {
        private readonly ILogger<MqttMessagePipeline> _logger;
        private readonly ConcurrentBag<IMqttMessageTransformer> _transformers = new ConcurrentBag<IMqttMessageTransformer>();
        private readonly ConcurrentBag<IMqttMessageValidator> _validators = new ConcurrentBag<IMqttMessageValidator>();

        /// <summary>
        /// 初始化消息处理管道
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MqttMessagePipeline(ILogger<MqttMessagePipeline> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 添加消息转换器
        /// </summary>
        /// <param name="transformer">转换器</param>
        public void AddTransformer(IMqttMessageTransformer transformer)
        {
            if (transformer == null)
            {
                throw new ArgumentNullException(nameof(transformer));
            }

            _transformers.Add(transformer);
            _logger.LogInformation("添加消息转换器: {TransformerName}, 优先级: {Priority}", 
                transformer.Name, transformer.Priority);
        }

        /// <summary>
        /// 添加消息验证器
        /// </summary>
        /// <param name="validator">验证器</param>
        public void AddValidator(IMqttMessageValidator validator)
        {
            if (validator == null)
            {
                throw new ArgumentNullException(nameof(validator));
            }

            _validators.Add(validator);
            _logger.LogInformation("添加消息验证器: {ValidatorName}, 优先级: {Priority}", 
                validator.Name, validator.Priority);
        }

        /// <summary>
        /// 执行消息管道处理
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>处理后的消息和验证结果</returns>
        public async Task<MqttMessagePipelineResult> ProcessAsync(MqttMessage message, MqttMessageContext context)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            var result = new MqttMessagePipelineResult
            {
                ProcessedMessage = message,
                IsSuccess = true
            };

            try
            {
                _logger.LogDebug("开始执行消息管道处理: {MessageType}", message.GetType().Name);

                // 1. 执行消息转换
                result.ProcessedMessage = await ExecuteTransformationsAsync(message, context, result);
                if (result.ProcessedMessage == null)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "消息转换失败";
                    return result;
                }

                // 2. 执行消息验证
                await ExecuteValidationsAsync(result.ProcessedMessage, context, result);

                _logger.LogDebug("消息管道处理完成: 转换器数量={TransformerCount}, 验证器数量={ValidatorCount}, 验证通过={IsValid}",
                    result.TransformationHistory.Count, result.ValidationResults.Count, result.IsValid);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行消息管道处理时发生异常");
                result.IsSuccess = false;
                result.ErrorMessage = $"管道处理异常: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 执行消息转换
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <param name="result">管道处理结果</param>
        /// <returns>转换后的消息</returns>
        private async Task<MqttMessage> ExecuteTransformationsAsync(MqttMessage message, MqttMessageContext context, MqttMessagePipelineResult result)
        {
            var currentMessage = message;

            // 获取适用的转换器，按优先级排序
            var applicableTransformers = _transformers
                .Where(t => t.CanTransform(currentMessage, context))
                .OrderBy(t => t.Priority)
                .ToList();

            foreach (var transformer in applicableTransformers)
            {
                try
                {
                    _logger.LogDebug("执行消息转换: {TransformerName}", transformer.Name);
                    
                    var transformedMessage = await transformer.TransformAsync(currentMessage, context);
                    if (transformedMessage != null)
                    {
                        currentMessage = transformedMessage;
                        result.TransformationHistory.Add(transformer.Name);
                        
                        _logger.LogDebug("消息转换成功: {TransformerName}", transformer.Name);
                    }
                    else
                    {
                        _logger.LogWarning("转换器返回空消息: {TransformerName}", transformer.Name);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行消息转换时发生异常: {TransformerName}", transformer.Name);
                    // 转换失败不中断整个管道，继续处理
                }
            }

            return currentMessage;
        }

        /// <summary>
        /// 执行消息验证
        /// </summary>
        /// <param name="message">要验证的消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <param name="result">管道处理结果</param>
        /// <returns>验证任务</returns>
        private async Task ExecuteValidationsAsync(MqttMessage message, MqttMessageContext context, MqttMessagePipelineResult result)
        {
            // 获取适用的验证器，按优先级排序
            var applicableValidators = _validators
                .Where(v => v.CanValidate(message, context))
                .OrderBy(v => v.Priority)
                .ToList();

            foreach (var validator in applicableValidators)
            {
                try
                {
                    _logger.LogDebug("执行消息验证: {ValidatorName}", validator.Name);
                    
                    var validationResult = await validator.ValidateAsync(message, context);
                    result.ValidationResults.Add(validationResult);
                    
                    _logger.LogDebug("消息验证完成: {ValidatorName}, 结果: {IsValid}", 
                        validator.Name, validationResult.IsValid);
                    
                    if (!validationResult.IsValid)
                    {
                        _logger.LogWarning("消息验证失败: {ValidatorName}, 错误: {ErrorMessage}", 
                            validator.Name, validationResult.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行消息验证时发生异常: {ValidatorName}", validator.Name);
                    
                    // 验证异常也作为验证失败记录
                    result.ValidationResults.Add(MqttMessageValidationResult.Failure(
                        $"验证器异常: {ex.Message}", "VALIDATOR_EXCEPTION"));
                }
            }
        }
    }

    /// <summary>
    /// 基础消息转换器抽象类
    /// </summary>
    public abstract class BaseMqttMessageTransformer : IMqttMessageTransformer
    {
        protected readonly ILogger _logger;

        /// <summary>
        /// 转换器名称
        /// </summary>
        public abstract string Name { get; }

        /// <summary>
        /// 转换器优先级
        /// </summary>
        public virtual int Priority { get; protected set; } = 100;

        /// <summary>
        /// 初始化基础转换器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        protected BaseMqttMessageTransformer(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 判断是否可以转换指定的消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>如果可以转换返回true，否则返回false</returns>
        public abstract bool CanTransform(MqttMessage message, MqttMessageContext context);

        /// <summary>
        /// 转换消息
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>转换后的消息</returns>
        public abstract Task<MqttMessage> TransformAsync(MqttMessage message, MqttMessageContext context);
    }

    /// <summary>
    /// 基础消息验证器抽象类
    /// </summary>
    public abstract class BaseMqttMessageValidator : IMqttMessageValidator
    {
        protected readonly ILogger _logger;

        /// <summary>
        /// 验证器名称
        /// </summary>
        public abstract string Name { get; }

        /// <summary>
        /// 验证器优先级
        /// </summary>
        public virtual int Priority { get; protected set; } = 100;

        /// <summary>
        /// 初始化基础验证器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        protected BaseMqttMessageValidator(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 判断是否可以验证指定的消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>如果可以验证返回true，否则返回false</returns>
        public abstract bool CanValidate(MqttMessage message, MqttMessageContext context);

        /// <summary>
        /// 验证消息
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>验证结果</returns>
        public abstract Task<MqttMessageValidationResult> ValidateAsync(MqttMessage message, MqttMessageContext context);
    }
} 