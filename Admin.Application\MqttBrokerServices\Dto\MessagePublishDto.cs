using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Admin.Application.MqttBrokerServices.Dto
{
    #region 请求 DTO

    /// <summary>
    /// 发布消息请求DTO
    /// </summary>
    public class PublishMessageRequest
    {
        /// <summary>
        /// 主题
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        [StringLength(256, ErrorMessage = "主题长度不能超过256个字符")]
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 消息负载
        /// </summary>
        [Required(ErrorMessage = "消息负载不能为空")]
        public string Payload { get; set; } = string.Empty;

        /// <summary>
        /// 服务质量等级 (0, 1, 2)
        /// </summary>
        [Range(0, 2, ErrorMessage = "QoS必须在0-2之间")]
        public int Qos { get; set; } = 0;

        /// <summary>
        /// 是否保留消息
        /// </summary>
        public bool Retain { get; set; } = false;

        /// <summary>
        /// 编码方式
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public PayloadEncoding Encoding { get; set; } = PayloadEncoding.Utf8;

        /// <summary>
        /// 发布者标识（可选，用于审计）
        /// </summary>
        public string? PublisherId { get; set; }

        /// <summary>
        /// 发布备注（可选）
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 批量发布消息请求DTO
    /// </summary>
    public class BatchPublishRequest
    {
        /// <summary>
        /// 消息列表
        /// </summary>
        [Required(ErrorMessage = "消息列表不能为空")]
        [MinLength(1, ErrorMessage = "至少需要一条消息")]
        [MaxLength(100, ErrorMessage = "一次最多只能发布100条消息")]
        public List<SinglePublishMessage> Messages { get; set; } = new();

        /// <summary>
        /// 发布者标识（可选，用于审计）
        /// </summary>
        public string? PublisherId { get; set; }

        /// <summary>
        /// 批量发布备注（可选）
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 单条发布消息DTO
    /// </summary>
    public class SinglePublishMessage
    {
        /// <summary>
        /// 主题
        /// </summary>
        [Required(ErrorMessage = "主题不能为空")]
        [StringLength(256, ErrorMessage = "主题长度不能超过256个字符")]
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 消息负载
        /// </summary>
        [Required(ErrorMessage = "消息负载不能为空")]
        public string Payload { get; set; } = string.Empty;

        /// <summary>
        /// 服务质量等级 (0, 1, 2)
        /// </summary>
        [Range(0, 2, ErrorMessage = "QoS必须在0-2之间")]
        public int Qos { get; set; } = 0;

        /// <summary>
        /// 是否保留消息
        /// </summary>
        public bool Retain { get; set; } = false;

        /// <summary>
        /// 编码方式
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public PayloadEncoding Encoding { get; set; } = PayloadEncoding.Utf8;
    }

    /// <summary>
    /// 获取保留消息请求DTO
    /// </summary>
    public class GetRetainedMessagesRequest : PagedRequestDto
    {
        /// <summary>
        /// 主题模式过滤
        /// </summary>
        public string? TopicPattern { get; set; }

        /// <summary>
        /// 最小负载大小过滤
        /// </summary>
        public int? MinPayloadSize { get; set; }

        /// <summary>
        /// 最大负载大小过滤
        /// </summary>
        public int? MaxPayloadSize { get; set; }

        /// <summary>
        /// 开始时间过滤
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间过滤
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public RetainedMessageSortBy? SortBy { get; set; }

        /// <summary>
        /// 排序方向
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public SortDirection? SortDirection { get; set; }
    }

    /// <summary>
    /// 清除保留消息请求DTO
    /// </summary>
    public class ClearRetainedMessageRequest
    {
        /// <summary>
        /// 清除原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 操作者
        /// </summary>
        public string? OperatorId { get; set; }
    }

    /// <summary>
    /// 批量清除保留消息请求DTO
    /// </summary>
    public class BatchClearRetainedMessagesRequest
    {
        /// <summary>
        /// 主题列表
        /// </summary>
        [Required(ErrorMessage = "主题列表不能为空")]
        [MinLength(1, ErrorMessage = "至少需要一个主题")]
        public List<string> Topics { get; set; } = new();

        /// <summary>
        /// 清除原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 操作者
        /// </summary>
        public string? OperatorId { get; set; }
    }

    #endregion

    #region 响应 DTO

    /// <summary>
    /// 发布消息响应DTO
    /// </summary>
    public class PublishMessageResponse
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string MessageId { get; set; } = string.Empty;

        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int Qos { get; set; }

        /// <summary>
        /// 是否保留消息
        /// </summary>
        public bool Retain { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime PublishTime { get; set; }

        /// <summary>
        /// 订阅者数量
        /// </summary>
        public int SubscriberCount { get; set; }

        /// <summary>
        /// 负载大小（字节）
        /// </summary>
        public int PayloadSize { get; set; }

        /// <summary>
        /// 发布状态
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public PublishStatus Status { get; set; }

        /// <summary>
        /// 错误信息（如果发布失败）
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 批量发布响应DTO
    /// </summary>
    public class BatchPublishResponse
    {
        /// <summary>
        /// 总消息数
        /// </summary>
        public int TotalMessages { get; set; }

        /// <summary>
        /// 成功发布的消息数
        /// </summary>
        public int SuccessfulPublishes { get; set; }

        /// <summary>
        /// 失败的发布数
        /// </summary>
        public int FailedPublishes { get; set; }

        /// <summary>
        /// 批量发布时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime PublishTime { get; set; }

        /// <summary>
        /// 详细结果
        /// </summary>
        public List<SinglePublishResult> Results { get; set; } = new();

        /// <summary>
        /// 总体统计
        /// </summary>
        public BatchPublishStatistics Statistics { get; set; } = new();
    }

    /// <summary>
    /// 单条发布结果DTO
    /// </summary>
    public class SinglePublishResult
    {
        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息ID（成功时）
        /// </summary>
        public string? MessageId { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime PublishTime { get; set; }

        /// <summary>
        /// 订阅者数量
        /// </summary>
        public int SubscriberCount { get; set; }

        /// <summary>
        /// 错误信息（失败时）
        /// </summary>
        public string? Error { get; set; }

        /// <summary>
        /// 负载大小（字节）
        /// </summary>
        public int PayloadSize { get; set; }
    }

    /// <summary>
    /// 批量发布统计DTO
    /// </summary>
    public class BatchPublishStatistics
    {
        /// <summary>
        /// 总订阅者数
        /// </summary>
        public int TotalSubscribers { get; set; }

        /// <summary>
        /// 总负载大小（字节）
        /// </summary>
        public long TotalPayloadSize { get; set; }

        /// <summary>
        /// 平均负载大小（字节）
        /// </summary>
        public double AveragePayloadSize { get; set; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 每秒处理消息数
        /// </summary>
        public double MessagesPerSecond { get; set; }
    }

    /// <summary>
    /// 保留消息DTO
    /// </summary>
    public class RetainedMessageDto
    {
        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 消息负载
        /// </summary>
        public string Payload { get; set; } = string.Empty;

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public int Qos { get; set; }

        /// <summary>
        /// 保留时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime RetainTime { get; set; }

        /// <summary>
        /// 负载大小（字节）
        /// </summary>
        public int PayloadSize { get; set; }

        /// <summary>
        /// 编码方式
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public PayloadEncoding Encoding { get; set; }

        /// <summary>
        /// 发布者（如果可用）
        /// </summary>
        public string? PublisherId { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime? LastUpdateTime { get; set; }
    }

    /// <summary>
    /// 保留消息列表响应DTO
    /// </summary>
    public class GetRetainedMessagesResponse : PagedResponseDto<RetainedMessageDto>
    {
        /// <summary>
        /// 保留消息统计
        /// </summary>
        public RetainedMessageStatistics Statistics { get; set; } = new();

        public GetRetainedMessagesResponse(List<RetainedMessageDto> messages, int totalCount, int page, int pageSize)
            : base(messages, totalCount, page, pageSize)
        {
        }
    }

    /// <summary>
    /// 保留消息统计DTO
    /// </summary>
    public class RetainedMessageStatistics
    {
        /// <summary>
        /// 总保留消息数
        /// </summary>
        public int TotalRetainedMessages { get; set; }

        /// <summary>
        /// 总负载大小（字节）
        /// </summary>
        public long TotalPayloadSize { get; set; }

        /// <summary>
        /// 平均负载大小（字节）
        /// </summary>
        public double AveragePayloadSize { get; set; }

        /// <summary>
        /// 最大负载大小（字节）
        /// </summary>
        public int MaxPayloadSize { get; set; }

        /// <summary>
        /// 最小负载大小（字节）
        /// </summary>
        public int MinPayloadSize { get; set; }

        /// <summary>
        /// 唯一主题数
        /// </summary>
        public int UniqueTopics { get; set; }
    }

    /// <summary>
    /// 清除保留消息响应DTO
    /// </summary>
    public class ClearRetainedMessageResponse
    {
        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 清除时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime ClearTime { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息（失败时）
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 清除的消息大小（字节）
        /// </summary>
        public int ClearedPayloadSize { get; set; }
    }

    /// <summary>
    /// 批量清除保留消息响应DTO
    /// </summary>
    public class BatchClearRetainedMessagesResponse
    {
        /// <summary>
        /// 总主题数
        /// </summary>
        public int TotalTopics { get; set; }

        /// <summary>
        /// 成功清除的主题数
        /// </summary>
        public int SuccessfulClears { get; set; }

        /// <summary>
        /// 失败的清除数
        /// </summary>
        public int FailedClears { get; set; }

        /// <summary>
        /// 清除时间
        /// </summary>
        [JsonConverter(typeof(JsonDateTimeConverter))]
        public DateTime ClearTime { get; set; }

        /// <summary>
        /// 详细结果
        /// </summary>
        public List<ClearRetainedMessageResponse> Results { get; set; } = new();

        /// <summary>
        /// 总清除的负载大小（字节）
        /// </summary>
        public long TotalClearedPayloadSize { get; set; }
    }

    #endregion

    #region 枚举

    /// <summary>
    /// 负载编码方式
    /// </summary>
    public enum PayloadEncoding
    {
        /// <summary>
        /// UTF-8编码
        /// </summary>
        Utf8,

        /// <summary>
        /// Base64编码
        /// </summary>
        Base64,

        /// <summary>
        /// 十六进制编码
        /// </summary>
        Hex
    }

    /// <summary>
    /// 发布状态
    /// </summary>
    public enum PublishStatus
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success,

        /// <summary>
        /// 失败
        /// </summary>
        Failed,

        /// <summary>
        /// 部分成功
        /// </summary>
        PartialSuccess
    }

    /// <summary>
    /// 保留消息排序字段
    /// </summary>
    public enum RetainedMessageSortBy
    {
        /// <summary>
        /// 按主题排序
        /// </summary>
        Topic,

        /// <summary>
        /// 按保留时间排序
        /// </summary>
        RetainTime,

        /// <summary>
        /// 按负载大小排序
        /// </summary>
        PayloadSize,

        /// <summary>
        /// 按更新时间排序
        /// </summary>
        LastUpdateTime
    }

    /// <summary>
    /// 排序方向
    /// </summary>
    public enum SortDirection
    {
        /// <summary>
        /// 升序
        /// </summary>
        Ascending,

        /// <summary>
        /// 降序
        /// </summary>
        Descending
    }

    #endregion

    #region 结果类

    /// <summary>
    /// 消息发布结果
    /// </summary>
    public class MessagePublishResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 发布响应数据
        /// </summary>
        public PublishMessageResponse? Data { get; set; }

        public static MessagePublishResult Success(PublishMessageResponse data)
        {
            return new MessagePublishResult
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static MessagePublishResult Error(string errorMessage)
        {
            return new MessagePublishResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 批量消息发布结果
    /// </summary>
    public class BatchMessagePublishResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 批量发布响应数据
        /// </summary>
        public BatchPublishResponse? Data { get; set; }

        public static BatchMessagePublishResult Success(BatchPublishResponse data)
        {
            return new BatchMessagePublishResult
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static BatchMessagePublishResult Error(string errorMessage)
        {
            return new BatchMessagePublishResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 保留消息管理结果
    /// </summary>
    public class RetainedMessageManagementResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 数据
        /// </summary>
        public object? Data { get; set; }

        public static RetainedMessageManagementResult Success(object? data = null)
        {
            return new RetainedMessageManagementResult
            {
                IsSuccess = true,
                Data = data
            };
        }

        public static RetainedMessageManagementResult Error(string errorMessage)
        {
            return new RetainedMessageManagementResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    #endregion
} 