{"openapi": "3.0.1", "info": {"title": "MQTT代理管理"}, "paths": {"/api/v1/mqtt-acl-management/paged-list": {"get": {"tags": ["MqttAclManagement"], "summary": "分页查询", "parameters": [{"name": "RuleName", "in": "query", "description": "规则名称", "schema": {"type": "string"}}, {"name": "AccessType", "in": "query", "description": "访问类型", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "description": "用户名", "schema": {"type": "string"}}, {"name": "ClientId", "in": "query", "description": "客户端ID", "schema": {"type": "string"}}, {"name": "Topic", "in": "query", "description": "主题", "schema": {"type": "string"}}, {"name": "Permission", "in": "query", "description": "权限（allow/deny）", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "description": "是否激活", "schema": {"type": "boolean"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.AclRuleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.AclRuleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.AclRuleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/mqtt-acl-management/{id}": {"get": {"tags": ["MqttAclManagement"], "summary": "单条查询", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}}}}}, "put": {"tags": ["MqttAclManagement"], "summary": "编辑", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["MqttAclManagement"], "summary": "删除", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/mqtt-acl-management": {"post": {"tags": ["MqttAclManagement"], "summary": "添加", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int64"}}, "application/json": {"schema": {"type": "integer", "format": "int64"}}, "text/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/mqtt-acl-management/batch-delete": {"post": {"tags": ["MqttAclManagement"], "summary": "批量删除", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/v1/mqtt-acl-management/test-permission": {"post": {"tags": ["MqttAclManagement"], "summary": "测试权限", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.TestPermissionInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.TestPermissionInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.TestPermissionInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput"}}}}}}}, "/api/v1/mqtt-acl-management/{id}/toggle": {"post": {"tags": ["MqttAclManagement"], "summary": "启用/禁用ACL规则", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success"}}}}, "/api/v1/mqtt-acl-management/import": {"post": {"tags": ["MqttAclManagement"], "summary": "导入ACL规则", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ImportResultOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ImportResultOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ImportResultOutput"}}}}}}}, "/api/v1/mqtt-acl-management/export": {"post": {"tags": ["MqttAclManagement"], "summary": "导出ACL规则", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}}}}}}}, "/api/v1/mqtt-acl-management/device-acl-rules": {"post": {"tags": ["MqttAclManagement"], "summary": "为设备创建ACL规则", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult"}}}}}}, "delete": {"tags": ["MqttAclManagement"], "summary": "删除设备的ACL规则", "parameters": [{"name": "DeviceId", "in": "query", "description": "设备ID", "required": true, "schema": {"type": "string"}}, {"name": "DeleteAllRelated", "in": "query", "description": "是否删除所有相关规则（包括自定义规则）", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/mqtt-acl-management/query-device-acl-rules": {"post": {"tags": ["MqttAclManagement"], "summary": "查询设备的ACL规则", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}}}}}}}}, "/api/v1/mqtt-acl-management/{oldDeviceId}/device-acl-rules": {"put": {"tags": ["MqttAclManagement"], "summary": "更新设备ACL规则（当设备信息变更时）", "parameters": [{"name": "oldDeviceId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult"}}}}}}}, "/api/v1/mqtt-broker-management/start": {"post": {"tags": ["MqttBrokerManagement"], "summary": "启动代理服务", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StartBrokerInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StartBrokerInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StartBrokerInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}}}}}}, "/api/v1/mqtt-broker-management/stop": {"post": {"tags": ["MqttBrokerManagement"], "summary": "停止代理服务", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}}}}}}, "/api/v1/mqtt-broker-management/restart": {"post": {"tags": ["MqttBrokerManagement"], "summary": "重启代理服务", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput"}}}}}}}, "/api/v1/mqtt-broker-management/status": {"get": {"tags": ["MqttBrokerManagement"], "summary": "获取代理服务状态", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput"}}}}}}}, "/api/v1/mqtt-broker-management/configuration": {"get": {"tags": ["MqttBrokerManagement"], "summary": "获取代理服务配置", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput"}}}}}}, "put": {"tags": ["MqttBrokerManagement"], "summary": "更新代理服务配置", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse"}}}}}}}, "/api/v1/mqtt-connection-management/connections": {"get": {"tags": ["MqttConnectionManagement"], "summary": "获取所有连接", "parameters": [{"name": "ClientId", "in": "query", "description": "客户端ID过滤", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "description": "用户名过滤", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "IP地址过滤", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "description": "连接状态过滤", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "description": "页码（默认：1）", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "每页大小（默认：20）", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}}}}}}, "/api/v1/mqtt-connection-management/{clientId}/connection-detail": {"get": {"tags": ["MqttConnectionManagement"], "summary": "获取单个连接详情", "parameters": [{"name": "clientId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}}}}}}, "/api/v1/mqtt-connection-management/{clientId}/disconnect-connection": {"post": {"tags": ["MqttConnectionManagement"], "summary": "断开指定连接", "parameters": [{"name": "clientId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}}}}}}, "/api/v1/mqtt-connection-management/batch-disconnect": {"post": {"tags": ["MqttConnectionManagement"], "summary": "批量断开连接", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult"}}}}}}}, "/api/v1/mqtt-message-publish/publish-message": {"post": {"tags": ["MqttMessagePublish"], "summary": "发布消息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MessagePublishResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MessagePublishResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MessagePublishResult"}}}}}}}, "/api/v1/mqtt-message-publish/batch-publish": {"post": {"tags": ["MqttMessagePublish"], "summary": "批量发布消息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult"}}}}}}}, "/api/v1/mqtt-message-publish/retained-messages": {"get": {"tags": ["MqttMessagePublish"], "summary": "获取保留消息列表", "parameters": [{"name": "TopicPattern", "in": "query", "description": "主题模式（支持模糊搜索）", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "PublisherId", "in": "query", "description": "发布者客户端ID", "schema": {"type": "string"}}, {"name": "Qos", "in": "query", "description": "QoS等级", "schema": {"maximum": 2, "minimum": 0, "type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/mqtt-message-publish/clear-retained-message": {"post": {"tags": ["MqttMessagePublish"], "summary": "清除指定主题的保留消息", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput"}}}}}}}, "/api/v1/mqtt-message-publish/topic-subscriber-count": {"get": {"tags": ["MqttMessagePublish"], "summary": "获取主题的订阅者数量", "parameters": [{"name": "topic", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/v1/mqtt-session-management/sessions": {"get": {"tags": ["MqttSessionManagement"], "summary": "获取所有会话", "parameters": [{"name": "ClientId", "in": "query", "description": "客户端ID过滤", "schema": {"type": "string"}}, {"name": "State", "in": "query", "description": "会话状态过滤", "schema": {"type": "string"}}, {"name": "Persistent", "in": "query", "description": "是否持久会话过滤", "schema": {"type": "boolean"}}, {"name": "Expired", "in": "query", "description": "是否过期过滤", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "description": "页码（默认：1）", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "每页大小（默认：20）", "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}}}}}}, "/api/v1/mqtt-session-management/{sessionId}/session-detail": {"get": {"tags": ["MqttSessionManagement"], "summary": "获取单个会话详情", "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}}}}}}, "/api/v1/mqtt-session-management/{clientId}/session-by-client-id": {"get": {"tags": ["MqttSessionManagement"], "summary": "根据客户端ID获取会话详情", "parameters": [{"name": "clientId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}}}}}}, "/api/v1/mqtt-session-management/{sessionId}/session-state": {"put": {"tags": ["MqttSessionManagement"], "summary": "更新会话状态", "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}}}}}}, "/api/v1/mqtt-session-management/{sessionId}/cleanup-session": {"post": {"tags": ["MqttSessionManagement"], "summary": "清理会话", "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}}}}}}, "/api/v1/mqtt-session-management/batch-session-operation": {"post": {"tags": ["MqttSessionManagement"], "summary": "批量会话操作", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}}}}}}, "/api/v1/mqtt-session-management/cleanup-expired-sessions": {"post": {"tags": ["MqttSessionManagement"], "summary": "清理过期会话", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SessionManagementResult"}}}}}}}, "/api/v1/mqtt-statistics/overview": {"get": {"tags": ["MqttStatistics"], "summary": "获取总体统计概览", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-statistics/performance-metrics": {"get": {"tags": ["MqttStatistics"], "summary": "获取实时性能指标", "parameters": [{"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "Interval", "in": "query", "description": "时间间隔", "schema": {"$ref": "#/components/schemas/System.TimeSpan"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-statistics/connection-statistics": {"get": {"tags": ["MqttStatistics"], "summary": "获取连接统计", "parameters": [{"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "GroupBy", "in": "query", "description": "时间分组方式", "schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.TimeGroupBy"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-statistics/message-statistics": {"get": {"tags": ["MqttStatistics"], "summary": "获取消息统计", "parameters": [{"name": "StartTime", "in": "query", "description": "开始时间", "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "schema": {"type": "string"}}, {"name": "GroupBy", "in": "query", "description": "时间分组方式", "schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.TimeGroupBy"}}, {"name": "TopTopicsCount", "in": "query", "description": "顶级主题数量", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-statistics/topic-analysis": {"get": {"tags": ["MqttStatistics"], "summary": "获取主题分析", "parameters": [{"name": "TopTopicsCount", "in": "query", "description": "顶级主题数量", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "最大深度", "schema": {"type": "integer", "format": "int32"}}, {"name": "TopicFilter", "in": "query", "description": "主题过滤器", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-statistics/session-statistics": {"get": {"tags": ["MqttStatistics"], "summary": "获取会话统计", "parameters": [{"name": "request", "in": "query", "schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsRequest"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-statistics/historical-statistics": {"get": {"tags": ["MqttStatistics"], "summary": "获取历史统计数据", "parameters": [{"name": "StartTime", "in": "query", "description": "开始时间", "required": true, "schema": {"type": "string"}}, {"name": "EndTime", "in": "query", "description": "结束时间", "required": true, "schema": {"type": "string"}}, {"name": "Interval", "in": "query", "description": "时间间隔", "schema": {"$ref": "#/components/schemas/System.TimeSpan"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-statistics/export-statistics-report": {"post": {"tags": ["MqttStatistics"], "summary": "导出统计报告", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsResult"}}}}}}}, "/api/v1/mqtt-user-management": {"post": {"tags": ["MqttUserManagement"], "summary": "添加MQTT用户", "requestBody": {"description": "用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}, "put": {"tags": ["MqttUserManagement"], "summary": "更新MQTT用户", "parameters": [{"name": "username", "in": "query", "description": "用户名", "schema": {"type": "string"}}], "requestBody": {"description": "更新信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}, "delete": {"tags": ["MqttUserManagement"], "summary": "删除MQTT用户", "parameters": [{"name": "username", "in": "query", "description": "用户名", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}, "get": {"tags": ["MqttUserManagement"], "summary": "获取MQTT用户详情", "parameters": [{"name": "username", "in": "query", "description": "用户名", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MqttUserOutput"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MqttUserOutput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MqttUserOutput"}}}}}}}, "/api/v1/mqtt-user-management/query": {"post": {"tags": ["MqttUserManagement"], "summary": "分页查询MQTT用户", "requestBody": {"description": "查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.MqttUserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.MqttUserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.MqttUserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}}}}, "/api/v1/mqtt-user-management/toggle": {"post": {"tags": ["MqttUserManagement"], "summary": "启用/禁用MQTT用户", "parameters": [{"name": "username", "in": "query", "description": "用户名", "schema": {"type": "string"}}, {"name": "isEnabled", "in": "query", "description": "是否启用", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/mqtt-user-management/reset-password": {"post": {"tags": ["MqttUserManagement"], "summary": "重置MQTT用户密码", "parameters": [{"name": "username", "in": "query", "description": "用户名", "schema": {"type": "string"}}, {"name": "newPassword", "in": "query", "description": "新密码", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/mqtt-user-management/device-user": {"post": {"tags": ["MqttUserManagement"], "summary": "为设备创建MQTT用户", "requestBody": {"description": "设备信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/mqtt-user-management/{deviceId}/device-user": {"delete": {"tags": ["MqttUserManagement"], "summary": "删除设备MQTT用户", "parameters": [{"name": "deviceId", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"Admin.Application.MqttBrokerServices.Dto.AclConfigurationSectionOutput": {"type": "object", "properties": {"enableAcl": {"type": "boolean", "description": "是否启用ACL"}, "ruleCount": {"type": "integer", "description": "规则数量", "format": "int32"}}, "additionalProperties": false, "description": "ACL配置节输出"}, "Admin.Application.MqttBrokerServices.Dto.AclConfigurationUpdateDto": {"type": "object", "properties": {"enableAcl": {"type": "boolean", "description": "是否启用ACL", "nullable": true}, "defaultPolicy": {"type": "string", "description": "默认策略", "nullable": true}}, "additionalProperties": false, "description": "ACL配置更新DTO"}, "Admin.Application.MqttBrokerServices.Dto.AclRuleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "规则ID", "format": "int64"}, "ruleName": {"type": "string", "description": "规则名称", "nullable": true}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "accessType": {"$ref": "#/components/schemas/Admin.Communication.Mqtt.Configuration.MqttAccessType"}, "allow": {"type": "boolean", "description": "是否允许访问"}, "username": {"type": "string", "description": "用户名（可选）", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID（可选）", "nullable": true}, "ipAddress": {"type": "string", "description": "IP地址（可选）", "nullable": true}, "topic": {"type": "string", "description": "MQTT主题", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "isActive": {"type": "boolean", "description": "是否激活"}, "effectiveStartTime": {"type": "string", "description": "生效开始时间", "nullable": true}, "effectiveEndTime": {"type": "string", "description": "生效结束时间", "nullable": true}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间", "nullable": true}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "updateBy": {"type": "integer", "description": "更新人", "format": "int64", "nullable": true}, "remark": {"type": "string", "description": "备注", "nullable": true}}, "additionalProperties": false, "description": "ACL规则输出"}, "Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput": {"required": ["accessType", "ruleName", "topic"], "type": "object", "properties": {"ruleName": {"maxLength": 100, "minLength": 0, "type": "string", "description": "规则名称"}, "priority": {"maximum": 999, "minimum": 0, "type": "integer", "description": "优先级", "format": "int32"}, "accessType": {"$ref": "#/components/schemas/Admin.Communication.Mqtt.Configuration.MqttAccessType"}, "allow": {"type": "boolean", "description": "是否允许访问"}, "username": {"maxLength": 50, "minLength": 0, "type": "string", "description": "用户名（可选）", "nullable": true}, "clientId": {"maxLength": 100, "minLength": 0, "type": "string", "description": "客户端ID（可选）", "nullable": true}, "ipAddress": {"maxLength": 50, "minLength": 0, "type": "string", "description": "IP地址（可选）", "nullable": true}, "topic": {"maxLength": 500, "minLength": 0, "type": "string", "description": "MQTT主题"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "description": "描述", "nullable": true}, "isActive": {"type": "boolean", "description": "是否激活"}, "effectiveStartTime": {"type": "string", "description": "生效开始时间", "nullable": true}, "effectiveEndTime": {"type": "string", "description": "生效结束时间", "nullable": true}}, "additionalProperties": false, "description": "添加ACL规则输入"}, "Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput": {"required": ["password", "username"], "type": "object", "properties": {"username": {"maxLength": 50, "minLength": 0, "type": "string", "description": "用户名"}, "password": {"maxLength": 100, "minLength": 0, "type": "string", "description": "密码"}, "clientIdLimit": {"maxLength": 500, "minLength": 0, "type": "string", "description": "客户端ID限制，多个用逗号分隔，为空表示不限制", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间，为空表示永不过期", "nullable": true}, "description": {"maxLength": 200, "minLength": 0, "type": "string", "description": "描述", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "添加MQTT用户请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationSectionOutput": {"type": "object", "properties": {"defaultUsername": {"type": "string", "description": "默认用户名", "nullable": true}, "defaultPassword": {"type": "string", "description": "默认密码（已屏蔽）", "nullable": true}, "requireAuthentication": {"type": "boolean", "description": "是否需要认证"}, "userCount": {"type": "integer", "description": "用户数量", "format": "int32"}}, "additionalProperties": false, "description": "认证配置节输出"}, "Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationUpdateDto": {"type": "object", "properties": {"defaultUsername": {"type": "string", "description": "默认用户名", "nullable": true}, "defaultPassword": {"type": "string", "description": "默认密码", "nullable": true}, "requireAuthentication": {"type": "boolean", "description": "是否需要认证", "nullable": true}}, "additionalProperties": false, "description": "认证配置更新DTO"}, "Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest": {"required": ["clientIds"], "type": "object", "properties": {"clientIds": {"type": "array", "items": {"type": "string"}, "description": "客户端ID列表"}, "reason": {"type": "string", "description": "断开原因", "nullable": true}, "sendDisconnectMessage": {"type": "boolean", "description": "是否发送断开消息"}}, "additionalProperties": false, "description": "批量断开连接请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "description": "是否成功"}, "errorMessage": {"type": "string", "description": "错误消息", "nullable": true}, "data": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse"}}, "additionalProperties": false, "description": "批量消息发布结果"}, "Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest": {"required": ["messages"], "type": "object", "properties": {"messages": {"maxItems": 100, "minItems": 1, "type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage"}, "description": "消息列表"}, "publisherId": {"type": "string", "description": "发布者标识（可选，用于审计）", "nullable": true}, "description": {"type": "string", "description": "批量发布备注（可选）", "nullable": true}}, "additionalProperties": false, "description": "批量发布消息请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse": {"type": "object", "properties": {"totalMessages": {"type": "integer", "description": "总消息数", "format": "int32"}, "successfulPublishes": {"type": "integer", "description": "成功发布的消息数", "format": "int32"}, "failedPublishes": {"type": "integer", "description": "失败的发布数", "format": "int32"}, "publishTime": {"type": "string", "description": "批量发布时间"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.SinglePublishResult"}, "description": "详细结果", "nullable": true}, "statistics": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics"}}, "additionalProperties": false, "description": "批量发布响应DTO"}, "Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics": {"type": "object", "properties": {"totalSubscribers": {"type": "integer", "description": "总订阅者数", "format": "int32"}, "totalPayloadSize": {"type": "integer", "description": "总负载大小（字节）", "format": "int64"}, "averagePayloadSize": {"type": "number", "description": "平均负载大小（字节）", "format": "double"}, "processingTimeMs": {"type": "integer", "description": "处理耗时（毫秒）", "format": "int64"}, "messagesPerSecond": {"type": "number", "description": "每秒处理消息数", "format": "double"}}, "additionalProperties": false, "description": "批量发布统计DTO"}, "Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest": {"required": ["operation", "sessionIds"], "type": "object", "properties": {"sessionIds": {"type": "array", "items": {"type": "string"}, "description": "会话ID列表"}, "operation": {"minLength": 1, "type": "string", "description": "操作类型"}, "reason": {"type": "string", "description": "操作原因", "nullable": true}}, "additionalProperties": false, "description": "批量会话操作请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput": {"type": "object", "properties": {"broker": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput"}, "authentication": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationSectionOutput"}, "acl": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclConfigurationSectionOutput"}}, "additionalProperties": false, "description": "代理配置输出"}, "Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput": {"type": "object", "properties": {"port": {"type": "integer", "description": "端口", "format": "int32"}, "maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32"}, "maxConnectionsPerIp": {"type": "integer", "description": "单个IP最大连接数", "format": "int32"}, "connectionTimeout": {"type": "integer", "description": "连接超时时间", "format": "int32"}, "useTls": {"type": "boolean", "description": "是否使用TLS"}, "certificatePath": {"type": "string", "description": "证书路径", "nullable": true}, "allowAnonymousAccess": {"type": "boolean", "description": "是否允许匿名访问"}, "enableRetainedMessages": {"type": "boolean", "description": "是否启用保留消息"}, "enableStatistics": {"type": "boolean", "description": "是否启用统计"}, "maxTopicLength": {"type": "integer", "description": "最大主题长度", "format": "int32"}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32"}, "bindAddress": {"type": "string", "description": "绑定地址", "nullable": true}, "enableWebSockets": {"type": "boolean", "description": "是否启用WebSocket"}, "webSocketPort": {"type": "integer", "description": "WebSocket端口", "format": "int32"}, "webSocketPath": {"type": "string", "description": "WebSocket路径", "nullable": true}}, "additionalProperties": false, "description": "代理配置节输出"}, "Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto": {"type": "object", "properties": {"port": {"maximum": 65535, "minimum": 1, "type": "integer", "description": "监听端口", "format": "int32", "nullable": true}, "maxConnections": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "maxConnectionsPerIp": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "单个IP最大连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "连接超时时间(秒)", "format": "int32", "nullable": true}, "enableTls": {"type": "boolean", "description": "是否启用TLS", "nullable": true}, "certificatePath": {"type": "string", "description": "证书路径", "nullable": true}, "allowAnonymousAccess": {"type": "boolean", "description": "是否允许匿名访问", "nullable": true}, "enableRetainedMessages": {"type": "boolean", "description": "是否启用保留消息", "nullable": true}, "enableStatistics": {"type": "boolean", "description": "是否启用统计", "nullable": true}, "maxTopicLength": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "最大主题长度", "format": "int32", "nullable": true}, "maxMessageSize": {"maximum": 2147483647, "minimum": 1, "type": "integer", "description": "最大消息大小", "format": "int32", "nullable": true}, "bindAddress": {"type": "string", "description": "绑定地址", "nullable": true}}, "additionalProperties": false, "description": "代理配置更新DTO"}, "Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput": {"type": "object", "properties": {"message": {"type": "string", "description": "消息", "nullable": true}, "port": {"type": "integer", "description": "端口", "format": "int32", "nullable": true}, "isRunning": {"type": "boolean", "description": "是否运行中"}, "startTime": {"type": "string", "description": "启动时间", "nullable": true}, "stopTime": {"type": "string", "description": "停止时间", "nullable": true}, "restartTime": {"type": "string", "description": "重启时间", "nullable": true}, "previousUptime": {"type": "string", "description": "之前运行时长", "nullable": true}, "timestamp": {"type": "string", "description": "时间戳"}}, "additionalProperties": false, "description": "代理服务操作输出"}, "Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput": {"type": "object", "properties": {"maxConnections": {"maximum": 100000, "minimum": 1, "type": "integer", "description": "最大连接数", "format": "int32", "nullable": true}, "maxConnectionsPerIp": {"maximum": 1000, "minimum": 1, "type": "integer", "description": "单个IP最大连接数", "format": "int32", "nullable": true}, "connectionTimeout": {"maximum": 3600, "minimum": 1, "type": "integer", "description": "连接超时时间(秒)", "format": "int32", "nullable": true}, "enableTls": {"type": "boolean", "description": "是否启用TLS", "nullable": true}, "allowAnonymousAccess": {"type": "boolean", "description": "是否允许匿名访问", "nullable": true}}, "additionalProperties": false, "description": "代理启动配置输入"}, "Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput": {"type": "object", "properties": {"maxConnections": {"type": "integer", "description": "最大连接数", "format": "int32"}, "maxConnectionsPerIp": {"type": "integer", "description": "单个IP最大连接数", "format": "int32"}, "connectionTimeout": {"type": "integer", "description": "连接超时时间", "format": "int32"}, "enableRetainedMessages": {"type": "boolean", "description": "是否启用保留消息"}, "enableStatistics": {"type": "boolean", "description": "是否启用统计"}, "allowAnonymousAccess": {"type": "boolean", "description": "是否允许匿名访问"}, "useTls": {"type": "boolean", "description": "是否使用TLS"}, "maxMessageSize": {"type": "integer", "description": "最大消息大小", "format": "int32"}, "maxTopicLength": {"type": "integer", "description": "最大主题长度", "format": "int32"}}, "additionalProperties": false, "description": "代理状态配置输出"}, "Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput": {"type": "object", "properties": {"isRunning": {"type": "boolean", "description": "是否运行中"}, "port": {"type": "integer", "description": "端口", "format": "int32"}, "startTime": {"type": "string", "description": "启动时间", "nullable": true}, "uptime": {"type": "string", "description": "运行时长", "nullable": true}, "version": {"type": "string", "description": "版本", "nullable": true}, "connectedClientCount": {"type": "integer", "description": "已连接客户端数量", "format": "int32"}, "configuration": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput"}}, "additionalProperties": false, "description": "代理状态输出"}, "Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput": {"required": ["topic"], "type": "object", "properties": {"topic": {"maxLength": 256, "minLength": 0, "type": "string", "description": "要清除的主题"}}, "additionalProperties": false, "description": "清除保留消息输入"}, "Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput": {"type": "object", "properties": {"topic": {"type": "string", "description": "主题", "nullable": true}, "clearTime": {"type": "string", "description": "清除时间"}, "success": {"type": "boolean", "description": "是否成功"}, "clearedPayloadSize": {"type": "integer", "description": "清除的负载大小", "format": "int32"}, "errorMessage": {"type": "string", "description": "错误信息（如果有）", "nullable": true}}, "additionalProperties": false, "description": "清除保留消息输出"}, "Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "description": "是否成功"}, "message": {"type": "string", "description": "消息", "nullable": true}, "data": {"description": "数据", "nullable": true}, "errorMessage": {"type": "string", "description": "错误信息", "nullable": true}, "timestamp": {"type": "string", "description": "时间戳"}}, "additionalProperties": false, "description": "连接管理操作结果"}, "Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput": {"required": ["deviceId"], "type": "object", "properties": {"deviceId": {"minLength": 1, "type": "string", "description": "设备ID"}, "deviceName": {"type": "string", "description": "设备名称", "nullable": true}, "topicPrefix": {"type": "string", "description": "自定义主题前缀（可选，默认使用设备ID）", "nullable": true}, "allowSystemTopics": {"type": "boolean", "description": "是否允许订阅系统主题"}, "customAllowedTopics": {"type": "array", "items": {"type": "string"}, "description": "自定义允许的主题列表", "nullable": true}, "customDeniedTopics": {"type": "array", "items": {"type": "string"}, "description": "自定义禁止的主题列表", "nullable": true}}, "additionalProperties": false, "description": "创建设备ACL规则请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput": {"required": ["deviceId", "deviceSecret"], "type": "object", "properties": {"deviceId": {"minLength": 1, "type": "string", "description": "设备ID（作为用户名）"}, "deviceSecret": {"minLength": 1, "type": "string", "description": "设备密钥（作为密码）"}, "deviceName": {"type": "string", "description": "设备名称（作为描述）", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间，为空表示永不过期", "nullable": true}}, "additionalProperties": false, "description": "创建设备MQTT用户请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.CreatedAclRule": {"type": "object", "properties": {"ruleId": {"type": "integer", "description": "规则ID", "format": "int64"}, "ruleName": {"type": "string", "description": "规则名称", "nullable": true}, "topic": {"type": "string", "description": "主题", "nullable": true}, "accessType": {"$ref": "#/components/schemas/Admin.Communication.Mqtt.Configuration.MqttAccessType"}, "allow": {"type": "boolean", "description": "是否允许"}}, "additionalProperties": false, "description": "创建成功的ACL规则"}, "Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult": {"type": "object", "properties": {"deviceId": {"type": "string", "description": "设备ID", "nullable": true}, "createdRules": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.CreatedAclRule"}, "description": "创建成功的规则列表", "nullable": true}, "failedRules": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.FailedAclRule"}, "description": "创建失败的规则列表", "nullable": true}, "totalRequested": {"type": "integer", "description": "总请求数", "format": "int32"}, "successCount": {"type": "integer", "description": "成功数", "format": "int32"}, "failCount": {"type": "integer", "description": "失败数", "format": "int32"}, "createTime": {"type": "string", "description": "创建时间"}}, "additionalProperties": false, "description": "设备ACL规则创建结果"}, "Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest": {"type": "object", "properties": {"reason": {"type": "string", "description": "断开原因", "nullable": true}, "sendDisconnectMessage": {"type": "boolean", "description": "是否发送断开消息"}}, "additionalProperties": false, "description": "断开连接请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.ExportFormat": {"enum": [0, 1, 2], "type": "integer", "description": "导出格式", "format": "int32"}, "Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest": {"required": ["format"], "type": "object", "properties": {"format": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.ExportFormat"}, "startTime": {"type": "string", "description": "开始时间", "nullable": true}, "endTime": {"type": "string", "description": "结束时间", "nullable": true}, "includeTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.StatisticsType"}, "description": "包含的统计类型", "nullable": true}}, "additionalProperties": false, "description": "导出统计请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.FailedAclRule": {"type": "object", "properties": {"ruleName": {"type": "string", "description": "规则名称", "nullable": true}, "topic": {"type": "string", "description": "主题", "nullable": true}, "accessType": {"$ref": "#/components/schemas/Admin.Communication.Mqtt.Configuration.MqttAccessType"}, "failureReason": {"type": "string", "description": "失败原因", "nullable": true}}, "additionalProperties": false, "description": "创建失败的ACL规则"}, "Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsRequest": {"type": "object", "additionalProperties": false, "description": "获取会话统计请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.ImportResultOutput": {"type": "object", "properties": {"importedRules": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}, "description": "导入成功的规则", "nullable": true}, "failedRules": {"type": "array", "items": {"type": "string"}, "description": "导入失败的规则", "nullable": true}, "totalRequested": {"type": "integer", "description": "请求总数", "format": "int32"}, "successCount": {"type": "integer", "description": "成功数量", "format": "int32"}, "failCount": {"type": "integer", "description": "失败数量", "format": "int32"}, "importTime": {"type": "string", "description": "导入时间"}}, "additionalProperties": false, "description": "导入结果输出"}, "Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput": {"type": "object", "properties": {"id": {"type": "integer", "description": "规则ID", "format": "int64"}, "priority": {"type": "integer", "description": "优先级", "format": "int32"}, "permission": {"type": "string", "description": "权限", "nullable": true}, "matchedPattern": {"type": "string", "description": "匹配的模式", "nullable": true}, "matchType": {"type": "string", "description": "匹配类型", "nullable": true}}, "additionalProperties": false, "description": "匹配的规则输出"}, "Admin.Application.MqttBrokerServices.Dto.MessagePublishResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "description": "是否成功"}, "errorMessage": {"type": "string", "description": "错误消息", "nullable": true}, "data": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse"}}, "additionalProperties": false, "description": "消息发布结果"}, "Admin.Application.MqttBrokerServices.Dto.MqttUserOutput": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名", "nullable": true}, "clientIdLimit": {"type": "string", "description": "客户端ID限制", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间", "nullable": true}, "lastLoginTime": {"type": "string", "description": "最后登录时间", "nullable": true}, "lastLoginIp": {"type": "string", "description": "最后登录IP", "nullable": true}, "description": {"type": "string", "description": "描述", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}, "createTime": {"type": "string", "description": "创建时间"}, "isExpired": {"type": "boolean", "description": "是否过期", "readOnly": true}, "statusDescription": {"type": "string", "description": "状态描述", "nullable": true, "readOnly": true}}, "additionalProperties": false, "description": "MQTT用户输出DTO"}, "Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput": {"type": "object", "properties": {"pageIndex": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "pageSize": {"maximum": 200, "minimum": 5, "type": "integer", "format": "int32"}, "username": {"type": "string", "description": "用户名过滤", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用过滤", "nullable": true}}, "additionalProperties": false, "description": "MQTT用户查询请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.PayloadEncoding": {"enum": [0, 1, 2], "type": "integer", "description": "负载编码方式", "format": "int32"}, "Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput": {"type": "object", "properties": {"isAuthorized": {"type": "boolean", "description": "是否授权"}, "failureReason": {"type": "string", "description": "失败原因", "nullable": true}, "matchedRule": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput"}, "allMatchedRules": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput"}, "description": "所有匹配的规则", "nullable": true}, "testTime": {"type": "string", "description": "测试时间"}}, "additionalProperties": false, "description": "权限测试输出"}, "Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest": {"required": ["payload", "topic"], "type": "object", "properties": {"topic": {"maxLength": 256, "minLength": 0, "type": "string", "description": "主题"}, "payload": {"minLength": 1, "type": "string", "description": "消息负载"}, "qos": {"maximum": 2, "minimum": 0, "type": "integer", "description": "服务质量等级 (0, 1, 2)", "format": "int32"}, "retain": {"type": "boolean", "description": "是否保留消息"}, "encoding": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PayloadEncoding"}, "publisherId": {"type": "string", "description": "发布者标识（可选，用于审计）", "nullable": true}, "description": {"type": "string", "description": "发布备注（可选）", "nullable": true}}, "additionalProperties": false, "description": "发布消息请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse": {"type": "object", "properties": {"messageId": {"type": "string", "description": "消息ID", "nullable": true}, "topic": {"type": "string", "description": "主题", "nullable": true}, "qos": {"type": "integer", "description": "服务质量等级", "format": "int32"}, "retain": {"type": "boolean", "description": "是否保留消息"}, "publishTime": {"type": "string", "description": "发布时间"}, "subscriberCount": {"type": "integer", "description": "订阅者数量", "format": "int32"}, "payloadSize": {"type": "integer", "description": "负载大小（字节）", "format": "int32"}, "status": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PublishStatus"}, "errorMessage": {"type": "string", "description": "错误信息（如果发布失败）", "nullable": true}}, "additionalProperties": false, "description": "发布消息响应DTO"}, "Admin.Application.MqttBrokerServices.Dto.PublishStatus": {"enum": [0, 1, 2], "type": "integer", "description": "发布状态", "format": "int32"}, "Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput": {"required": ["accessType", "ruleName", "topic"], "type": "object", "properties": {"ruleName": {"maxLength": 100, "minLength": 0, "type": "string", "description": "规则名称"}, "priority": {"maximum": 999, "minimum": 0, "type": "integer", "description": "优先级", "format": "int32"}, "accessType": {"$ref": "#/components/schemas/Admin.Communication.Mqtt.Configuration.MqttAccessType"}, "allow": {"type": "boolean", "description": "是否允许访问"}, "username": {"maxLength": 50, "minLength": 0, "type": "string", "description": "用户名（可选）", "nullable": true}, "clientId": {"maxLength": 100, "minLength": 0, "type": "string", "description": "客户端ID（可选）", "nullable": true}, "ipAddress": {"maxLength": 50, "minLength": 0, "type": "string", "description": "IP地址（可选）", "nullable": true}, "topic": {"maxLength": 500, "minLength": 0, "type": "string", "description": "MQTT主题"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "description": "描述", "nullable": true}, "isActive": {"type": "boolean", "description": "是否激活"}, "effectiveStartTime": {"type": "string", "description": "生效开始时间", "nullable": true}, "effectiveEndTime": {"type": "string", "description": "生效结束时间", "nullable": true}}, "additionalProperties": false, "description": "更新ACL规则输入"}, "Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput": {"type": "object", "properties": {"deviceId": {"type": "string", "description": "设备ID", "nullable": true}, "clientId": {"type": "string", "description": "客户端ID", "nullable": true}, "topicFilter": {"type": "string", "description": "主题过滤", "nullable": true}, "accessType": {"$ref": "#/components/schemas/Admin.Communication.Mqtt.Configuration.MqttAccessType"}, "onlyActive": {"type": "boolean", "description": "是否只查询激活的规则"}}, "additionalProperties": false, "description": "设备ACL规则查询请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput": {"type": "object", "properties": {"gracefulShutdown": {"type": "boolean", "description": "是否优雅停机，默认为true"}, "shutdownTimeout": {"maximum": 300, "minimum": 1, "type": "integer", "description": "停机超时时间(秒)，默认为30秒", "format": "int32"}}, "additionalProperties": false, "description": "重启代理服务输入"}, "Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput": {"type": "object", "properties": {"topic": {"type": "string", "description": "主题", "nullable": true}, "payload": {"type": "string", "description": "消息负载", "nullable": true}, "qos": {"type": "integer", "description": "服务质量等级", "format": "int32"}, "retainTime": {"type": "string", "description": "消息保留时间"}, "lastUpdateTime": {"type": "string", "description": "最后更新时间"}, "payloadSize": {"type": "integer", "description": "负载大小（字节）", "format": "int32"}, "encoding": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PayloadEncoding"}, "publisherId": {"type": "string", "description": "发布者客户端ID", "nullable": true}, "id": {"type": "integer", "description": "消息ID", "format": "int64"}}, "additionalProperties": false, "description": "保留消息输出"}, "Admin.Application.MqttBrokerServices.Dto.SessionManagementResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "description": "操作是否成功"}, "message": {"type": "string", "description": "消息", "nullable": true}, "data": {"description": "返回数据", "nullable": true}, "errorMessage": {"type": "string", "description": "错误信息", "nullable": true}, "timestamp": {"type": "string", "description": "时间戳"}}, "additionalProperties": false, "description": "会话管理操作结果"}, "Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage": {"required": ["payload", "topic"], "type": "object", "properties": {"topic": {"maxLength": 256, "minLength": 0, "type": "string", "description": "主题"}, "payload": {"minLength": 1, "type": "string", "description": "消息负载"}, "qos": {"maximum": 2, "minimum": 0, "type": "integer", "description": "服务质量等级 (0, 1, 2)", "format": "int32"}, "retain": {"type": "boolean", "description": "是否保留消息"}, "encoding": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.PayloadEncoding"}}, "additionalProperties": false, "description": "单条发布消息DTO"}, "Admin.Application.MqttBrokerServices.Dto.SinglePublishResult": {"type": "object", "properties": {"topic": {"type": "string", "description": "主题", "nullable": true}, "success": {"type": "boolean", "description": "是否成功"}, "messageId": {"type": "string", "description": "消息ID（成功时）", "nullable": true}, "publishTime": {"type": "string", "description": "发布时间"}, "subscriberCount": {"type": "integer", "description": "订阅者数量", "format": "int32"}, "error": {"type": "string", "description": "错误信息（失败时）", "nullable": true}, "payloadSize": {"type": "integer", "description": "负载大小（字节）", "format": "int32"}}, "additionalProperties": false, "description": "单条发布结果DTO"}, "Admin.Application.MqttBrokerServices.Dto.StartBrokerInput": {"type": "object", "properties": {"port": {"maximum": 65535, "minimum": 1, "type": "integer", "description": "监听端口，默认为1883", "format": "int32"}, "configuration": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput"}}, "additionalProperties": false, "description": "启动代理服务输入"}, "Admin.Application.MqttBrokerServices.Dto.StatisticsResult": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "description": "是否成功"}, "message": {"type": "string", "description": "消息", "nullable": true}, "data": {"description": "数据", "nullable": true}, "errorMessage": {"type": "string", "description": "错误信息", "nullable": true}, "timestamp": {"type": "string", "description": "时间戳"}}, "additionalProperties": false, "description": "统计结果封装"}, "Admin.Application.MqttBrokerServices.Dto.StatisticsType": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "description": "统计类型", "format": "int32"}, "Admin.Application.MqttBrokerServices.Dto.TestPermissionInput": {"required": ["accessType", "clientId", "topic"], "type": "object", "properties": {"clientId": {"minLength": 1, "type": "string", "description": "客户端ID"}, "username": {"type": "string", "description": "用户名", "nullable": true}, "topic": {"minLength": 1, "type": "string", "description": "主题"}, "accessType": {"$ref": "#/components/schemas/Admin.Communication.Mqtt.Configuration.MqttAccessType"}}, "additionalProperties": false, "description": "权限测试输入"}, "Admin.Application.MqttBrokerServices.Dto.TimeGroupBy": {"enum": [0, 1, 2, 3, 4], "type": "integer", "description": "时间分组方式", "format": "int32"}, "Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest": {"type": "object", "properties": {"broker": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto"}, "authentication": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationUpdateDto"}, "acl": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclConfigurationUpdateDto"}}, "additionalProperties": false, "description": "更新配置请求"}, "Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse": {"type": "object", "properties": {"updatedTime": {"type": "string", "description": "更新时间"}, "requiresRestart": {"type": "boolean", "description": "是否需要重启"}, "changedSettings": {"type": "array", "items": {"type": "string"}, "description": "已更改的设置列表", "nullable": true}}, "additionalProperties": false, "description": "更新配置响应"}, "Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput": {"type": "object", "properties": {"password": {"maxLength": 100, "minLength": 0, "type": "string", "description": "密码（为空表示不修改密码）", "nullable": true}, "clientIdLimit": {"maxLength": 500, "minLength": 0, "type": "string", "description": "客户端ID限制，多个用逗号分隔，为空表示不限制", "nullable": true}, "expireTime": {"type": "string", "description": "过期时间，为空表示永不过期", "nullable": true}, "description": {"maxLength": 200, "minLength": 0, "type": "string", "description": "描述", "nullable": true}, "isEnabled": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false, "description": "更新MQTT用户请求DTO"}, "Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest": {"required": ["action"], "type": "object", "properties": {"action": {"minLength": 1, "type": "string", "description": "操作类型"}, "reason": {"type": "string", "description": "操作原因", "nullable": true}}, "additionalProperties": false, "description": "会话状态更新请求DTO"}, "Admin.Communication.Mqtt.Configuration.MqttAccessType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.AclRuleOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.AclRuleOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.MqttUserOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.MqttUserOutput"}, "nullable": true}}, "additionalProperties": false}, "Admin.SqlSugar.PagedList`1[[Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput, Admin.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput"}, "nullable": true}}, "additionalProperties": false}, "System.TimeSpan": {"type": "object", "properties": {"ticks": {"type": "integer", "format": "int64"}, "days": {"type": "integer", "format": "int32", "readOnly": true}, "hours": {"type": "integer", "format": "int32", "readOnly": true}, "milliseconds": {"type": "integer", "format": "int32", "readOnly": true}, "microseconds": {"type": "integer", "format": "int32", "readOnly": true}, "nanoseconds": {"type": "integer", "format": "int32", "readOnly": true}, "minutes": {"type": "integer", "format": "int32", "readOnly": true}, "seconds": {"type": "integer", "format": "int32", "readOnly": true}, "totalDays": {"type": "number", "format": "double", "readOnly": true}, "totalHours": {"type": "number", "format": "double", "readOnly": true}, "totalMilliseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMicroseconds": {"type": "number", "format": "double", "readOnly": true}, "totalNanoseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMinutes": {"type": "number", "format": "double", "readOnly": true}, "totalSeconds": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "MqttAclManagement", "description": "MQTT ACL权限管理服务"}, {"name": "MqttBrokerManagement", "description": "MQTT代理服务管理应用层服务"}, {"name": "MqttConnectionManagement", "description": "MQTT连接管理服务"}, {"name": "MqttMessagePublish", "description": "MQTT消息发布管理服务"}, {"name": "MqttSessionManagement", "description": "MQTT会话管理服务实现"}, {"name": "MqttStatistics", "description": "MQTT统计监控服务实现"}, {"name": "MqttUserManagement", "description": "MQTT用户管理应用服务"}, {"name": "MqttAclManagement", "description": "MQTT ACL权限管理服务"}, {"name": "MqttBrokerManagement", "description": "MQTT代理服务管理应用层服务"}, {"name": "MqttConnectionManagement", "description": "MQTT连接管理服务"}, {"name": "MqttMessagePublish", "description": "MQTT消息发布管理服务"}, {"name": "MqttSessionManagement", "description": "MQTT会话管理服务实现"}, {"name": "MqttStatistics", "description": "MQTT统计监控服务实现"}, {"name": "MqttUserManagement", "description": "MQTT用户管理应用服务"}, {"name": "MqttAclManagement", "description": "MQTT ACL权限管理服务"}, {"name": "MqttBrokerManagement", "description": "MQTT代理服务管理应用层服务"}, {"name": "MqttConnectionManagement", "description": "MQTT连接管理服务"}, {"name": "MqttMessagePublish", "description": "MQTT消息发布管理服务"}, {"name": "MqttSessionManagement", "description": "MQTT会话管理服务实现"}, {"name": "MqttStatistics", "description": "MQTT统计监控服务实现"}, {"name": "MqttUserManagement", "description": "MQTT用户管理应用服务"}]}