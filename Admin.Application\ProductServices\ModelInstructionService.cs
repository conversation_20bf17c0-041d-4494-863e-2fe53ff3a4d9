// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ProductServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ProductServices;

/// <summary>
/// 模型指令服务
/// </summary>
[ApiExplorerSettings(GroupName = ApiExplorerGroupConst.LOT)]
public class ModelInstructionService(
    ISqlSugarClient db,
    Repository<ModelInstructionEntity> modelInstructionRepository) : ApplicationService
{
    private readonly ISqlSugarClient _db = db;
    private readonly Repository<ModelInstructionEntity> _modelInstructionRepository = modelInstructionRepository;

    /// <summary>
    /// 获取模型指令分页列表
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns>分页列表</returns>
    public async Task<PagedList<ModelInstructionOutput>> GetPagedListAsync(ModelInstructionQueryInput input)
    {
        var query = _db.Queryable<ModelInstructionEntity>()
            .LeftJoin<ProductModelEntity>((mi, pm) => mi.ModelId == pm.Id)
            .WhereIF(input.ModelId.HasValue, (mi, pm) => mi.ModelId == input.ModelId.Value)
            .WhereIF(!string.IsNullOrEmpty(input.InstructionName), (mi, pm) => mi.InstructionName.Contains(input.InstructionName))
            .WhereIF(input.IsEnabled.HasValue, (mi, pm) => mi.IsEnabled == input.IsEnabled.Value)
            .OrderByDescending((mi, pm) => mi.CreateTime)
            .Select((mi, pm) => new ModelInstructionOutput
            {
                Id = mi.Id,
                ModelId = mi.ModelId,
                ModelName = pm.ModelName,
                InstructionName = mi.InstructionName,
                FunctionCode = mi.FunctionCode,
                StartAddress = mi.StartAddress,
                ReadCount = mi.ReadCount,
                Encode = mi.Encode,
                ResponseTime = mi.ResponseTime,
                RetryCount = mi.RetryCount,
                IsEnabled = mi.IsEnabled,
                CreateTime = mi.CreateTime,
                UpdateTime = mi.UpdateTime,
                CreateBy = mi.CreateBy,
                UpdateBy = mi.UpdateBy,
                Remark = mi.Remark
            });

        var pagedList = await query.ToPurestPagedListAsync(input.PageIndex, input.PageSize);
        var result = pagedList.Adapt<PagedList<ModelInstructionOutput>>();

        // 映射枚举名称
        foreach (var item in result.Items)
        {
            item.FunctionCodeName = GetFunctionCodeName(item.FunctionCode);
            item.EncodeName = GetEncodeName(item.Encode);
        }

        return result;
    }

    /// <summary>
    /// 根据ID获取模型指令详情
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <returns>指令详情</returns>
    public async Task<ModelInstructionOutput> GetByIdAsync(long id)
    {
        var entity = await _db.Queryable<ModelInstructionEntity>()
            .LeftJoin<ProductModelEntity>((mi, pm) => mi.ModelId == pm.Id)
            .Where((mi, pm) => mi.Id == id)
            .Select((mi, pm) => new ModelInstructionOutput
            {
                Id = mi.Id,
                ModelId = mi.ModelId,
                ModelName = pm.ModelName,
                InstructionName = mi.InstructionName,
                FunctionCode = mi.FunctionCode,
                StartAddress = mi.StartAddress,
                ReadCount = mi.ReadCount,
                Encode = mi.Encode,
                ResponseTime = mi.ResponseTime,
                RetryCount = mi.RetryCount,
                IsEnabled = mi.IsEnabled,
                CreateTime = mi.CreateTime,
                UpdateTime = mi.UpdateTime,
                CreateBy = mi.CreateBy,
                UpdateBy = mi.UpdateBy,
                Remark = mi.Remark
            })
            .FirstAsync() ??
            throw PersistdValidateException.Message("模型指令不存在");

        // 映射枚举名称
        entity.FunctionCodeName = GetFunctionCodeName(entity.FunctionCode);
        entity.EncodeName = GetEncodeName(entity.Encode);

        return entity;
    }

    /// <summary>
    /// 创建模型指令
    /// </summary>
    /// <param name="input">创建输入</param>
    /// <returns>创建结果</returns>
    public async Task<ModelInstructionOutput> CreateAsync(ModelInstructionInput input)
    {
        // 验证模型是否存在
        var model = await _db.Queryable<ProductModelEntity>()
            .Where(pm => pm.Id == input.ModelId)
            .FirstAsync() ??
            throw PersistdValidateException.Message("产品模型不存在");

        // 验证指令名称是否重复（同一模型下）
        var exists = await _db.Queryable<ModelInstructionEntity>()
            .Where(mi => mi.ModelId == input.ModelId && mi.InstructionName == input.InstructionName)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("该模型下已存在同名指令");

        var entity = input.Adapt<ModelInstructionEntity>();
        await _modelInstructionRepository.InsertAsync(entity);

        return await GetByIdAsync(entity.Id);
    }

    /// <summary>
    /// 更新模型指令
    /// 只能修改InstructionName、FunctionCode、StartAddress、ReadCount、Encode、ResponseTime、RetryCount、IsEnabled
    /// </summary>
    /// <param name="input">更新输入</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(UpdateModelInstructionInput input)
    {
        var entity = await _modelInstructionRepository.GetByIdAsync(input.Id) ??
            throw PersistdValidateException.Message("模型指令不存在");

        // 验证指令名称是否重复（同一模型下，排除自己）
        var exists = await _db.Queryable<ModelInstructionEntity>()
            .Where(mi => mi.ModelId == entity.ModelId && mi.InstructionName == input.InstructionName && mi.Id != input.Id)
            .AnyAsync();
        if (exists)
            throw PersistdValidateException.Message("该模型下已存在同名指令");

        // 只更新允许修改的字段
        entity.InstructionName = input.InstructionName;
        entity.FunctionCode = input.FunctionCode;
        entity.StartAddress = input.StartAddress;
        entity.ReadCount = input.ReadCount;
        entity.Encode = input.Encode;
        entity.ResponseTime = input.ResponseTime;
        entity.RetryCount = input.RetryCount;
        entity.IsEnabled = input.IsEnabled;

        return await _modelInstructionRepository.UpdateAsync(entity);
    }

    /// <summary>
    /// 删除模型指令
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <returns>删除结果</returns>
    [UnitOfWork]
    public async Task<bool> DeleteAsync(long id)
    {
        var entity = await _modelInstructionRepository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("模型指令不存在");

        return await _modelInstructionRepository.DeleteAsync(entity);
    }

    /// <summary>
    /// 批量删除模型指令
    /// </summary>
    /// <param name="ids">指令ID列表</param>
    /// <returns>删除结果</returns>
    [UnitOfWork]
    public async Task<bool> BatchDeleteAsync(List<long> ids)
    {
        if (!ids.Any())
            throw PersistdValidateException.Message("请选择要删除的指令");

        return await _modelInstructionRepository.DeleteAsync(mi => ids.Contains(mi.Id));
    }

    /// <summary>
    /// 根据模型ID获取指令列表
    /// </summary>
    /// <param name="modelId">模型ID</param>
    /// <returns>指令列表</returns>
    public async Task<List<ModelInstructionSimpleOutput>> GetByModelIdAsync(long modelId)
    {
        var instructions = await _db.Queryable<ModelInstructionEntity>()
            .Where(mi => mi.ModelId == modelId && mi.IsEnabled)
            .Select(mi => new ModelInstructionSimpleOutput
            {
                Id = mi.Id,
                ModelId = mi.ModelId,
                InstructionName = mi.InstructionName,
                FunctionCode = mi.FunctionCode,
                IsEnabled = mi.IsEnabled
            })
            .OrderBy(mi => mi.InstructionName)
            .ToListAsync();

        var result = instructions.Adapt<List<ModelInstructionSimpleOutput>>();

        // 映射功能码名称
        foreach (var item in result)
        {
            item.FunctionCodeName = GetFunctionCodeName(item.FunctionCode);
        }

        return result;
    }

    /// <summary>
    /// 设置指令启用状态
    /// </summary>
    /// <param name="id">指令ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>设置结果</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        var entity = await _modelInstructionRepository.GetByIdAsync(id) ??
            throw PersistdValidateException.Message("模型指令不存在");

        entity.IsEnabled = isEnabled;
        return await _modelInstructionRepository.UpdateAsync(entity);
    }

    /// <summary>
    /// 获取所有启用的指令列表
    /// </summary>
    /// <returns>指令列表</returns>
    public async Task<List<ModelInstructionSimpleOutput>> GetSimpleListAsync()
    {
        var instructions = await _db.Queryable<ModelInstructionEntity>()
            .Where(mi => mi.IsEnabled)
            .Select(mi => new ModelInstructionSimpleOutput
            {
                Id = mi.Id,
                ModelId = mi.ModelId,
                InstructionName = mi.InstructionName,
                FunctionCode = mi.FunctionCode,
                IsEnabled = mi.IsEnabled
            })
            .OrderBy(mi => mi.InstructionName)
            .ToListAsync();

        var result = instructions.Adapt<List<ModelInstructionSimpleOutput>>();

        // 映射功能码名称
        foreach (var item in result)
        {
            item.FunctionCodeName = GetFunctionCodeName(item.FunctionCode);
        }

        return result;
    }

    /// <summary>
    /// 获取功能码名称
    /// </summary>
    /// <param name="functionCode">功能码</param>
    /// <returns>功能码名称</returns>
    private static string GetFunctionCodeName(int functionCode)
    {
        return functionCode switch
        {
            1 => "读线圈状态",
            2 => "读离散输入状态",
            3 => "读保持寄存器",
            4 => "读输入寄存器",
            5 => "写单个线圈",
            6 => "写单个寄存器",
            15 => "写多个线圈",
            16 => "写多个寄存器",
            _ => "未知功能码"
        };
    }

    /// <summary>
    /// 获取编码名称
    /// </summary>
    /// <param name="encode">编码</param>
    /// <returns>编码名称</returns>
    private static string GetEncodeName(int encode)
    {
        return encode switch
        {
            1 => "HEX",
            2 => "ASCII",
            _ => "未知编码"
        };
    }
}