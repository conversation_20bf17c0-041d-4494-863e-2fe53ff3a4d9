// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.SqlSugar.Entity.Business.LOT;

/// <summary>
/// 历史控制指令发送记录
/// </summary>
[SugarTable("HIS_CONTROL_SEND")]
public partial class HisControlSendEntity
{
    /// <summary>
    /// 主键
    /// </summary>
    [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    [SugarColumn(ColumnName = "CMD_NAME")]
    public string CmdName { get; set; }

    /// <summary>
    /// 发送内容
    /// </summary>
    [SugarColumn(ColumnName = "SEND_CONTENT")]
    public string SendContent { get; set; }

    /// <summary>
    /// 返回内容
    /// </summary>
    [SugarColumn(ColumnName = "RESPONSE_CONTENT")]
    public string? ResponseContent { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    [SugarColumn(ColumnName = "SEND_TIME")]
    public DateTime SendTime { get; set; }

    /// <summary>
    /// 控制结果
    /// </summary>
    [SugarColumn(ColumnName = "CONTROL_RESULT")]
    public string? ControlResult { get; set; }

    /// <summary>
    /// 控制类型
    /// 1:手动控制 2:联动控制 3:定时控制
    /// </summary>
    [SugarColumn(ColumnName = "CONTROL_TYPE")]
    public int ControlType { get; set; }

    /// <summary>
    /// 通讯介质
    /// </summary>
    [SugarColumn(ColumnName = "MEDIUM")]
    public string? Medium { get; set; }

    /// <summary>
    /// 介质信息
    /// </summary>
    [SugarColumn(ColumnName = "MEDIUM_INFO")]
    public string? MediumInfo { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    [SugarColumn(ColumnName = "SOURCE")]
    public string? Source { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    [SugarColumn(ColumnName = "USERNAME")]
    public string? Username { get; set; }
}
