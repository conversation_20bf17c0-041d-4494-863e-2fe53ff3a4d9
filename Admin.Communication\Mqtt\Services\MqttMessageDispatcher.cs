using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Admin.Communication.Mqtt.Abstractions;
using Admin.Communication.Mqtt.Models;
using Admin.Core.ExceptionExtensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.Services
{
    /// <summary>
    /// MQTT消息分发服务，负责将消息分发给相应的处理器
    /// </summary>
    public class MqttMessageDispatcher : IMqttMessageDispatcher, IDisposable
    {
        private readonly ILogger<MqttMessageDispatcher> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly ConcurrentDictionary<string, IMqttMessageHandler> _handlers = new ConcurrentDictionary<string, IMqttMessageHandler>();
        private readonly ConcurrentDictionary<string, MqttMessageDispatcherStatistics> _statistics = new ConcurrentDictionary<string, MqttMessageDispatcherStatistics>();
        private bool _disposed = false;

        /// <summary>
        /// 获取已注册的处理器数量
        /// </summary>
        public int RegisteredHandlerCount => _handlers.Count;

        /// <summary>
        /// 获取所有处理器名称
        /// </summary>
        public IReadOnlyList<string> RegisteredHandlerNames => _handlers.Keys.ToList();

        /// <summary>
        /// 初始化消息分发器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="serviceProvider">服务提供者</param>
        public MqttMessageDispatcher(ILogger<MqttMessageDispatcher> logger, IServiceProvider serviceProvider)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// 初始化分发器
        /// </summary>
        /// <returns>初始化任务</returns>
        public async Task InitializeAsync()
        {
            _logger.LogInformation("正在初始化MQTT消息分发器...");

            try
            {
                // 从依赖注入容器中获取所有消息处理器
                var handlers = _serviceProvider.GetServices<IMqttMessageHandler>();
                
                foreach (var handler in handlers)
                {
                    await RegisterHandlerAsync(handler);
                }

                _logger.LogInformation("MQTT消息分发器初始化完成，已注册 {HandlerCount} 个处理器", _handlers.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化MQTT消息分发器失败");
                throw;
            }
        }

        /// <summary>
        /// 注册消息处理器
        /// </summary>
        /// <param name="handler">消息处理器</param>
        /// <returns>注册任务</returns>
        public async Task RegisterHandlerAsync(IMqttMessageHandler handler)
        {
            if (handler == null)
            {
                throw PersistdValidateException.Message("消息处理器不能为空");
            }

            if (_handlers.ContainsKey(handler.Name))
            {
                _logger.LogWarning("处理器 {HandlerName} 已经注册，将替换现有处理器", handler.Name);
                
                // 移除现有处理器
                if (_handlers.TryRemove(handler.Name, out var existingHandler))
                {
                    await existingHandler.DisposeAsync();
                }
            }

            try
            {
                // 初始化处理器
                await handler.InitializeAsync();
                
                // 注册处理器
                _handlers[handler.Name] = handler;
                _statistics[handler.Name] = new MqttMessageDispatcherStatistics();
                
                _logger.LogInformation("成功注册消息处理器: {HandlerName} ({HandlerDescription}), 优先级: {Priority}", 
                    handler.Name, handler.Description, handler.Priority);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册消息处理器失败: {HandlerName}", handler.Name);
                throw;
            }
        }

        /// <summary>
        /// 取消注册消息处理器
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        /// <returns>取消注册任务</returns>
        public async Task UnregisterHandlerAsync(string handlerName)
        {
            if (string.IsNullOrEmpty(handlerName))
            {
                throw PersistdValidateException.Message("处理器名称不能为空");
            }

            if (_handlers.TryRemove(handlerName, out var handler))
            {
                try
                {
                    await handler.DisposeAsync();
                    _statistics.TryRemove(handlerName, out _);
                    
                    _logger.LogInformation("成功取消注册消息处理器: {HandlerName}", handlerName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "取消注册消息处理器时发生错误: {HandlerName}", handlerName);
                    throw;
                }
            }
            else
            {
                _logger.LogWarning("尝试取消注册不存在的处理器: {HandlerName}", handlerName);
            }
        }

        /// <summary>
        /// 分发消息给合适的处理器
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>分发结果</returns>
        public async Task<MqttMessageDispatchResult> DispatchAsync(MqttMessage message, MqttMessageContext context)
        {
            if (message == null)
            {
                throw PersistdValidateException.Message("MQTT消息不能为空");
            }

            if (context == null)
            {
                throw PersistdValidateException.Message("消息处理上下文不能为空");
            }

            var result = new MqttMessageDispatchResult
            {
                Message = message,
                Context = context,
                StartTime = DateTime.Now
            };

            try
            {
                _logger.LogDebug("开始分发消息: {MessageType}", message.GetType().Name);

                // 获取能够处理此消息的处理器，按优先级排序
                var eligibleHandlers = GetEligibleHandlers(message, context)
                    .OrderBy(h => h.Priority)
                    .ToList();

                if (eligibleHandlers.Count == 0)
                {
                    _logger.LogDebug("没有找到能够处理消息的处理器: {MessageType}", message.GetType().Name);
                    result.IsSuccess = true;
                    result.ErrorMessage = "没有找到合适的处理器";
                    return result;
                }

                _logger.LogDebug("找到 {HandlerCount} 个能够处理消息的处理器", eligibleHandlers.Count);

                // 依次调用处理器
                foreach (var handler in eligibleHandlers)
                {
                    try
                    {
                        var handlerResult = await handler.HandleAsync(message, context);
                        result.HandlerResults.Add(handler.Name, handlerResult);

                        // 更新统计信息
                        if (_statistics.TryGetValue(handler.Name, out var stats))
                        {
                            stats.IncrementProcessed();
                            if (handlerResult.IsSuccess)
                            {
                                stats.IncrementSuccessful();
                            }
                            else
                            {
                                stats.IncrementFailed();
                            }
                            stats.UpdateAverageProcessingTime(handlerResult.ElapsedMilliseconds);
                        }

                        _logger.LogDebug("处理器 {HandlerName} 处理结果: {IsSuccess}, 耗时: {ElapsedMs}ms", 
                            handler.Name, handlerResult.IsSuccess, handlerResult.ElapsedMilliseconds);

                        // 如果处理器返回了响应消息，记录下来
                        if (handlerResult.ResponseMessage != null)
                        {
                            result.ResponseMessages.Add(handlerResult.ResponseMessage);
                        }

                        // 如果处理器要求停止继续处理，则跳出循环
                        if (!handlerResult.ContinueProcessing)
                        {
                            _logger.LogDebug("处理器 {HandlerName} 要求停止继续处理", handler.Name);
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理器 {HandlerName} 处理消息时发生异常", handler.Name);
                        
                        var errorResult = MqttMessageHandleResult.Failure($"处理器异常: {ex.Message}", ex);
                        result.HandlerResults.Add(handler.Name, errorResult);
                        
                        // 更新统计信息
                        if (_statistics.TryGetValue(handler.Name, out var stats))
                        {
                            stats.IncrementProcessed();
                            stats.IncrementFailed();
                        }
                    }
                }

                result.IsSuccess = true;
                result.ProcessedHandlerCount = result.HandlerResults.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分发消息时发生异常: {MessageType}", message.GetType().Name);
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                result.Exception = ex;
            }
            finally
            {
                result.EndTime = DateTime.Now;
                result.ElapsedMilliseconds = (long)(result.EndTime - result.StartTime).TotalMilliseconds;
            }

            _logger.LogDebug("消息分发完成: {MessageType}, 处理器数量: {HandlerCount}, 成功: {IsSuccess}, 耗时: {ElapsedMs}ms", 
                message.GetType().Name, result.ProcessedHandlerCount, result.IsSuccess, result.ElapsedMilliseconds);

            return result;
        }

        /// <summary>
        /// 获取处理器信息
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        /// <returns>处理器信息</returns>
        public MqttMessageHandlerInfo GetHandlerInfo(string handlerName)
        {
            if (string.IsNullOrEmpty(handlerName))
            {
                return null;
            }

            if (!_handlers.TryGetValue(handlerName, out var handler))
            {
                return null;
            }

            var statistics = _statistics.TryGetValue(handlerName, out var stats) ? stats : new MqttMessageDispatcherStatistics();

            return new MqttMessageHandlerInfo
            {
                Name = handler.Name,
                Description = handler.Description,
                IsEnabled = handler.IsEnabled,
                Priority = handler.Priority,
                SupportedTopicPatterns = handler.SupportedTopicPatterns,
                SupportedMessageTypes = handler.SupportedMessageTypes?.Select(t => t.Name).ToArray() ?? new string[0],
                TotalProcessed = statistics.TotalProcessed,
                TotalSuccessful = statistics.TotalSuccessful,
                TotalFailed = statistics.TotalFailed,
                SuccessRate = statistics.SuccessRate,
                AverageProcessingTime = statistics.AverageProcessingTime
            };
        }

        /// <summary>
        /// 获取所有处理器信息
        /// </summary>
        /// <returns>处理器信息列表</returns>
        public IReadOnlyList<MqttMessageHandlerInfo> GetAllHandlerInfos()
        {
            return _handlers.Keys.Select(GetHandlerInfo).Where(info => info != null).ToList();
        }

        /// <summary>
        /// 启用或禁用处理器
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        /// <param name="enabled">是否启用</param>
        /// <returns>操作是否成功</returns>
        public bool SetHandlerEnabled(string handlerName, bool enabled)
        {
            if (string.IsNullOrEmpty(handlerName))
            {
                return false;
            }

            if (_handlers.TryGetValue(handlerName, out var handler))
            {
                handler.IsEnabled = enabled;
                _logger.LogInformation("处理器 {HandlerName} 已{Status}", handlerName, enabled ? "启用" : "禁用");
                return true;
            }

            return false;
        }

        /// <summary>
        /// 重置处理器统计信息
        /// </summary>
        /// <param name="handlerName">处理器名称，为空则重置所有</param>
        public void ResetStatistics(string handlerName = null)
        {
            if (string.IsNullOrEmpty(handlerName))
            {
                // 重置所有统计信息
                foreach (var stats in _statistics.Values)
                {
                    stats.Reset();
                }
                _logger.LogInformation("已重置所有处理器的统计信息");
            }
            else if (_statistics.TryGetValue(handlerName, out var stats))
            {
                stats.Reset();
                _logger.LogInformation("已重置处理器 {HandlerName} 的统计信息", handlerName);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _logger.LogInformation("正在销毁MQTT消息分发器...");

                var disposeTasks = _handlers.Values.Select(h => h.DisposeAsync()).ToArray();
                Task.WaitAll(disposeTasks);

                _handlers.Clear();
                _statistics.Clear();
                _disposed = true;

                _logger.LogInformation("MQTT消息分发器已销毁");
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取能够处理指定消息的处理器
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>合适的处理器列表</returns>
        private IEnumerable<IMqttMessageHandler> GetEligibleHandlers(MqttMessage message, MqttMessageContext context)
        {
            return _handlers.Values.Where(handler => handler.CanHandle(message, context));
        }

        #endregion
    }

    /// <summary>
    /// 消息分发器接口
    /// </summary>
    public interface IMqttMessageDispatcher
    {
        /// <summary>
        /// 已注册的处理器数量
        /// </summary>
        int RegisteredHandlerCount { get; }

        /// <summary>
        /// 已注册的处理器名称
        /// </summary>
        IReadOnlyList<string> RegisteredHandlerNames { get; }

        /// <summary>
        /// 初始化分发器
        /// </summary>
        /// <returns>初始化任务</returns>
        Task InitializeAsync();

        /// <summary>
        /// 注册消息处理器
        /// </summary>
        /// <param name="handler">消息处理器</param>
        /// <returns>注册任务</returns>
        Task RegisterHandlerAsync(IMqttMessageHandler handler);

        /// <summary>
        /// 取消注册消息处理器
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        /// <returns>取消注册任务</returns>
        Task UnregisterHandlerAsync(string handlerName);

        /// <summary>
        /// 分发消息给合适的处理器
        /// </summary>
        /// <param name="message">MQTT消息</param>
        /// <param name="context">消息处理上下文</param>
        /// <returns>分发结果</returns>
        Task<MqttMessageDispatchResult> DispatchAsync(MqttMessage message, MqttMessageContext context);

        /// <summary>
        /// 获取处理器信息
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        /// <returns>处理器信息</returns>
        MqttMessageHandlerInfo GetHandlerInfo(string handlerName);

        /// <summary>
        /// 获取所有处理器信息
        /// </summary>
        /// <returns>处理器信息列表</returns>
        IReadOnlyList<MqttMessageHandlerInfo> GetAllHandlerInfos();

        /// <summary>
        /// 启用或禁用处理器
        /// </summary>
        /// <param name="handlerName">处理器名称</param>
        /// <param name="enabled">是否启用</param>
        /// <returns>操作是否成功</returns>
        bool SetHandlerEnabled(string handlerName, bool enabled);

        /// <summary>
        /// 重置处理器统计信息
        /// </summary>
        /// <param name="handlerName">处理器名称，为空则重置所有</param>
        void ResetStatistics(string handlerName = null);
    }

    /// <summary>
    /// 消息分发结果
    /// </summary>
    public class MqttMessageDispatchResult
    {
        /// <summary>
        /// 原始消息
        /// </summary>
        public MqttMessage Message { get; set; }

        /// <summary>
        /// 消息上下文
        /// </summary>
        public MqttMessageContext Context { get; set; }

        /// <summary>
        /// 是否分发成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }

        /// <summary>
        /// 处理的处理器数量
        /// </summary>
        public int ProcessedHandlerCount { get; set; }

        /// <summary>
        /// 各处理器的处理结果
        /// </summary>
        public Dictionary<string, MqttMessageHandleResult> HandlerResults { get; set; } = new Dictionary<string, MqttMessageHandleResult>();

        /// <summary>
        /// 响应消息列表
        /// </summary>
        public List<MqttMessage> ResponseMessages { get; set; } = new List<MqttMessage>();
    }

    /// <summary>
    /// 消息处理器信息
    /// </summary>
    public class MqttMessageHandlerInfo
    {
        /// <summary>
        /// 处理器名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 处理器描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 支持的主题模式
        /// </summary>
        public string[] SupportedTopicPatterns { get; set; }

        /// <summary>
        /// 支持的消息类型
        /// </summary>
        public string[] SupportedMessageTypes { get; set; }

        /// <summary>
        /// 总处理数
        /// </summary>
        public long TotalProcessed { get; set; }

        /// <summary>
        /// 总成功数
        /// </summary>
        public long TotalSuccessful { get; set; }

        /// <summary>
        /// 总失败数
        /// </summary>
        public long TotalFailed { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTime { get; set; }
    }

    /// <summary>
    /// 消息分发器统计信息
    /// </summary>
    public class MqttMessageDispatcherStatistics
    {
        private readonly object _lock = new object();
        private long _totalProcessed = 0;
        private long _totalSuccessful = 0;
        private long _totalFailed = 0;
        private long _totalProcessingTime = 0;

        /// <summary>
        /// 总处理数
        /// </summary>
        public long TotalProcessed => _totalProcessed;

        /// <summary>
        /// 总成功数
        /// </summary>
        public long TotalSuccessful => _totalSuccessful;

        /// <summary>
        /// 总失败数
        /// </summary>
        public long TotalFailed => _totalFailed;

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => _totalProcessed > 0 ? (double)_totalSuccessful / _totalProcessed : 0;

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTime => _totalProcessed > 0 ? (double)_totalProcessingTime / _totalProcessed : 0;

        /// <summary>
        /// 增加处理数
        /// </summary>
        public void IncrementProcessed()
        {
            lock (_lock)
            {
                _totalProcessed++;
            }
        }

        /// <summary>
        /// 增加成功数
        /// </summary>
        public void IncrementSuccessful()
        {
            lock (_lock)
            {
                _totalSuccessful++;
            }
        }

        /// <summary>
        /// 增加失败数
        /// </summary>
        public void IncrementFailed()
        {
            lock (_lock)
            {
                _totalFailed++;
            }
        }

        /// <summary>
        /// 更新平均处理时间
        /// </summary>
        /// <param name="processingTime">处理时间（毫秒）</param>
        public void UpdateAverageProcessingTime(long processingTime)
        {
            lock (_lock)
            {
                _totalProcessingTime += processingTime;
            }
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            lock (_lock)
            {
                _totalProcessed = 0;
                _totalSuccessful = 0;
                _totalFailed = 0;
                _totalProcessingTime = 0;
            }
        }
    }
} 