// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using System.ComponentModel.DataAnnotations;

namespace Admin.Communication.Control.Models;

/// <summary>
/// 执行指令请求
/// </summary>
public class ExecuteCommandRequest
{
    /// <summary>
    /// 指令ID
    /// </summary>
    [Required(ErrorMessage = "指令ID不能为空")]
    [Range(1, long.MaxValue, ErrorMessage = "指令ID必须大于0")]
    public long CommandId { get; set; }

    /// <summary>
    /// 目标设备ID（可选，如果不指定则使用指令配置的设备ID）
    /// </summary>
    public long? DeviceId { get; set; }

    /// <summary>
    /// 动态参数（用于替换指令内容中的占位符）
    /// </summary>
    public Dictionary<string, object>? Parameters { get; set; }

    /// <summary>
    /// 来源标识
    /// </summary>
    public string? Source { get; set; } = "手动控制";

    /// <summary>
    /// 操作用户名
    /// </summary>
    public string? Username { get; set; }
}

/// <summary>
/// 批量执行指令请求
/// </summary>
public class BatchExecuteCommandRequest
{
    /// <summary>
    /// 指令ID列表
    /// </summary>
    [Required(ErrorMessage = "指令ID列表不能为空")]
    public List<long> CommandIds { get; set; }

    /// <summary>
    /// 指令间隔时间（毫秒）
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "指令间隔时间不能小于0")]
    public int IntervalMs { get; set; } = 1000;

    /// <summary>
    /// 来源标识
    /// </summary>
    public string? Source { get; set; } = "批量控制";

    /// <summary>
    /// 操作用户名
    /// </summary>
    public string? Username { get; set; }
}

/// <summary>
/// 指令执行结果
/// </summary>
public class CommandExecuteResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 控制记录ID
    /// </summary>
    public long ControlRecordId { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 控制结果
    /// </summary>
    public string? ControlResult { get; set; }

    /// <summary>
    /// 响应内容
    /// </summary>
    public string? ResponseContent { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecuteTime { get; set; } = DateTime.Now;

    public static CommandExecuteResult Success(long recordId, string result, string? responseContent = null)
    {
        return new CommandExecuteResult
        {
            IsSuccess = true,
            ControlRecordId = recordId,
            ControlResult = result,
            ResponseContent = responseContent
        };
    }

    public static CommandExecuteResult Failed(string errorMessage)
    {
        return new CommandExecuteResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 批量执行结果
/// </summary>
public class BatchExecuteResult
{
    /// <summary>
    /// 总数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 详细结果
    /// </summary>
    public List<CommandExecuteResult> Results { get; set; } = new();
}
