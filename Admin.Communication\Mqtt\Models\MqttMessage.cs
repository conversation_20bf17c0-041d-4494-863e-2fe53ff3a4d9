using System;
using System.Text;

namespace Admin.Communication.Mqtt.Models
{
    /// <summary>
    /// MQTT消息基类
    /// </summary>
    public class MqttMessage
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public ushort MessageId { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        public byte MessageType { get; set; }

        /// <summary>
        /// 是否是重复消息
        /// </summary>
        public bool IsDuplicate { get; set; }

        /// <summary>
        /// 服务质量等级
        /// </summary>
        public byte QualityOfService { get; set; }

        /// <summary>
        /// 是否保留消息
        /// </summary>
        public bool Retain { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 协议版本
        /// </summary>
        public byte ProtocolVersion { get; set; } = (byte)MqttProtocol.ProtocolVersion.V311;
    }

    /// <summary>
    /// MQTT连接消息
    /// </summary>
    public class MqttConnectMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttConnectMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.Connect;
        }

        /// <summary>
        /// 协议名称
        /// </summary>
        public string ProtocolName { get; set; } = MqttProtocol.ProtocolName.V311;

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 是否清除会话
        /// </summary>
        public bool CleanSession { get; set; }

        /// <summary>
        /// 保持连接时间(秒)
        /// </summary>
        public ushort KeepAlive { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 是否有遗嘱消息
        /// </summary>
        public bool HasWill { get; set; }

        /// <summary>
        /// 遗嘱消息主题
        /// </summary>
        public string WillTopic { get; set; }

        /// <summary>
        /// 遗嘱消息内容
        /// </summary>
        public byte[] WillMessage { get; set; }

        /// <summary>
        /// 遗嘱消息服务质量等级
        /// </summary>
        public byte WillQoS { get; set; }

        /// <summary>
        /// 是否保留遗嘱消息
        /// </summary>
        public bool WillRetain { get; set; }
    }

    /// <summary>
    /// MQTT连接确认消息
    /// </summary>
    public class MqttConnAckMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttConnAckMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.ConnAck;
        }

        /// <summary>
        /// 是否存在会话
        /// </summary>
        public bool SessionPresent { get; set; }

        /// <summary>
        /// 连接返回码
        /// </summary>
        public MqttProtocol.ConnectReturnCode ReturnCode { get; set; }
    }

    /// <summary>
    /// MQTT发布消息
    /// </summary>
    public class MqttPublishMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttPublishMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.Publish;
        }

        /// <summary>
        /// 主题
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public byte[] Payload { get; set; }

        /// <summary>
        /// 获取消息内容的字符串表示
        /// </summary>
        /// <returns>字符串内容</returns>
        public string GetPayloadAsString()
        {
            return Payload == null ? string.Empty : Encoding.UTF8.GetString(Payload);
        }

        /// <summary>
        /// 设置消息内容
        /// </summary>
        /// <param name="content">字符串内容</param>
        public void SetPayload(string content)
        {
            Payload = string.IsNullOrEmpty(content) ? new byte[0] : Encoding.UTF8.GetBytes(content);
        }
    }

    /// <summary>
    /// MQTT发布确认消息(QoS 1)
    /// </summary>
    public class MqttPubAckMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttPubAckMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.PubAck;
        }
    }

    /// <summary>
    /// MQTT发布已接收消息(QoS 2第一阶段)
    /// </summary>
    public class MqttPubRecMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttPubRecMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.PubRec;
        }
    }

    /// <summary>
    /// MQTT发布释放消息(QoS 2第二阶段)
    /// </summary>
    public class MqttPubRelMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttPubRelMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.PubRel;
            QualityOfService = 1; // PubRel消息必须是QoS 1
        }
    }

    /// <summary>
    /// MQTT发布完成消息(QoS 2第三阶段)
    /// </summary>
    public class MqttPubCompMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttPubCompMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.PubComp;
        }
    }

    /// <summary>
    /// MQTT订阅消息
    /// </summary>
    public class MqttSubscribeMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttSubscribeMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.Subscribe;
            QualityOfService = 1; // Subscribe消息必须是QoS 1
        }

        /// <summary>
        /// 订阅列表
        /// </summary>
        public MqttSubscription[] Subscriptions { get; set; }
    }

    /// <summary>
    /// MQTT订阅确认消息
    /// </summary>
    public class MqttSubAckMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttSubAckMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.SubAck;
        }

        /// <summary>
        /// 返回码列表
        /// </summary>
        public byte[] ReturnCodes { get; set; }
    }

    /// <summary>
    /// MQTT取消订阅消息
    /// </summary>
    public class MqttUnsubscribeMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttUnsubscribeMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.Unsubscribe;
            QualityOfService = 1; // Unsubscribe消息必须是QoS 1
        }

        /// <summary>
        /// 主题过滤器列表
        /// </summary>
        public string[] TopicFilters { get; set; }
    }

    /// <summary>
    /// MQTT取消订阅确认消息
    /// </summary>
    public class MqttUnsubAckMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttUnsubAckMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.UnsubAck;
        }
    }

    /// <summary>
    /// MQTT心跳请求消息
    /// </summary>
    public class MqttPingReqMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttPingReqMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.PingReq;
        }
    }

    /// <summary>
    /// MQTT心跳响应消息
    /// </summary>
    public class MqttPingRespMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttPingRespMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.PingResp;
        }
    }

    /// <summary>
    /// MQTT断开连接消息
    /// </summary>
    public class MqttDisconnectMessage : MqttMessage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MqttDisconnectMessage()
        {
            MessageType = (byte)MqttProtocol.MessageType.Disconnect;
        }
    }
} 