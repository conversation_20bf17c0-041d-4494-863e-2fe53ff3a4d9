// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

namespace Admin.Communication.Modbus.Models;

/// <summary>
/// 指令调度状态
/// </summary>
public class InstructionScheduleState
{
    /// <summary>
    /// 设备指令ID
    /// </summary>
    public long DeviceInstructionId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public string InstructionName { get; set; } = string.Empty;

    /// <summary>
    /// 指令命令串
    /// </summary>
    public string Command { get; set; } = string.Empty;

    /// <summary>
    /// 读取间隔(毫秒)
    /// </summary>
    public int ReadInterval { get; set; }

    /// <summary>
    /// 响应超时时间(毫秒)
    /// </summary>
    public int ResponseTime { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 上次执行时间
    /// </summary>
    public DateTime LastExecutionTime { get; set; }

    /// <summary>
    /// 下次执行时间
    /// </summary>
    public DateTime NextExecutionTime { get; set; }

    /// <summary>
    /// 执行状态
    /// </summary>
    public InstructionExecutionStatus Status { get; set; }

    /// <summary>
    /// 当前重试次数
    /// </summary>
    public int CurrentRetryCount { get; set; }

    /// <summary>
    /// 等待响应的任务
    /// </summary>
    public TaskCompletionSource<ModbusResponse>? PendingResponse { get; set; }

    /// <summary>
    /// 指令发送时间
    /// </summary>
    public DateTime SendTime { get; set; }
}

/// <summary>
/// 指令执行状态枚举
/// </summary>
public enum InstructionExecutionStatus
{
    /// <summary>
    /// 准备执行
    /// </summary>
    Ready,

    /// <summary>
    /// 正在执行
    /// </summary>
    Executing,

    /// <summary>
    /// 等待响应
    /// </summary>
    WaitingResponse,

    /// <summary>
    /// 重试中
    /// </summary>
    Retrying,

    /// <summary>
    /// 执行成功
    /// </summary>
    Success,

    /// <summary>
    /// 执行失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已禁用
    /// </summary>
    Disabled
}
