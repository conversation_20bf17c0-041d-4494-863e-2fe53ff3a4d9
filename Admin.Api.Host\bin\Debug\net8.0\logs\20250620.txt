[00:16:39] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT 5.0连接请求，但会话管理器未配置

[00:16:59] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端 mqttx_a6952e19 消息时发生错误
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadByteAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 92
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 26
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1069
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ProcessClientMessagesAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 646

[00:17:16] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端 mqttx_a6952e19 消息时发生错误
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadByteAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 92
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 26
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1069
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ProcessClientMessagesAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 646

[10:22:09] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139

[10:22:09] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[10:22:09] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[10:22:09] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[10:22:09] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[10:26:11] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139

[10:26:11] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[10:26:11] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[10:26:11] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[10:26:11] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[10:28:28] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端 mqttx_a6952e19 消息时发生错误
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadByteAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 92
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 26
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1081
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ProcessClientMessagesAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 658

[10:28:40] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT 5.0连接请求，但会话管理器未配置

[10:28:45] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
处理客户端 mqttx_a6952e19 消息时发生错误
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.ReadAsync(Byte[] buffer, Int32 offset, Int32 count, CancellationToken cancellationToken)
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ReadByteAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 92
   at Admin.Communication.Mqtt.Protocol.MqttPacketParser.ParsePacketAsync(Stream stream, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Protocol\MqttPacketParser.cs:line 26
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ReadMessageAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 1081
   at Admin.Communication.Mqtt.Services.MqttBrokerService.ProcessClientMessagesAsync(MqttClientConnection connection, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 658

[10:33:46] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT 5.0连接请求，但会话管理器未配置

[12:02:52] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139

[12:02:52] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[12:02:53] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[12:02:53] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[12:02:53] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[12:03:32] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139

[12:03:32] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[12:03:32] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[12:03:32] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[12:03:32] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[12:59:41] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139

[12:59:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[12:59:41] [ERR] Admin.Communication.Mqtt.Services.MqttBrokerHostedService 
启动MQTT代理服务时发生错误
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40

[12:59:41] [ERR] Microsoft.Extensions.Hosting.Internal.Host 
Hosting failed to start
System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at System.Net.Sockets.TcpListener.Start(Int32 backlog)
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 139
   at Admin.Communication.Mqtt.Services.MqttBrokerService.StartAsync(Int32 port, CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerService.cs:line 153
   at Admin.Communication.Mqtt.Services.MqttBrokerHostedService.StartAsync(CancellationToken cancellationToken) in D:\code projects\purest-admin-main\api\Admin.Communication\Mqtt\Services\MqttBrokerHostedService.cs:line 40
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)

[12:59:41] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

[14:05:58] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT 5.0连接请求，但会话管理器未配置

[16:38:57] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT 5.0连接请求，但会话管理器未配置

[16:39:55] [WRN] Admin.Communication.Mqtt.Services.MqttBrokerService 
MQTT代理服务未在运行

