using System;
using System.Diagnostics;
using System.IO.Compression;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Admin.Communication.Mqtt.Serialization
{
    /// <summary>
    /// JSON消息序列化器
    /// </summary>
    public class JsonMessageSerializer : IMessageSerializer
    {
        private readonly ILogger<JsonMessageSerializer> _logger;
        private SerializerOptions _options;
        private JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// 序列化器名称
        /// </summary>
        public string Name => "JsonMessageSerializer";

        /// <summary>
        /// 序列化器描述
        /// </summary>
        public string Description => "基于System.Text.Json的JSON消息序列化器，支持高性能序列化和反序列化";

        /// <summary>
        /// 支持的内容类型
        /// </summary>
        public string ContentType => "application/json";

        /// <summary>
        /// 序列化器优先级
        /// </summary>
        public int Priority => 10;

        /// <summary>
        /// 初始化JSON序列化器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public JsonMessageSerializer(ILogger<JsonMessageSerializer> logger = null)
        {
            _logger = logger;
            _options = new SerializerOptions();
            UpdateJsonOptions();
        }

        /// <summary>
        /// 是否支持指定的类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否支持</returns>
        public bool CanSerialize(Type type)
        {
            if (type == null)
                return false;

            // 支持大部分常见类型，排除一些特殊类型
            if (type == typeof(IntPtr) || type == typeof(UIntPtr))
                return false;

            if (type.IsPointer)
                return false;

            // 支持基本类型、字符串、数组、集合、自定义类等
            return true;
        }

        /// <summary>
        /// 序列化对象到字节数组
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>序列化后的字节数组</returns>
        public byte[] Serialize<T>(T obj)
        {
            try
            {
                if (obj == null)
                {
                    return Encoding.UTF8.GetBytes("null");
                }

                var json = JsonSerializer.Serialize(obj, _jsonOptions);
                var data = Encoding.UTF8.GetBytes(json);

                // 如果启用压缩
                if (_options.EnableCompression)
                {
                    data = CompressData(data);
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "序列化对象失败: {ObjectType}", typeof(T).Name);
                throw new InvalidOperationException($"序列化对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 异步序列化对象到字节数组
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>序列化后的字节数组</returns>
        public async Task<byte[]> SerializeAsync<T>(T obj)
        {
            return await Task.Run(() => Serialize(obj));
        }

        /// <summary>
        /// 反序列化字节数组到对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="data">要反序列化的字节数组</param>
        /// <returns>反序列化后的对象</returns>
        public T Deserialize<T>(byte[] data)
        {
            try
            {
                if (data == null || data.Length == 0)
                {
                    return default(T);
                }

                // 如果启用了压缩，先解压
                if (_options.EnableCompression && IsCompressed(data))
                {
                    data = DecompressData(data);
                }

                var json = Encoding.UTF8.GetString(data);
                
                // 处理null值
                if (json == "null")
                {
                    return default(T);
                }

                return JsonSerializer.Deserialize<T>(json, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "反序列化对象失败: {TargetType}", typeof(T).Name);
                throw new InvalidOperationException($"反序列化对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 异步反序列化字节数组到对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="data">要反序列化的字节数组</param>
        /// <returns>反序列化后的对象</returns>
        public async Task<T> DeserializeAsync<T>(byte[] data)
        {
            return await Task.Run(() => Deserialize<T>(data));
        }

        /// <summary>
        /// 反序列化字节数组到指定类型的对象
        /// </summary>
        /// <param name="data">要反序列化的字节数组</param>
        /// <param name="type">目标类型</param>
        /// <returns>反序列化后的对象</returns>
        public object Deserialize(byte[] data, Type type)
        {
            try
            {
                if (data == null || data.Length == 0)
                {
                    return type.IsValueType ? Activator.CreateInstance(type) : null;
                }

                // 如果启用了压缩，先解压
                if (_options.EnableCompression && IsCompressed(data))
                {
                    data = DecompressData(data);
                }

                var json = Encoding.UTF8.GetString(data);
                
                // 处理null值
                if (json == "null")
                {
                    return type.IsValueType ? Activator.CreateInstance(type) : null;
                }

                return JsonSerializer.Deserialize(json, type, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "反序列化对象失败: {TargetType}", type.Name);
                throw new InvalidOperationException($"反序列化对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 异步反序列化字节数组到指定类型的对象
        /// </summary>
        /// <param name="data">要反序列化的字节数组</param>
        /// <param name="type">目标类型</param>
        /// <returns>反序列化后的对象</returns>
        public async Task<object> DeserializeAsync(byte[] data, Type type)
        {
            return await Task.Run(() => Deserialize(data, type));
        }

        /// <summary>
        /// 序列化对象到字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>序列化后的字符串</returns>
        public string SerializeToString<T>(T obj)
        {
            try
            {
                return JsonSerializer.Serialize(obj, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "序列化对象到字符串失败: {ObjectType}", typeof(T).Name);
                throw new InvalidOperationException($"序列化对象到字符串失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从字符串反序列化对象
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="data">要反序列化的字符串</param>
        /// <returns>反序列化后的对象</returns>
        public T DeserializeFromString<T>(string data)
        {
            try
            {
                if (string.IsNullOrEmpty(data) || data == "null")
                {
                    return default(T);
                }

                return JsonSerializer.Deserialize<T>(data, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "从字符串反序列化对象失败: {TargetType}", typeof(T).Name);
                throw new InvalidOperationException($"从字符串反序列化对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证数据是否可以被此序列化器反序列化
        /// </summary>
        /// <param name="data">要验证的数据</param>
        /// <returns>是否可以反序列化</returns>
        public bool CanDeserialize(byte[] data)
        {
            if (data == null || data.Length == 0)
                return false;

            try
            {
                // 如果启用了压缩，先尝试解压
                var testData = data;
                if (_options.EnableCompression && IsCompressed(data))
                {
                    testData = DecompressData(data);
                }

                var json = Encoding.UTF8.GetString(testData);
                
                // 简单的JSON格式验证
                json = json.Trim();
                if (string.IsNullOrEmpty(json))
                    return false;

                // 检查是否是有效的JSON格式
                return (json.StartsWith("{") && json.EndsWith("}")) ||
                       (json.StartsWith("[") && json.EndsWith("]")) ||
                       json == "null" ||
                       json == "true" ||
                       json == "false" ||
                       json.StartsWith("\"") && json.EndsWith("\"") ||
                       double.TryParse(json, out _);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取序列化器的配置选项
        /// </summary>
        /// <returns>配置选项</returns>
        public SerializerOptions GetOptions()
        {
            return _options;
        }

        /// <summary>
        /// 设置序列化器的配置选项
        /// </summary>
        /// <param name="options">配置选项</param>
        public void SetOptions(SerializerOptions options)
        {
            _options = options ?? new SerializerOptions();
            UpdateJsonOptions();
        }

        #region 私有方法

        /// <summary>
        /// 更新JSON序列化选项
        /// </summary>
        private void UpdateJsonOptions()
        {
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                WriteIndented = _options.Indented,
                DefaultIgnoreCondition = _options.IgnoreNullValues ? 
                    System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull : 
                    System.Text.Json.Serialization.JsonIgnoreCondition.Never,
                PropertyNamingPolicy = _options.UseCamelCase ? JsonNamingPolicy.CamelCase : null,
                MaxDepth = _options.MaxDepth,
                AllowTrailingCommas = true,
                ReadCommentHandling = JsonCommentHandling.Skip
            };
        }

        /// <summary>
        /// 压缩数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>压缩后的数据</returns>
        private byte[] CompressData(byte[] data)
        {
            try
            {
                using var output = new System.IO.MemoryStream();
                using (var gzip = new GZipStream(output, CompressionMode.Compress))
                {
                    gzip.Write(data, 0, data.Length);
                }
                
                var compressed = output.ToArray();
                
                // 添加压缩标识头（4字节魔数）
                var result = new byte[compressed.Length + 4];
                BitConverter.GetBytes(0x1F8B0800).CopyTo(result, 0); // GZIP魔数
                compressed.CopyTo(result, 4);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "数据压缩失败，返回原始数据");
                return data;
            }
        }

        /// <summary>
        /// 解压数据
        /// </summary>
        /// <param name="data">压缩的数据</param>
        /// <returns>解压后的数据</returns>
        private byte[] DecompressData(byte[] data)
        {
            try
            {
                // 移除压缩标识头
                var compressedData = new byte[data.Length - 4];
                Array.Copy(data, 4, compressedData, 0, compressedData.Length);

                using var input = new System.IO.MemoryStream(compressedData);
                using var gzip = new GZipStream(input, CompressionMode.Decompress);
                using var output = new System.IO.MemoryStream();
                
                gzip.CopyTo(output);
                return output.ToArray();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "数据解压失败，返回原始数据");
                return data;
            }
        }

        /// <summary>
        /// 检查数据是否被压缩
        /// </summary>
        /// <param name="data">要检查的数据</param>
        /// <returns>是否被压缩</returns>
        private bool IsCompressed(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // 检查压缩标识头
            var magicNumber = BitConverter.ToInt32(data, 0);
            return magicNumber == 0x1F8B0800;
        }

        #endregion
    }

    /// <summary>
    /// JSON序列化器扩展方法
    /// </summary>
    public static class JsonSerializerExtensions
    {
        /// <summary>
        /// 创建带有默认配置的JSON序列化器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <returns>JSON序列化器</returns>
        public static JsonMessageSerializer CreateDefault(ILogger<JsonMessageSerializer> logger = null)
        {
            return new JsonMessageSerializer(logger);
        }

        /// <summary>
        /// 创建带有压缩功能的JSON序列化器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <returns>JSON序列化器</returns>
        public static JsonMessageSerializer CreateWithCompression(ILogger<JsonMessageSerializer> logger = null)
        {
            var serializer = new JsonMessageSerializer(logger);
            var options = serializer.GetOptions();
            options.EnableCompression = true;
            serializer.SetOptions(options);
            return serializer;
        }

        /// <summary>
        /// 创建带有格式化的JSON序列化器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <returns>JSON序列化器</returns>
        public static JsonMessageSerializer CreateWithIndentation(ILogger<JsonMessageSerializer> logger = null)
        {
            var serializer = new JsonMessageSerializer(logger);
            var options = serializer.GetOptions();
            options.Indented = true;
            serializer.SetOptions(options);
            return serializer;
        }
    }
} 