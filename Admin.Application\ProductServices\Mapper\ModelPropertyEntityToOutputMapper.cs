// Copyright © 2023-present https://github.com/dymproject/purest-admin作者以及贡献者

using Admin.Application.ProductServices.Dto;
using Admin.SqlSugar.Entity.Business.LOT;

namespace Admin.Application.ProductServices.Mapper;

/// <summary>
/// 模型属性实体到输出DTO的映射配置
/// </summary>
public class ModelPropertyEntityToOutputMapper : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // 配置 ModelPropertyEntity 到 ModelPropertyOutput 的映射，包含主键和枚举名称转换
        config.ForType<ModelPropertyEntity, ModelPropertyOutput>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.DataTypeName, src => GetDataTypeName(src.DataType));

        // 配置 ModelPropertyEntity 到 ModelPropertySimpleOutput 的映射
        config.ForType<ModelPropertyEntity, ModelPropertySimpleOutput>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.DataTypeName, src => GetDataTypeName(src.DataType));

        // 为了向后兼容，保留原有的映射配置
        config.ForType<ModelPropertyEntity, ProductPropertyOutput>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.DataTypeName, src => GetDataTypeName(src.DataType));

        config.ForType<ModelPropertyEntity, ProductPropertySimpleOutput>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.DataTypeName, src => GetDataTypeName(src.DataType));
    }

    /// <summary>
    /// 获取数据类型名称
    /// </summary>
    private static string GetDataTypeName(int dataType) => dataType switch
    {
        1 => "decimal模拟量",
        2 => "string字符串",
        3 => "datetime时间",
        4 => "json",
        5 => "enum枚举",
        _ => "未知"
    };
}